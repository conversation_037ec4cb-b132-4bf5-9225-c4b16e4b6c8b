{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/decoder/Decoder.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport DecoderResult from '../../common/DecoderResult';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport IllegalStateException from '../../IllegalStateException';\nimport FormatException from '../../FormatException';\nimport StringUtils from '../../common/StringUtils';\nimport Integer from '../../util/Integer';\n// import java.util.Arrays;\nvar Table;\n(function (Table) {\n    Table[Table[\"UPPER\"] = 0] = \"UPPER\";\n    Table[Table[\"LOWER\"] = 1] = \"LOWER\";\n    Table[Table[\"MIXED\"] = 2] = \"MIXED\";\n    Table[Table[\"DIGIT\"] = 3] = \"DIGIT\";\n    Table[Table[\"PUNCT\"] = 4] = \"PUNCT\";\n    Table[Table[\"BINARY\"] = 5] = \"BINARY\";\n})(Table || (Table = {}));\n/**\n * <p>The main class which implements Aztec Code decoding -- as opposed to locating and extracting\n * the Aztec Code from an image.</p>\n *\n * <AUTHOR> Olivier\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n    }\n    Decoder.prototype.decode = function (detectorResult) {\n        this.ddata = detectorResult;\n        var matrix = detectorResult.getBits();\n        var rawbits = this.extractBits(matrix);\n        var correctedBits = this.correctBits(rawbits);\n        var rawBytes = Decoder.convertBoolArrayToByteArray(correctedBits);\n        var result = Decoder.getEncodedData(correctedBits);\n        var decoderResult = new DecoderResult(rawBytes, result, null, null);\n        decoderResult.setNumBits(correctedBits.length);\n        return decoderResult;\n    };\n    // This method is used for testing the high-level encoder\n    Decoder.highLevelDecode = function (correctedBits) {\n        return this.getEncodedData(correctedBits);\n    };\n    /**\n     * Gets the string encoded in the aztec code bits\n     *\n     * @return the decoded string\n     */\n    Decoder.getEncodedData = function (correctedBits) {\n        var endIndex = correctedBits.length;\n        var latchTable = Table.UPPER; // table most recently latched to\n        var shiftTable = Table.UPPER; // table to use for the next read\n        var result = '';\n        var index = 0;\n        while (index < endIndex) {\n            if (shiftTable === Table.BINARY) {\n                if (endIndex - index < 5) {\n                    break;\n                }\n                var length_1 = Decoder.readCode(correctedBits, index, 5);\n                index += 5;\n                if (length_1 === 0) {\n                    if (endIndex - index < 11) {\n                        break;\n                    }\n                    length_1 = Decoder.readCode(correctedBits, index, 11) + 31;\n                    index += 11;\n                }\n                for (var charCount = 0; charCount < length_1; charCount++) {\n                    if (endIndex - index < 8) {\n                        index = endIndex; // Force outer loop to exit\n                        break;\n                    }\n                    var code = Decoder.readCode(correctedBits, index, 8);\n                    result += /*(char)*/ StringUtils.castAsNonUtf8Char(code);\n                    index += 8;\n                }\n                // Go back to whatever mode we had been in\n                shiftTable = latchTable;\n            }\n            else {\n                var size = shiftTable === Table.DIGIT ? 4 : 5;\n                if (endIndex - index < size) {\n                    break;\n                }\n                var code = Decoder.readCode(correctedBits, index, size);\n                index += size;\n                var str = Decoder.getCharacter(shiftTable, code);\n                if (str.startsWith('CTRL_')) {\n                    // Table changes\n                    // ISO/IEC 24778:2008 prescribes ending a shift sequence in the mode from which it was invoked.\n                    // That's including when that mode is a shift.\n                    // Our test case dlusbs.png for issue #642 exercises that.\n                    latchTable = shiftTable; // Latch the current mode, so as to return to Upper after U/S B/S\n                    shiftTable = Decoder.getTable(str.charAt(5));\n                    if (str.charAt(6) === 'L') {\n                        latchTable = shiftTable;\n                    }\n                }\n                else {\n                    result += str;\n                    // Go back to whatever mode we had been in\n                    shiftTable = latchTable;\n                }\n            }\n        }\n        return result;\n    };\n    /**\n     * gets the table corresponding to the char passed\n     */\n    Decoder.getTable = function (t) {\n        switch (t) {\n            case 'L':\n                return Table.LOWER;\n            case 'P':\n                return Table.PUNCT;\n            case 'M':\n                return Table.MIXED;\n            case 'D':\n                return Table.DIGIT;\n            case 'B':\n                return Table.BINARY;\n            case 'U':\n            default:\n                return Table.UPPER;\n        }\n    };\n    /**\n     * Gets the character (or string) corresponding to the passed code in the given table\n     *\n     * @param table the table used\n     * @param code the code of the character\n     */\n    Decoder.getCharacter = function (table, code) {\n        switch (table) {\n            case Table.UPPER:\n                return Decoder.UPPER_TABLE[code];\n            case Table.LOWER:\n                return Decoder.LOWER_TABLE[code];\n            case Table.MIXED:\n                return Decoder.MIXED_TABLE[code];\n            case Table.PUNCT:\n                return Decoder.PUNCT_TABLE[code];\n            case Table.DIGIT:\n                return Decoder.DIGIT_TABLE[code];\n            default:\n                // Should not reach here.\n                throw new IllegalStateException('Bad table');\n        }\n    };\n    /**\n     * <p>Performs RS error correction on an array of bits.</p>\n     *\n     * @return the corrected array\n     * @throws FormatException if the input contains too many errors\n     */\n    Decoder.prototype.correctBits = function (rawbits) {\n        var gf;\n        var codewordSize;\n        if (this.ddata.getNbLayers() <= 2) {\n            codewordSize = 6;\n            gf = GenericGF.AZTEC_DATA_6;\n        }\n        else if (this.ddata.getNbLayers() <= 8) {\n            codewordSize = 8;\n            gf = GenericGF.AZTEC_DATA_8;\n        }\n        else if (this.ddata.getNbLayers() <= 22) {\n            codewordSize = 10;\n            gf = GenericGF.AZTEC_DATA_10;\n        }\n        else {\n            codewordSize = 12;\n            gf = GenericGF.AZTEC_DATA_12;\n        }\n        var numDataCodewords = this.ddata.getNbDatablocks();\n        var numCodewords = rawbits.length / codewordSize;\n        if (numCodewords < numDataCodewords) {\n            throw new FormatException();\n        }\n        var offset = rawbits.length % codewordSize;\n        var dataWords = new Int32Array(numCodewords);\n        for (var i = 0; i < numCodewords; i++, offset += codewordSize) {\n            dataWords[i] = Decoder.readCode(rawbits, offset, codewordSize);\n        }\n        try {\n            var rsDecoder = new ReedSolomonDecoder(gf);\n            rsDecoder.decode(dataWords, numCodewords - numDataCodewords);\n        }\n        catch (ex) {\n            throw new FormatException(ex);\n        }\n        // Now perform the unstuffing operation.\n        // First, count how many bits are going to be thrown out as stuffing\n        var mask = (1 << codewordSize) - 1;\n        var stuffedBits = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            var dataWord = dataWords[i];\n            if (dataWord === 0 || dataWord === mask) {\n                throw new FormatException();\n            }\n            else if (dataWord === 1 || dataWord === mask - 1) {\n                stuffedBits++;\n            }\n        }\n        // Now, actually unpack the bits and remove the stuffing\n        var correctedBits = new Array(numDataCodewords * codewordSize - stuffedBits);\n        var index = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            var dataWord = dataWords[i];\n            if (dataWord === 1 || dataWord === mask - 1) {\n                // next codewordSize-1 bits are all zeros or all ones\n                correctedBits.fill(dataWord > 1, index, index + codewordSize - 1);\n                // Arrays.fill(correctedBits, index, index + codewordSize - 1, dataWord > 1);\n                index += codewordSize - 1;\n            }\n            else {\n                for (var bit = codewordSize - 1; bit >= 0; --bit) {\n                    correctedBits[index++] = (dataWord & (1 << bit)) !== 0;\n                }\n            }\n        }\n        return correctedBits;\n    };\n    /**\n     * Gets the array of bits from an Aztec Code matrix\n     *\n     * @return the array of bits\n     */\n    Decoder.prototype.extractBits = function (matrix) {\n        var compact = this.ddata.isCompact();\n        var layers = this.ddata.getNbLayers();\n        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n        var alignmentMap = new Int32Array(baseMatrixSize);\n        var rawbits = new Array(this.totalBitsInLayer(layers, compact));\n        if (compact) {\n            for (var i = 0; i < alignmentMap.length; i++) {\n                alignmentMap[i] = i;\n            }\n        }\n        else {\n            var matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision((Integer.truncDivision(baseMatrixSize, 2) - 1), 15);\n            var origCenter = baseMatrixSize / 2;\n            var center = Integer.truncDivision(matrixSize, 2);\n            for (var i = 0; i < origCenter; i++) {\n                var newOffset = i + Integer.truncDivision(i, 15);\n                alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n                alignmentMap[origCenter + i] = center + newOffset + 1;\n            }\n        }\n        for (var i = 0, rowOffset = 0; i < layers; i++) {\n            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n            // The top-left most point of this layer is <low, low> (not including alignment lines)\n            var low = i * 2;\n            // The bottom-right most point of this layer is <high, high> (not including alignment lines)\n            var high = baseMatrixSize - 1 - low;\n            // We pull bits from the two 2 x rowSize columns and two rowSize x 2 rows\n            for (var j = 0; j < rowSize; j++) {\n                var columnOffset = j * 2;\n                for (var k = 0; k < 2; k++) {\n                    // left column\n                    rawbits[rowOffset + columnOffset + k] =\n                        matrix.get(alignmentMap[low + k], alignmentMap[low + j]);\n                    // bottom row\n                    rawbits[rowOffset + 2 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[low + j], alignmentMap[high - k]);\n                    // right column\n                    rawbits[rowOffset + 4 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[high - k], alignmentMap[high - j]);\n                    // top row\n                    rawbits[rowOffset + 6 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[high - j], alignmentMap[low + k]);\n                }\n            }\n            rowOffset += rowSize * 8;\n        }\n        return rawbits;\n    };\n    /**\n     * Reads a code of given length and at given index in an array of bits\n     */\n    Decoder.readCode = function (rawbits, startIndex, length) {\n        var res = 0;\n        for (var i = startIndex; i < startIndex + length; i++) {\n            res <<= 1;\n            if (rawbits[i]) {\n                res |= 0x01;\n            }\n        }\n        return res;\n    };\n    /**\n     * Reads a code of length 8 in an array of bits, padding with zeros\n     */\n    Decoder.readByte = function (rawbits, startIndex) {\n        var n = rawbits.length - startIndex;\n        if (n >= 8) {\n            return Decoder.readCode(rawbits, startIndex, 8);\n        }\n        return Decoder.readCode(rawbits, startIndex, n) << (8 - n);\n    };\n    /**\n     * Packs a bit array into bytes, most significant bit first\n     */\n    Decoder.convertBoolArrayToByteArray = function (boolArr) {\n        var byteArr = new Uint8Array((boolArr.length + 7) / 8);\n        for (var i = 0; i < byteArr.length; i++) {\n            byteArr[i] = Decoder.readByte(boolArr, 8 * i);\n        }\n        return byteArr;\n    };\n    Decoder.prototype.totalBitsInLayer = function (layers, compact) {\n        return ((compact ? 88 : 112) + 16 * layers) * layers;\n    };\n    Decoder.UPPER_TABLE = [\n        'CTRL_PS', ' ', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',\n        'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'CTRL_LL', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'\n    ];\n    Decoder.LOWER_TABLE = [\n        'CTRL_PS', ' ', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p',\n        'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'CTRL_US', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'\n    ];\n    Decoder.MIXED_TABLE = [\n        'CTRL_PS', ' ', '\\x01', '\\x02', '\\x03', '\\x04', '\\x05', '\\x06', '\\x07', '\\b', '\\t', '\\n',\n        '\\x0b', '\\f', '\\r', '\\x1b', '\\x1c', '\\x1d', '\\x1e', '\\x1f', '@', '\\\\', '^', '_',\n        '`', '|', '~', '\\x7f', 'CTRL_LL', 'CTRL_UL', 'CTRL_PL', 'CTRL_BS'\n    ];\n    Decoder.PUNCT_TABLE = [\n        '', '\\r', '\\r\\n', '. ', ', ', ': ', '!', '\"', '#', '$', '%', '&', '\\'', '(', ')',\n        '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '[', ']', '{', '}', 'CTRL_UL'\n    ];\n    Decoder.DIGIT_TABLE = [\n        'CTRL_PS', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ',', '.', 'CTRL_UL', 'CTRL_US'\n    ];\n    return Decoder;\n}());\nexport default Decoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,2BAA2B;AAC3B,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG;AACjC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;AACvB;;;;;CAKC,GACD,IAAI,UAAyB;IACzB,SAAS,WACT;IACA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,cAAc;QAC/C,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,SAAS,eAAe,OAAO;QACnC,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;QAC/B,IAAI,gBAAgB,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,QAAQ,2BAA2B,CAAC;QACnD,IAAI,SAAS,QAAQ,cAAc,CAAC;QACpC,IAAI,gBAAgB,IAAI,+KAAA,CAAA,UAAa,CAAC,UAAU,QAAQ,MAAM;QAC9D,cAAc,UAAU,CAAC,cAAc,MAAM;QAC7C,OAAO;IACX;IACA,yDAAyD;IACzD,QAAQ,eAAe,GAAG,SAAU,aAAa;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA;;;;KAIC,GACD,QAAQ,cAAc,GAAG,SAAU,aAAa;QAC5C,IAAI,WAAW,cAAc,MAAM;QACnC,IAAI,aAAa,MAAM,KAAK,EAAE,iCAAiC;QAC/D,IAAI,aAAa,MAAM,KAAK,EAAE,iCAAiC;QAC/D,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,MAAO,QAAQ,SAAU;YACrB,IAAI,eAAe,MAAM,MAAM,EAAE;gBAC7B,IAAI,WAAW,QAAQ,GAAG;oBACtB;gBACJ;gBACA,IAAI,WAAW,QAAQ,QAAQ,CAAC,eAAe,OAAO;gBACtD,SAAS;gBACT,IAAI,aAAa,GAAG;oBAChB,IAAI,WAAW,QAAQ,IAAI;wBACvB;oBACJ;oBACA,WAAW,QAAQ,QAAQ,CAAC,eAAe,OAAO,MAAM;oBACxD,SAAS;gBACb;gBACA,IAAK,IAAI,YAAY,GAAG,YAAY,UAAU,YAAa;oBACvD,IAAI,WAAW,QAAQ,GAAG;wBACtB,QAAQ,UAAU,2BAA2B;wBAC7C;oBACJ;oBACA,IAAI,OAAO,QAAQ,QAAQ,CAAC,eAAe,OAAO;oBAClD,UAAU,QAAQ,GAAG,6KAAA,CAAA,UAAW,CAAC,iBAAiB,CAAC;oBACnD,SAAS;gBACb;gBACA,0CAA0C;gBAC1C,aAAa;YACjB,OACK;gBACD,IAAI,OAAO,eAAe,MAAM,KAAK,GAAG,IAAI;gBAC5C,IAAI,WAAW,QAAQ,MAAM;oBACzB;gBACJ;gBACA,IAAI,OAAO,QAAQ,QAAQ,CAAC,eAAe,OAAO;gBAClD,SAAS;gBACT,IAAI,MAAM,QAAQ,YAAY,CAAC,YAAY;gBAC3C,IAAI,IAAI,UAAU,CAAC,UAAU;oBACzB,gBAAgB;oBAChB,+FAA+F;oBAC/F,8CAA8C;oBAC9C,0DAA0D;oBAC1D,aAAa,YAAY,iEAAiE;oBAC1F,aAAa,QAAQ,QAAQ,CAAC,IAAI,MAAM,CAAC;oBACzC,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK;wBACvB,aAAa;oBACjB;gBACJ,OACK;oBACD,UAAU;oBACV,0CAA0C;oBAC1C,aAAa;gBACjB;YACJ;QACJ;QACA,OAAO;IACX;IACA;;KAEC,GACD,QAAQ,QAAQ,GAAG,SAAU,CAAC;QAC1B,OAAQ;YACJ,KAAK;gBACD,OAAO,MAAM,KAAK;YACtB,KAAK;gBACD,OAAO,MAAM,KAAK;YACtB,KAAK;gBACD,OAAO,MAAM,KAAK;YACtB,KAAK;gBACD,OAAO,MAAM,KAAK;YACtB,KAAK;gBACD,OAAO,MAAM,MAAM;YACvB,KAAK;YACL;gBACI,OAAO,MAAM,KAAK;QAC1B;IACJ;IACA;;;;;KAKC,GACD,QAAQ,YAAY,GAAG,SAAU,KAAK,EAAE,IAAI;QACxC,OAAQ;YACJ,KAAK,MAAM,KAAK;gBACZ,OAAO,QAAQ,WAAW,CAAC,KAAK;YACpC,KAAK,MAAM,KAAK;gBACZ,OAAO,QAAQ,WAAW,CAAC,KAAK;YACpC,KAAK,MAAM,KAAK;gBACZ,OAAO,QAAQ,WAAW,CAAC,KAAK;YACpC,KAAK,MAAM,KAAK;gBACZ,OAAO,QAAQ,WAAW,CAAC,KAAK;YACpC,KAAK,MAAM,KAAK;gBACZ,OAAO,QAAQ,WAAW,CAAC,KAAK;YACpC;gBACI,yBAAyB;gBACzB,MAAM,IAAI,6KAAA,CAAA,UAAqB,CAAC;QACxC;IACJ;IACA;;;;;KAKC,GACD,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO;QAC7C,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,MAAM,GAAG;YAC/B,eAAe;YACf,KAAK,0LAAA,CAAA,UAAS,CAAC,YAAY;QAC/B,OACK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,MAAM,GAAG;YACpC,eAAe;YACf,KAAK,0LAAA,CAAA,UAAS,CAAC,YAAY;QAC/B,OACK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,MAAM,IAAI;YACrC,eAAe;YACf,KAAK,0LAAA,CAAA,UAAS,CAAC,aAAa;QAChC,OACK;YACD,eAAe;YACf,KAAK,0LAAA,CAAA,UAAS,CAAC,aAAa;QAChC;QACA,IAAI,mBAAmB,IAAI,CAAC,KAAK,CAAC,eAAe;QACjD,IAAI,eAAe,QAAQ,MAAM,GAAG;QACpC,IAAI,eAAe,kBAAkB;YACjC,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI,SAAS,QAAQ,MAAM,GAAG;QAC9B,IAAI,YAAY,IAAI,WAAW;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,KAAK,UAAU,aAAc;YAC3D,SAAS,CAAC,EAAE,GAAG,QAAQ,QAAQ,CAAC,SAAS,QAAQ;QACrD;QACA,IAAI;YACA,IAAI,YAAY,IAAI,mMAAA,CAAA,UAAkB,CAAC;YACvC,UAAU,MAAM,CAAC,WAAW,eAAe;QAC/C,EACA,OAAO,IAAI;YACP,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,wCAAwC;QACxC,oEAAoE;QACpE,IAAI,OAAO,CAAC,KAAK,YAAY,IAAI;QACjC,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;YACvC,IAAI,WAAW,SAAS,CAAC,EAAE;YAC3B,IAAI,aAAa,KAAK,aAAa,MAAM;gBACrC,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B,OACK,IAAI,aAAa,KAAK,aAAa,OAAO,GAAG;gBAC9C;YACJ;QACJ;QACA,wDAAwD;QACxD,IAAI,gBAAgB,IAAI,MAAM,mBAAmB,eAAe;QAChE,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;YACvC,IAAI,WAAW,SAAS,CAAC,EAAE;YAC3B,IAAI,aAAa,KAAK,aAAa,OAAO,GAAG;gBACzC,qDAAqD;gBACrD,cAAc,IAAI,CAAC,WAAW,GAAG,OAAO,QAAQ,eAAe;gBAC/D,6EAA6E;gBAC7E,SAAS,eAAe;YAC5B,OACK;gBACD,IAAK,IAAI,MAAM,eAAe,GAAG,OAAO,GAAG,EAAE,IAAK;oBAC9C,aAAa,CAAC,QAAQ,GAAG,CAAC,WAAY,KAAK,GAAI,MAAM;gBACzD;YACJ;QACJ;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;QAC5C,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,SAAS;QAClC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW;QACnC,IAAI,iBAAiB,CAAC,UAAU,KAAK,EAAE,IAAI,SAAS,GAAG,gCAAgC;QACvF,IAAI,eAAe,IAAI,WAAW;QAClC,IAAI,UAAU,IAAI,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACtD,IAAI,SAAS;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC1C,YAAY,CAAC,EAAE,GAAG;YACtB;QACJ,OACK;YACD,IAAI,aAAa,iBAAiB,IAAI,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAE,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,gBAAgB,KAAK,GAAI;YAChH,IAAI,aAAa,iBAAiB;YAClC,IAAI,SAAS,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY;YAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;gBACjC,IAAI,YAAY,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,GAAG;gBAC7C,YAAY,CAAC,aAAa,IAAI,EAAE,GAAG,SAAS,YAAY;gBACxD,YAAY,CAAC,aAAa,EAAE,GAAG,SAAS,YAAY;YACxD;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,YAAY,GAAG,IAAI,QAAQ,IAAK;YAC5C,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE;YAClD,sFAAsF;YACtF,IAAI,MAAM,IAAI;YACd,4FAA4F;YAC5F,IAAI,OAAO,iBAAiB,IAAI;YAChC,yEAAyE;YACzE,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAC9B,IAAI,eAAe,IAAI;gBACvB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBACxB,cAAc;oBACd,OAAO,CAAC,YAAY,eAAe,EAAE,GACjC,OAAO,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;oBAC3D,aAAa;oBACb,OAAO,CAAC,YAAY,IAAI,UAAU,eAAe,EAAE,GAC/C,OAAO,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,OAAO,EAAE;oBAC5D,eAAe;oBACf,OAAO,CAAC,YAAY,IAAI,UAAU,eAAe,EAAE,GAC/C,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,OAAO,EAAE;oBAC7D,UAAU;oBACV,OAAO,CAAC,YAAY,IAAI,UAAU,eAAe,EAAE,GAC/C,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;gBAChE;YACJ;YACA,aAAa,UAAU;QAC3B;QACA,OAAO;IACX;IACA;;KAEC,GACD,QAAQ,QAAQ,GAAG,SAAU,OAAO,EAAE,UAAU,EAAE,MAAM;QACpD,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,YAAY,IAAI,aAAa,QAAQ,IAAK;YACnD,QAAQ;YACR,IAAI,OAAO,CAAC,EAAE,EAAE;gBACZ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;KAEC,GACD,QAAQ,QAAQ,GAAG,SAAU,OAAO,EAAE,UAAU;QAC5C,IAAI,IAAI,QAAQ,MAAM,GAAG;QACzB,IAAI,KAAK,GAAG;YACR,OAAO,QAAQ,QAAQ,CAAC,SAAS,YAAY;QACjD;QACA,OAAO,QAAQ,QAAQ,CAAC,SAAS,YAAY,MAAO,IAAI;IAC5D;IACA;;KAEC,GACD,QAAQ,2BAA2B,GAAG,SAAU,OAAO;QACnD,IAAI,UAAU,IAAI,WAAW,CAAC,QAAQ,MAAM,GAAG,CAAC,IAAI;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,OAAO,CAAC,EAAE,GAAG,QAAQ,QAAQ,CAAC,SAAS,IAAI;QAC/C;QACA,OAAO;IACX;IACA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM,EAAE,OAAO;QAC1D,OAAO,CAAC,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;IAClD;IACA,QAAQ,WAAW,GAAG;QAClB;QAAW;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC3F;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAW;QAAW;QAAW;KACtF;IACD,QAAQ,WAAW,GAAG;QAClB;QAAW;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC3F;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAW;QAAW;QAAW;KACtF;IACD,QAAQ,WAAW,GAAG;QAClB;QAAW;QAAK;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAM;QAAM;QACpF;QAAQ;QAAM;QAAM;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAK;QAAM;QAAK;QAC5E;QAAK;QAAK;QAAK;QAAQ;QAAW;QAAW;QAAW;KAC3D;IACD,QAAQ,WAAW,GAAG;QAClB;QAAI;QAAM;QAAQ;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAC7E;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KACnF;IACD,QAAQ,WAAW,GAAG;QAClB;QAAW;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAW;KAC1F;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/AztecDetectorResult.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DetectorResult from '../common/DetectorResult';\n/**\n * <p>Extends {@link DetectorResult} with more information specific to the Aztec format,\n * like the number of layers and whether it's compact.</p>\n *\n * <AUTHOR> Owen\n */\nvar AztecDetectorResult = /** @class */ (function (_super) {\n    __extends(AztecDetectorResult, _super);\n    function AztecDetectorResult(bits, points, compact, nbDatablocks, nbLayers) {\n        var _this = _super.call(this, bits, points) || this;\n        _this.compact = compact;\n        _this.nbDatablocks = nbDatablocks;\n        _this.nbLayers = nbLayers;\n        return _this;\n    }\n    AztecDetectorResult.prototype.getNbLayers = function () {\n        return this.nbLayers;\n    };\n    AztecDetectorResult.prototype.getNbDatablocks = function () {\n        return this.nbDatablocks;\n    };\n    AztecDetectorResult.prototype.isCompact = function () {\n        return this.compact;\n    };\n    return AztecDetectorResult;\n}(DetectorResult));\nexport default AztecDetectorResult;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;;;;CAKC,GACD,IAAI,sBAAqC,SAAU,MAAM;IACrD,UAAU,qBAAqB;IAC/B,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ;QACtE,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,WAAW,IAAI;QACnD,MAAM,OAAO,GAAG;QAChB,MAAM,YAAY,GAAG;QACrB,MAAM,QAAQ,GAAG;QACjB,OAAO;IACX;IACA,oBAAoB,SAAS,CAAC,WAAW,GAAG;QACxC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,oBAAoB,SAAS,CAAC,eAAe,GAAG;QAC5C,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,oBAAoB,SAAS,CAAC,SAAS,GAAG;QACtC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,OAAO;AACX,EAAE,gLAAA,CAAA,UAAc;uCACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/detector/Detector.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport ResultPoint from '../../ResultPoint';\nimport AztecDetectorResult from '../AztecDetectorResult';\nimport MathUtils from '../../common/detector/MathUtils';\nimport WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport NotFoundException from '../../NotFoundException';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport Integer from '../../util/Integer';\nvar Point = /** @class */ (function () {\n    function Point(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    Point.prototype.toResultPoint = function () {\n        return new ResultPoint(this.getX(), this.getY());\n    };\n    Point.prototype.getX = function () {\n        return this.x;\n    };\n    Point.prototype.getY = function () {\n        return this.y;\n    };\n    return Point;\n}());\nexport { Point };\n/**\n * Encapsulates logic that can detect an Aztec Code in an image, even if the Aztec Code\n * is rotated or skewed, or partially obscured.\n *\n * <AUTHOR> Olivier\n * <AUTHOR> Yellin\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.EXPECTED_CORNER_BITS = new Int32Array([\n            0xee0,\n            0x1dc,\n            0x83b,\n            0x707,\n        ]);\n        this.image = image;\n    }\n    Detector.prototype.detect = function () {\n        return this.detectMirror(false);\n    };\n    /**\n     * Detects an Aztec Code in an image.\n     *\n     * @param isMirror if true, image is a mirror-image of original\n     * @return {@link AztecDetectorResult} encapsulating results of detecting an Aztec Code\n     * @throws NotFoundException if no Aztec Code can be found\n     */\n    Detector.prototype.detectMirror = function (isMirror) {\n        // 1. Get the center of the aztec matrix\n        var pCenter = this.getMatrixCenter();\n        // 2. Get the center points of the four diagonal points just outside the bull's eye\n        //  [topRight, bottomRight, bottomLeft, topLeft]\n        var bullsEyeCorners = this.getBullsEyeCorners(pCenter);\n        if (isMirror) {\n            var temp = bullsEyeCorners[0];\n            bullsEyeCorners[0] = bullsEyeCorners[2];\n            bullsEyeCorners[2] = temp;\n        }\n        // 3. Get the size of the matrix and other parameters from the bull's eye\n        this.extractParameters(bullsEyeCorners);\n        // 4. Sample the grid\n        var bits = this.sampleGrid(this.image, bullsEyeCorners[this.shift % 4], bullsEyeCorners[(this.shift + 1) % 4], bullsEyeCorners[(this.shift + 2) % 4], bullsEyeCorners[(this.shift + 3) % 4]);\n        // 5. Get the corners of the matrix.\n        var corners = this.getMatrixCornerPoints(bullsEyeCorners);\n        return new AztecDetectorResult(bits, corners, this.compact, this.nbDataBlocks, this.nbLayers);\n    };\n    /**\n     * Extracts the number of data layers and data blocks from the layer around the bull's eye.\n     *\n     * @param bullsEyeCorners the array of bull's eye corners\n     * @throws NotFoundException in case of too many errors or invalid parameters\n     */\n    Detector.prototype.extractParameters = function (bullsEyeCorners) {\n        if (!this.isValidPoint(bullsEyeCorners[0]) || !this.isValidPoint(bullsEyeCorners[1]) ||\n            !this.isValidPoint(bullsEyeCorners[2]) || !this.isValidPoint(bullsEyeCorners[3])) {\n            throw new NotFoundException();\n        }\n        var length = 2 * this.nbCenterLayers;\n        // Get the bits around the bull's eye\n        var sides = new Int32Array([\n            this.sampleLine(bullsEyeCorners[0], bullsEyeCorners[1], length),\n            this.sampleLine(bullsEyeCorners[1], bullsEyeCorners[2], length),\n            this.sampleLine(bullsEyeCorners[2], bullsEyeCorners[3], length),\n            this.sampleLine(bullsEyeCorners[3], bullsEyeCorners[0], length) // Top\n        ]);\n        // bullsEyeCorners[shift] is the corner of the bulls'eye that has three\n        // orientation marks.\n        // sides[shift] is the row/column that goes from the corner with three\n        // orientation marks to the corner with two.\n        this.shift = this.getRotation(sides, length);\n        // Flatten the parameter bits into a single 28- or 40-bit long\n        var parameterData = 0;\n        for (var i = 0; i < 4; i++) {\n            var side = sides[(this.shift + i) % 4];\n            if (this.compact) {\n                // Each side of the form ..XXXXXXX. where Xs are parameter data\n                parameterData <<= 7;\n                parameterData += (side >> 1) & 0x7F;\n            }\n            else {\n                // Each side of the form ..XXXXX.XXXXX. where Xs are parameter data\n                parameterData <<= 10;\n                parameterData += ((side >> 2) & (0x1f << 5)) + ((side >> 1) & 0x1F);\n            }\n        }\n        // Corrects parameter data using RS.  Returns just the data portion\n        // without the error correction.\n        var correctedData = this.getCorrectedParameterData(parameterData, this.compact);\n        if (this.compact) {\n            // 8 bits:  2 bits layers and 6 bits data blocks\n            this.nbLayers = (correctedData >> 6) + 1;\n            this.nbDataBlocks = (correctedData & 0x3F) + 1;\n        }\n        else {\n            // 16 bits:  5 bits layers and 11 bits data blocks\n            this.nbLayers = (correctedData >> 11) + 1;\n            this.nbDataBlocks = (correctedData & 0x7FF) + 1;\n        }\n    };\n    Detector.prototype.getRotation = function (sides, length) {\n        // In a normal pattern, we expect to See\n        //   **    .*             D       A\n        //   *      *\n        //\n        //   .      *\n        //   ..    ..             C       B\n        //\n        // Grab the 3 bits from each of the sides the form the locator pattern and concatenate\n        // into a 12-bit integer.  Start with the bit at A\n        var cornerBits = 0;\n        sides.forEach(function (side, idx, arr) {\n            // XX......X where X's are orientation marks\n            var t = ((side >> (length - 2)) << 1) + (side & 1);\n            cornerBits = (cornerBits << 3) + t;\n        });\n        // for (var side in sides) {\n        //     // XX......X where X's are orientation marks\n        //     var t = ((side >> (length - 2)) << 1) + (side & 1);\n        //     cornerBits = (cornerBits << 3) + t;\n        // }\n        // Mov the bottom bit to the top, so that the three bits of the locator pattern at A are\n        // together.  cornerBits is now:\n        //  3 orientation bits at A || 3 orientation bits at B || ... || 3 orientation bits at D\n        cornerBits = ((cornerBits & 1) << 11) + (cornerBits >> 1);\n        // The result shift indicates which element of BullsEyeCorners[] goes into the top-left\n        // corner. Since the four rotation values have a Hamming distance of 8, we\n        // can easily tolerate two errors.\n        for (var shift = 0; shift < 4; shift++) {\n            if (Integer.bitCount(cornerBits ^ this.EXPECTED_CORNER_BITS[shift]) <= 2) {\n                return shift;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Corrects the parameter bits using Reed-Solomon algorithm.\n     *\n     * @param parameterData parameter bits\n     * @param compact true if this is a compact Aztec code\n     * @throws NotFoundException if the array contains too many errors\n     */\n    Detector.prototype.getCorrectedParameterData = function (parameterData, compact) {\n        var numCodewords;\n        var numDataCodewords;\n        if (compact) {\n            numCodewords = 7;\n            numDataCodewords = 2;\n        }\n        else {\n            numCodewords = 10;\n            numDataCodewords = 4;\n        }\n        var numECCodewords = numCodewords - numDataCodewords;\n        var parameterWords = new Int32Array(numCodewords);\n        for (var i = numCodewords - 1; i >= 0; --i) {\n            parameterWords[i] = parameterData & 0xF;\n            parameterData >>= 4;\n        }\n        try {\n            var rsDecoder = new ReedSolomonDecoder(GenericGF.AZTEC_PARAM);\n            rsDecoder.decode(parameterWords, numECCodewords);\n        }\n        catch (ignored) {\n            throw new NotFoundException();\n        }\n        // Toss the error correction.  Just return the data as an integer\n        var result = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            result = (result << 4) + parameterWords[i];\n        }\n        return result;\n    };\n    /**\n     * Finds the corners of a bull-eye centered on the passed point.\n     * This returns the centers of the diagonal points just outside the bull's eye\n     * Returns [topRight, bottomRight, bottomLeft, topLeft]\n     *\n     * @param pCenter Center point\n     * @return The corners of the bull-eye\n     * @throws NotFoundException If no valid bull-eye can be found\n     */\n    Detector.prototype.getBullsEyeCorners = function (pCenter) {\n        var pina = pCenter;\n        var pinb = pCenter;\n        var pinc = pCenter;\n        var pind = pCenter;\n        var color = true;\n        for (this.nbCenterLayers = 1; this.nbCenterLayers < 9; this.nbCenterLayers++) {\n            var pouta = this.getFirstDifferent(pina, color, 1, -1);\n            var poutb = this.getFirstDifferent(pinb, color, 1, 1);\n            var poutc = this.getFirstDifferent(pinc, color, -1, 1);\n            var poutd = this.getFirstDifferent(pind, color, -1, -1);\n            // d      a\n            //\n            // c      b\n            if (this.nbCenterLayers > 2) {\n                var q = (this.distancePoint(poutd, pouta) * this.nbCenterLayers) / (this.distancePoint(pind, pina) * (this.nbCenterLayers + 2));\n                if (q < 0.75 || q > 1.25 || !this.isWhiteOrBlackRectangle(pouta, poutb, poutc, poutd)) {\n                    break;\n                }\n            }\n            pina = pouta;\n            pinb = poutb;\n            pinc = poutc;\n            pind = poutd;\n            color = !color;\n        }\n        if (this.nbCenterLayers !== 5 && this.nbCenterLayers !== 7) {\n            throw new NotFoundException();\n        }\n        this.compact = this.nbCenterLayers === 5;\n        // Expand the square by .5 pixel in each direction so that we're on the border\n        // between the white square and the black square\n        var pinax = new ResultPoint(pina.getX() + 0.5, pina.getY() - 0.5);\n        var pinbx = new ResultPoint(pinb.getX() + 0.5, pinb.getY() + 0.5);\n        var pincx = new ResultPoint(pinc.getX() - 0.5, pinc.getY() + 0.5);\n        var pindx = new ResultPoint(pind.getX() - 0.5, pind.getY() - 0.5);\n        // Expand the square so that its corners are the centers of the points\n        // just outside the bull's eye.\n        return this.expandSquare([pinax, pinbx, pincx, pindx], 2 * this.nbCenterLayers - 3, 2 * this.nbCenterLayers);\n    };\n    /**\n     * Finds a candidate center point of an Aztec code from an image\n     *\n     * @return the center point\n     */\n    Detector.prototype.getMatrixCenter = function () {\n        var pointA;\n        var pointB;\n        var pointC;\n        var pointD;\n        // Get a white rectangle that can be the border of the matrix in center bull's eye or\n        try {\n            var cornerPoints = new WhiteRectangleDetector(this.image).detect();\n            pointA = cornerPoints[0];\n            pointB = cornerPoints[1];\n            pointC = cornerPoints[2];\n            pointD = cornerPoints[3];\n        }\n        catch (e) {\n            // This exception can be in case the initial rectangle is white\n            // In that case, surely in the bull's eye, we try to expand the rectangle.\n            var cx_1 = this.image.getWidth() / 2;\n            var cy_1 = this.image.getHeight() / 2;\n            pointA = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 - 7), false, 1, -1).toResultPoint();\n            pointB = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 + 7), false, 1, 1).toResultPoint();\n            pointC = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 + 7), false, -1, 1).toResultPoint();\n            pointD = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 - 7), false, -1, -1).toResultPoint();\n        }\n        // Compute the center of the rectangle\n        var cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n        var cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n        // Redetermine the white rectangle starting from previously computed center.\n        // This will ensure that we end up with a white rectangle in center bull's eye\n        // in order to compute a more accurate center.\n        try {\n            var cornerPoints = new WhiteRectangleDetector(this.image, 15, cx, cy).detect();\n            pointA = cornerPoints[0];\n            pointB = cornerPoints[1];\n            pointC = cornerPoints[2];\n            pointD = cornerPoints[3];\n        }\n        catch (e) {\n            // This exception can be in case the initial rectangle is white\n            // In that case we try to expand the rectangle.\n            pointA = this.getFirstDifferent(new Point(cx + 7, cy - 7), false, 1, -1).toResultPoint();\n            pointB = this.getFirstDifferent(new Point(cx + 7, cy + 7), false, 1, 1).toResultPoint();\n            pointC = this.getFirstDifferent(new Point(cx - 7, cy + 7), false, -1, 1).toResultPoint();\n            pointD = this.getFirstDifferent(new Point(cx - 7, cy - 7), false, -1, -1).toResultPoint();\n        }\n        // Recompute the center of the rectangle\n        cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n        cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n        return new Point(cx, cy);\n    };\n    /**\n     * Gets the Aztec code corners from the bull's eye corners and the parameters.\n     *\n     * @param bullsEyeCorners the array of bull's eye corners\n     * @return the array of aztec code corners\n     */\n    Detector.prototype.getMatrixCornerPoints = function (bullsEyeCorners) {\n        return this.expandSquare(bullsEyeCorners, 2 * this.nbCenterLayers, this.getDimension());\n    };\n    /**\n     * Creates a BitMatrix by sampling the provided image.\n     * topLeft, topRight, bottomRight, and bottomLeft are the centers of the squares on the\n     * diagonal just outside the bull's eye.\n     */\n    Detector.prototype.sampleGrid = function (image, topLeft, topRight, bottomRight, bottomLeft) {\n        var sampler = GridSamplerInstance.getInstance();\n        var dimension = this.getDimension();\n        var low = dimension / 2 - this.nbCenterLayers;\n        var high = dimension / 2 + this.nbCenterLayers;\n        return sampler.sampleGrid(image, dimension, dimension, low, low, // topleft\n        high, low, // topright\n        high, high, // bottomright\n        low, high, // bottomleft\n        topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n    };\n    /**\n     * Samples a line.\n     *\n     * @param p1   start point (inclusive)\n     * @param p2   end point (exclusive)\n     * @param size number of bits\n     * @return the array of bits as an int (first bit is high-order bit of result)\n     */\n    Detector.prototype.sampleLine = function (p1, p2, size) {\n        var result = 0;\n        var d = this.distanceResultPoint(p1, p2);\n        var moduleSize = d / size;\n        var px = p1.getX();\n        var py = p1.getY();\n        var dx = moduleSize * (p2.getX() - p1.getX()) / d;\n        var dy = moduleSize * (p2.getY() - p1.getY()) / d;\n        for (var i = 0; i < size; i++) {\n            if (this.image.get(MathUtils.round(px + i * dx), MathUtils.round(py + i * dy))) {\n                result |= 1 << (size - i - 1);\n            }\n        }\n        return result;\n    };\n    /**\n     * @return true if the border of the rectangle passed in parameter is compound of white points only\n     *         or black points only\n     */\n    Detector.prototype.isWhiteOrBlackRectangle = function (p1, p2, p3, p4) {\n        var corr = 3;\n        p1 = new Point(p1.getX() - corr, p1.getY() + corr);\n        p2 = new Point(p2.getX() - corr, p2.getY() - corr);\n        p3 = new Point(p3.getX() + corr, p3.getY() - corr);\n        p4 = new Point(p4.getX() + corr, p4.getY() + corr);\n        var cInit = this.getColor(p4, p1);\n        if (cInit === 0) {\n            return false;\n        }\n        var c = this.getColor(p1, p2);\n        if (c !== cInit) {\n            return false;\n        }\n        c = this.getColor(p2, p3);\n        if (c !== cInit) {\n            return false;\n        }\n        c = this.getColor(p3, p4);\n        return c === cInit;\n    };\n    /**\n     * Gets the color of a segment\n     *\n     * @return 1 if segment more than 90% black, -1 if segment is more than 90% white, 0 else\n     */\n    Detector.prototype.getColor = function (p1, p2) {\n        var d = this.distancePoint(p1, p2);\n        var dx = (p2.getX() - p1.getX()) / d;\n        var dy = (p2.getY() - p1.getY()) / d;\n        var error = 0;\n        var px = p1.getX();\n        var py = p1.getY();\n        var colorModel = this.image.get(p1.getX(), p1.getY());\n        var iMax = Math.ceil(d);\n        for (var i = 0; i < iMax; i++) {\n            px += dx;\n            py += dy;\n            if (this.image.get(MathUtils.round(px), MathUtils.round(py)) !== colorModel) {\n                error++;\n            }\n        }\n        var errRatio = error / d;\n        if (errRatio > 0.1 && errRatio < 0.9) {\n            return 0;\n        }\n        return (errRatio <= 0.1) === colorModel ? 1 : -1;\n    };\n    /**\n     * Gets the coordinate of the first point with a different color in the given direction\n     */\n    Detector.prototype.getFirstDifferent = function (init, color, dx, dy) {\n        var x = init.getX() + dx;\n        var y = init.getY() + dy;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            x += dx;\n            y += dy;\n        }\n        x -= dx;\n        y -= dy;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            x += dx;\n        }\n        x -= dx;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            y += dy;\n        }\n        y -= dy;\n        return new Point(x, y);\n    };\n    /**\n     * Expand the square represented by the corner points by pushing out equally in all directions\n     *\n     * @param cornerPoints the corners of the square, which has the bull's eye at its center\n     * @param oldSide the original length of the side of the square in the target bit matrix\n     * @param newSide the new length of the size of the square in the target bit matrix\n     * @return the corners of the expanded square\n     */\n    Detector.prototype.expandSquare = function (cornerPoints, oldSide, newSide) {\n        var ratio = newSide / (2.0 * oldSide);\n        var dx = cornerPoints[0].getX() - cornerPoints[2].getX();\n        var dy = cornerPoints[0].getY() - cornerPoints[2].getY();\n        var centerx = (cornerPoints[0].getX() + cornerPoints[2].getX()) / 2.0;\n        var centery = (cornerPoints[0].getY() + cornerPoints[2].getY()) / 2.0;\n        var result0 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n        var result2 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n        dx = cornerPoints[1].getX() - cornerPoints[3].getX();\n        dy = cornerPoints[1].getY() - cornerPoints[3].getY();\n        centerx = (cornerPoints[1].getX() + cornerPoints[3].getX()) / 2.0;\n        centery = (cornerPoints[1].getY() + cornerPoints[3].getY()) / 2.0;\n        var result1 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n        var result3 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n        var results = [result0, result1, result2, result3];\n        return results;\n    };\n    Detector.prototype.isValid = function (x, y) {\n        return x >= 0 && x < this.image.getWidth() && y > 0 && y < this.image.getHeight();\n    };\n    Detector.prototype.isValidPoint = function (point) {\n        var x = MathUtils.round(point.getX());\n        var y = MathUtils.round(point.getY());\n        return this.isValid(x, y);\n    };\n    Detector.prototype.distancePoint = function (a, b) {\n        return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n    };\n    Detector.prototype.distanceResultPoint = function (a, b) {\n        return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n    };\n    Detector.prototype.getDimension = function () {\n        if (this.compact) {\n            return 4 * this.nbLayers + 11;\n        }\n        if (this.nbLayers <= 4) {\n            return 4 * this.nbLayers + 15;\n        }\n        return 4 * this.nbLayers + 2 * (Integer.truncDivision((this.nbLayers - 4), 8) + 1) + 15;\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACA,IAAI,QAAuB;IACvB,SAAS,MAAM,CAAC,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACb;IACA,MAAM,SAAS,CAAC,aAAa,GAAG;QAC5B,OAAO,IAAI,mKAAA,CAAA,UAAW,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;IACjD;IACA,MAAM,SAAS,CAAC,IAAI,GAAG;QACnB,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,MAAM,SAAS,CAAC,IAAI,GAAG;QACnB,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,OAAO;AACX;;AAEA;;;;;;CAMC,GACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,KAAK;QACnB,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW;YACvC;YACA;YACA;YACA;SACH;QACD,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,SAAS,SAAS,CAAC,MAAM,GAAG;QACxB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B;IACA;;;;;;KAMC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ;QAChD,wCAAwC;QACxC,IAAI,UAAU,IAAI,CAAC,eAAe;QAClC,mFAAmF;QACnF,gDAAgD;QAChD,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,CAAC;QAC9C,IAAI,UAAU;YACV,IAAI,OAAO,eAAe,CAAC,EAAE;YAC7B,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE;YACvC,eAAe,CAAC,EAAE,GAAG;QACzB;QACA,yEAAyE;QACzE,IAAI,CAAC,iBAAiB,CAAC;QACvB,qBAAqB;QACrB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE;QAC3L,oCAAoC;QACpC,IAAI,UAAU,IAAI,CAAC,qBAAqB,CAAC;QACzC,OAAO,IAAI,oLAAA,CAAA,UAAmB,CAAC,MAAM,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ;IAChG;IACA;;;;;KAKC,GACD,SAAS,SAAS,CAAC,iBAAiB,GAAG,SAAU,eAAe;QAC5D,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,KAC/E,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,GAAG;YAClF,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,SAAS,IAAI,IAAI,CAAC,cAAc;QACpC,qCAAqC;QACrC,IAAI,QAAQ,IAAI,WAAW;YACvB,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE,QAAQ,MAAM;SACzE;QACD,uEAAuE;QACvE,qBAAqB;QACrB,sEAAsE;QACtE,4CAA4C;QAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;QACrC,8DAA8D;QAC9D,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE;YACtC,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,+DAA+D;gBAC/D,kBAAkB;gBAClB,iBAAiB,AAAC,QAAQ,IAAK;YACnC,OACK;gBACD,mEAAmE;gBACnE,kBAAkB;gBAClB,iBAAiB,CAAC,AAAC,QAAQ,IAAM,QAAQ,CAAE,IAAI,CAAC,AAAC,QAAQ,IAAK,IAAI;YACtE;QACJ;QACA,mEAAmE;QACnE,gCAAgC;QAChC,IAAI,gBAAgB,IAAI,CAAC,yBAAyB,CAAC,eAAe,IAAI,CAAC,OAAO;QAC9E,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,gDAAgD;YAChD,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,CAAC,IAAI;YACvC,IAAI,CAAC,YAAY,GAAG,CAAC,gBAAgB,IAAI,IAAI;QACjD,OACK;YACD,kDAAkD;YAClD,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE,IAAI;YACxC,IAAI,CAAC,YAAY,GAAG,CAAC,gBAAgB,KAAK,IAAI;QAClD;IACJ;IACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK,EAAE,MAAM;QACpD,wCAAwC;QACxC,mCAAmC;QACnC,aAAa;QACb,EAAE;QACF,aAAa;QACb,mCAAmC;QACnC,EAAE;QACF,sFAAsF;QACtF,kDAAkD;QAClD,IAAI,aAAa;QACjB,MAAM,OAAO,CAAC,SAAU,IAAI,EAAE,GAAG,EAAE,GAAG;YAClC,4CAA4C;YAC5C,IAAI,IAAI,CAAC,AAAC,QAAS,SAAS,KAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YACjD,aAAa,CAAC,cAAc,CAAC,IAAI;QACrC;QACA,4BAA4B;QAC5B,mDAAmD;QACnD,0DAA0D;QAC1D,0CAA0C;QAC1C,IAAI;QACJ,wFAAwF;QACxF,gCAAgC;QAChC,wFAAwF;QACxF,aAAa,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC;QACxD,uFAAuF;QACvF,0EAA0E;QAC1E,kCAAkC;QAClC,IAAK,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAS;YACpC,IAAI,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,GAAG;gBACtE,OAAO;YACX;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA;;;;;;KAMC,GACD,SAAS,SAAS,CAAC,yBAAyB,GAAG,SAAU,aAAa,EAAE,OAAO;QAC3E,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS;YACT,eAAe;YACf,mBAAmB;QACvB,OACK;YACD,eAAe;YACf,mBAAmB;QACvB;QACA,IAAI,iBAAiB,eAAe;QACpC,IAAI,iBAAiB,IAAI,WAAW;QACpC,IAAK,IAAI,IAAI,eAAe,GAAG,KAAK,GAAG,EAAE,EAAG;YACxC,cAAc,CAAC,EAAE,GAAG,gBAAgB;YACpC,kBAAkB;QACtB;QACA,IAAI;YACA,IAAI,YAAY,IAAI,mMAAA,CAAA,UAAkB,CAAC,0LAAA,CAAA,UAAS,CAAC,WAAW;YAC5D,UAAU,MAAM,CAAC,gBAAgB;QACrC,EACA,OAAO,SAAS;YACZ,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,iEAAiE;QACjE,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;YACvC,SAAS,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,EAAE;QAC9C;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO;QACrD,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,QAAQ;QACZ,IAAK,IAAI,CAAC,cAAc,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,IAAI,CAAC,cAAc,GAAI;YAC1E,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC,MAAM,OAAO,GAAG,CAAC;YACpD,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC,MAAM,OAAO,GAAG;YACnD,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC,MAAM,OAAO,CAAC,GAAG;YACpD,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC;YACrD,WAAW;YACX,EAAE;YACF,WAAW;YACX,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;gBACzB,IAAI,IAAI,AAAC,IAAI,CAAC,aAAa,CAAC,OAAO,SAAS,IAAI,CAAC,cAAc,GAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;gBAC9H,IAAI,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,OAAO,OAAO,QAAQ;oBACnF;gBACJ;YACJ;YACA,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,QAAQ,CAAC;QACb;QACA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,cAAc,KAAK,GAAG;YACxD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,KAAK;QACvC,8EAA8E;QAC9E,gDAAgD;QAChD,IAAI,QAAQ,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK;QAC7D,IAAI,QAAQ,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK;QAC7D,IAAI,QAAQ,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK;QAC7D,IAAI,QAAQ,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK;QAC7D,sEAAsE;QACtE,+BAA+B;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC;YAAC;YAAO;YAAO;YAAO;SAAM,EAAE,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc;IAC/G;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,eAAe,GAAG;QACjC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,qFAAqF;QACrF,IAAI;YACA,IAAI,eAAe,IAAI,oMAAA,CAAA,UAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;YAChE,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;QAC5B,EACA,OAAO,GAAG;YACN,+DAA+D;YAC/D,0EAA0E;YAC1E,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK;YACnC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK;YACpC,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,CAAC,GAAG,aAAa;YAC1F,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,GAAG,aAAa;YACzF,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,GAAG,GAAG,aAAa;YAC1F,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,aAAa;QAC/F;QACA,sCAAsC;QACtC,IAAI,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QAC3F,IAAI,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QAC3F,4EAA4E;QAC5E,8EAA8E;QAC9E,8CAA8C;QAC9C,IAAI;YACA,IAAI,eAAe,IAAI,oMAAA,CAAA,UAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI,MAAM;YAC5E,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;YACxB,SAAS,YAAY,CAAC,EAAE;QAC5B,EACA,OAAO,GAAG;YACN,+DAA+D;YAC/D,+CAA+C;YAC/C,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,IAAI,OAAO,GAAG,CAAC,GAAG,aAAa;YACtF,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,IAAI,OAAO,GAAG,GAAG,aAAa;YACrF,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,GAAG,aAAa;YACtF,SAAS,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,aAAa;QAC3F;QACA,wCAAwC;QACxC,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QACvF,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QACvF,OAAO,IAAI,MAAM,IAAI;IACzB;IACA;;;;;KAKC,GACD,SAAS,SAAS,CAAC,qBAAqB,GAAG,SAAU,eAAe;QAChE,OAAO,IAAI,CAAC,YAAY,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY;IACxF;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU;QACvF,IAAI,UAAU,qLAAA,CAAA,UAAmB,CAAC,WAAW;QAC7C,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc;QAC7C,IAAI,OAAO,YAAY,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,QAAQ,UAAU,CAAC,OAAO,WAAW,WAAW,KAAK,KAC5D,MAAM,KACN,MAAM,MACN,KAAK,MACL,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI;IAChJ;IACA;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,UAAU,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,IAAI;QAClD,IAAI,SAAS;QACb,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI;QACrC,IAAI,aAAa,IAAI;QACrB,IAAI,KAAK,GAAG,IAAI;QAChB,IAAI,KAAK,GAAG,IAAI;QAChB,IAAI,KAAK,aAAa,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,IAAI;QAChD,IAAI,KAAK,aAAa,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,IAAI;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM;gBAC5E,UAAU,KAAM,OAAO,IAAI;YAC/B;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,SAAS,SAAS,CAAC,uBAAuB,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACjE,IAAI,OAAO;QACX,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK;QAC7C,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK;QAC7C,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK;QAC7C,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK;QAC7C,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC9B,IAAI,UAAU,GAAG;YACb,OAAO;QACX;QACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC1B,IAAI,MAAM,OAAO;YACb,OAAO;QACX;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI;QACtB,IAAI,MAAM,OAAO;YACb,OAAO;QACX;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI;QACtB,OAAO,MAAM;IACjB;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE,EAAE,EAAE;QAC1C,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI;QAC/B,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,IAAI;QACnC,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,IAAI;QACnC,IAAI,QAAQ;QACZ,IAAI,KAAK,GAAG,IAAI;QAChB,IAAI,KAAK,GAAG,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI;QAClD,IAAI,OAAO,KAAK,IAAI,CAAC;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,MAAM;YACN,MAAM;YACN,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,KAAK,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,SAAS,YAAY;gBACzE;YACJ;QACJ;QACA,IAAI,WAAW,QAAQ;QACvB,IAAI,WAAW,OAAO,WAAW,KAAK;YAClC,OAAO;QACX;QACA,OAAO,AAAC,YAAY,QAAS,aAAa,IAAI,CAAC;IACnD;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;QAChE,IAAI,IAAI,KAAK,IAAI,KAAK;QACtB,IAAI,IAAI,KAAK,IAAI,KAAK;QACtB,MAAO,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,MAAO;YACzD,KAAK;YACL,KAAK;QACT;QACA,KAAK;QACL,KAAK;QACL,MAAO,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,MAAO;YACzD,KAAK;QACT;QACA,KAAK;QACL,MAAO,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,MAAO;YACzD,KAAK;QACT;QACA,KAAK;QACL,OAAO,IAAI,MAAM,GAAG;IACxB;IACA;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,OAAO;QACtE,IAAI,QAAQ,UAAU,CAAC,MAAM,OAAO;QACpC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI;QACtD,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI;QACtD,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI;QAClE,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI;QAClE,IAAI,UAAU,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU,QAAQ,IAAI,UAAU,QAAQ;QACtE,IAAI,UAAU,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU,QAAQ,IAAI,UAAU,QAAQ;QACtE,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI;QAClD,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI;QAClD,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI;QAC9D,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI;QAC9D,IAAI,UAAU,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU,QAAQ,IAAI,UAAU,QAAQ;QACtE,IAAI,UAAU,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU,QAAQ,IAAI,UAAU,QAAQ;QACtE,IAAI,UAAU;YAAC;YAAS;YAAS;YAAS;SAAQ;QAClD,OAAO;IACX;IACA,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC,EAAE,CAAC;QACvC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;IACnF;IACA,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;QAC7C,IAAI,IAAI,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,MAAM,IAAI;QAClC,IAAI,IAAI,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,MAAM,IAAI;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;IAC3B;IACA,SAAS,SAAS,CAAC,aAAa,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7C,OAAO,uLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI;IAClE;IACA,SAAS,SAAS,CAAC,mBAAmB,GAAG,SAAU,CAAC,EAAE,CAAC;QACnD,OAAO,uLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI;IAClE;IACA,SAAS,SAAS,CAAC,YAAY,GAAG;QAC9B,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG;QAC/B;QACA,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG;YACpB,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG;QAC/B;QACA,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAE,IAAI,CAAC,QAAQ,GAAG,GAAI,KAAK,CAAC,IAAI;IACzF;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/AztecReader.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Result from '../Result';\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n// import java.util.List;\n// import java.util.Map;\n/**\n * This implementation can detect and decode Aztec codes in an image.\n *\n * <AUTHOR>\n */\nvar AztecReader = /** @class */ (function () {\n    function AztecReader() {\n    }\n    /**\n     * Locates and decodes a Data Matrix code in an image.\n     *\n     * @return a String representing the content encoded by the Data Matrix code\n     * @throws NotFoundException if a Data Matrix code cannot be found\n     * @throws FormatException if a Data Matrix code cannot be decoded\n     */\n    AztecReader.prototype.decode = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        var exception = null;\n        var detector = new Detector(image.getBlackMatrix());\n        var points = null;\n        var decoderResult = null;\n        try {\n            var detectorResult = detector.detectMirror(false);\n            points = detectorResult.getPoints();\n            this.reportFoundResultPoints(hints, points);\n            decoderResult = new Decoder().decode(detectorResult);\n        }\n        catch (e) {\n            exception = e;\n        }\n        if (decoderResult == null) {\n            try {\n                var detectorResult = detector.detectMirror(true);\n                points = detectorResult.getPoints();\n                this.reportFoundResultPoints(hints, points);\n                decoderResult = new Decoder().decode(detectorResult);\n            }\n            catch (e) {\n                if (exception != null) {\n                    throw exception;\n                }\n                throw e;\n            }\n        }\n        var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), decoderResult.getNumBits(), points, BarcodeFormat.AZTEC, System.currentTimeMillis());\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments != null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel != null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        return result;\n    };\n    AztecReader.prototype.reportFoundResultPoints = function (hints, points) {\n        if (hints != null) {\n            var rpcb_1 = hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n            if (rpcb_1 != null) {\n                points.forEach(function (point, idx, arr) {\n                    rpcb_1.foundPossibleResultPoint(point);\n                });\n            }\n        }\n    };\n    // @Override\n    AztecReader.prototype.reset = function () {\n        // do nothing\n    };\n    return AztecReader;\n}());\nexport default AztecReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,yBAAyB;AACzB,wBAAwB;AACxB;;;;CAIC,GACD,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA;;;;;;KAMC,GACD,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QACjD,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,YAAY;QAChB,IAAI,WAAW,IAAI,qLAAA,CAAA,UAAQ,CAAC,MAAM,cAAc;QAChD,IAAI,SAAS;QACb,IAAI,gBAAgB;QACpB,IAAI;YACA,IAAI,iBAAiB,SAAS,YAAY,CAAC;YAC3C,SAAS,eAAe,SAAS;YACjC,IAAI,CAAC,uBAAuB,CAAC,OAAO;YACpC,gBAAgB,IAAI,mLAAA,CAAA,UAAO,GAAG,MAAM,CAAC;QACzC,EACA,OAAO,GAAG;YACN,YAAY;QAChB;QACA,IAAI,iBAAiB,MAAM;YACvB,IAAI;gBACA,IAAI,iBAAiB,SAAS,YAAY,CAAC;gBAC3C,SAAS,eAAe,SAAS;gBACjC,IAAI,CAAC,uBAAuB,CAAC,OAAO;gBACpC,gBAAgB,IAAI,mLAAA,CAAA,UAAO,GAAG,MAAM,CAAC;YACzC,EACA,OAAO,GAAG;gBACN,IAAI,aAAa,MAAM;oBACnB,MAAM;gBACV;gBACA,MAAM;YACV;QACJ;QACA,IAAI,SAAS,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,OAAO,IAAI,cAAc,WAAW,IAAI,cAAc,UAAU,IAAI,QAAQ,qKAAA,CAAA,UAAa,CAAC,KAAK,EAAE,sKAAA,CAAA,UAAM,CAAC,iBAAiB;QAC/J,IAAI,eAAe,cAAc,eAAe;QAChD,IAAI,gBAAgB,MAAM;YACtB,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,aAAa,EAAE;QACzD;QACA,IAAI,UAAU,cAAc,UAAU;QACtC,IAAI,WAAW,MAAM;YACjB,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,sBAAsB,EAAE;QAClE;QACA,OAAO;IACX;IACA,YAAY,SAAS,CAAC,uBAAuB,GAAG,SAAU,KAAK,EAAE,MAAM;QACnE,IAAI,SAAS,MAAM;YACf,IAAI,SAAS,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B;YAChE,IAAI,UAAU,MAAM;gBAChB,OAAO,OAAO,CAAC,SAAU,KAAK,EAAE,GAAG,EAAE,GAAG;oBACpC,OAAO,wBAAwB,CAAC;gBACpC;YACJ;QACJ;IACJ;IACA,YAAY;IACZ,YAAY,SAAS,CAAC,KAAK,GAAG;IAC1B,aAAa;IACjB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/AztecCode.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n/**\n * Aztec 2D code representation\n *\n * <AUTHOR>\n */\nvar AztecCode = /** @class */ (function () {\n    function AztecCode() {\n    }\n    /**\n     * @return {@code true} if compact instead of full mode\n     */\n    AztecCode.prototype.isCompact = function () {\n        return this.compact;\n    };\n    AztecCode.prototype.setCompact = function (compact) {\n        this.compact = compact;\n    };\n    /**\n     * @return size in pixels (width and height)\n     */\n    AztecCode.prototype.getSize = function () {\n        return this.size;\n    };\n    AztecCode.prototype.setSize = function (size) {\n        this.size = size;\n    };\n    /**\n     * @return number of levels\n     */\n    AztecCode.prototype.getLayers = function () {\n        return this.layers;\n    };\n    AztecCode.prototype.setLayers = function (layers) {\n        this.layers = layers;\n    };\n    /**\n     * @return number of data codewords\n     */\n    AztecCode.prototype.getCodeWords = function () {\n        return this.codeWords;\n    };\n    AztecCode.prototype.setCodeWords = function (codeWords) {\n        this.codeWords = codeWords;\n    };\n    /**\n     * @return the symbol image\n     */\n    AztecCode.prototype.getMatrix = function () {\n        return this.matrix;\n    };\n    AztecCode.prototype.setMatrix = function (matrix) {\n        this.matrix = matrix;\n    };\n    return AztecCode;\n}());\nexport default AztecCode;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA;;;;CAIC;;;AACD,IAAI,YAA2B;IAC3B,SAAS,aACT;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO;QAC9C,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,OAAO,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,UAAU,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI;QACxC,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QAC5C,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,YAAY,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,UAAU,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QAClD,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QAC5C,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/Token.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar Token = /** @class */ (function () {\n    function Token(previous) {\n        this.previous = previous;\n    }\n    Token.prototype.getPrevious = function () {\n        return this.previous;\n    };\n    return Token;\n}());\nexport default Token;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AACA,IAAI,QAAuB;IACvB,SAAS,MAAM,QAAQ;QACnB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,MAAM,SAAS,CAAC,WAAW,GAAG;QAC1B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/SimpleToken.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Token from './Token';\nimport Integer from '../../util/Integer';\nvar SimpleToken = /** @class */ (function (_super) {\n    __extends(SimpleToken, _super);\n    function SimpleToken(previous, value, bitCount) {\n        var _this = _super.call(this, previous) || this;\n        _this.value = value;\n        _this.bitCount = bitCount;\n        return _this;\n    }\n    /**\n     * @Override\n     */\n    SimpleToken.prototype.appendTo = function (bitArray, text) {\n        bitArray.appendBits(this.value, this.bitCount);\n    };\n    SimpleToken.prototype.add = function (value, bitCount) {\n        return new SimpleToken(this, value, bitCount);\n    };\n    SimpleToken.prototype.addBinaryShift = function (start, byteCount) {\n        // no-op can't binary shift a simple token\n        console.warn('addBinaryShift on SimpleToken, this simply returns a copy of this token');\n        return new SimpleToken(this, start, byteCount);\n    };\n    /**\n     * @Override\n     */\n    SimpleToken.prototype.toString = function () {\n        var value = this.value & ((1 << this.bitCount) - 1);\n        value |= 1 << this.bitCount;\n        return '<' + Integer.toBinaryString(value | (1 << this.bitCount)).substring(1) + '>';\n    };\n    return SimpleToken;\n}(Token));\nexport default SimpleToken;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAcA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS,YAAY,QAAQ,EAAE,KAAK,EAAE,QAAQ;QAC1C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,aAAa,IAAI;QAC/C,MAAM,KAAK,GAAG;QACd,MAAM,QAAQ,GAAG;QACjB,OAAO;IACX;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,QAAQ,EAAE,IAAI;QACrD,SAAS,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ;IACjD;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK,EAAE,QAAQ;QACjD,OAAO,IAAI,YAAY,IAAI,EAAE,OAAO;IACxC;IACA,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK,EAAE,SAAS;QAC7D,0CAA0C;QAC1C,QAAQ,IAAI,CAAC;QACb,OAAO,IAAI,YAAY,IAAI,EAAE,OAAO;IACxC;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI;QACjD,SAAS,KAAK,IAAI,CAAC,QAAQ;QAC3B,OAAO,MAAM,uKAAA,CAAA,UAAO,CAAC,cAAc,CAAC,QAAS,KAAK,IAAI,CAAC,QAAQ,EAAG,SAAS,CAAC,KAAK;IACrF;IACA,OAAO;AACX,EAAE,iLAAA,CAAA,UAAK;uCACQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/BinaryShiftToken.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport SimpleToken from './SimpleToken';\nvar BinaryShiftToken = /** @class */ (function (_super) {\n    __extends(BinaryShiftToken, _super);\n    function BinaryShiftToken(previous, binaryShiftStart, binaryShiftByteCount) {\n        var _this = _super.call(this, previous, 0, 0) || this;\n        _this.binaryShiftStart = binaryShiftStart;\n        _this.binaryShiftByteCount = binaryShiftByteCount;\n        return _this;\n    }\n    /**\n     * @Override\n     */\n    BinaryShiftToken.prototype.appendTo = function (bitArray, text) {\n        for (var i = 0; i < this.binaryShiftByteCount; i++) {\n            if (i === 0 || (i === 31 && this.binaryShiftByteCount <= 62)) {\n                // We need a header before the first character, and before\n                // character 31 when the total byte code is <= 62\n                bitArray.appendBits(31, 5); // BINARY_SHIFT\n                if (this.binaryShiftByteCount > 62) {\n                    bitArray.appendBits(this.binaryShiftByteCount - 31, 16);\n                }\n                else if (i === 0) {\n                    // 1 <= binaryShiftByteCode <= 62\n                    bitArray.appendBits(Math.min(this.binaryShiftByteCount, 31), 5);\n                }\n                else {\n                    // 32 <= binaryShiftCount <= 62 and i == 31\n                    bitArray.appendBits(this.binaryShiftByteCount - 31, 5);\n                }\n            }\n            bitArray.appendBits(text[this.binaryShiftStart + i], 8);\n        }\n    };\n    BinaryShiftToken.prototype.addBinaryShift = function (start, byteCount) {\n        // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n        return new BinaryShiftToken(this, start, byteCount);\n    };\n    /**\n     * @Override\n     */\n    BinaryShiftToken.prototype.toString = function () {\n        return '<' + this.binaryShiftStart + '::' + (this.binaryShiftStart + this.binaryShiftByteCount - 1) + '>';\n    };\n    return BinaryShiftToken;\n}(SimpleToken));\nexport default BinaryShiftToken;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAcA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,mBAAkC,SAAU,MAAM;IAClD,UAAU,kBAAkB;IAC5B,SAAS,iBAAiB,QAAQ,EAAE,gBAAgB,EAAE,oBAAoB;QACtE,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,MAAM,IAAI;QACrD,MAAM,gBAAgB,GAAG;QACzB,MAAM,oBAAoB,GAAG;QAC7B,OAAO;IACX;IACA;;KAEC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAU,QAAQ,EAAE,IAAI;QAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAK;YAChD,IAAI,MAAM,KAAM,MAAM,MAAM,IAAI,CAAC,oBAAoB,IAAI,IAAK;gBAC1D,0DAA0D;gBAC1D,iDAAiD;gBACjD,SAAS,UAAU,CAAC,IAAI,IAAI,eAAe;gBAC3C,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI;oBAChC,SAAS,UAAU,CAAC,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBACxD,OACK,IAAI,MAAM,GAAG;oBACd,iCAAiC;oBACjC,SAAS,UAAU,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK;gBACjE,OACK;oBACD,2CAA2C;oBAC3C,SAAS,UAAU,CAAC,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBACxD;YACJ;YACA,SAAS,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,EAAE,EAAE;QACzD;IACJ;IACA,iBAAiB,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK,EAAE,SAAS;QAClE,uFAAuF;QACvF,OAAO,IAAI,iBAAiB,IAAI,EAAE,OAAO;IAC7C;IACA;;KAEC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG;QAClC,OAAO,MAAM,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI;IAC1G;IACA,OAAO;AACX,EAAE,uLAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/TokenHelpers.js"], "sourcesContent": ["import SimpleToken from './SimpleToken';\nimport BinaryShiftToken from './BinaryShiftToken';\nexport function addBinaryShift(token, start, byteCount) {\n    // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n    return new BinaryShiftToken(token, start, byteCount);\n}\nexport function add(token, value, bitCount) {\n    return new SimpleToken(token, value, bitCount);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,SAAS;IAClD,uFAAuF;IACvF,OAAO,IAAI,4LAAA,CAAA,UAAgB,CAAC,OAAO,OAAO;AAC9C;AACO,SAAS,IAAI,KAAK,EAAE,KAAK,EAAE,QAAQ;IACtC,OAAO,IAAI,uLAAA,CAAA,UAAW,CAAC,OAAO,OAAO;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/EncoderConstants.js"], "sourcesContent": ["import SimpleToken from './SimpleToken';\nexport var /*final*/ MODE_NAMES = [\n    'UPPER',\n    'LOWER',\n    'DIGIT',\n    'MIXED',\n    'PUNCT'\n];\nexport var /*final*/ MODE_UPPER = 0; // 5 bits\nexport var /*final*/ MODE_LOWER = 1; // 5 bits\nexport var /*final*/ MODE_DIGIT = 2; // 4 bits\nexport var /*final*/ MODE_MIXED = 3; // 5 bits\nexport var /*final*/ MODE_PUNCT = 4; // 5 bits\nexport var EMPTY_TOKEN = new SimpleToken(null, 0, 0);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,IAAI,OAAO,GAAG,aAAa;IAC9B;IACA;IACA;IACA;IACA;CACH;AACM,IAAI,OAAO,GAAG,aAAa,GAAG,SAAS;AACvC,IAAI,OAAO,GAAG,aAAa,GAAG,SAAS;AACvC,IAAI,OAAO,GAAG,aAAa,GAAG,SAAS;AACvC,IAAI,OAAO,GAAG,aAAa,GAAG,SAAS;AACvC,IAAI,OAAO,GAAG,aAAa,GAAG,SAAS;AACvC,IAAI,cAAc,IAAI,uLAAA,CAAA,UAAW,CAAC,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/LatchTable.js"], "sourcesContent": ["// The Latch Table shows, for each pair of Modes, the optimal method for\n// getting from one mode to another.  In the worst possible case, this can\n// be up to 14 bits.  In the best possible case, we are already there!\n// The high half-word of each entry gives the number of bits.\n// The low half-word of each entry are the actual bits necessary to change\nexport var LATCH_TABLE = [\n    Int32Array.from([\n        0,\n        (5 << 16) + 28,\n        (5 << 16) + 30,\n        (5 << 16) + 29,\n        (10 << 16) + (29 << 5) + 30 // UPPER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (9 << 16) + (30 << 4) + 14,\n        0,\n        (5 << 16) + 30,\n        (5 << 16) + 29,\n        (10 << 16) + (29 << 5) + 30 // LOWER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (4 << 16) + 14,\n        (9 << 16) + (14 << 5) + 28,\n        0,\n        (9 << 16) + (14 << 5) + 29,\n        (14 << 16) + (14 << 10) + (29 << 5) + 30\n        // DIGIT -> UPPER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (5 << 16) + 29,\n        (5 << 16) + 28,\n        (10 << 16) + (29 << 5) + 30,\n        0,\n        (5 << 16) + 30 // MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (5 << 16) + 31,\n        (10 << 16) + (31 << 5) + 28,\n        (10 << 16) + (31 << 5) + 30,\n        (10 << 16) + (31 << 5) + 29,\n        0\n    ])\n];\n"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,0EAA0E;AAC1E,sEAAsE;AACtE,6DAA6D;AAC7D,0EAA0E;;;;AACnE,IAAI,cAAc;IACrB,WAAW,IAAI,CAAC;QACZ;QACA,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,0BAA0B;KACzD;IACD,WAAW,IAAI,CAAC;QACZ,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACxB;QACA,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,0BAA0B;KACzD;IACD,WAAW,IAAI,CAAC;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACxB;QACA,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACxB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;KAEzC;IACD,WAAW,IAAI,CAAC;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACzB;QACA,CAAC,KAAK,EAAE,IAAI,GAAG,iBAAiB;KACnC;IACD,WAAW,IAAI,CAAC;QACZ,CAAC,KAAK,EAAE,IAAI;QACZ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACzB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACzB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QACzB;KACH;CACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/ShiftTable.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Arrays from '../../util/Arrays';\nimport * as C from './EncoderConstants';\nexport function static_SHIFT_TABLE(SHIFT_TABLE) {\n    var e_1, _a;\n    try {\n        for (var SHIFT_TABLE_1 = __values(SHIFT_TABLE), SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next(); !SHIFT_TABLE_1_1.done; SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next()) {\n            var table = SHIFT_TABLE_1_1.value /*Int32Array*/;\n            Arrays.fill(table, -1);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (SHIFT_TABLE_1_1 && !SHIFT_TABLE_1_1.done && (_a = SHIFT_TABLE_1.return)) _a.call(SHIFT_TABLE_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    SHIFT_TABLE[C.MODE_UPPER][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_LOWER][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_LOWER][C.MODE_UPPER] = 28;\n    SHIFT_TABLE[C.MODE_MIXED][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_DIGIT][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_DIGIT][C.MODE_UPPER] = 15;\n    return SHIFT_TABLE;\n}\nexport var /*final*/ SHIFT_TABLE = static_SHIFT_TABLE(Arrays.createInt32Array(6, 6)); // mode shift codes, per table\n"], "names": [], "mappings": ";;;;AAWA;AACA;AAZA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAGO,SAAS,mBAAmB,WAAW;IAC1C,IAAI,KAAK;IACT,IAAI;QACA,IAAK,IAAI,gBAAgB,SAAS,cAAc,kBAAkB,cAAc,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,kBAAkB,cAAc,IAAI,GAAI;YACnJ,IAAI,QAAQ,gBAAgB,KAAK,CAAC,YAAY;YAC9C,sKAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAAO,CAAC;QACxB;IACJ,EACA,OAAO,OAAO;QAAE,MAAM;YAAE,OAAO;QAAM;IAAG,SAChC;QACJ,IAAI;YACA,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC;QACzF,SACQ;YAAE,IAAI,KAAK,MAAM,IAAI,KAAK;QAAE;IACxC;IACA,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,WAAW,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,4LAAA,CAAA,aAAY,CAAC,GAAG;IAC1C,OAAO;AACX;AACO,IAAI,OAAO,GAAG,cAAc,mBAAmB,sKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,GAAG,KAAK,8BAA8B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/State.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.aztec.encoder;\n// import java.util.Deque;\n// import java.util.LinkedList;\n// import com.google.zxing.common.BitArray;\nimport BitArray from '../../common/BitArray';\nimport * as TokenHelpers from './TokenHelpers';\nimport * as C from './EncoderConstants';\nimport * as LatchTable from './LatchTable';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * State represents all information about a sequence necessary to generate the current output.\n * Note that a state is immutable.\n */\nvar State = /** @class */ (function () {\n    function State(token, mode, binaryBytes, bitCount) {\n        this.token = token;\n        this.mode = mode;\n        this.binaryShiftByteCount = binaryBytes;\n        this.bitCount = bitCount;\n        // Make sure we match the token\n        // int binaryShiftBitCount = (binaryShiftByteCount * 8) +\n        //    (binaryShiftByteCount === 0 ? 0 :\n        //     binaryShiftByteCount <= 31 ? 10 :\n        //     binaryShiftByteCount <= 62 ? 20 : 21);\n        // assert this.bitCount === token.getTotalBitCount() + binaryShiftBitCount;\n    }\n    State.prototype.getMode = function () {\n        return this.mode;\n    };\n    State.prototype.getToken = function () {\n        return this.token;\n    };\n    State.prototype.getBinaryShiftByteCount = function () {\n        return this.binaryShiftByteCount;\n    };\n    State.prototype.getBitCount = function () {\n        return this.bitCount;\n    };\n    // Create a new state representing this state with a latch to a (not\n    // necessary different) mode, and then a code.\n    State.prototype.latchAndAppend = function (mode, value) {\n        // assert binaryShiftByteCount === 0;\n        var bitCount = this.bitCount;\n        var token = this.token;\n        if (mode !== this.mode) {\n            var latch = LatchTable.LATCH_TABLE[this.mode][mode];\n            token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n            bitCount += latch >> 16;\n        }\n        var latchModeBitCount = mode === C.MODE_DIGIT ? 4 : 5;\n        token = TokenHelpers.add(token, value, latchModeBitCount);\n        return new State(token, mode, 0, bitCount + latchModeBitCount);\n    };\n    // Create a new state representing this state, with a temporary shift\n    // to a different mode to output a single value.\n    State.prototype.shiftAndAppend = function (mode, value) {\n        // assert binaryShiftByteCount === 0 && this.mode !== mode;\n        var token = this.token;\n        var thisModeBitCount = this.mode === C.MODE_DIGIT ? 4 : 5;\n        // Shifts exist only to UPPER and PUNCT, both with tokens size 5.\n        token = TokenHelpers.add(token, ShiftTable.SHIFT_TABLE[this.mode][mode], thisModeBitCount);\n        token = TokenHelpers.add(token, value, 5);\n        return new State(token, this.mode, 0, this.bitCount + thisModeBitCount + 5);\n    };\n    // Create a new state representing this state, but an additional character\n    // output in Binary Shift mode.\n    State.prototype.addBinaryShiftChar = function (index) {\n        var token = this.token;\n        var mode = this.mode;\n        var bitCount = this.bitCount;\n        if (this.mode === C.MODE_PUNCT || this.mode === C.MODE_DIGIT) {\n            // assert binaryShiftByteCount === 0;\n            var latch = LatchTable.LATCH_TABLE[mode][C.MODE_UPPER];\n            token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n            bitCount += latch >> 16;\n            mode = C.MODE_UPPER;\n        }\n        var deltaBitCount = this.binaryShiftByteCount === 0 || this.binaryShiftByteCount === 31\n            ? 18\n            : this.binaryShiftByteCount === 62\n                ? 9\n                : 8;\n        var result = new State(token, mode, this.binaryShiftByteCount + 1, bitCount + deltaBitCount);\n        if (result.binaryShiftByteCount === 2047 + 31) {\n            // The string is as long as it's allowed to be.  We should end it.\n            result = result.endBinaryShift(index + 1);\n        }\n        return result;\n    };\n    // Create the state identical to this one, but we are no longer in\n    // Binary Shift mode.\n    State.prototype.endBinaryShift = function (index) {\n        if (this.binaryShiftByteCount === 0) {\n            return this;\n        }\n        var token = this.token;\n        token = TokenHelpers.addBinaryShift(token, index - this.binaryShiftByteCount, this.binaryShiftByteCount);\n        // assert token.getTotalBitCount() === this.bitCount;\n        return new State(token, this.mode, 0, this.bitCount);\n    };\n    // Returns true if \"this\" state is better (equal: or) to be in than \"that\"\n    // state under all possible circumstances.\n    State.prototype.isBetterThanOrEqualTo = function (other) {\n        var newModeBitCount = this.bitCount + (LatchTable.LATCH_TABLE[this.mode][other.mode] >> 16);\n        if (this.binaryShiftByteCount < other.binaryShiftByteCount) {\n            // add additional B/S encoding cost of other, if any\n            newModeBitCount +=\n                State.calculateBinaryShiftCost(other) -\n                    State.calculateBinaryShiftCost(this);\n        }\n        else if (this.binaryShiftByteCount > other.binaryShiftByteCount &&\n            other.binaryShiftByteCount > 0) {\n            // maximum possible additional cost (it: h)\n            newModeBitCount += 10;\n        }\n        return newModeBitCount <= other.bitCount;\n    };\n    State.prototype.toBitArray = function (text) {\n        var e_1, _a;\n        // Reverse the tokens, so that they are in the order that they should\n        // be output\n        var symbols = [];\n        for (var token = this.endBinaryShift(text.length).token; token !== null; token = token.getPrevious()) {\n            symbols.unshift(token);\n        }\n        var bitArray = new BitArray();\n        try {\n            // Add each token to the result.\n            for (var symbols_1 = __values(symbols), symbols_1_1 = symbols_1.next(); !symbols_1_1.done; symbols_1_1 = symbols_1.next()) {\n                var symbol = symbols_1_1.value;\n                symbol.appendTo(bitArray, text);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (symbols_1_1 && !symbols_1_1.done && (_a = symbols_1.return)) _a.call(symbols_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // assert bitArray.getSize() === this.bitCount;\n        return bitArray;\n    };\n    /**\n     * @Override\n     */\n    State.prototype.toString = function () {\n        return StringUtils.format('%s bits=%d bytes=%d', C.MODE_NAMES[this.mode], this.bitCount, this.binaryShiftByteCount);\n    };\n    State.calculateBinaryShiftCost = function (state) {\n        if (state.binaryShiftByteCount > 62) {\n            return 21; // B/S with extended length\n        }\n        if (state.binaryShiftByteCount > 31) {\n            return 20; // two B/S\n        }\n        if (state.binaryShiftByteCount > 0) {\n            return 10; // one B/S\n        }\n        return 0;\n    };\n    State.INITIAL_STATE = new State(C.EMPTY_TOKEN, C.MODE_UPPER, 0, 0);\n    return State;\n}());\nexport default State;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,0CAA0C;AAC1C,0BAA0B;AAC1B,+BAA+B;AAC/B,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AApBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAWA;;;CAGC,GACD,IAAI,QAAuB;IACvB,SAAS,MAAM,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ;QAC7C,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,QAAQ,GAAG;IAChB,+BAA+B;IAC/B,yDAAyD;IACzD,uCAAuC;IACvC,wCAAwC;IACxC,6CAA6C;IAC7C,2EAA2E;IAC/E;IACA,MAAM,SAAS,CAAC,OAAO,GAAG;QACtB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,MAAM,SAAS,CAAC,QAAQ,GAAG;QACvB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,MAAM,SAAS,CAAC,uBAAuB,GAAG;QACtC,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,MAAM,SAAS,CAAC,WAAW,GAAG;QAC1B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,oEAAoE;IACpE,8CAA8C;IAC9C,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI,EAAE,KAAK;QAClD,qCAAqC;QACrC,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACpB,IAAI,QAAQ,sLAAA,CAAA,cAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK;YACnD,QAAQ,wLAAA,CAAA,MAAgB,CAAC,OAAO,QAAQ,QAAQ,SAAS;YACzD,YAAY,SAAS;QACzB;QACA,IAAI,oBAAoB,SAAS,4LAAA,CAAA,aAAY,GAAG,IAAI;QACpD,QAAQ,wLAAA,CAAA,MAAgB,CAAC,OAAO,OAAO;QACvC,OAAO,IAAI,MAAM,OAAO,MAAM,GAAG,WAAW;IAChD;IACA,qEAAqE;IACrE,gDAAgD;IAChD,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI,EAAE,KAAK;QAClD,2DAA2D;QAC3D,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,mBAAmB,IAAI,CAAC,IAAI,KAAK,4LAAA,CAAA,aAAY,GAAG,IAAI;QACxD,iEAAiE;QACjE,QAAQ,wLAAA,CAAA,MAAgB,CAAC,OAAO,sLAAA,CAAA,cAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;QACzE,QAAQ,wLAAA,CAAA,MAAgB,CAAC,OAAO,OAAO;QACvC,OAAO,IAAI,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,mBAAmB;IAC7E;IACA,0EAA0E;IAC1E,+BAA+B;IAC/B,MAAM,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK;QAChD,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,4LAAA,CAAA,aAAY,IAAI,IAAI,CAAC,IAAI,KAAK,4LAAA,CAAA,aAAY,EAAE;YAC1D,qCAAqC;YACrC,IAAI,QAAQ,sLAAA,CAAA,cAAsB,CAAC,KAAK,CAAC,4LAAA,CAAA,aAAY,CAAC;YACtD,QAAQ,wLAAA,CAAA,MAAgB,CAAC,OAAO,QAAQ,QAAQ,SAAS;YACzD,YAAY,SAAS;YACrB,OAAO,4LAAA,CAAA,aAAY;QACvB;QACA,IAAI,gBAAgB,IAAI,CAAC,oBAAoB,KAAK,KAAK,IAAI,CAAC,oBAAoB,KAAK,KAC/E,KACA,IAAI,CAAC,oBAAoB,KAAK,KAC1B,IACA;QACV,IAAI,SAAS,IAAI,MAAM,OAAO,MAAM,IAAI,CAAC,oBAAoB,GAAG,GAAG,WAAW;QAC9E,IAAI,OAAO,oBAAoB,KAAK,OAAO,IAAI;YAC3C,kEAAkE;YAClE,SAAS,OAAO,cAAc,CAAC,QAAQ;QAC3C;QACA,OAAO;IACX;IACA,kEAAkE;IAClE,qBAAqB;IACrB,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QAC5C,IAAI,IAAI,CAAC,oBAAoB,KAAK,GAAG;YACjC,OAAO,IAAI;QACf;QACA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,QAAQ,wLAAA,CAAA,iBAA2B,CAAC,OAAO,QAAQ,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;QACvG,qDAAqD;QACrD,OAAO,IAAI,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ;IACvD;IACA,0EAA0E;IAC1E,0CAA0C;IAC1C,MAAM,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK;QACnD,IAAI,kBAAkB,IAAI,CAAC,QAAQ,GAAG,CAAC,sLAAA,CAAA,cAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE;QAC1F,IAAI,IAAI,CAAC,oBAAoB,GAAG,MAAM,oBAAoB,EAAE;YACxD,oDAAoD;YACpD,mBACI,MAAM,wBAAwB,CAAC,SAC3B,MAAM,wBAAwB,CAAC,IAAI;QAC/C,OACK,IAAI,IAAI,CAAC,oBAAoB,GAAG,MAAM,oBAAoB,IAC3D,MAAM,oBAAoB,GAAG,GAAG;YAChC,2CAA2C;YAC3C,mBAAmB;QACvB;QACA,OAAO,mBAAmB,MAAM,QAAQ;IAC5C;IACA,MAAM,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI;QACvC,IAAI,KAAK;QACT,qEAAqE;QACrE,YAAY;QACZ,IAAI,UAAU,EAAE;QAChB,IAAK,IAAI,QAAQ,IAAI,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,MAAM,QAAQ,MAAM,WAAW,GAAI;YAClG,QAAQ,OAAO,CAAC;QACpB;QACA,IAAI,WAAW,IAAI,0KAAA,CAAA,UAAQ;QAC3B,IAAI;YACA,gCAAgC;YAChC,IAAK,IAAI,YAAY,SAAS,UAAU,cAAc,UAAU,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,cAAc,UAAU,IAAI,GAAI;gBACvH,IAAI,SAAS,YAAY,KAAK;gBAC9B,OAAO,QAAQ,CAAC,UAAU;YAC9B;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,eAAe,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;YAC7E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,+CAA+C;QAC/C,OAAO;IACX;IACA;;KAEC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG;QACvB,OAAO,6KAAA,CAAA,UAAW,CAAC,MAAM,CAAC,uBAAuB,4LAAA,CAAA,aAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB;IACtH;IACA,MAAM,wBAAwB,GAAG,SAAU,KAAK;QAC5C,IAAI,MAAM,oBAAoB,GAAG,IAAI;YACjC,OAAO,IAAI,2BAA2B;QAC1C;QACA,IAAI,MAAM,oBAAoB,GAAG,IAAI;YACjC,OAAO,IAAI,UAAU;QACzB;QACA,IAAI,MAAM,oBAAoB,GAAG,GAAG;YAChC,OAAO,IAAI,UAAU;QACzB;QACA,OAAO;IACX;IACA,MAAM,aAAa,GAAG,IAAI,MAAM,4LAAA,CAAA,cAAa,EAAE,4LAAA,CAAA,aAAY,EAAE,GAAG;IAChE,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/CharMap.js"], "sourcesContent": ["import * as C from './EncoderConstants';\nimport Arrays from '../../util/Arrays';\nimport StringUtils from '../../common/StringUtils';\nexport function static_CHAR_MAP(CHAR_MAP) {\n    var spaceCharCode = StringUtils.getCharCode(' ');\n    var pointCharCode = StringUtils.getCharCode('.');\n    var commaCharCode = StringUtils.getCharCode(',');\n    CHAR_MAP[C.MODE_UPPER][spaceCharCode] = 1;\n    var zUpperCharCode = StringUtils.getCharCode('Z');\n    var aUpperCharCode = StringUtils.getCharCode('A');\n    for (var c = aUpperCharCode; c <= zUpperCharCode; c++) {\n        CHAR_MAP[C.MODE_UPPER][c] = c - aUpperCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_LOWER][spaceCharCode] = 1;\n    var zLowerCharCode = StringUtils.getCharCode('z');\n    var aLowerCharCode = StringUtils.getCharCode('a');\n    for (var c = aLowerCharCode; c <= zLowerCharCode; c++) {\n        CHAR_MAP[C.MODE_LOWER][c] = c - aLowerCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_DIGIT][spaceCharCode] = 1;\n    var nineCharCode = StringUtils.getCharCode('9');\n    var zeroCharCode = StringUtils.getCharCode('0');\n    for (var c = zeroCharCode; c <= nineCharCode; c++) {\n        CHAR_MAP[C.MODE_DIGIT][c] = c - zeroCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_DIGIT][commaCharCode] = 12;\n    CHAR_MAP[C.MODE_DIGIT][pointCharCode] = 13;\n    var mixedTable = [\n        '\\x00',\n        ' ',\n        '\\x01',\n        '\\x02',\n        '\\x03',\n        '\\x04',\n        '\\x05',\n        '\\x06',\n        '\\x07',\n        '\\b',\n        '\\t',\n        '\\n',\n        '\\x0b',\n        '\\f',\n        '\\r',\n        '\\x1b',\n        '\\x1c',\n        '\\x1d',\n        '\\x1e',\n        '\\x1f',\n        '@',\n        '\\\\',\n        '^',\n        '_',\n        '`',\n        '|',\n        '~',\n        '\\x7f'\n    ];\n    for (var i = 0; i < mixedTable.length; i++) {\n        CHAR_MAP[C.MODE_MIXED][StringUtils.getCharCode(mixedTable[i])] = i;\n    }\n    var punctTable = [\n        '\\x00',\n        '\\r',\n        '\\x00',\n        '\\x00',\n        '\\x00',\n        '\\x00',\n        '!',\n        '\\'',\n        '#',\n        '$',\n        '%',\n        '&',\n        '\\'',\n        '(',\n        ')',\n        '*',\n        '+',\n        ',',\n        '-',\n        '.',\n        '/',\n        ':',\n        ';',\n        '<',\n        '=',\n        '>',\n        '?',\n        '[',\n        ']',\n        '{',\n        '}'\n    ];\n    for (var i = 0; i < punctTable.length; i++) {\n        if (StringUtils.getCharCode(punctTable[i]) > 0) {\n            CHAR_MAP[C.MODE_PUNCT][StringUtils.getCharCode(punctTable[i])] = i;\n        }\n    }\n    return CHAR_MAP;\n}\nexport var CHAR_MAP = static_CHAR_MAP(Arrays.createInt32Array(5, 256));\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,SAAS,gBAAgB,QAAQ;IACpC,IAAI,gBAAgB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC5C,IAAI,gBAAgB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC5C,IAAI,gBAAgB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC5C,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,cAAc,GAAG;IACxC,IAAI,iBAAiB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC7C,IAAI,iBAAiB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC7C,IAAK,IAAI,IAAI,gBAAgB,KAAK,gBAAgB,IAAK;QACnD,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,EAAE,GAAG,IAAI,iBAAiB;IACrD;IACA,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,cAAc,GAAG;IACxC,IAAI,iBAAiB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC7C,IAAI,iBAAiB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC7C,IAAK,IAAI,IAAI,gBAAgB,KAAK,gBAAgB,IAAK;QACnD,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,EAAE,GAAG,IAAI,iBAAiB;IACrD;IACA,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,cAAc,GAAG;IACxC,IAAI,eAAe,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC3C,IAAI,eAAe,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;IAC3C,IAAK,IAAI,IAAI,cAAc,KAAK,cAAc,IAAK;QAC/C,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,EAAE,GAAG,IAAI,eAAe;IACnD;IACA,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,cAAc,GAAG;IACxC,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,cAAc,GAAG;IACxC,IAAI,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACxC,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG;IACrE;IACA,IAAI,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACxC,IAAI,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,IAAI,GAAG;YAC5C,QAAQ,CAAC,4LAAA,CAAA,aAAY,CAAC,CAAC,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG;QACrE;IACJ;IACA,OAAO;AACX;AACO,IAAI,WAAW,gBAAgB,sKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import java.util.Collection;\n// import java.util.Collections;\nimport Collections from '../../util/Collections';\n// import java.util.Comparator;\n// import java.util.Iterator;\n// import java.util.LinkedList;\nimport State from './State';\nimport * as C from './EncoderConstants';\nimport * as CharMap from './CharMap';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * This produces nearly optimal encodings of text into the first-level of\n * encoding used by Aztec code.\n *\n * It uses a dynamic algorithm.  For each prefix of the string, it determines\n * a set of encodings that could lead to this prefix.  We repeatedly add a\n * character and generate a new set of optimal encodings until we have read\n * through the entire input.\n *\n * <AUTHOR> Yellin\n * <AUTHOR> Abdullaev\n */\nvar HighLevelEncoder = /** @class */ (function () {\n    function HighLevelEncoder(text) {\n        this.text = text;\n    }\n    /**\n     * @return text represented by this encoder encoded as a {@link BitArray}\n     */\n    HighLevelEncoder.prototype.encode = function () {\n        var spaceCharCode = StringUtils.getCharCode(' ');\n        var lineBreakCharCode = StringUtils.getCharCode('\\n');\n        var states = Collections.singletonList(State.INITIAL_STATE);\n        for (var index = 0; index < this.text.length; index++) {\n            var pairCode = void 0;\n            var nextChar = index + 1 < this.text.length ? this.text[index + 1] : 0;\n            switch (this.text[index]) {\n                case StringUtils.getCharCode('\\r'):\n                    pairCode = nextChar === lineBreakCharCode ? 2 : 0;\n                    break;\n                case StringUtils.getCharCode('.'):\n                    pairCode = nextChar === spaceCharCode ? 3 : 0;\n                    break;\n                case StringUtils.getCharCode(','):\n                    pairCode = nextChar === spaceCharCode ? 4 : 0;\n                    break;\n                case StringUtils.getCharCode(':'):\n                    pairCode = nextChar === spaceCharCode ? 5 : 0;\n                    break;\n                default:\n                    pairCode = 0;\n            }\n            if (pairCode > 0) {\n                // We have one of the four special PUNCT pairs.  Treat them specially.\n                // Get a new set of states for the two new characters.\n                states = HighLevelEncoder.updateStateListForPair(states, index, pairCode);\n                index++;\n            }\n            else {\n                // Get a new set of states for the new character.\n                states = this.updateStateListForChar(states, index);\n            }\n        }\n        // We are left with a set of states.  Find the shortest one.\n        var minState = Collections.min(states, function (a, b) {\n            return a.getBitCount() - b.getBitCount();\n        });\n        // Convert it to a bit array, and return.\n        return minState.toBitArray(this.text);\n    };\n    // We update a set of states for a new character by updating each state\n    // for the new character, merging the results, and then removing the\n    // non-optimal states.\n    HighLevelEncoder.prototype.updateStateListForChar = function (states, index) {\n        var e_1, _a;\n        var result = [];\n        try {\n            for (var states_1 = __values(states), states_1_1 = states_1.next(); !states_1_1.done; states_1_1 = states_1.next()) {\n                var state = states_1_1.value /*State*/;\n                this.updateStateForChar(state, index, result);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (states_1_1 && !states_1_1.done && (_a = states_1.return)) _a.call(states_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return HighLevelEncoder.simplifyStates(result);\n    };\n    // Return a set of states that represent the possible ways of updating this\n    // state for the next character.  The resulting set of states are added to\n    // the \"result\" list.\n    HighLevelEncoder.prototype.updateStateForChar = function (state, index, result) {\n        var ch = (this.text[index] & 0xff);\n        var charInCurrentTable = CharMap.CHAR_MAP[state.getMode()][ch] > 0;\n        var stateNoBinary = null;\n        for (var mode /*int*/ = 0; mode <= C.MODE_PUNCT; mode++) {\n            var charInMode = CharMap.CHAR_MAP[mode][ch];\n            if (charInMode > 0) {\n                if (stateNoBinary == null) {\n                    // Only create stateNoBinary the first time it's required.\n                    stateNoBinary = state.endBinaryShift(index);\n                }\n                // Try generating the character by latching to its mode\n                if (!charInCurrentTable ||\n                    mode === state.getMode() ||\n                    mode === C.MODE_DIGIT) {\n                    // If the character is in the current table, we don't want to latch to\n                    // any other mode except possibly digit (which uses only 4 bits).  Any\n                    // other latch would be equally successful *after* this character, and\n                    // so wouldn't save any bits.\n                    var latchState = stateNoBinary.latchAndAppend(mode, charInMode);\n                    result.push(latchState);\n                }\n                // Try generating the character by switching to its mode.\n                if (!charInCurrentTable &&\n                    ShiftTable.SHIFT_TABLE[state.getMode()][mode] >= 0) {\n                    // It never makes sense to temporarily shift to another mode if the\n                    // character exists in the current mode.  That can never save bits.\n                    var shiftState = stateNoBinary.shiftAndAppend(mode, charInMode);\n                    result.push(shiftState);\n                }\n            }\n        }\n        if (state.getBinaryShiftByteCount() > 0 ||\n            CharMap.CHAR_MAP[state.getMode()][ch] === 0) {\n            // It's never worthwhile to go into binary shift mode if you're not already\n            // in binary shift mode, and the character exists in your current mode.\n            // That can never save bits over just outputting the char in the current mode.\n            var binaryState = state.addBinaryShiftChar(index);\n            result.push(binaryState);\n        }\n    };\n    HighLevelEncoder.updateStateListForPair = function (states, index, pairCode) {\n        var e_2, _a;\n        var result = [];\n        try {\n            for (var states_2 = __values(states), states_2_1 = states_2.next(); !states_2_1.done; states_2_1 = states_2.next()) {\n                var state = states_2_1.value /*State*/;\n                this.updateStateForPair(state, index, pairCode, result);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (states_2_1 && !states_2_1.done && (_a = states_2.return)) _a.call(states_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return this.simplifyStates(result);\n    };\n    HighLevelEncoder.updateStateForPair = function (state, index, pairCode, result) {\n        var stateNoBinary = state.endBinaryShift(index);\n        // Possibility 1.  Latch to C.MODE_PUNCT, and then append this code\n        result.push(stateNoBinary.latchAndAppend(C.MODE_PUNCT, pairCode));\n        if (state.getMode() !== C.MODE_PUNCT) {\n            // Possibility 2.  Shift to C.MODE_PUNCT, and then append this code.\n            // Every state except C.MODE_PUNCT (handled above) can shift\n            result.push(stateNoBinary.shiftAndAppend(C.MODE_PUNCT, pairCode));\n        }\n        if (pairCode === 3 || pairCode === 4) {\n            // both characters are in DIGITS.  Sometimes better to just add two digits\n            var digitState = stateNoBinary\n                .latchAndAppend(C.MODE_DIGIT, 16 - pairCode) // period or comma in DIGIT\n                .latchAndAppend(C.MODE_DIGIT, 1); // space in DIGIT\n            result.push(digitState);\n        }\n        if (state.getBinaryShiftByteCount() > 0) {\n            // It only makes sense to do the characters as binary if we're already\n            // in binary mode.\n            var binaryState = state\n                .addBinaryShiftChar(index)\n                .addBinaryShiftChar(index + 1);\n            result.push(binaryState);\n        }\n    };\n    HighLevelEncoder.simplifyStates = function (states) {\n        var e_3, _a, e_4, _b;\n        var result = [];\n        try {\n            for (var states_3 = __values(states), states_3_1 = states_3.next(); !states_3_1.done; states_3_1 = states_3.next()) {\n                var newState = states_3_1.value;\n                var add = true;\n                var _loop_1 = function (oldState) {\n                    if (oldState.isBetterThanOrEqualTo(newState)) {\n                        add = false;\n                        return \"break\";\n                    }\n                    if (newState.isBetterThanOrEqualTo(oldState)) {\n                        // iterator.remove();\n                        result = result.filter(function (x) { return x !== oldState; }); // remove old state\n                    }\n                };\n                try {\n                    for (var result_1 = (e_4 = void 0, __values(result)), result_1_1 = result_1.next(); !result_1_1.done; result_1_1 = result_1.next()) {\n                        var oldState = result_1_1.value;\n                        var state_1 = _loop_1(oldState);\n                        if (state_1 === \"break\")\n                            break;\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (result_1_1 && !result_1_1.done && (_b = result_1.return)) _b.call(result_1);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                if (add) {\n                    result.push(newState);\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (states_3_1 && !states_3_1.done && (_a = states_3.return)) _a.call(states_3);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return result;\n    };\n    return HighLevelEncoder;\n}());\nexport default HighLevelEncoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,+BAA+B;AAC/B,gCAAgC;AAChC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AArBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAYA;;;;;;;;;;;CAWC,GACD,IAAI,mBAAkC;IAClC,SAAS,iBAAiB,IAAI;QAC1B,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;KAEC,GACD,iBAAiB,SAAS,CAAC,MAAM,GAAG;QAChC,IAAI,gBAAgB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;QAC5C,IAAI,oBAAoB,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;QAChD,IAAI,SAAS,2KAAA,CAAA,UAAW,CAAC,aAAa,CAAC,iLAAA,CAAA,UAAK,CAAC,aAAa;QAC1D,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAS;YACnD,IAAI,WAAW,KAAK;YACpB,IAAI,WAAW,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG;YACrE,OAAQ,IAAI,CAAC,IAAI,CAAC,MAAM;gBACpB,KAAK,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;oBACzB,WAAW,aAAa,oBAAoB,IAAI;oBAChD;gBACJ,KAAK,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;oBACzB,WAAW,aAAa,gBAAgB,IAAI;oBAC5C;gBACJ,KAAK,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;oBACzB,WAAW,aAAa,gBAAgB,IAAI;oBAC5C;gBACJ,KAAK,6KAAA,CAAA,UAAW,CAAC,WAAW,CAAC;oBACzB,WAAW,aAAa,gBAAgB,IAAI;oBAC5C;gBACJ;oBACI,WAAW;YACnB;YACA,IAAI,WAAW,GAAG;gBACd,sEAAsE;gBACtE,sDAAsD;gBACtD,SAAS,iBAAiB,sBAAsB,CAAC,QAAQ,OAAO;gBAChE;YACJ,OACK;gBACD,iDAAiD;gBACjD,SAAS,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YACjD;QACJ;QACA,4DAA4D;QAC5D,IAAI,WAAW,2KAAA,CAAA,UAAW,CAAC,GAAG,CAAC,QAAQ,SAAU,CAAC,EAAE,CAAC;YACjD,OAAO,EAAE,WAAW,KAAK,EAAE,WAAW;QAC1C;QACA,yCAAyC;QACzC,OAAO,SAAS,UAAU,CAAC,IAAI,CAAC,IAAI;IACxC;IACA,uEAAuE;IACvE,oEAAoE;IACpE,sBAAsB;IACtB,iBAAiB,SAAS,CAAC,sBAAsB,GAAG,SAAU,MAAM,EAAE,KAAK;QACvE,IAAI,KAAK;QACT,IAAI,SAAS,EAAE;QACf,IAAI;YACA,IAAK,IAAI,WAAW,SAAS,SAAS,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;gBAChH,IAAI,QAAQ,WAAW,KAAK,CAAC,OAAO;gBACpC,IAAI,CAAC,kBAAkB,CAAC,OAAO,OAAO;YAC1C;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;YAC1E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,iBAAiB,cAAc,CAAC;IAC3C;IACA,2EAA2E;IAC3E,0EAA0E;IAC1E,qBAAqB;IACrB,iBAAiB,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,MAAM;QAC1E,IAAI,KAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAC7B,IAAI,qBAAqB,mLAAA,CAAA,WAAgB,CAAC,MAAM,OAAO,GAAG,CAAC,GAAG,GAAG;QACjE,IAAI,gBAAgB;QACpB,IAAK,IAAI,KAAK,KAAK,MAAK,GAAG,QAAQ,4LAAA,CAAA,aAAY,EAAE,OAAQ;YACrD,IAAI,aAAa,mLAAA,CAAA,WAAgB,CAAC,KAAK,CAAC,GAAG;YAC3C,IAAI,aAAa,GAAG;gBAChB,IAAI,iBAAiB,MAAM;oBACvB,0DAA0D;oBAC1D,gBAAgB,MAAM,cAAc,CAAC;gBACzC;gBACA,uDAAuD;gBACvD,IAAI,CAAC,sBACD,SAAS,MAAM,OAAO,MACtB,SAAS,4LAAA,CAAA,aAAY,EAAE;oBACvB,sEAAsE;oBACtE,sEAAsE;oBACtE,sEAAsE;oBACtE,6BAA6B;oBAC7B,IAAI,aAAa,cAAc,cAAc,CAAC,MAAM;oBACpD,OAAO,IAAI,CAAC;gBAChB;gBACA,yDAAyD;gBACzD,IAAI,CAAC,sBACD,sLAAA,CAAA,cAAsB,CAAC,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,GAAG;oBACpD,mEAAmE;oBACnE,mEAAmE;oBACnE,IAAI,aAAa,cAAc,cAAc,CAAC,MAAM;oBACpD,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;QACA,IAAI,MAAM,uBAAuB,KAAK,KAClC,mLAAA,CAAA,WAAgB,CAAC,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,GAAG;YAC7C,2EAA2E;YAC3E,uEAAuE;YACvE,8EAA8E;YAC9E,IAAI,cAAc,MAAM,kBAAkB,CAAC;YAC3C,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,iBAAiB,sBAAsB,GAAG,SAAU,MAAM,EAAE,KAAK,EAAE,QAAQ;QACvE,IAAI,KAAK;QACT,IAAI,SAAS,EAAE;QACf,IAAI;YACA,IAAK,IAAI,WAAW,SAAS,SAAS,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;gBAChH,IAAI,QAAQ,WAAW,KAAK,CAAC,OAAO;gBACpC,IAAI,CAAC,kBAAkB,CAAC,OAAO,OAAO,UAAU;YACpD;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;YAC1E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA,iBAAiB,kBAAkB,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM;QAC1E,IAAI,gBAAgB,MAAM,cAAc,CAAC;QACzC,mEAAmE;QACnE,OAAO,IAAI,CAAC,cAAc,cAAc,CAAC,4LAAA,CAAA,aAAY,EAAE;QACvD,IAAI,MAAM,OAAO,OAAO,4LAAA,CAAA,aAAY,EAAE;YAClC,oEAAoE;YACpE,4DAA4D;YAC5D,OAAO,IAAI,CAAC,cAAc,cAAc,CAAC,4LAAA,CAAA,aAAY,EAAE;QAC3D;QACA,IAAI,aAAa,KAAK,aAAa,GAAG;YAClC,0EAA0E;YAC1E,IAAI,aAAa,cACZ,cAAc,CAAC,4LAAA,CAAA,aAAY,EAAE,KAAK,UAAU,2BAA2B;aACvE,cAAc,CAAC,4LAAA,CAAA,aAAY,EAAE,IAAI,iBAAiB;YACvD,OAAO,IAAI,CAAC;QAChB;QACA,IAAI,MAAM,uBAAuB,KAAK,GAAG;YACrC,sEAAsE;YACtE,kBAAkB;YAClB,IAAI,cAAc,MACb,kBAAkB,CAAC,OACnB,kBAAkB,CAAC,QAAQ;YAChC,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,iBAAiB,cAAc,GAAG,SAAU,MAAM;QAC9C,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,SAAS,EAAE;QACf,IAAI;YACA,IAAK,IAAI,WAAW,SAAS,SAAS,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;gBAChH,IAAI,WAAW,WAAW,KAAK;gBAC/B,IAAI,MAAM;gBACV,IAAI,UAAU,SAAU,QAAQ;oBAC5B,IAAI,SAAS,qBAAqB,CAAC,WAAW;wBAC1C,MAAM;wBACN,OAAO;oBACX;oBACA,IAAI,SAAS,qBAAqB,CAAC,WAAW;wBAC1C,qBAAqB;wBACrB,SAAS,OAAO,MAAM,CAAC,SAAU,CAAC;4BAAI,OAAO,MAAM;wBAAU,IAAI,mBAAmB;oBACxF;gBACJ;gBACA,IAAI;oBACA,IAAK,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,SAAS,OAAO,GAAG,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;wBAChI,IAAI,WAAW,WAAW,KAAK;wBAC/B,IAAI,UAAU,QAAQ;wBACtB,IAAI,YAAY,SACZ;oBACR;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;oBAC1E,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;gBACA,IAAI,KAAK;oBACL,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;YAC1E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/encoder/Encoder.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BitArray from '../../common/BitArray';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport StringUtils from '../../common/StringUtils';\nimport BitMatrix from '../../common/BitMatrix';\nimport AztecCode from './AztecCode';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport Integer from '../../util/Integer';\n/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.aztec.encoder;\n// import com.google.zxing.common.BitArray;\n// import com.google.zxing.common.BitMatrix;\n// import com.google.zxing.common.reedsolomon.GenericGF;\n// import com.google.zxing.common.reedsolomon.ReedSolomonEncoder;\n/**\n * Generates Aztec 2D barcodes.\n *\n * <AUTHOR> Abdullaev\n */\nvar Encoder = /** @class */ (function () {\n    function Encoder() {\n    }\n    /**\n     * Encodes the given binary content as an Aztec symbol\n     *\n     * @param data input data string\n     * @return Aztec symbol matrix with metadata\n     */\n    Encoder.encodeBytes = function (data) {\n        return Encoder.encode(data, Encoder.DEFAULT_EC_PERCENT, Encoder.DEFAULT_AZTEC_LAYERS);\n    };\n    /**\n     * Encodes the given binary content as an Aztec symbol\n     *\n     * @param data input data string\n     * @param minECCPercent minimal percentage of error check words (According to ISO/IEC 24778:2008,\n     *                      a minimum of 23% + 3 words is recommended)\n     * @param userSpecifiedLayers if non-zero, a user-specified value for the number of layers\n     * @return Aztec symbol matrix with metadata\n     */\n    Encoder.encode = function (data, minECCPercent, userSpecifiedLayers) {\n        // High-level encode\n        var bits = new HighLevelEncoder(data).encode();\n        // stuff bits and choose symbol size\n        var eccBits = Integer.truncDivision((bits.getSize() * minECCPercent), 100) + 11;\n        var totalSizeBits = bits.getSize() + eccBits;\n        var compact;\n        var layers;\n        var totalBitsInLayer;\n        var wordSize;\n        var stuffedBits;\n        if (userSpecifiedLayers !== Encoder.DEFAULT_AZTEC_LAYERS) {\n            compact = userSpecifiedLayers < 0;\n            layers = Math.abs(userSpecifiedLayers);\n            if (layers > (compact ? Encoder.MAX_NB_BITS_COMPACT : Encoder.MAX_NB_BITS)) {\n                throw new IllegalArgumentException(StringUtils.format('Illegal value %s for layers', userSpecifiedLayers));\n            }\n            totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n            wordSize = Encoder.WORD_SIZE[layers];\n            var usableBitsInLayers = totalBitsInLayer - (totalBitsInLayer % wordSize);\n            stuffedBits = Encoder.stuffBits(bits, wordSize);\n            if (stuffedBits.getSize() + eccBits > usableBitsInLayers) {\n                throw new IllegalArgumentException('Data to large for user specified layer');\n            }\n            if (compact && stuffedBits.getSize() > wordSize * 64) {\n                // Compact format only allows 64 data words, though C4 can hold more words than that\n                throw new IllegalArgumentException('Data to large for user specified layer');\n            }\n        }\n        else {\n            wordSize = 0;\n            stuffedBits = null;\n            // We look at the possible table sizes in the order Compact1, Compact2, Compact3,\n            // Compact4, Normal4,...  Normal(i) for i < 4 isn't typically used since Compact(i+1)\n            // is the same size, but has more data.\n            for (var i /*int*/ = 0;; i++) {\n                if (i > Encoder.MAX_NB_BITS) {\n                    throw new IllegalArgumentException('Data too large for an Aztec code');\n                }\n                compact = i <= 3;\n                layers = compact ? i + 1 : i;\n                totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n                if (totalSizeBits > totalBitsInLayer) {\n                    continue;\n                }\n                // [Re]stuff the bits if this is the first opportunity, or if the\n                // wordSize has changed\n                if (stuffedBits == null || wordSize !== Encoder.WORD_SIZE[layers]) {\n                    wordSize = Encoder.WORD_SIZE[layers];\n                    stuffedBits = Encoder.stuffBits(bits, wordSize);\n                }\n                var usableBitsInLayers = totalBitsInLayer - (totalBitsInLayer % wordSize);\n                if (compact && stuffedBits.getSize() > wordSize * 64) {\n                    // Compact format only allows 64 data words, though C4 can hold more words than that\n                    continue;\n                }\n                if (stuffedBits.getSize() + eccBits <= usableBitsInLayers) {\n                    break;\n                }\n            }\n        }\n        var messageBits = Encoder.generateCheckWords(stuffedBits, totalBitsInLayer, wordSize);\n        // generate mode message\n        var messageSizeInWords = stuffedBits.getSize() / wordSize;\n        var modeMessage = Encoder.generateModeMessage(compact, layers, messageSizeInWords);\n        // allocate symbol\n        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n        var alignmentMap = new Int32Array(baseMatrixSize);\n        var matrixSize;\n        if (compact) {\n            // no alignment marks in compact mode, alignmentMap is a no-op\n            matrixSize = baseMatrixSize;\n            for (var i /*int*/ = 0; i < alignmentMap.length; i++) {\n                alignmentMap[i] = i;\n            }\n        }\n        else {\n            matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision((Integer.truncDivision(baseMatrixSize, 2) - 1), 15);\n            var origCenter = Integer.truncDivision(baseMatrixSize, 2);\n            var center = Integer.truncDivision(matrixSize, 2);\n            for (var i /*int*/ = 0; i < origCenter; i++) {\n                var newOffset = i + Integer.truncDivision(i, 15);\n                alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n                alignmentMap[origCenter + i] = center + newOffset + 1;\n            }\n        }\n        var matrix = new BitMatrix(matrixSize);\n        // draw data bits\n        for (var i /*int*/ = 0, rowOffset = 0; i < layers; i++) {\n            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n            for (var j /*int*/ = 0; j < rowSize; j++) {\n                var columnOffset = j * 2;\n                for (var k /*int*/ = 0; k < 2; k++) {\n                    if (messageBits.get(rowOffset + columnOffset + k)) {\n                        matrix.set(alignmentMap[i * 2 + k], alignmentMap[i * 2 + j]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 2 + columnOffset + k)) {\n                        matrix.set(alignmentMap[i * 2 + j], alignmentMap[baseMatrixSize - 1 - i * 2 - k]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 4 + columnOffset + k)) {\n                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - k], alignmentMap[baseMatrixSize - 1 - i * 2 - j]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 6 + columnOffset + k)) {\n                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - j], alignmentMap[i * 2 + k]);\n                    }\n                }\n            }\n            rowOffset += rowSize * 8;\n        }\n        // draw mode message\n        Encoder.drawModeMessage(matrix, compact, matrixSize, modeMessage);\n        // draw alignment marks\n        if (compact) {\n            Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 5);\n        }\n        else {\n            Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 7);\n            for (var i /*int*/ = 0, j = 0; i < Integer.truncDivision(baseMatrixSize, 2) - 1; i += 15, j += 16) {\n                for (var k /*int*/ = Integer.truncDivision(matrixSize, 2) & 1; k < matrixSize; k += 2) {\n                    matrix.set(Integer.truncDivision(matrixSize, 2) - j, k);\n                    matrix.set(Integer.truncDivision(matrixSize, 2) + j, k);\n                    matrix.set(k, Integer.truncDivision(matrixSize, 2) - j);\n                    matrix.set(k, Integer.truncDivision(matrixSize, 2) + j);\n                }\n            }\n        }\n        var aztec = new AztecCode();\n        aztec.setCompact(compact);\n        aztec.setSize(matrixSize);\n        aztec.setLayers(layers);\n        aztec.setCodeWords(messageSizeInWords);\n        aztec.setMatrix(matrix);\n        return aztec;\n    };\n    Encoder.drawBullsEye = function (matrix, center, size) {\n        for (var i /*int*/ = 0; i < size; i += 2) {\n            for (var j /*int*/ = center - i; j <= center + i; j++) {\n                matrix.set(j, center - i);\n                matrix.set(j, center + i);\n                matrix.set(center - i, j);\n                matrix.set(center + i, j);\n            }\n        }\n        matrix.set(center - size, center - size);\n        matrix.set(center - size + 1, center - size);\n        matrix.set(center - size, center - size + 1);\n        matrix.set(center + size, center - size);\n        matrix.set(center + size, center - size + 1);\n        matrix.set(center + size, center + size - 1);\n    };\n    Encoder.generateModeMessage = function (compact, layers, messageSizeInWords) {\n        var modeMessage = new BitArray();\n        if (compact) {\n            modeMessage.appendBits(layers - 1, 2);\n            modeMessage.appendBits(messageSizeInWords - 1, 6);\n            modeMessage = Encoder.generateCheckWords(modeMessage, 28, 4);\n        }\n        else {\n            modeMessage.appendBits(layers - 1, 5);\n            modeMessage.appendBits(messageSizeInWords - 1, 11);\n            modeMessage = Encoder.generateCheckWords(modeMessage, 40, 4);\n        }\n        return modeMessage;\n    };\n    Encoder.drawModeMessage = function (matrix, compact, matrixSize, modeMessage) {\n        var center = Integer.truncDivision(matrixSize, 2);\n        if (compact) {\n            for (var i /*int*/ = 0; i < 7; i++) {\n                var offset = center - 3 + i;\n                if (modeMessage.get(i)) {\n                    matrix.set(offset, center - 5);\n                }\n                if (modeMessage.get(i + 7)) {\n                    matrix.set(center + 5, offset);\n                }\n                if (modeMessage.get(20 - i)) {\n                    matrix.set(offset, center + 5);\n                }\n                if (modeMessage.get(27 - i)) {\n                    matrix.set(center - 5, offset);\n                }\n            }\n        }\n        else {\n            for (var i /*int*/ = 0; i < 10; i++) {\n                var offset = center - 5 + i + Integer.truncDivision(i, 5);\n                if (modeMessage.get(i)) {\n                    matrix.set(offset, center - 7);\n                }\n                if (modeMessage.get(i + 10)) {\n                    matrix.set(center + 7, offset);\n                }\n                if (modeMessage.get(29 - i)) {\n                    matrix.set(offset, center + 7);\n                }\n                if (modeMessage.get(39 - i)) {\n                    matrix.set(center - 7, offset);\n                }\n            }\n        }\n    };\n    Encoder.generateCheckWords = function (bitArray, totalBits, wordSize) {\n        var e_1, _a;\n        // bitArray is guaranteed to be a multiple of the wordSize, so no padding needed\n        var messageSizeInWords = bitArray.getSize() / wordSize;\n        var rs = new ReedSolomonEncoder(Encoder.getGF(wordSize));\n        var totalWords = Integer.truncDivision(totalBits, wordSize);\n        var messageWords = Encoder.bitsToWords(bitArray, wordSize, totalWords);\n        rs.encode(messageWords, totalWords - messageSizeInWords);\n        var startPad = totalBits % wordSize;\n        var messageBits = new BitArray();\n        messageBits.appendBits(0, startPad);\n        try {\n            for (var _b = __values(Array.from(messageWords)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var messageWord = _c.value /*: int*/;\n                messageBits.appendBits(messageWord, wordSize);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return messageBits;\n    };\n    Encoder.bitsToWords = function (stuffedBits, wordSize, totalWords) {\n        var message = new Int32Array(totalWords);\n        var i;\n        var n;\n        for (i = 0, n = stuffedBits.getSize() / wordSize; i < n; i++) {\n            var value = 0;\n            for (var j /*int*/ = 0; j < wordSize; j++) {\n                value |= stuffedBits.get(i * wordSize + j) ? (1 << wordSize - j - 1) : 0;\n            }\n            message[i] = value;\n        }\n        return message;\n    };\n    Encoder.getGF = function (wordSize) {\n        switch (wordSize) {\n            case 4:\n                return GenericGF.AZTEC_PARAM;\n            case 6:\n                return GenericGF.AZTEC_DATA_6;\n            case 8:\n                return GenericGF.AZTEC_DATA_8;\n            case 10:\n                return GenericGF.AZTEC_DATA_10;\n            case 12:\n                return GenericGF.AZTEC_DATA_12;\n            default:\n                throw new IllegalArgumentException('Unsupported word size ' + wordSize);\n        }\n    };\n    Encoder.stuffBits = function (bits, wordSize) {\n        var out = new BitArray();\n        var n = bits.getSize();\n        var mask = (1 << wordSize) - 2;\n        for (var i /*int*/ = 0; i < n; i += wordSize) {\n            var word = 0;\n            for (var j /*int*/ = 0; j < wordSize; j++) {\n                if (i + j >= n || bits.get(i + j)) {\n                    word |= 1 << (wordSize - 1 - j);\n                }\n            }\n            if ((word & mask) === mask) {\n                out.appendBits(word & mask, wordSize);\n                i--;\n            }\n            else if ((word & mask) === 0) {\n                out.appendBits(word | 1, wordSize);\n                i--;\n            }\n            else {\n                out.appendBits(word, wordSize);\n            }\n        }\n        return out;\n    };\n    Encoder.totalBitsInLayer = function (layers, compact) {\n        return ((compact ? 88 : 112) + 16 * layers) * layers;\n    };\n    Encoder.DEFAULT_EC_PERCENT = 33; // default minimal percentage of error check words\n    Encoder.DEFAULT_AZTEC_LAYERS = 0;\n    Encoder.MAX_NB_BITS = 32;\n    Encoder.MAX_NB_BITS_COMPACT = 4;\n    Encoder.WORD_SIZE = Int32Array.from([\n        4, 6, 6, 8, 8, 8, 8, 8, 8, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10,\n        12, 12, 12, 12, 12, 12, 12, 12, 12, 12\n    ]);\n    return Encoder;\n}());\nexport default Encoder;\n"], "names": [], "mappings": ";;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;AAUA;;;;;;;;;;;;;;CAcC,GACD,0CAA0C;AAC1C,2CAA2C;AAC3C,4CAA4C;AAC5C,wDAAwD;AACxD,iEAAiE;AACjE;;;;CAIC,GACD,IAAI,UAAyB;IACzB,SAAS,WACT;IACA;;;;;KAKC,GACD,QAAQ,WAAW,GAAG,SAAU,IAAI;QAChC,OAAO,QAAQ,MAAM,CAAC,MAAM,QAAQ,kBAAkB,EAAE,QAAQ,oBAAoB;IACxF;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,aAAa,EAAE,mBAAmB;QAC/D,oBAAoB;QACpB,IAAI,OAAO,IAAI,4LAAA,CAAA,UAAgB,CAAC,MAAM,MAAM;QAC5C,oCAAoC;QACpC,IAAI,UAAU,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAE,KAAK,OAAO,KAAK,eAAgB,OAAO;QAC7E,IAAI,gBAAgB,KAAK,OAAO,KAAK;QACrC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,wBAAwB,QAAQ,oBAAoB,EAAE;YACtD,UAAU,sBAAsB;YAChC,SAAS,KAAK,GAAG,CAAC;YAClB,IAAI,SAAS,CAAC,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,WAAW,GAAG;gBACxE,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,6KAAA,CAAA,UAAW,CAAC,MAAM,CAAC,+BAA+B;YACzF;YACA,mBAAmB,QAAQ,gBAAgB,CAAC,QAAQ;YACpD,WAAW,QAAQ,SAAS,CAAC,OAAO;YACpC,IAAI,qBAAqB,mBAAoB,mBAAmB;YAChE,cAAc,QAAQ,SAAS,CAAC,MAAM;YACtC,IAAI,YAAY,OAAO,KAAK,UAAU,oBAAoB;gBACtD,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;YACvC;YACA,IAAI,WAAW,YAAY,OAAO,KAAK,WAAW,IAAI;gBAClD,oFAAoF;gBACpF,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;YACvC;QACJ,OACK;YACD,WAAW;YACX,cAAc;YACd,iFAAiF;YACjF,qFAAqF;YACrF,uCAAuC;YACvC,IAAK,IAAI,EAAE,KAAK,MAAK,IAAI,IAAK;gBAC1B,IAAI,IAAI,QAAQ,WAAW,EAAE;oBACzB,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;gBACvC;gBACA,UAAU,KAAK;gBACf,SAAS,UAAU,IAAI,IAAI;gBAC3B,mBAAmB,QAAQ,gBAAgB,CAAC,QAAQ;gBACpD,IAAI,gBAAgB,kBAAkB;oBAClC;gBACJ;gBACA,iEAAiE;gBACjE,uBAAuB;gBACvB,IAAI,eAAe,QAAQ,aAAa,QAAQ,SAAS,CAAC,OAAO,EAAE;oBAC/D,WAAW,QAAQ,SAAS,CAAC,OAAO;oBACpC,cAAc,QAAQ,SAAS,CAAC,MAAM;gBAC1C;gBACA,IAAI,qBAAqB,mBAAoB,mBAAmB;gBAChE,IAAI,WAAW,YAAY,OAAO,KAAK,WAAW,IAAI;oBAElD;gBACJ;gBACA,IAAI,YAAY,OAAO,KAAK,WAAW,oBAAoB;oBACvD;gBACJ;YACJ;QACJ;QACA,IAAI,cAAc,QAAQ,kBAAkB,CAAC,aAAa,kBAAkB;QAC5E,wBAAwB;QACxB,IAAI,qBAAqB,YAAY,OAAO,KAAK;QACjD,IAAI,cAAc,QAAQ,mBAAmB,CAAC,SAAS,QAAQ;QAC/D,kBAAkB;QAClB,IAAI,iBAAiB,CAAC,UAAU,KAAK,EAAE,IAAI,SAAS,GAAG,gCAAgC;QACvF,IAAI,eAAe,IAAI,WAAW;QAClC,IAAI;QACJ,IAAI,SAAS;YACT,8DAA8D;YAC9D,aAAa;YACb,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAClD,YAAY,CAAC,EAAE,GAAG;YACtB;QACJ,OACK;YACD,aAAa,iBAAiB,IAAI,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAE,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,gBAAgB,KAAK,GAAI;YAC5G,IAAI,aAAa,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,gBAAgB;YACvD,IAAI,SAAS,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY;YAC/C,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,YAAY,IAAK;gBACzC,IAAI,YAAY,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,GAAG;gBAC7C,YAAY,CAAC,aAAa,IAAI,EAAE,GAAG,SAAS,YAAY;gBACxD,YAAY,CAAC,aAAa,EAAE,GAAG,SAAS,YAAY;YACxD;QACJ;QACA,IAAI,SAAS,IAAI,2KAAA,CAAA,UAAS,CAAC;QAC3B,iBAAiB;QACjB,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,YAAY,GAAG,IAAI,QAAQ,IAAK;YACpD,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE;YAClD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,SAAS,IAAK;gBACtC,IAAI,eAAe,IAAI;gBACvB,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAK;oBAChC,IAAI,YAAY,GAAG,CAAC,YAAY,eAAe,IAAI;wBAC/C,OAAO,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,IAAI,EAAE;oBAC/D;oBACA,IAAI,YAAY,GAAG,CAAC,YAAY,UAAU,IAAI,eAAe,IAAI;wBAC7D,OAAO,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,IAAI,IAAI,IAAI,EAAE;oBACpF;oBACA,IAAI,YAAY,GAAG,CAAC,YAAY,UAAU,IAAI,eAAe,IAAI;wBAC7D,OAAO,GAAG,CAAC,YAAY,CAAC,iBAAiB,IAAI,IAAI,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,IAAI,IAAI,IAAI,EAAE;oBACzG;oBACA,IAAI,YAAY,GAAG,CAAC,YAAY,UAAU,IAAI,eAAe,IAAI;wBAC7D,OAAO,GAAG,CAAC,YAAY,CAAC,iBAAiB,IAAI,IAAI,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,IAAI,EAAE;oBACpF;gBACJ;YACJ;YACA,aAAa,UAAU;QAC3B;QACA,oBAAoB;QACpB,QAAQ,eAAe,CAAC,QAAQ,SAAS,YAAY;QACrD,uBAAuB;QACvB,IAAI,SAAS;YACT,QAAQ,YAAY,CAAC,QAAQ,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,IAAI;QACvE,OACK;YACD,QAAQ,YAAY,CAAC,QAAQ,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,IAAI;YACnE,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,gBAAgB,KAAK,GAAG,KAAK,IAAI,KAAK,GAAI;gBAC/F,IAAK,IAAI,EAAE,KAAK,MAAK,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,EAAG;oBACnF,OAAO,GAAG,CAAC,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,KAAK,GAAG;oBACrD,OAAO,GAAG,CAAC,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,KAAK,GAAG;oBACrD,OAAO,GAAG,CAAC,GAAG,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,KAAK;oBACrD,OAAO,GAAG,CAAC,GAAG,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY,KAAK;gBACzD;YACJ;QACJ;QACA,IAAI,QAAQ,IAAI,qLAAA,CAAA,UAAS;QACzB,MAAM,UAAU,CAAC;QACjB,MAAM,OAAO,CAAC;QACd,MAAM,SAAS,CAAC;QAChB,MAAM,YAAY,CAAC;QACnB,MAAM,SAAS,CAAC;QAChB,OAAO;IACX;IACA,QAAQ,YAAY,GAAG,SAAU,MAAM,EAAE,MAAM,EAAE,IAAI;QACjD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,MAAM,KAAK,EAAG;YACtC,IAAK,IAAI,EAAE,KAAK,MAAK,SAAS,GAAG,KAAK,SAAS,GAAG,IAAK;gBACnD,OAAO,GAAG,CAAC,GAAG,SAAS;gBACvB,OAAO,GAAG,CAAC,GAAG,SAAS;gBACvB,OAAO,GAAG,CAAC,SAAS,GAAG;gBACvB,OAAO,GAAG,CAAC,SAAS,GAAG;YAC3B;QACJ;QACA,OAAO,GAAG,CAAC,SAAS,MAAM,SAAS;QACnC,OAAO,GAAG,CAAC,SAAS,OAAO,GAAG,SAAS;QACvC,OAAO,GAAG,CAAC,SAAS,MAAM,SAAS,OAAO;QAC1C,OAAO,GAAG,CAAC,SAAS,MAAM,SAAS;QACnC,OAAO,GAAG,CAAC,SAAS,MAAM,SAAS,OAAO;QAC1C,OAAO,GAAG,CAAC,SAAS,MAAM,SAAS,OAAO;IAC9C;IACA,QAAQ,mBAAmB,GAAG,SAAU,OAAO,EAAE,MAAM,EAAE,kBAAkB;QACvE,IAAI,cAAc,IAAI,0KAAA,CAAA,UAAQ;QAC9B,IAAI,SAAS;YACT,YAAY,UAAU,CAAC,SAAS,GAAG;YACnC,YAAY,UAAU,CAAC,qBAAqB,GAAG;YAC/C,cAAc,QAAQ,kBAAkB,CAAC,aAAa,IAAI;QAC9D,OACK;YACD,YAAY,UAAU,CAAC,SAAS,GAAG;YACnC,YAAY,UAAU,CAAC,qBAAqB,GAAG;YAC/C,cAAc,QAAQ,kBAAkB,CAAC,aAAa,IAAI;QAC9D;QACA,OAAO;IACX;IACA,QAAQ,eAAe,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW;QACxE,IAAI,SAAS,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,YAAY;QAC/C,IAAI,SAAS;YACT,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAK;gBAChC,IAAI,SAAS,SAAS,IAAI;gBAC1B,IAAI,YAAY,GAAG,CAAC,IAAI;oBACpB,OAAO,GAAG,CAAC,QAAQ,SAAS;gBAChC;gBACA,IAAI,YAAY,GAAG,CAAC,IAAI,IAAI;oBACxB,OAAO,GAAG,CAAC,SAAS,GAAG;gBAC3B;gBACA,IAAI,YAAY,GAAG,CAAC,KAAK,IAAI;oBACzB,OAAO,GAAG,CAAC,QAAQ,SAAS;gBAChC;gBACA,IAAI,YAAY,GAAG,CAAC,KAAK,IAAI;oBACzB,OAAO,GAAG,CAAC,SAAS,GAAG;gBAC3B;YACJ;QACJ,OACK;YACD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,IAAI,IAAK;gBACjC,IAAI,SAAS,SAAS,IAAI,IAAI,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,GAAG;gBACvD,IAAI,YAAY,GAAG,CAAC,IAAI;oBACpB,OAAO,GAAG,CAAC,QAAQ,SAAS;gBAChC;gBACA,IAAI,YAAY,GAAG,CAAC,IAAI,KAAK;oBACzB,OAAO,GAAG,CAAC,SAAS,GAAG;gBAC3B;gBACA,IAAI,YAAY,GAAG,CAAC,KAAK,IAAI;oBACzB,OAAO,GAAG,CAAC,QAAQ,SAAS;gBAChC;gBACA,IAAI,YAAY,GAAG,CAAC,KAAK,IAAI;oBACzB,OAAO,GAAG,CAAC,SAAS,GAAG;gBAC3B;YACJ;QACJ;IACJ;IACA,QAAQ,kBAAkB,GAAG,SAAU,QAAQ,EAAE,SAAS,EAAE,QAAQ;QAChE,IAAI,KAAK;QACT,gFAAgF;QAChF,IAAI,qBAAqB,SAAS,OAAO,KAAK;QAC9C,IAAI,KAAK,IAAI,mMAAA,CAAA,UAAkB,CAAC,QAAQ,KAAK,CAAC;QAC9C,IAAI,aAAa,uKAAA,CAAA,UAAO,CAAC,aAAa,CAAC,WAAW;QAClD,IAAI,eAAe,QAAQ,WAAW,CAAC,UAAU,UAAU;QAC3D,GAAG,MAAM,CAAC,cAAc,aAAa;QACrC,IAAI,WAAW,YAAY;QAC3B,IAAI,cAAc,IAAI,0KAAA,CAAA,UAAQ;QAC9B,YAAY,UAAU,CAAC,GAAG;QAC1B,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,MAAM,IAAI,CAAC,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACxF,IAAI,cAAc,GAAG,KAAK,CAAC,OAAO;gBAClC,YAAY,UAAU,CAAC,aAAa;YACxC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,QAAQ,WAAW,GAAG,SAAU,WAAW,EAAE,QAAQ,EAAE,UAAU;QAC7D,IAAI,UAAU,IAAI,WAAW;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,YAAY,OAAO,KAAK,UAAU,IAAI,GAAG,IAAK;YAC1D,IAAI,QAAQ;YACZ,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,UAAU,IAAK;gBACvC,SAAS,YAAY,GAAG,CAAC,IAAI,WAAW,KAAM,KAAK,WAAW,IAAI,IAAK;YAC3E;YACA,OAAO,CAAC,EAAE,GAAG;QACjB;QACA,OAAO;IACX;IACA,QAAQ,KAAK,GAAG,SAAU,QAAQ;QAC9B,OAAQ;YACJ,KAAK;gBACD,OAAO,0LAAA,CAAA,UAAS,CAAC,WAAW;YAChC,KAAK;gBACD,OAAO,0LAAA,CAAA,UAAS,CAAC,YAAY;YACjC,KAAK;gBACD,OAAO,0LAAA,CAAA,UAAS,CAAC,YAAY;YACjC,KAAK;gBACD,OAAO,0LAAA,CAAA,UAAS,CAAC,aAAa;YAClC,KAAK;gBACD,OAAO,0LAAA,CAAA,UAAS,CAAC,aAAa;YAClC;gBACI,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,2BAA2B;QACtE;IACJ;IACA,QAAQ,SAAS,GAAG,SAAU,IAAI,EAAE,QAAQ;QACxC,IAAI,MAAM,IAAI,0KAAA,CAAA,UAAQ;QACtB,IAAI,IAAI,KAAK,OAAO;QACpB,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI;QAC7B,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,KAAK,SAAU;YAC1C,IAAI,OAAO;YACX,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,UAAU,IAAK;gBACvC,IAAI,IAAI,KAAK,KAAK,KAAK,GAAG,CAAC,IAAI,IAAI;oBAC/B,QAAQ,KAAM,WAAW,IAAI;gBACjC;YACJ;YACA,IAAI,CAAC,OAAO,IAAI,MAAM,MAAM;gBACxB,IAAI,UAAU,CAAC,OAAO,MAAM;gBAC5B;YACJ,OACK,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG;gBAC1B,IAAI,UAAU,CAAC,OAAO,GAAG;gBACzB;YACJ,OACK;gBACD,IAAI,UAAU,CAAC,MAAM;YACzB;QACJ;QACA,OAAO;IACX;IACA,QAAQ,gBAAgB,GAAG,SAAU,MAAM,EAAE,OAAO;QAChD,OAAO,CAAC,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;IAClD;IACA,QAAQ,kBAAkB,GAAG,IAAI,kDAAkD;IACnF,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,WAAW,GAAG;IACtB,QAAQ,mBAAmB,GAAG;IAC9B,QAAQ,SAAS,GAAG,WAAW,IAAI,CAAC;QAChC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAC/E;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KACvC;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/aztec/AztecWriter.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.aztec;\n// import com.google.zxing.BarcodeFormat;\nimport BarcodeFormat from '../BarcodeFormat';\n// import com.google.zxing.EncodeHintType;\nimport EncodeHintType from '../EncodeHintType';\n// import com.google.zxing.aztec.encoder.Encoder;\nimport Encoder from './encoder/Encoder';\n// import com.google.zxing.common.BitMatrix;\nimport BitMatrix from '../common/BitMatrix';\n// import java.nio.charset.Charset;\nimport Charset from '../util/Charset';\n// import java.nio.charset.StandardCharsets;\nimport StandardCharsets from '../util/StandardCharsets';\n// import java.util.Map;\nimport Integer from '../util/Integer';\nimport IllegalStateException from '../IllegalStateException';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport StringUtils from '../common/StringUtils';\n/**\n * Renders an Aztec code as a {@link BitMatrix}.\n */\nvar AztecWriter = /** @class */ (function () {\n    function AztecWriter() {\n    }\n    // @Override\n    AztecWriter.prototype.encode = function (contents, format, width, height) {\n        return this.encodeWithHints(contents, format, width, height, null);\n    };\n    // @Override\n    AztecWriter.prototype.encodeWithHints = function (contents, format, width, height, hints) {\n        var charset = StandardCharsets.ISO_8859_1;\n        var eccPercent = Encoder.DEFAULT_EC_PERCENT;\n        var layers = Encoder.DEFAULT_AZTEC_LAYERS;\n        if (hints != null) {\n            if (hints.has(EncodeHintType.CHARACTER_SET)) {\n                charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n            }\n            if (hints.has(EncodeHintType.ERROR_CORRECTION)) {\n                eccPercent = Integer.parseInt(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (hints.has(EncodeHintType.AZTEC_LAYERS)) {\n                layers = Integer.parseInt(hints.get(EncodeHintType.AZTEC_LAYERS).toString());\n            }\n        }\n        return AztecWriter.encodeLayers(contents, format, width, height, charset, eccPercent, layers);\n    };\n    AztecWriter.encodeLayers = function (contents, format, width, height, charset, eccPercent, layers) {\n        if (format !== BarcodeFormat.AZTEC) {\n            throw new IllegalArgumentException('Can only encode AZTEC, but got ' + format);\n        }\n        var aztec = Encoder.encode(StringUtils.getBytes(contents, charset), eccPercent, layers);\n        return AztecWriter.renderResult(aztec, width, height);\n    };\n    AztecWriter.renderResult = function (code, width, height) {\n        var input = code.getMatrix();\n        if (input == null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var outputWidth = Math.max(width, inputWidth);\n        var outputHeight = Math.max(height, inputHeight);\n        var multiple = Math.min(outputWidth / inputWidth, outputHeight / inputHeight);\n        var leftPadding = (outputWidth - (inputWidth * multiple)) / 2;\n        var topPadding = (outputHeight - (inputHeight * multiple)) / 2;\n        var output = new BitMatrix(outputWidth, outputHeight);\n        for (var inputY /*int*/ = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX /*int*/ = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY)) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    return AztecWriter;\n}());\nexport default AztecWriter;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA,kCAAkC;AAClC,yCAAyC;;;;AACzC;AACA,0CAA0C;AAC1C;AACA,iDAAiD;AACjD;AACA,4CAA4C;AAC5C;AACA,mCAAmC;AACnC;AACA,4CAA4C;AAC5C;AACA,wBAAwB;AACxB;AACA;AACA;AACA;;;;;;;;;;;AACA;;CAEC,GACD,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA,YAAY;IACZ,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;QACpE,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,QAAQ,OAAO,QAAQ;IACjE;IACA,YAAY;IACZ,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;QACpF,IAAI,UAAU,gLAAA,CAAA,UAAgB,CAAC,UAAU;QACzC,IAAI,aAAa,mLAAA,CAAA,UAAO,CAAC,kBAAkB;QAC3C,IAAI,SAAS,mLAAA,CAAA,UAAO,CAAC,oBAAoB;QACzC,IAAI,SAAS,MAAM;YACf,IAAI,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,aAAa,GAAG;gBACzC,UAAU,uKAAA,CAAA,UAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,aAAa,EAAE,QAAQ;YAC9E;YACA,IAAI,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB,GAAG;gBAC5C,aAAa,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB,EAAE,QAAQ;YACrF;YACA,IAAI,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,YAAY,GAAG;gBACxC,SAAS,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,YAAY,EAAE,QAAQ;YAC7E;QACJ;QACA,OAAO,YAAY,YAAY,CAAC,UAAU,QAAQ,OAAO,QAAQ,SAAS,YAAY;IAC1F;IACA,YAAY,YAAY,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM;QAC7F,IAAI,WAAW,qKAAA,CAAA,UAAa,CAAC,KAAK,EAAE;YAChC,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,oCAAoC;QAC3E;QACA,IAAI,QAAQ,mLAAA,CAAA,UAAO,CAAC,MAAM,CAAC,6KAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,UAAU,UAAU,YAAY;QAChF,OAAO,YAAY,YAAY,CAAC,OAAO,OAAO;IAClD;IACA,YAAY,YAAY,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,MAAM;QACpD,IAAI,QAAQ,KAAK,SAAS;QAC1B,IAAI,SAAS,MAAM;YACf,MAAM,IAAI,6KAAA,CAAA,UAAqB;QACnC;QACA,IAAI,aAAa,MAAM,QAAQ;QAC/B,IAAI,cAAc,MAAM,SAAS;QACjC,IAAI,cAAc,KAAK,GAAG,CAAC,OAAO;QAClC,IAAI,eAAe,KAAK,GAAG,CAAC,QAAQ;QACpC,IAAI,WAAW,KAAK,GAAG,CAAC,cAAc,YAAY,eAAe;QACjE,IAAI,cAAc,CAAC,cAAe,aAAa,QAAS,IAAI;QAC5D,IAAI,aAAa,CAAC,eAAgB,cAAc,QAAS,IAAI;QAC7D,IAAI,SAAS,IAAI,2KAAA,CAAA,UAAS,CAAC,aAAa;QACxC,IAAK,IAAI,OAAO,KAAK,MAAK,GAAG,UAAU,YAAY,SAAS,aAAa,UAAU,WAAW,SAAU;YACpG,gDAAgD;YAChD,IAAK,IAAI,OAAO,KAAK,MAAK,GAAG,UAAU,aAAa,SAAS,YAAY,UAAU,WAAW,SAAU;gBACpG,IAAI,MAAM,GAAG,CAAC,QAAQ,SAAS;oBAC3B,OAAO,SAAS,CAAC,SAAS,SAAS,UAAU;gBACjD;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}]}