module.exports = {

"[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/Version.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ECB": ()=>ECB,
    "ECBlocks": ()=>ECBlocks,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will
 * use blocks of differing sizes within one version, so, this encapsulates the parameters for
 * each set of blocks. It also holds the number of error-correction codewords per block since it
 * will be the same across all blocks within one version.</p>
 */ var ECBlocks = function() {
    function ECBlocks(ecCodewords, ecBlocks1, ecBlocks2) {
        this.ecCodewords = ecCodewords;
        this.ecBlocks = [
            ecBlocks1
        ];
        ecBlocks2 && this.ecBlocks.push(ecBlocks2);
    }
    ECBlocks.prototype.getECCodewords = function() {
        return this.ecCodewords;
    };
    ECBlocks.prototype.getECBlocks = function() {
        return this.ecBlocks;
    };
    return ECBlocks;
}();
;
/**
 * <p>Encapsulates the parameters for one error-correction block in one symbol version.
 * This includes the number of data codewords, and the number of times a block with these
 * parameters is used consecutively in the Data Matrix code version's format.</p>
 */ var ECB = function() {
    function ECB(count, dataCodewords) {
        this.count = count;
        this.dataCodewords = dataCodewords;
    }
    ECB.prototype.getCount = function() {
        return this.count;
    };
    ECB.prototype.getDataCodewords = function() {
        return this.dataCodewords;
    };
    return ECB;
}();
;
/**
 * The Version object encapsulates attributes about a particular
 * size Data Matrix Code.
 *
 * <AUTHOR> (Brian Brown)
 */ var Version = function() {
    function Version(versionNumber, symbolSizeRows, symbolSizeColumns, dataRegionSizeRows, dataRegionSizeColumns, ecBlocks) {
        var e_1, _a;
        this.versionNumber = versionNumber;
        this.symbolSizeRows = symbolSizeRows;
        this.symbolSizeColumns = symbolSizeColumns;
        this.dataRegionSizeRows = dataRegionSizeRows;
        this.dataRegionSizeColumns = dataRegionSizeColumns;
        this.ecBlocks = ecBlocks;
        // Calculate the total number of codewords
        var total = 0;
        var ecCodewords = ecBlocks.getECCodewords();
        var ecbArray = ecBlocks.getECBlocks();
        try {
            for(var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()){
                var ecBlock = ecbArray_1_1.value;
                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        this.totalCodewords = total;
    }
    Version.prototype.getVersionNumber = function() {
        return this.versionNumber;
    };
    Version.prototype.getSymbolSizeRows = function() {
        return this.symbolSizeRows;
    };
    Version.prototype.getSymbolSizeColumns = function() {
        return this.symbolSizeColumns;
    };
    Version.prototype.getDataRegionSizeRows = function() {
        return this.dataRegionSizeRows;
    };
    Version.prototype.getDataRegionSizeColumns = function() {
        return this.dataRegionSizeColumns;
    };
    Version.prototype.getTotalCodewords = function() {
        return this.totalCodewords;
    };
    Version.prototype.getECBlocks = function() {
        return this.ecBlocks;
    };
    /**
     * <p>Deduces version information from Data Matrix dimensions.</p>
     *
     * @param numRows Number of rows in modules
     * @param numColumns Number of columns in modules
     * @return Version for a Data Matrix Code of those dimensions
     * @throws FormatException if dimensions do correspond to a valid Data Matrix size
     */ Version.getVersionForDimensions = function(numRows, numColumns) {
        var e_2, _a;
        if ((numRows & 0x01) !== 0 || (numColumns & 0x01) !== 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        try {
            for(var _b = __values(Version.VERSIONS), _c = _b.next(); !_c.done; _c = _b.next()){
                var version = _c.value;
                if (version.symbolSizeRows === numRows && version.symbolSizeColumns === numColumns) {
                    return version;
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    //  @Override
    Version.prototype.toString = function() {
        return '' + this.versionNumber;
    };
    /**
     * See ISO 16022:2006 5.5.1 Table 7
     */ Version.buildVersions = function() {
        return [
            new Version(1, 10, 10, 8, 8, new ECBlocks(5, new ECB(1, 3))),
            new Version(2, 12, 12, 10, 10, new ECBlocks(7, new ECB(1, 5))),
            new Version(3, 14, 14, 12, 12, new ECBlocks(10, new ECB(1, 8))),
            new Version(4, 16, 16, 14, 14, new ECBlocks(12, new ECB(1, 12))),
            new Version(5, 18, 18, 16, 16, new ECBlocks(14, new ECB(1, 18))),
            new Version(6, 20, 20, 18, 18, new ECBlocks(18, new ECB(1, 22))),
            new Version(7, 22, 22, 20, 20, new ECBlocks(20, new ECB(1, 30))),
            new Version(8, 24, 24, 22, 22, new ECBlocks(24, new ECB(1, 36))),
            new Version(9, 26, 26, 24, 24, new ECBlocks(28, new ECB(1, 44))),
            new Version(10, 32, 32, 14, 14, new ECBlocks(36, new ECB(1, 62))),
            new Version(11, 36, 36, 16, 16, new ECBlocks(42, new ECB(1, 86))),
            new Version(12, 40, 40, 18, 18, new ECBlocks(48, new ECB(1, 114))),
            new Version(13, 44, 44, 20, 20, new ECBlocks(56, new ECB(1, 144))),
            new Version(14, 48, 48, 22, 22, new ECBlocks(68, new ECB(1, 174))),
            new Version(15, 52, 52, 24, 24, new ECBlocks(42, new ECB(2, 102))),
            new Version(16, 64, 64, 14, 14, new ECBlocks(56, new ECB(2, 140))),
            new Version(17, 72, 72, 16, 16, new ECBlocks(36, new ECB(4, 92))),
            new Version(18, 80, 80, 18, 18, new ECBlocks(48, new ECB(4, 114))),
            new Version(19, 88, 88, 20, 20, new ECBlocks(56, new ECB(4, 144))),
            new Version(20, 96, 96, 22, 22, new ECBlocks(68, new ECB(4, 174))),
            new Version(21, 104, 104, 24, 24, new ECBlocks(56, new ECB(6, 136))),
            new Version(22, 120, 120, 18, 18, new ECBlocks(68, new ECB(6, 175))),
            new Version(23, 132, 132, 20, 20, new ECBlocks(62, new ECB(8, 163))),
            new Version(24, 144, 144, 22, 22, new ECBlocks(62, new ECB(8, 156), new ECB(2, 155))),
            new Version(25, 8, 18, 6, 16, new ECBlocks(7, new ECB(1, 5))),
            new Version(26, 8, 32, 6, 14, new ECBlocks(11, new ECB(1, 10))),
            new Version(27, 12, 26, 10, 24, new ECBlocks(14, new ECB(1, 16))),
            new Version(28, 12, 36, 10, 16, new ECBlocks(18, new ECB(1, 22))),
            new Version(29, 16, 36, 14, 16, new ECBlocks(24, new ECB(1, 32))),
            new Version(30, 16, 48, 14, 22, new ECBlocks(28, new ECB(1, 49)))
        ];
    };
    Version.VERSIONS = Version.buildVersions();
    return Version;
}();
const __TURBOPACK__default__export__ = Version;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/BitMatrixParser.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$Version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/Version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
;
;
;
;
/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <AUTHOR> (Brian Brown)
 */ var BitMatrixParser = function() {
    /**
     * @param bitMatrix {@link BitMatrix} to parse
     * @throws FormatException if dimension is < 8 or > 144 or not 0 mod 2
     */ function BitMatrixParser(bitMatrix) {
        var dimension = bitMatrix.getHeight();
        if (dimension < 8 || dimension > 144 || (dimension & 0x01) !== 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        this.version = BitMatrixParser.readVersion(bitMatrix);
        this.mappingBitMatrix = this.extractDataRegion(bitMatrix);
        this.readMappingMatrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.mappingBitMatrix.getWidth(), this.mappingBitMatrix.getHeight());
    }
    BitMatrixParser.prototype.getVersion = function() {
        return this.version;
    };
    /**
     * <p>Creates the version object based on the dimension of the original bit matrix from
     * the datamatrix code.</p>
     *
     * <p>See ISO 16022:2006 Table 7 - ECC 200 symbol attributes</p>
     *
     * @param bitMatrix Original {@link BitMatrix} including alignment patterns
     * @return {@link Version} encapsulating the Data Matrix Code's "version"
     * @throws FormatException if the dimensions of the mapping matrix are not valid
     * Data Matrix dimensions.
     */ BitMatrixParser.readVersion = function(bitMatrix) {
        var numRows = bitMatrix.getHeight();
        var numColumns = bitMatrix.getWidth();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$Version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getVersionForDimensions(numRows, numColumns);
    };
    /**
     * <p>Reads the bits in the {@link BitMatrix} representing the mapping matrix (No alignment patterns)
     * in the correct order in order to reconstitute the codewords bytes contained within the
     * Data Matrix Code.</p>
     *
     * @return bytes encoded within the Data Matrix Code
     * @throws FormatException if the exact number of bytes expected is not read
     */ BitMatrixParser.prototype.readCodewords = function() {
        var result = new Int8Array(this.version.getTotalCodewords());
        var resultOffset = 0;
        var row = 4;
        var column = 0;
        var numRows = this.mappingBitMatrix.getHeight();
        var numColumns = this.mappingBitMatrix.getWidth();
        var corner1Read = false;
        var corner2Read = false;
        var corner3Read = false;
        var corner4Read = false;
        // Read all of the codewords
        do {
            // Check the four corner cases
            if (row === numRows && column === 0 && !corner1Read) {
                result[resultOffset++] = this.readCorner1(numRows, numColumns) & 0xff;
                row -= 2;
                column += 2;
                corner1Read = true;
            } else if (row === numRows - 2 && column === 0 && (numColumns & 0x03) !== 0 && !corner2Read) {
                result[resultOffset++] = this.readCorner2(numRows, numColumns) & 0xff;
                row -= 2;
                column += 2;
                corner2Read = true;
            } else if (row === numRows + 4 && column === 2 && (numColumns & 0x07) === 0 && !corner3Read) {
                result[resultOffset++] = this.readCorner3(numRows, numColumns) & 0xff;
                row -= 2;
                column += 2;
                corner3Read = true;
            } else if (row === numRows - 2 && column === 0 && (numColumns & 0x07) === 4 && !corner4Read) {
                result[resultOffset++] = this.readCorner4(numRows, numColumns) & 0xff;
                row -= 2;
                column += 2;
                corner4Read = true;
            } else {
                // Sweep upward diagonally to the right
                do {
                    if (row < numRows && column >= 0 && !this.readMappingMatrix.get(column, row)) {
                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;
                    }
                    row -= 2;
                    column += 2;
                }while (row >= 0 && column < numColumns)
                row += 1;
                column += 3;
                // Sweep downward diagonally to the left
                do {
                    if (row >= 0 && column < numColumns && !this.readMappingMatrix.get(column, row)) {
                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;
                    }
                    row += 2;
                    column -= 2;
                }while (row < numRows && column >= 0)
                row += 3;
                column += 1;
            }
        }while (row < numRows || column < numColumns)
        if (resultOffset !== this.version.getTotalCodewords()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return result;
    };
    /**
     * <p>Reads a bit of the mapping matrix accounting for boundary wrapping.</p>
     *
     * @param row Row to read in the mapping matrix
     * @param column Column to read in the mapping matrix
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return value of the given bit in the mapping matrix
     */ BitMatrixParser.prototype.readModule = function(row, column, numRows, numColumns) {
        // Adjust the row and column indices based on boundary wrapping
        if (row < 0) {
            row += numRows;
            column += 4 - (numRows + 4 & 0x07);
        }
        if (column < 0) {
            column += numColumns;
            row += 4 - (numColumns + 4 & 0x07);
        }
        this.readMappingMatrix.set(column, row);
        return this.mappingBitMatrix.get(column, row);
    };
    /**
     * <p>Reads the 8 bits of the standard Utah-shaped pattern.</p>
     *
     * <p>See ISO 16022:2006, 5.8.1 Figure 6</p>
     *
     * @param row Current row in the mapping matrix, anchored at the 8th bit (LSB) of the pattern
     * @param column Current column in the mapping matrix, anchored at the 8th bit (LSB) of the pattern
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return byte from the utah shape
     */ BitMatrixParser.prototype.readUtah = function(row, column, numRows, numColumns) {
        var currentByte = 0;
        if (this.readModule(row - 2, column - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row - 2, column - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row - 1, column - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row - 1, column - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row - 1, column, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row, column - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row, column - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(row, column, numRows, numColumns)) {
            currentByte |= 1;
        }
        return currentByte;
    };
    /**
     * <p>Reads the 8 bits of the special corner condition 1.</p>
     *
     * <p>See ISO 16022:2006, Figure F.3</p>
     *
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return byte from the Corner condition 1
     */ BitMatrixParser.prototype.readCorner1 = function(numRows, numColumns) {
        var currentByte = 0;
        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 1, 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 1, 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        return currentByte;
    };
    /**
     * <p>Reads the 8 bits of the special corner condition 2.</p>
     *
     * <p>See ISO 16022:2006, Figure F.4</p>
     *
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return byte from the Corner condition 2
     */ BitMatrixParser.prototype.readCorner2 = function(numRows, numColumns) {
        var currentByte = 0;
        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 4, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        return currentByte;
    };
    /**
     * <p>Reads the 8 bits of the special corner condition 3.</p>
     *
     * <p>See ISO 16022:2006, Figure F.5</p>
     *
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return byte from the Corner condition 3
     */ BitMatrixParser.prototype.readCorner3 = function(numRows, numColumns) {
        var currentByte = 0;
        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 1, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 3, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        return currentByte;
    };
    /**
     * <p>Reads the 8 bits of the special corner condition 4.</p>
     *
     * <p>See ISO 16022:2006, Figure F.6</p>
     *
     * @param numRows Number of rows in the mapping matrix
     * @param numColumns Number of columns in the mapping matrix
     * @return byte from the Corner condition 4
     */ BitMatrixParser.prototype.readCorner4 = function(numRows, numColumns) {
        var currentByte = 0;
        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        currentByte <<= 1;
        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {
            currentByte |= 1;
        }
        return currentByte;
    };
    /**
     * <p>Extracts the data region from a {@link BitMatrix} that contains
     * alignment patterns.</p>
     *
     * @param bitMatrix Original {@link BitMatrix} with alignment patterns
     * @return BitMatrix that has the alignment patterns removed
     */ BitMatrixParser.prototype.extractDataRegion = function(bitMatrix) {
        var symbolSizeRows = this.version.getSymbolSizeRows();
        var symbolSizeColumns = this.version.getSymbolSizeColumns();
        if (bitMatrix.getHeight() !== symbolSizeRows) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Dimension of bitMatrix must match the version size');
        }
        var dataRegionSizeRows = this.version.getDataRegionSizeRows();
        var dataRegionSizeColumns = this.version.getDataRegionSizeColumns();
        var numDataRegionsRow = symbolSizeRows / dataRegionSizeRows | 0;
        var numDataRegionsColumn = symbolSizeColumns / dataRegionSizeColumns | 0;
        var sizeDataRegionRow = numDataRegionsRow * dataRegionSizeRows;
        var sizeDataRegionColumn = numDataRegionsColumn * dataRegionSizeColumns;
        var bitMatrixWithoutAlignment = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](sizeDataRegionColumn, sizeDataRegionRow);
        for(var dataRegionRow = 0; dataRegionRow < numDataRegionsRow; ++dataRegionRow){
            var dataRegionRowOffset = dataRegionRow * dataRegionSizeRows;
            for(var dataRegionColumn = 0; dataRegionColumn < numDataRegionsColumn; ++dataRegionColumn){
                var dataRegionColumnOffset = dataRegionColumn * dataRegionSizeColumns;
                for(var i = 0; i < dataRegionSizeRows; ++i){
                    var readRowOffset = dataRegionRow * (dataRegionSizeRows + 2) + 1 + i;
                    var writeRowOffset = dataRegionRowOffset + i;
                    for(var j = 0; j < dataRegionSizeColumns; ++j){
                        var readColumnOffset = dataRegionColumn * (dataRegionSizeColumns + 2) + 1 + j;
                        if (bitMatrix.get(readColumnOffset, readRowOffset)) {
                            var writeColumnOffset = dataRegionColumnOffset + j;
                            bitMatrixWithoutAlignment.set(writeColumnOffset, writeRowOffset);
                        }
                    }
                }
            }
        }
        return bitMatrixWithoutAlignment;
    };
    return BitMatrixParser;
}();
const __TURBOPACK__default__export__ = BitMatrixParser;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DataBlock.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>Encapsulates a block of data within a Data Matrix Code. Data Matrix Codes may split their data into
 * multiple blocks, each of which is a unit of data and error-correction codewords. Each
 * is represented by an instance of this class.</p>
 *
 * <AUTHOR> (Brian Brown)
 */ var DataBlock = function() {
    function DataBlock(numDataCodewords, codewords) {
        this.numDataCodewords = numDataCodewords;
        this.codewords = codewords;
    }
    /**
     * <p>When Data Matrix Codes use multiple data blocks, they actually interleave the bytes of each of them.
     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This
     * method will separate the data into original blocks.</p>
     *
     * @param rawCodewords bytes as read directly from the Data Matrix Code
     * @param version version of the Data Matrix Code
     * @return DataBlocks containing original bytes, "de-interleaved" from representation in the
     *         Data Matrix Code
     */ DataBlock.getDataBlocks = function(rawCodewords, version) {
        var e_1, _a, e_2, _b;
        // Figure out the number and size of data blocks used by this version
        var ecBlocks = version.getECBlocks();
        // First count the total number of data blocks
        var totalBlocks = 0;
        var ecBlockArray = ecBlocks.getECBlocks();
        try {
            for(var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()){
                var ecBlock = ecBlockArray_1_1.value;
                totalBlocks += ecBlock.getCount();
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        // Now establish DataBlocks of the appropriate size and number of data codewords
        var result = new Array(totalBlocks);
        var numResultBlocks = 0;
        try {
            for(var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()){
                var ecBlock = ecBlockArray_2_1.value;
                for(var i = 0; i < ecBlock.getCount(); i++){
                    var numDataCodewords = ecBlock.getDataCodewords();
                    var numBlockCodewords = ecBlocks.getECCodewords() + numDataCodewords;
                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        // All blocks have the same amount of data, except that the last n
        // (where n may be 0) have 1 less byte. Figure out where these start.
        // TODO(bbrown): There is only one case where there is a difference for Data Matrix for size 144
        var longerBlocksTotalCodewords = result[0].codewords.length;
        // int shorterBlocksTotalCodewords = longerBlocksTotalCodewords - 1;
        var longerBlocksNumDataCodewords = longerBlocksTotalCodewords - ecBlocks.getECCodewords();
        var shorterBlocksNumDataCodewords = longerBlocksNumDataCodewords - 1;
        // The last elements of result may be 1 element shorter for 144 matrix
        // first fill out as many elements as all of them have minus 1
        var rawCodewordsOffset = 0;
        for(var i = 0; i < shorterBlocksNumDataCodewords; i++){
            for(var j = 0; j < numResultBlocks; j++){
                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];
            }
        }
        // Fill out the last data block in the longer ones
        var specialVersion = version.getVersionNumber() === 24;
        var numLongerBlocks = specialVersion ? 8 : numResultBlocks;
        for(var j = 0; j < numLongerBlocks; j++){
            result[j].codewords[longerBlocksNumDataCodewords - 1] = rawCodewords[rawCodewordsOffset++];
        }
        // Now add in error correction blocks
        var max = result[0].codewords.length;
        for(var i = longerBlocksNumDataCodewords; i < max; i++){
            for(var j = 0; j < numResultBlocks; j++){
                var jOffset = specialVersion ? (j + 8) % numResultBlocks : j;
                var iOffset = specialVersion && jOffset > 7 ? i - 1 : i;
                result[jOffset].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];
            }
        }
        if (rawCodewordsOffset !== rawCodewords.length) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return result;
    };
    DataBlock.prototype.getNumDataCodewords = function() {
        return this.numDataCodewords;
    };
    DataBlock.prototype.getCodewords = function() {
        return this.codewords;
    };
    return DataBlock;
}();
const __TURBOPACK__default__export__ = DataBlock;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var Mode;
(function(Mode) {
    Mode[Mode["PAD_ENCODE"] = 0] = "PAD_ENCODE";
    Mode[Mode["ASCII_ENCODE"] = 1] = "ASCII_ENCODE";
    Mode[Mode["C40_ENCODE"] = 2] = "C40_ENCODE";
    Mode[Mode["TEXT_ENCODE"] = 3] = "TEXT_ENCODE";
    Mode[Mode["ANSIX12_ENCODE"] = 4] = "ANSIX12_ENCODE";
    Mode[Mode["EDIFACT_ENCODE"] = 5] = "EDIFACT_ENCODE";
    Mode[Mode["BASE256_ENCODE"] = 6] = "BASE256_ENCODE";
})(Mode || (Mode = {}));
/**
 * <p>Data Matrix Codes can encode text as bits in one of several modes, and can use multiple modes
 * in one Data Matrix Code. This class decodes the bits back into text.</p>
 *
 * <p>See ISO 16022:2006, 5.2.1 - *******</p>
 *
 * <AUTHOR> (Brian Brown)
 * <AUTHOR> Owen
 */ var DecodedBitStreamParser = function() {
    function DecodedBitStreamParser() {}
    DecodedBitStreamParser.decode = function(bytes) {
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](bytes);
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        var resultTrailer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        var byteSegments = new Array();
        var mode = Mode.ASCII_ENCODE;
        do {
            if (mode === Mode.ASCII_ENCODE) {
                mode = this.decodeAsciiSegment(bits, result, resultTrailer);
            } else {
                switch(mode){
                    case Mode.C40_ENCODE:
                        this.decodeC40Segment(bits, result);
                        break;
                    case Mode.TEXT_ENCODE:
                        this.decodeTextSegment(bits, result);
                        break;
                    case Mode.ANSIX12_ENCODE:
                        this.decodeAnsiX12Segment(bits, result);
                        break;
                    case Mode.EDIFACT_ENCODE:
                        this.decodeEdifactSegment(bits, result);
                        break;
                    case Mode.BASE256_ENCODE:
                        this.decodeBase256Segment(bits, result, byteSegments);
                        break;
                    default:
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                mode = Mode.ASCII_ENCODE;
            }
        }while (mode !== Mode.PAD_ENCODE && bits.available() > 0)
        if (resultTrailer.length() > 0) {
            result.append(resultTrailer.toString());
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, null);
    };
    /**
     * See ISO 16022:2006, 5.2.3 and Annex C, Table C.2
     */ DecodedBitStreamParser.decodeAsciiSegment = function(bits, result, resultTrailer) {
        var upperShift = false;
        do {
            var oneByte = bits.readBits(8);
            if (oneByte === 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            } else if (oneByte <= 128) {
                if (upperShift) {
                    oneByte += 128;
                // upperShift = false;
                }
                result.append(String.fromCharCode(oneByte - 1));
                return Mode.ASCII_ENCODE;
            } else if (oneByte === 129) {
                return Mode.PAD_ENCODE;
            } else if (oneByte <= 229) {
                var value = oneByte - 130;
                if (value < 10) {
                    result.append('0');
                }
                result.append('' + value);
            } else {
                switch(oneByte){
                    case 230:
                        return Mode.C40_ENCODE;
                    case 231:
                        return Mode.BASE256_ENCODE;
                    case 232:
                        result.append(String.fromCharCode(29)); // translate as ASCII 29
                        break;
                    case 233:
                    case 234:
                        break;
                    case 235:
                        upperShift = true;
                        break;
                    case 236:
                        result.append('[)>\u001E05\u001D');
                        resultTrailer.insert(0, '\u001E\u0004');
                        break;
                    case 237:
                        result.append('[)>\u001E06\u001D');
                        resultTrailer.insert(0, '\u001E\u0004');
                        break;
                    case 238:
                        return Mode.ANSIX12_ENCODE;
                    case 239:
                        return Mode.TEXT_ENCODE;
                    case 240:
                        return Mode.EDIFACT_ENCODE;
                    case 241:
                        break;
                    default:
                        // Not to be used in ASCII encodation
                        // but work around encoders that end with 254, latch back to ASCII
                        if (oneByte !== 254 || bits.available() !== 0) {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                }
            }
        }while (bits.available() > 0)
        return Mode.ASCII_ENCODE;
    };
    /**
     * See ISO 16022:2006, 5.2.5 and Annex C, Table C.1
     */ DecodedBitStreamParser.decodeC40Segment = function(bits, result) {
        // Three C40 values are encoded in a 16-bit value as
        // (1600 * C1) + (40 * C2) + C3 + 1
        // TODO(bbrown): The Upper Shift with C40 doesn't work in the 4 value scenario all the time
        var upperShift = false;
        var cValues = [];
        var shift = 0;
        do {
            // If there is only one byte left then it will be encoded as ASCII
            if (bits.available() === 8) {
                return;
            }
            var firstByte = bits.readBits(8);
            if (firstByte === 254) {
                return;
            }
            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);
            for(var i = 0; i < 3; i++){
                var cValue = cValues[i];
                switch(shift){
                    case 0:
                        if (cValue < 3) {
                            shift = cValue + 1;
                        } else if (cValue < this.C40_BASIC_SET_CHARS.length) {
                            var c40char = this.C40_BASIC_SET_CHARS[cValue];
                            if (upperShift) {
                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));
                                upperShift = false;
                            } else {
                                result.append(c40char);
                            }
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case 1:
                        if (upperShift) {
                            result.append(String.fromCharCode(cValue + 128));
                            upperShift = false;
                        } else {
                            result.append(String.fromCharCode(cValue));
                        }
                        shift = 0;
                        break;
                    case 2:
                        if (cValue < this.C40_SHIFT2_SET_CHARS.length) {
                            var c40char = this.C40_SHIFT2_SET_CHARS[cValue];
                            if (upperShift) {
                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));
                                upperShift = false;
                            } else {
                                result.append(c40char);
                            }
                        } else {
                            switch(cValue){
                                case 27:
                                    result.append(String.fromCharCode(29)); // translate as ASCII 29
                                    break;
                                case 30:
                                    upperShift = true;
                                    break;
                                default:
                                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                            }
                        }
                        shift = 0;
                        break;
                    case 3:
                        if (upperShift) {
                            result.append(String.fromCharCode(cValue + 224));
                            upperShift = false;
                        } else {
                            result.append(String.fromCharCode(cValue + 96));
                        }
                        shift = 0;
                        break;
                    default:
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
            }
        }while (bits.available() > 0)
    };
    /**
     * See ISO 16022:2006, 5.2.6 and Annex C, Table C.2
     */ DecodedBitStreamParser.decodeTextSegment = function(bits, result) {
        // Three Text values are encoded in a 16-bit value as
        // (1600 * C1) + (40 * C2) + C3 + 1
        // TODO(bbrown): The Upper Shift with Text doesn't work in the 4 value scenario all the time
        var upperShift = false;
        var cValues = [];
        var shift = 0;
        do {
            // If there is only one byte left then it will be encoded as ASCII
            if (bits.available() === 8) {
                return;
            }
            var firstByte = bits.readBits(8);
            if (firstByte === 254) {
                return;
            }
            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);
            for(var i = 0; i < 3; i++){
                var cValue = cValues[i];
                switch(shift){
                    case 0:
                        if (cValue < 3) {
                            shift = cValue + 1;
                        } else if (cValue < this.TEXT_BASIC_SET_CHARS.length) {
                            var textChar = this.TEXT_BASIC_SET_CHARS[cValue];
                            if (upperShift) {
                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));
                                upperShift = false;
                            } else {
                                result.append(textChar);
                            }
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case 1:
                        if (upperShift) {
                            result.append(String.fromCharCode(cValue + 128));
                            upperShift = false;
                        } else {
                            result.append(String.fromCharCode(cValue));
                        }
                        shift = 0;
                        break;
                    case 2:
                        // Shift 2 for Text is the same encoding as C40
                        if (cValue < this.TEXT_SHIFT2_SET_CHARS.length) {
                            var textChar = this.TEXT_SHIFT2_SET_CHARS[cValue];
                            if (upperShift) {
                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));
                                upperShift = false;
                            } else {
                                result.append(textChar);
                            }
                        } else {
                            switch(cValue){
                                case 27:
                                    result.append(String.fromCharCode(29)); // translate as ASCII 29
                                    break;
                                case 30:
                                    upperShift = true;
                                    break;
                                default:
                                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                            }
                        }
                        shift = 0;
                        break;
                    case 3:
                        if (cValue < this.TEXT_SHIFT3_SET_CHARS.length) {
                            var textChar = this.TEXT_SHIFT3_SET_CHARS[cValue];
                            if (upperShift) {
                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));
                                upperShift = false;
                            } else {
                                result.append(textChar);
                            }
                            shift = 0;
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    default:
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
            }
        }while (bits.available() > 0)
    };
    /**
     * See ISO 16022:2006, 5.2.7
     */ DecodedBitStreamParser.decodeAnsiX12Segment = function(bits, result) {
        // Three ANSI X12 values are encoded in a 16-bit value as
        // (1600 * C1) + (40 * C2) + C3 + 1
        var cValues = [];
        do {
            // If there is only one byte left then it will be encoded as ASCII
            if (bits.available() === 8) {
                return;
            }
            var firstByte = bits.readBits(8);
            if (firstByte === 254) {
                return;
            }
            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);
            for(var i = 0; i < 3; i++){
                var cValue = cValues[i];
                switch(cValue){
                    case 0:
                        result.append('\r');
                        break;
                    case 1:
                        result.append('*');
                        break;
                    case 2:
                        result.append('>');
                        break;
                    case 3:
                        result.append(' ');
                        break;
                    default:
                        if (cValue < 14) {
                            result.append(String.fromCharCode(cValue + 44));
                        } else if (cValue < 40) {
                            result.append(String.fromCharCode(cValue + 51));
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                }
            }
        }while (bits.available() > 0)
    };
    DecodedBitStreamParser.parseTwoBytes = function(firstByte, secondByte, result) {
        var fullBitValue = (firstByte << 8) + secondByte - 1;
        var temp = Math.floor(fullBitValue / 1600);
        result[0] = temp;
        fullBitValue -= temp * 1600;
        temp = Math.floor(fullBitValue / 40);
        result[1] = temp;
        result[2] = fullBitValue - temp * 40;
    };
    /**
     * See ISO 16022:2006, 5.2.8 and Annex C Table C.3
     */ DecodedBitStreamParser.decodeEdifactSegment = function(bits, result) {
        do {
            // If there is only two or less bytes left then it will be encoded as ASCII
            if (bits.available() <= 16) {
                return;
            }
            for(var i = 0; i < 4; i++){
                var edifactValue = bits.readBits(6);
                // Check for the unlatch character
                if (edifactValue === 0x1F) {
                    // Read rest of byte, which should be 0, and stop
                    var bitsLeft = 8 - bits.getBitOffset();
                    if (bitsLeft !== 8) {
                        bits.readBits(bitsLeft);
                    }
                    return;
                }
                if ((edifactValue & 0x20) === 0) {
                    edifactValue |= 0x40; // Add a leading 01 to the 6 bit binary value
                }
                result.append(String.fromCharCode(edifactValue));
            }
        }while (bits.available() > 0)
    };
    /**
     * See ISO 16022:2006, 5.2.9 and Annex B, B.2
     */ DecodedBitStreamParser.decodeBase256Segment = function(bits, result, byteSegments) {
        // Figure out how long the Base 256 Segment is.
        var codewordPosition = 1 + bits.getByteOffset(); // position is 1-indexed
        var d1 = this.unrandomize255State(bits.readBits(8), codewordPosition++);
        var count;
        if (d1 === 0) {
            count = bits.available() / 8 | 0;
        } else if (d1 < 250) {
            count = d1;
        } else {
            count = 250 * (d1 - 249) + this.unrandomize255State(bits.readBits(8), codewordPosition++);
        }
        // We're seeing NegativeArraySizeException errors from users.
        if (count < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var bytes = new Uint8Array(count);
        for(var i = 0; i < count; i++){
            // Have seen this particular error in the wild, such as at
            // http://www.bcgen.com/demo/IDAutomationStreamingDataMatrix.aspx?MODE=3&D=Fred&PFMT=3&PT=F&X=0.3&O=0&LM=0.2
            if (bits.available() < 8) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            bytes[i] = this.unrandomize255State(bits.readBits(8), codewordPosition++);
        }
        byteSegments.push(bytes);
        try {
            result.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decode(bytes, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ISO88591));
        } catch (uee) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Platform does not support required encoding: ' + uee.message);
        }
    };
    /**
     * See ISO 16022:2006, Annex B, B.2
     */ DecodedBitStreamParser.unrandomize255State = function(randomizedBase256Codeword, base256CodewordPosition) {
        var pseudoRandomNumber = 149 * base256CodewordPosition % 255 + 1;
        var tempVariable = randomizedBase256Codeword - pseudoRandomNumber;
        return tempVariable >= 0 ? tempVariable : tempVariable + 256;
    };
    /**
     * See ISO 16022:2006, Annex C Table C.1
     * The C40 Basic Character Set (*'s used for placeholders for the shift values)
     */ DecodedBitStreamParser.C40_BASIC_SET_CHARS = [
        '*',
        '*',
        '*',
        ' ',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
    ];
    DecodedBitStreamParser.C40_SHIFT2_SET_CHARS = [
        '!',
        '"',
        '#',
        '$',
        '%',
        '&',
        '\'',
        '(',
        ')',
        '*',
        '+',
        ',',
        '-',
        '.',
        '/',
        ':',
        ';',
        '<',
        '=',
        '>',
        '?',
        '@',
        '[',
        '\\',
        ']',
        '^',
        '_'
    ];
    /**
     * See ISO 16022:2006, Annex C Table C.2
     * The Text Basic Character Set (*'s used for placeholders for the shift values)
     */ DecodedBitStreamParser.TEXT_BASIC_SET_CHARS = [
        '*',
        '*',
        '*',
        ' ',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
    ];
    // Shift 2 for Text is the same encoding as C40
    DecodedBitStreamParser.TEXT_SHIFT2_SET_CHARS = DecodedBitStreamParser.C40_SHIFT2_SET_CHARS;
    DecodedBitStreamParser.TEXT_SHIFT3_SET_CHARS = [
        '`',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        '{',
        '|',
        '}',
        '~',
        String.fromCharCode(127)
    ];
    return DecodedBitStreamParser;
}();
const __TURBOPACK__default__export__ = DecodedBitStreamParser;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/Decoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$BitMatrixParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/BitMatrixParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DataBlock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DataBlock.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>The main class which implements Data Matrix Code decoding -- as opposed to locating and extracting
 * the Data Matrix Code from an image.</p>
 *
 * <AUTHOR> (Brian Brown)
 */ var Decoder = function() {
    function Decoder() {
        this.rsDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX_FIELD_256);
    }
    /**
     * <p>Decodes a Data Matrix Code represented as a {@link BitMatrix}. A 1 or "true" is taken
     * to mean a black module.</p>
     *
     * @param bits booleans representing white/black Data Matrix Code modules
     * @return text and bytes encoded within the Data Matrix Code
     * @throws FormatException if the Data Matrix Code cannot be decoded
     * @throws ChecksumException if error correction fails
     */ Decoder.prototype.decode = function(bits) {
        var e_1, _a;
        // Construct a parser and read version, error-correction level
        var parser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$BitMatrixParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](bits);
        var version = parser.getVersion();
        // Read codewords
        var codewords = parser.readCodewords();
        // Separate into data blocks
        var dataBlocks = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DataBlock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDataBlocks(codewords, version);
        // Count total number of data bytes
        var totalBytes = 0;
        try {
            for(var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()){
                var db = dataBlocks_1_1.value;
                totalBytes += db.getNumDataCodewords();
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        var resultBytes = new Uint8Array(totalBytes);
        var dataBlocksCount = dataBlocks.length;
        // Error-correct and copy data blocks together into a stream of bytes
        for(var j = 0; j < dataBlocksCount; j++){
            var dataBlock = dataBlocks[j];
            var codewordBytes = dataBlock.getCodewords();
            var numDataCodewords = dataBlock.getNumDataCodewords();
            this.correctErrors(codewordBytes, numDataCodewords);
            for(var i = 0; i < numDataCodewords; i++){
                // De-interlace data blocks.
                resultBytes[i * dataBlocksCount + j] = codewordBytes[i];
            }
        }
        // Decode the contents of that stream of bytes
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decode(resultBytes);
    };
    /**
     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to
     * correct the errors in-place using Reed-Solomon error correction.</p>
     *
     * @param codewordBytes data and error correction codewords
     * @param numDataCodewords number of codewords that are data bytes
     * @throws ChecksumException if error correction fails
     */ Decoder.prototype.correctErrors = function(codewordBytes, numDataCodewords) {
        // const numCodewords = codewordBytes.length;
        // First read into an array of ints
        var codewordsInts = new Int32Array(codewordBytes);
        // for (let i = 0; i < numCodewords; i++) {
        //   codewordsInts[i] = codewordBytes[i] & 0xFF;
        // }
        try {
            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);
        } catch (ignored /* ReedSolomonException */ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        // Copy back into array of bytes -- only need to worry about the bytes that were data
        // We don't care about errors in the error-correction codewords
        for(var i = 0; i < numDataCodewords; i++){
            codewordBytes[i] = codewordsInts[i];
        }
    };
    return Decoder;
}();
const __TURBOPACK__default__export__ = Decoder;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/detector/Detector.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
;
;
;
;
;
/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>Encapsulates logic that can detect a Data Matrix Code in an image, even if the Data Matrix Code
 * is rotated or skewed, or partially obscured.</p>
 *
 * <AUTHOR> Owen
 */ var Detector = function() {
    function Detector(image) {
        this.image = image;
        this.rectangleDetector = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.image);
    }
    /**
     * <p>Detects a Data Matrix Code in an image.</p>
     *
     * @return {@link DetectorResult} encapsulating results of detecting a Data Matrix Code
     * @throws NotFoundException if no Data Matrix Code can be found
     */ Detector.prototype.detect = function() {
        var cornerPoints = this.rectangleDetector.detect();
        var points = this.detectSolid1(cornerPoints);
        points = this.detectSolid2(points);
        points[3] = this.correctTopRight(points);
        if (!points[3]) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        points = this.shiftToModuleCenter(points);
        var topLeft = points[0];
        var bottomLeft = points[1];
        var bottomRight = points[2];
        var topRight = points[3];
        var dimensionTop = this.transitionsBetween(topLeft, topRight) + 1;
        var dimensionRight = this.transitionsBetween(bottomRight, topRight) + 1;
        if ((dimensionTop & 0x01) === 1) {
            dimensionTop += 1;
        }
        if ((dimensionRight & 0x01) === 1) {
            dimensionRight += 1;
        }
        if (4 * dimensionTop < 7 * dimensionRight && 4 * dimensionRight < 7 * dimensionTop) {
            // The matrix is square
            dimensionTop = dimensionRight = Math.max(dimensionTop, dimensionRight);
        }
        var bits = Detector.sampleGrid(this.image, topLeft, bottomLeft, bottomRight, topRight, dimensionTop, dimensionRight);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](bits, [
            topLeft,
            bottomLeft,
            bottomRight,
            topRight
        ]);
    };
    Detector.shiftPoint = function(point, to, div) {
        var x = (to.getX() - point.getX()) / (div + 1);
        var y = (to.getY() - point.getY()) / (div + 1);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](point.getX() + x, point.getY() + y);
    };
    Detector.moveAway = function(point, fromX, fromY) {
        var x = point.getX();
        var y = point.getY();
        if (x < fromX) {
            x -= 1;
        } else {
            x += 1;
        }
        if (y < fromY) {
            y -= 1;
        } else {
            y += 1;
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](x, y);
    };
    /**
     * Detect a solid side which has minimum transition.
     */ Detector.prototype.detectSolid1 = function(cornerPoints) {
        // 0  2
        // 1  3
        var pointA = cornerPoints[0];
        var pointB = cornerPoints[1];
        var pointC = cornerPoints[3];
        var pointD = cornerPoints[2];
        var trAB = this.transitionsBetween(pointA, pointB);
        var trBC = this.transitionsBetween(pointB, pointC);
        var trCD = this.transitionsBetween(pointC, pointD);
        var trDA = this.transitionsBetween(pointD, pointA);
        // 0..3
        // :  :
        // 1--2
        var min = trAB;
        var points = [
            pointD,
            pointA,
            pointB,
            pointC
        ];
        if (min > trBC) {
            min = trBC;
            points[0] = pointA;
            points[1] = pointB;
            points[2] = pointC;
            points[3] = pointD;
        }
        if (min > trCD) {
            min = trCD;
            points[0] = pointB;
            points[1] = pointC;
            points[2] = pointD;
            points[3] = pointA;
        }
        if (min > trDA) {
            points[0] = pointC;
            points[1] = pointD;
            points[2] = pointA;
            points[3] = pointB;
        }
        return points;
    };
    /**
     * Detect a second solid side next to first solid side.
     */ Detector.prototype.detectSolid2 = function(points) {
        // A..D
        // :  :
        // B--C
        var pointA = points[0];
        var pointB = points[1];
        var pointC = points[2];
        var pointD = points[3];
        // Transition detection on the edge is not stable.
        // To safely detect, shift the points to the module center.
        var tr = this.transitionsBetween(pointA, pointD);
        var pointBs = Detector.shiftPoint(pointB, pointC, (tr + 1) * 4);
        var pointCs = Detector.shiftPoint(pointC, pointB, (tr + 1) * 4);
        var trBA = this.transitionsBetween(pointBs, pointA);
        var trCD = this.transitionsBetween(pointCs, pointD);
        // 0..3
        // |  :
        // 1--2
        if (trBA < trCD) {
            // solid sides: A-B-C
            points[0] = pointA;
            points[1] = pointB;
            points[2] = pointC;
            points[3] = pointD;
        } else {
            // solid sides: B-C-D
            points[0] = pointB;
            points[1] = pointC;
            points[2] = pointD;
            points[3] = pointA;
        }
        return points;
    };
    /**
     * Calculates the corner position of the white top right module.
     */ Detector.prototype.correctTopRight = function(points) {
        // A..D
        // |  :
        // B--C
        var pointA = points[0];
        var pointB = points[1];
        var pointC = points[2];
        var pointD = points[3];
        // shift points for safe transition detection.
        var trTop = this.transitionsBetween(pointA, pointD);
        var trRight = this.transitionsBetween(pointB, pointD);
        var pointAs = Detector.shiftPoint(pointA, pointB, (trRight + 1) * 4);
        var pointCs = Detector.shiftPoint(pointC, pointB, (trTop + 1) * 4);
        trTop = this.transitionsBetween(pointAs, pointD);
        trRight = this.transitionsBetween(pointCs, pointD);
        var candidate1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pointD.getX() + (pointC.getX() - pointB.getX()) / (trTop + 1), pointD.getY() + (pointC.getY() - pointB.getY()) / (trTop + 1));
        var candidate2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pointD.getX() + (pointA.getX() - pointB.getX()) / (trRight + 1), pointD.getY() + (pointA.getY() - pointB.getY()) / (trRight + 1));
        if (!this.isValid(candidate1)) {
            if (this.isValid(candidate2)) {
                return candidate2;
            }
            return null;
        }
        if (!this.isValid(candidate2)) {
            return candidate1;
        }
        var sumc1 = this.transitionsBetween(pointAs, candidate1) + this.transitionsBetween(pointCs, candidate1);
        var sumc2 = this.transitionsBetween(pointAs, candidate2) + this.transitionsBetween(pointCs, candidate2);
        if (sumc1 > sumc2) {
            return candidate1;
        } else {
            return candidate2;
        }
    };
    /**
     * Shift the edge points to the module center.
     */ Detector.prototype.shiftToModuleCenter = function(points) {
        // A..D
        // |  :
        // B--C
        var pointA = points[0];
        var pointB = points[1];
        var pointC = points[2];
        var pointD = points[3];
        // calculate pseudo dimensions
        var dimH = this.transitionsBetween(pointA, pointD) + 1;
        var dimV = this.transitionsBetween(pointC, pointD) + 1;
        // shift points for safe dimension detection
        var pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);
        var pointCs = Detector.shiftPoint(pointC, pointB, dimH * 4);
        //  calculate more precise dimensions
        dimH = this.transitionsBetween(pointAs, pointD) + 1;
        dimV = this.transitionsBetween(pointCs, pointD) + 1;
        if ((dimH & 0x01) === 1) {
            dimH += 1;
        }
        if ((dimV & 0x01) === 1) {
            dimV += 1;
        }
        // WhiteRectangleDetector returns points inside of the rectangle.
        // I want points on the edges.
        var centerX = (pointA.getX() + pointB.getX() + pointC.getX() + pointD.getX()) / 4;
        var centerY = (pointA.getY() + pointB.getY() + pointC.getY() + pointD.getY()) / 4;
        pointA = Detector.moveAway(pointA, centerX, centerY);
        pointB = Detector.moveAway(pointB, centerX, centerY);
        pointC = Detector.moveAway(pointC, centerX, centerY);
        pointD = Detector.moveAway(pointD, centerX, centerY);
        var pointBs;
        var pointDs;
        // shift points to the center of each modules
        pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);
        pointAs = Detector.shiftPoint(pointAs, pointD, dimH * 4);
        pointBs = Detector.shiftPoint(pointB, pointA, dimV * 4);
        pointBs = Detector.shiftPoint(pointBs, pointC, dimH * 4);
        pointCs = Detector.shiftPoint(pointC, pointD, dimV * 4);
        pointCs = Detector.shiftPoint(pointCs, pointB, dimH * 4);
        pointDs = Detector.shiftPoint(pointD, pointC, dimV * 4);
        pointDs = Detector.shiftPoint(pointDs, pointA, dimH * 4);
        return [
            pointAs,
            pointBs,
            pointCs,
            pointDs
        ];
    };
    Detector.prototype.isValid = function(p) {
        return p.getX() >= 0 && p.getX() < this.image.getWidth() && p.getY() > 0 && p.getY() < this.image.getHeight();
    };
    Detector.sampleGrid = function(image, topLeft, bottomLeft, bottomRight, topRight, dimensionX, dimensionY) {
        var sampler = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getInstance();
        return sampler.sampleGrid(image, dimensionX, dimensionY, 0.5, 0.5, dimensionX - 0.5, 0.5, dimensionX - 0.5, dimensionY - 0.5, 0.5, dimensionY - 0.5, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());
    };
    /**
     * Counts the number of black/white transitions between two points, using something like Bresenham's algorithm.
     */ Detector.prototype.transitionsBetween = function(from, to) {
        // See QR Code Detector, sizeOfBlackWhiteBlackRun()
        var fromX = Math.trunc(from.getX());
        var fromY = Math.trunc(from.getY());
        var toX = Math.trunc(to.getX());
        var toY = Math.trunc(to.getY());
        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);
        if (steep) {
            var temp = fromX;
            fromX = fromY;
            fromY = temp;
            temp = toX;
            toX = toY;
            toY = temp;
        }
        var dx = Math.abs(toX - fromX);
        var dy = Math.abs(toY - fromY);
        var error = -dx / 2;
        var ystep = fromY < toY ? 1 : -1;
        var xstep = fromX < toX ? 1 : -1;
        var transitions = 0;
        var inBlack = this.image.get(steep ? fromY : fromX, steep ? fromX : fromY);
        for(var x = fromX, y = fromY; x !== toX; x += xstep){
            var isBlack = this.image.get(steep ? y : x, steep ? x : y);
            if (isBlack !== inBlack) {
                transitions++;
                inBlack = isBlack;
            }
            error += dy;
            if (error > 0) {
                if (y === toY) {
                    break;
                }
                y += ystep;
                error -= dx;
            }
        }
        return transitions;
    };
    return Detector;
}();
const __TURBOPACK__default__export__ = Detector;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/Decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$detector$2f$Detector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/detector/Detector.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * This implementation can detect and decode Data Matrix codes in an image.
 *
 * <AUTHOR> (Brian Brown)
 */ var DataMatrixReader = function() {
    function DataMatrixReader() {
        this.decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    }
    /**
     * Locates and decodes a Data Matrix code in an image.
     *
     * @return a String representing the content encoded by the Data Matrix code
     * @throws NotFoundException if a Data Matrix code cannot be found
     * @throws FormatException if a Data Matrix code cannot be decoded
     * @throws ChecksumException if error correction fails
     */ // @Override
    // public Result decode(BinaryBitmap image) throws NotFoundException, ChecksumException, FormatException {
    //   return decode(image, null);
    // }
    // @Override
    DataMatrixReader.prototype.decode = function(image, hints) {
        if (hints === void 0) {
            hints = null;
        }
        var decoderResult;
        var points;
        if (hints != null && hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].PURE_BARCODE)) {
            var bits = DataMatrixReader.extractPureBits(image.getBlackMatrix());
            decoderResult = this.decoder.decode(bits);
            points = DataMatrixReader.NO_POINTS;
        } else {
            var detectorResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$detector$2f$Detector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](image.getBlackMatrix()).detect();
            decoderResult = this.decoder.decode(detectorResult.getBits());
            points = detectorResult.getPoints();
        }
        var rawBytes = decoderResult.getRawBytes();
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](decoderResult.getText(), rawBytes, 8 * rawBytes.length, points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].currentTimeMillis());
        var byteSegments = decoderResult.getByteSegments();
        if (byteSegments != null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].BYTE_SEGMENTS, byteSegments);
        }
        var ecLevel = decoderResult.getECLevel();
        if (ecLevel != null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION_LEVEL, ecLevel);
        }
        return result;
    };
    // @Override
    DataMatrixReader.prototype.reset = function() {
    // do nothing
    };
    /**
     * This method detects a code in a "pure" image -- that is, pure monochrome image
     * which contains only an unrotated, unskewed, image of a code, with some white border
     * around it. This is a specialized method that works exceptionally fast in this special
     * case.
     *
     * @see com.google.zxing.qrcode.QRCodeReader#extractPureBits(BitMatrix)
     */ DataMatrixReader.extractPureBits = function(image) {
        var leftTopBlack = image.getTopLeftOnBit();
        var rightBottomBlack = image.getBottomRightOnBit();
        if (leftTopBlack == null || rightBottomBlack == null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var moduleSize = this.moduleSize(leftTopBlack, image);
        var top = leftTopBlack[1];
        var bottom = rightBottomBlack[1];
        var left = leftTopBlack[0];
        var right = rightBottomBlack[0];
        var matrixWidth = (right - left + 1) / moduleSize;
        var matrixHeight = (bottom - top + 1) / moduleSize;
        if (matrixWidth <= 0 || matrixHeight <= 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        // Push in the "border" by half the module width so that we start
        // sampling in the middle of the module. Just in case the image is a
        // little off, this will help recover.
        var nudge = moduleSize / 2;
        top += nudge;
        left += nudge;
        // Now just read off the bits
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](matrixWidth, matrixHeight);
        for(var y = 0; y < matrixHeight; y++){
            var iOffset = top + y * moduleSize;
            for(var x = 0; x < matrixWidth; x++){
                if (image.get(left + x * moduleSize, iOffset)) {
                    bits.set(x, y);
                }
            }
        }
        return bits;
    };
    DataMatrixReader.moduleSize = function(leftTopBlack, image) {
        var width = image.getWidth();
        var x = leftTopBlack[0];
        var y = leftTopBlack[1];
        while(x < width && image.get(x, y)){
            x++;
        }
        if (x === width) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var moduleSize = x - leftTopBlack[0];
        if (moduleSize === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return moduleSize;
    };
    DataMatrixReader.NO_POINTS = [];
    return DataMatrixReader;
}();
const __TURBOPACK__default__export__ = DataMatrixReader;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)");
;
/**
 * Symbol Character Placement Program. Adapted from Annex M.1 in ISO/IEC 16022:2000(E).
 */ var DefaultPlacement = function() {
    /**
     * Main constructor
     *
     * @param codewords the codewords to place
     * @param numcols   the number of columns
     * @param numrows   the number of rows
     */ function DefaultPlacement(codewords, numcols, numrows) {
        this.codewords = codewords;
        this.numcols = numcols;
        this.numrows = numrows;
        this.bits = new Uint8Array(numcols * numrows);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(this.bits, 2); // Initialize with "not set" value
    }
    DefaultPlacement.prototype.getNumrows = function() {
        return this.numrows;
    };
    DefaultPlacement.prototype.getNumcols = function() {
        return this.numcols;
    };
    DefaultPlacement.prototype.getBits = function() {
        return this.bits;
    };
    DefaultPlacement.prototype.getBit = function(col, row) {
        return this.bits[row * this.numcols + col] === 1;
    };
    DefaultPlacement.prototype.setBit = function(col, row, bit) {
        this.bits[row * this.numcols + col] = bit ? 1 : 0;
    };
    DefaultPlacement.prototype.noBit = function(col, row) {
        return this.bits[row * this.numcols + col] === 2;
    };
    DefaultPlacement.prototype.place = function() {
        var pos = 0;
        var row = 4;
        var col = 0;
        do {
            // repeatedly first check for one of the special corner cases, then...
            if (row === this.numrows && col === 0) {
                this.corner1(pos++);
            }
            if (row === this.numrows - 2 && col === 0 && this.numcols % 4 !== 0) {
                this.corner2(pos++);
            }
            if (row === this.numrows - 2 && col === 0 && this.numcols % 8 === 4) {
                this.corner3(pos++);
            }
            if (row === this.numrows + 4 && col === 2 && this.numcols % 8 === 0) {
                this.corner4(pos++);
            }
            // sweep upward diagonally, inserting successive characters...
            do {
                if (row < this.numrows && col >= 0 && this.noBit(col, row)) {
                    this.utah(row, col, pos++);
                }
                row -= 2;
                col += 2;
            }while (row >= 0 && col < this.numcols)
            row++;
            col += 3;
            // and then sweep downward diagonally, inserting successive characters, ...
            do {
                if (row >= 0 && col < this.numcols && this.noBit(col, row)) {
                    this.utah(row, col, pos++);
                }
                row += 2;
                col -= 2;
            }while (row < this.numrows && col >= 0)
            row += 3;
            col++;
        // ...until the entire array is scanned
        }while (row < this.numrows || col < this.numcols)
        // Lastly, if the lower right-hand corner is untouched, fill in fixed pattern
        if (this.noBit(this.numcols - 1, this.numrows - 1)) {
            this.setBit(this.numcols - 1, this.numrows - 1, true);
            this.setBit(this.numcols - 2, this.numrows - 2, true);
        }
    };
    DefaultPlacement.prototype.module = function(row, col, pos, bit) {
        if (row < 0) {
            row += this.numrows;
            col += 4 - (this.numrows + 4) % 8;
        }
        if (col < 0) {
            col += this.numcols;
            row += 4 - (this.numcols + 4) % 8;
        }
        // Note the conversion:
        var v = this.codewords.charCodeAt(pos);
        v &= 1 << 8 - bit;
        this.setBit(col, row, v !== 0);
    };
    /**
     * Places the 8 bits of a utah-shaped symbol character in ECC200.
     *
     * @param row the row
     * @param col the column
     * @param pos character position
     */ DefaultPlacement.prototype.utah = function(row, col, pos) {
        this.module(row - 2, col - 2, pos, 1);
        this.module(row - 2, col - 1, pos, 2);
        this.module(row - 1, col - 2, pos, 3);
        this.module(row - 1, col - 1, pos, 4);
        this.module(row - 1, col, pos, 5);
        this.module(row, col - 2, pos, 6);
        this.module(row, col - 1, pos, 7);
        this.module(row, col, pos, 8);
    };
    DefaultPlacement.prototype.corner1 = function(pos) {
        this.module(this.numrows - 1, 0, pos, 1);
        this.module(this.numrows - 1, 1, pos, 2);
        this.module(this.numrows - 1, 2, pos, 3);
        this.module(0, this.numcols - 2, pos, 4);
        this.module(0, this.numcols - 1, pos, 5);
        this.module(1, this.numcols - 1, pos, 6);
        this.module(2, this.numcols - 1, pos, 7);
        this.module(3, this.numcols - 1, pos, 8);
    };
    DefaultPlacement.prototype.corner2 = function(pos) {
        this.module(this.numrows - 3, 0, pos, 1);
        this.module(this.numrows - 2, 0, pos, 2);
        this.module(this.numrows - 1, 0, pos, 3);
        this.module(0, this.numcols - 4, pos, 4);
        this.module(0, this.numcols - 3, pos, 5);
        this.module(0, this.numcols - 2, pos, 6);
        this.module(0, this.numcols - 1, pos, 7);
        this.module(1, this.numcols - 1, pos, 8);
    };
    DefaultPlacement.prototype.corner3 = function(pos) {
        this.module(this.numrows - 3, 0, pos, 1);
        this.module(this.numrows - 2, 0, pos, 2);
        this.module(this.numrows - 1, 0, pos, 3);
        this.module(0, this.numcols - 2, pos, 4);
        this.module(0, this.numcols - 1, pos, 5);
        this.module(1, this.numcols - 1, pos, 6);
        this.module(2, this.numcols - 1, pos, 7);
        this.module(3, this.numcols - 1, pos, 8);
    };
    DefaultPlacement.prototype.corner4 = function(pos) {
        this.module(this.numrows - 1, 0, pos, 1);
        this.module(this.numrows - 1, this.numcols - 1, pos, 2);
        this.module(0, this.numcols - 3, pos, 3);
        this.module(0, this.numcols - 2, pos, 4);
        this.module(0, this.numcols - 1, pos, 5);
        this.module(1, this.numcols - 3, pos, 6);
        this.module(1, this.numcols - 2, pos, 7);
        this.module(1, this.numcols - 1, pos, 8);
    };
    return DefaultPlacement;
}();
const __TURBOPACK__default__export__ = DefaultPlacement;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ALOG": ()=>ALOG,
    "ASCII_ENCODATION": ()=>ASCII_ENCODATION,
    "BASE256_ENCODATION": ()=>BASE256_ENCODATION,
    "C40_ENCODATION": ()=>C40_ENCODATION,
    "C40_UNLATCH": ()=>C40_UNLATCH,
    "EDIFACT_ENCODATION": ()=>EDIFACT_ENCODATION,
    "FACTORS": ()=>FACTORS,
    "FACTOR_SETS": ()=>FACTOR_SETS,
    "LATCH_TO_ANSIX12": ()=>LATCH_TO_ANSIX12,
    "LATCH_TO_BASE256": ()=>LATCH_TO_BASE256,
    "LATCH_TO_C40": ()=>LATCH_TO_C40,
    "LATCH_TO_EDIFACT": ()=>LATCH_TO_EDIFACT,
    "LATCH_TO_TEXT": ()=>LATCH_TO_TEXT,
    "LOG": ()=>LOG,
    "MACRO_05": ()=>MACRO_05,
    "MACRO_05_HEADER": ()=>MACRO_05_HEADER,
    "MACRO_06": ()=>MACRO_06,
    "MACRO_06_HEADER": ()=>MACRO_06_HEADER,
    "MACRO_TRAILER": ()=>MACRO_TRAILER,
    "MODULO_VALUE": ()=>MODULO_VALUE,
    "PAD": ()=>PAD,
    "SymbolShapeHint": ()=>SymbolShapeHint,
    "TEXT_ENCODATION": ()=>TEXT_ENCODATION,
    "UPPER_SHIFT": ()=>UPPER_SHIFT,
    "X12_ENCODATION": ()=>X12_ENCODATION,
    "X12_UNLATCH": ()=>X12_UNLATCH
});
var _a;
var FACTOR_SETS = [
    5,
    7,
    10,
    11,
    12,
    14,
    18,
    20,
    24,
    28,
    36,
    42,
    48,
    56,
    62,
    68
];
var FACTORS = [
    [
        228,
        48,
        15,
        111,
        62
    ],
    [
        23,
        68,
        144,
        134,
        240,
        92,
        254
    ],
    [
        28,
        24,
        185,
        166,
        223,
        248,
        116,
        255,
        110,
        61
    ],
    [
        175,
        138,
        205,
        12,
        194,
        168,
        39,
        245,
        60,
        97,
        120
    ],
    [
        41,
        153,
        158,
        91,
        61,
        42,
        142,
        213,
        97,
        178,
        100,
        242
    ],
    [
        156,
        97,
        192,
        252,
        95,
        9,
        157,
        119,
        138,
        45,
        18,
        186,
        83,
        185
    ],
    [
        83,
        195,
        100,
        39,
        188,
        75,
        66,
        61,
        241,
        213,
        109,
        129,
        94,
        254,
        225,
        48,
        90,
        188
    ],
    [
        15,
        195,
        244,
        9,
        233,
        71,
        168,
        2,
        188,
        160,
        153,
        145,
        253,
        79,
        108,
        82,
        27,
        174,
        186,
        172
    ],
    [
        52,
        190,
        88,
        205,
        109,
        39,
        176,
        21,
        155,
        197,
        251,
        223,
        155,
        21,
        5,
        172,
        254,
        124,
        12,
        181,
        184,
        96,
        50,
        193
    ],
    [
        211,
        231,
        43,
        97,
        71,
        96,
        103,
        174,
        37,
        151,
        170,
        53,
        75,
        34,
        249,
        121,
        17,
        138,
        110,
        213,
        141,
        136,
        120,
        151,
        233,
        168,
        93,
        255
    ],
    [
        245,
        127,
        242,
        218,
        130,
        250,
        162,
        181,
        102,
        120,
        84,
        179,
        220,
        251,
        80,
        182,
        229,
        18,
        2,
        4,
        68,
        33,
        101,
        137,
        95,
        119,
        115,
        44,
        175,
        184,
        59,
        25,
        225,
        98,
        81,
        112
    ],
    [
        77,
        193,
        137,
        31,
        19,
        38,
        22,
        153,
        247,
        105,
        122,
        2,
        245,
        133,
        242,
        8,
        175,
        95,
        100,
        9,
        167,
        105,
        214,
        111,
        57,
        121,
        21,
        1,
        253,
        57,
        54,
        101,
        248,
        202,
        69,
        50,
        150,
        177,
        226,
        5,
        9,
        5
    ],
    [
        245,
        132,
        172,
        223,
        96,
        32,
        117,
        22,
        238,
        133,
        238,
        231,
        205,
        188,
        237,
        87,
        191,
        106,
        16,
        147,
        118,
        23,
        37,
        90,
        170,
        205,
        131,
        88,
        120,
        100,
        66,
        138,
        186,
        240,
        82,
        44,
        176,
        87,
        187,
        147,
        160,
        175,
        69,
        213,
        92,
        253,
        225,
        19
    ],
    [
        175,
        9,
        223,
        238,
        12,
        17,
        220,
        208,
        100,
        29,
        175,
        170,
        230,
        192,
        215,
        235,
        150,
        159,
        36,
        223,
        38,
        200,
        132,
        54,
        228,
        146,
        218,
        234,
        117,
        203,
        29,
        232,
        144,
        238,
        22,
        150,
        201,
        117,
        62,
        207,
        164,
        13,
        137,
        245,
        127,
        67,
        247,
        28,
        155,
        43,
        203,
        107,
        233,
        53,
        143,
        46
    ],
    [
        242,
        93,
        169,
        50,
        144,
        210,
        39,
        118,
        202,
        188,
        201,
        189,
        143,
        108,
        196,
        37,
        185,
        112,
        134,
        230,
        245,
        63,
        197,
        190,
        250,
        106,
        185,
        221,
        175,
        64,
        114,
        71,
        161,
        44,
        147,
        6,
        27,
        218,
        51,
        63,
        87,
        10,
        40,
        130,
        188,
        17,
        163,
        31,
        176,
        170,
        4,
        107,
        232,
        7,
        94,
        166,
        224,
        124,
        86,
        47,
        11,
        204
    ],
    [
        220,
        228,
        173,
        89,
        251,
        149,
        159,
        56,
        89,
        33,
        147,
        244,
        154,
        36,
        73,
        127,
        213,
        136,
        248,
        180,
        234,
        197,
        158,
        177,
        68,
        122,
        93,
        213,
        15,
        160,
        227,
        236,
        66,
        139,
        153,
        185,
        202,
        167,
        179,
        25,
        220,
        232,
        96,
        210,
        231,
        136,
        223,
        239,
        181,
        241,
        59,
        52,
        172,
        25,
        49,
        232,
        211,
        189,
        64,
        54,
        108,
        153,
        132,
        63,
        96,
        103,
        82,
        186
    ]
];
var /*final*/ MODULO_VALUE = 0x12d;
var static_LOG = function(LOG, ALOG) {
    var p = 1;
    for(var i = 0; i < 255; i++){
        ALOG[i] = p;
        LOG[p] = i;
        p *= 2;
        if (p >= 256) {
            p ^= MODULO_VALUE;
        }
    }
    return {
        LOG: LOG,
        ALOG: ALOG
    };
};
var LOG = (_a = static_LOG([], []), _a.LOG), ALOG = _a.ALOG;
var SymbolShapeHint;
(function(SymbolShapeHint) {
    SymbolShapeHint[SymbolShapeHint["FORCE_NONE"] = 0] = "FORCE_NONE";
    SymbolShapeHint[SymbolShapeHint["FORCE_SQUARE"] = 1] = "FORCE_SQUARE";
    SymbolShapeHint[SymbolShapeHint["FORCE_RECTANGLE"] = 2] = "FORCE_RECTANGLE";
})(SymbolShapeHint || (SymbolShapeHint = {}));
var PAD = 129;
var LATCH_TO_C40 = 230;
var LATCH_TO_BASE256 = 231;
var UPPER_SHIFT = 235;
var MACRO_05 = 236;
var MACRO_06 = 237;
var LATCH_TO_ANSIX12 = 238;
var LATCH_TO_TEXT = 239;
var LATCH_TO_EDIFACT = 240;
var C40_UNLATCH = 254;
var X12_UNLATCH = 254;
var MACRO_05_HEADER = '[)>\u001E05\u001D';
var MACRO_06_HEADER = '[)>\u001E06\u001D';
var MACRO_TRAILER = '\u001E\u0004';
var ASCII_ENCODATION = 0;
var C40_ENCODATION = 1;
var TEXT_ENCODATION = 2;
var X12_ENCODATION = 3;
var EDIFACT_ENCODATION = 4;
var BASE256_ENCODATION = 5;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
;
;
/**
 * Error Correction Code for ECC200.
 */ var ErrorCorrection = function() {
    function ErrorCorrection() {}
    /**
     * Creates the ECC200 error correction for an encoded message.
     *
     * @param codewords  the codewords
     * @param symbolInfo information about the symbol to be encoded
     * @return the codewords with interleaved error correction.
     */ ErrorCorrection.encodeECC200 = function(codewords, symbolInfo) {
        if (codewords.length !== symbolInfo.getDataCapacity()) {
            throw new Error('The number of codewords does not match the selected symbol');
        }
        var sb = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        sb.append(codewords);
        var blockCount = symbolInfo.getInterleavedBlockCount();
        if (blockCount === 1) {
            var ecc = this.createECCBlock(codewords, symbolInfo.getErrorCodewords());
            sb.append(ecc);
        } else {
            // sb.setLength(sb.capacity());
            var dataSizes = [];
            var errorSizes = [];
            for(var i = 0; i < blockCount; i++){
                dataSizes[i] = symbolInfo.getDataLengthForInterleavedBlock(i + 1);
                errorSizes[i] = symbolInfo.getErrorLengthForInterleavedBlock(i + 1);
            }
            for(var block = 0; block < blockCount; block++){
                var temp = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                for(var d = block; d < symbolInfo.getDataCapacity(); d += blockCount){
                    temp.append(codewords.charAt(d));
                }
                var ecc = this.createECCBlock(temp.toString(), errorSizes[block]);
                var pos = 0;
                for(var e = block; e < errorSizes[block] * blockCount; e += blockCount){
                    sb.setCharAt(symbolInfo.getDataCapacity() + e, ecc.charAt(pos++));
                }
            }
        }
        return sb.toString();
    };
    ErrorCorrection.createECCBlock = function(codewords, numECWords) {
        var table = -1;
        for(var i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FACTOR_SETS"].length; i++){
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FACTOR_SETS"][i] === numECWords) {
                table = i;
                break;
            }
        }
        if (table < 0) {
            throw new Error('Illegal number of error correction codewords specified: ' + numECWords);
        }
        var poly = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FACTORS"][table];
        var ecc = [];
        for(var i = 0; i < numECWords; i++){
            ecc[i] = 0;
        }
        for(var i = 0; i < codewords.length; i++){
            var m = ecc[numECWords - 1] ^ codewords.charAt(i).charCodeAt(0);
            for(var k = numECWords - 1; k > 0; k--){
                if (m !== 0 && poly[k] !== 0) {
                    ecc[k] = ecc[k - 1] ^ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALOG"][(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LOG"][m] + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LOG"][poly[k]]) % 255];
                } else {
                    ecc[k] = ecc[k - 1];
                }
            }
            if (m !== 0 && poly[0] !== 0) {
                ecc[0] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALOG"][(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LOG"][m] + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LOG"][poly[0]]) % 255];
            } else {
                ecc[0] = 0;
            }
        }
        var eccReversed = [];
        for(var i = 0; i < numECWords; i++){
            eccReversed[i] = ecc[numECWords - i - 1];
        }
        return eccReversed.map(function(c) {
            return String.fromCharCode(c);
        }).join('');
    };
    return ErrorCorrection;
}();
const __TURBOPACK__default__export__ = ErrorCorrection;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ASCIIEncoder": ()=>ASCIIEncoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
;
;
var ASCIIEncoder = function() {
    function ASCIIEncoder() {}
    ASCIIEncoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"];
    };
    ASCIIEncoder.prototype.encode = function(context) {
        // step B
        var n = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].determineConsecutiveDigitCount(context.getMessage(), context.pos);
        if (n >= 2) {
            context.writeCodeword(this.encodeASCIIDigits(context.getMessage().charCodeAt(context.pos), context.getMessage().charCodeAt(context.pos + 1)));
            context.pos += 2;
        } else {
            var c = context.getCurrentChar();
            var newMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());
            if (newMode !== this.getEncodingMode()) {
                switch(newMode){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]:
                        context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_BASE256"]);
                        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]);
                        return;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]:
                        context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_C40"]);
                        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]);
                        return;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]:
                        context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_ANSIX12"]);
                        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]:
                        context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_TEXT"]);
                        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]:
                        context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_EDIFACT"]);
                        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]);
                        break;
                    default:
                        throw new Error('Illegal mode: ' + newMode);
                }
            } else if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isExtendedASCII(c)) {
                context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UPPER_SHIFT"]);
                context.writeCodeword(c - 128 + 1);
                context.pos++;
            } else {
                context.writeCodeword(c + 1);
                context.pos++;
            }
        }
    };
    ASCIIEncoder.prototype.encodeASCIIDigits = function(digit1, digit2) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(digit1) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(digit2)) {
            var num = (digit1 - 48) * 10 + (digit2 - 48);
            return num + 130;
        }
        throw new Error('not digits: ' + digit1 + digit2);
    };
    return ASCIIEncoder;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Base256Encoder": ()=>Base256Encoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
;
;
;
;
var Base256Encoder = function() {
    function Base256Encoder() {}
    Base256Encoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"];
    };
    Base256Encoder.prototype.encode = function(context) {
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        buffer.append(0); // Initialize length field
        while(context.hasMoreCharacters()){
            var c = context.getCurrentChar();
            buffer.append(c);
            context.pos++;
            var newMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());
            if (newMode !== this.getEncodingMode()) {
                // Return to ASCII encodation, which will actually handle latch to new mode
                context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
                break;
            }
        }
        var dataCount = buffer.length() - 1;
        var lengthFieldSize = 1;
        var currentSize = context.getCodewordCount() + dataCount + lengthFieldSize;
        context.updateSymbolInfo(currentSize);
        var mustPad = context.getSymbolInfo().getDataCapacity() - currentSize > 0;
        if (context.hasMoreCharacters() || mustPad) {
            if (dataCount <= 249) {
                buffer.setCharAt(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(dataCount));
            } else if (dataCount <= 1555) {
                buffer.setCharAt(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(Math.floor(dataCount / 250) + 249));
                buffer.insert(1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(dataCount % 250));
            } else {
                throw new Error('Message length not in valid ranges: ' + dataCount);
            }
        }
        for(var i = 0, c = buffer.length(); i < c; i++){
            context.writeCodeword(this.randomize255State(buffer.charAt(i).charCodeAt(0), context.getCodewordCount() + 1));
        }
    };
    Base256Encoder.prototype.randomize255State = function(ch, codewordPosition) {
        var pseudoRandom = 149 * codewordPosition % 255 + 1;
        var tempVariable = ch + pseudoRandom;
        if (tempVariable <= 255) {
            return tempVariable;
        } else {
            return tempVariable - 256;
        }
    };
    return Base256Encoder;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "C40Encoder": ()=>C40Encoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
;
;
;
var C40Encoder = function() {
    function C40Encoder() {}
    C40Encoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"];
    };
    C40Encoder.prototype.encodeMaximal = function(context) {
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        var lastCharSize = 0;
        var backtrackStartPosition = context.pos;
        var backtrackBufferLength = 0;
        while(context.hasMoreCharacters()){
            var c = context.getCurrentChar();
            context.pos++;
            lastCharSize = this.encodeChar(c, buffer);
            if (buffer.length() % 3 === 0) {
                backtrackStartPosition = context.pos;
                backtrackBufferLength = buffer.length();
            }
        }
        if (backtrackBufferLength !== buffer.length()) {
            var unwritten = Math.floor(buffer.length() / 3 * 2);
            var curCodewordCount = Math.floor(context.getCodewordCount() + unwritten + 1); // +1 for the latch to C40
            context.updateSymbolInfo(curCodewordCount);
            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;
            var rest = Math.floor(buffer.length() % 3);
            if (rest === 2 && available !== 2 || rest === 1 && (lastCharSize > 3 || available !== 1)) {
                // buffer.setLength(backtrackBufferLength);
                context.pos = backtrackStartPosition;
            }
        }
        if (buffer.length() > 0) {
            context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LATCH_TO_C40"]);
        }
        this.handleEOD(context, buffer);
    };
    C40Encoder.prototype.encode = function(context) {
        // step C
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        while(context.hasMoreCharacters()){
            var c = context.getCurrentChar();
            context.pos++;
            var lastCharSize = this.encodeChar(c, buffer);
            var unwritten = Math.floor(buffer.length() / 3) * 2;
            var curCodewordCount = context.getCodewordCount() + unwritten;
            context.updateSymbolInfo(curCodewordCount);
            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;
            if (!context.hasMoreCharacters()) {
                // Avoid having a single C40 value in the last triplet
                var removed = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                if (buffer.length() % 3 === 2 && available !== 2) {
                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);
                }
                while(buffer.length() % 3 === 1 && (lastCharSize > 3 || available !== 1)){
                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);
                }
                break;
            }
            var count = buffer.length();
            if (count % 3 === 0) {
                var newMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());
                if (newMode !== this.getEncodingMode()) {
                    // Return to ASCII encodation, which will actually handle latch to new mode
                    context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
                    break;
                }
            }
        }
        this.handleEOD(context, buffer);
    };
    C40Encoder.prototype.backtrackOneCharacter = function(context, buffer, removed, lastCharSize) {
        var count = buffer.length();
        var test = buffer.toString().substring(0, count - lastCharSize);
        buffer.setLengthToZero();
        buffer.append(test);
        // buffer.delete(count - lastCharSize, count);
        /*for (let i = count - lastCharSize; i < count; i++) {
          buffer.deleteCharAt(i);
        }*/ context.pos--;
        var c = context.getCurrentChar();
        lastCharSize = this.encodeChar(c, removed);
        context.resetSymbolInfo(); // Deal with possible reduction in symbol size
        return lastCharSize;
    };
    C40Encoder.prototype.writeNextTriplet = function(context, buffer) {
        context.writeCodewords(this.encodeToCodewords(buffer.toString()));
        var test = buffer.toString().substring(3);
        buffer.setLengthToZero();
        buffer.append(test);
    // buffer.delete(0, 3);
    /*for (let i = 0; i < 3; i++) {
          buffer.deleteCharAt(i);
        }*/ };
    /**
     * Handle "end of data" situations
     *
     * @param context the encoder context
     * @param buffer  the buffer with the remaining encoded characters
     */ C40Encoder.prototype.handleEOD = function(context, buffer) {
        var unwritten = Math.floor(buffer.length() / 3 * 2);
        var rest = buffer.length() % 3;
        var curCodewordCount = context.getCodewordCount() + unwritten;
        context.updateSymbolInfo(curCodewordCount);
        var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;
        if (rest === 2) {
            buffer.append('\0'); // Shift 1
            while(buffer.length() >= 3){
                this.writeNextTriplet(context, buffer);
            }
            if (context.hasMoreCharacters()) {
                context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_UNLATCH"]);
            }
        } else if (available === 1 && rest === 1) {
            while(buffer.length() >= 3){
                this.writeNextTriplet(context, buffer);
            }
            if (context.hasMoreCharacters()) {
                context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_UNLATCH"]);
            }
            // else no unlatch
            context.pos--;
        } else if (rest === 0) {
            while(buffer.length() >= 3){
                this.writeNextTriplet(context, buffer);
            }
            if (available > 0 || context.hasMoreCharacters()) {
                context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_UNLATCH"]);
            }
        } else {
            throw new Error('Unexpected case. Please report!');
        }
        context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
    };
    C40Encoder.prototype.encodeChar = function(c, sb) {
        if (c === ' '.charCodeAt(0)) {
            sb.append(3);
            return 1;
        }
        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {
            sb.append(c - 48 + 4);
            return 1;
        }
        if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {
            sb.append(c - 65 + 14);
            return 1;
        }
        if (c < ' '.charCodeAt(0)) {
            sb.append(0); // Shift 1 Set
            sb.append(c);
            return 2;
        }
        if (c <= '/'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 33);
            return 2;
        }
        if (c <= '@'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 58 + 15);
            return 2;
        }
        if (c <= '_'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 91 + 22);
            return 2;
        }
        if (c <= 127) {
            sb.append(2); // Shift 3 Set
            sb.append(c - 96);
            return 2;
        }
        sb.append(1 + "\u001E"); // Shift 2, Upper Shift
        var len = 2;
        len += this.encodeChar(c - 128, sb);
        return len;
    };
    C40Encoder.prototype.encodeToCodewords = function(sb) {
        var v = 1600 * sb.charCodeAt(0) + 40 * sb.charCodeAt(1) + sb.charCodeAt(2) + 1;
        var cw1 = v / 256;
        var cw2 = v % 256;
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        result.append(cw1);
        result.append(cw2);
        return result.toString();
    };
    return C40Encoder;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "EdifactEncoder": ()=>EdifactEncoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
;
;
;
;
var EdifactEncoder = function() {
    function EdifactEncoder() {}
    EdifactEncoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"];
    };
    EdifactEncoder.prototype.encode = function(context) {
        // step F
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        while(context.hasMoreCharacters()){
            var c = context.getCurrentChar();
            this.encodeChar(c, buffer);
            context.pos++;
            var count = buffer.length();
            if (count >= 4) {
                context.writeCodewords(this.encodeToCodewords(buffer.toString()));
                var test_1 = buffer.toString().substring(4);
                buffer.setLengthToZero();
                buffer.append(test_1);
                // buffer.delete(0, 4);
                // for (let i = 0; i < 4; i++) {
                //  buffer.deleteCharAt(i);
                // }
                var newMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());
                if (newMode !== this.getEncodingMode()) {
                    // Return to ASCII encodation, which will actually handle latch to new mode
                    context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
                    break;
                }
            }
        }
        buffer.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(31)); // Unlatch
        this.handleEOD(context, buffer);
    };
    /**
     * Handle "end of data" situations
     *
     * @param context the encoder context
     * @param buffer  the buffer with the remaining encoded characters
     */ EdifactEncoder.prototype.handleEOD = function(context, buffer) {
        try {
            var count = buffer.length();
            if (count === 0) {
                return; // Already finished
            }
            if (count === 1) {
                // Only an unlatch at the end
                context.updateSymbolInfo();
                var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();
                var remaining = context.getRemainingCharacters();
                // The following two lines are a hack inspired by the 'fix' from https://sourceforge.net/p/barcode4j/svn/221/
                if (remaining > available) {
                    context.updateSymbolInfo(context.getCodewordCount() + 1);
                    available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();
                }
                if (remaining <= available && available <= 2) {
                    return; // No unlatch
                }
            }
            if (count > 4) {
                throw new Error('Count must not exceed 4');
            }
            var restChars = count - 1;
            var encoded = this.encodeToCodewords(buffer.toString());
            var endOfSymbolReached = !context.hasMoreCharacters();
            var restInAscii = endOfSymbolReached && restChars <= 2;
            if (restChars <= 2) {
                context.updateSymbolInfo(context.getCodewordCount() + restChars);
                var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();
                if (available >= 3) {
                    restInAscii = false;
                    context.updateSymbolInfo(context.getCodewordCount() + encoded.length);
                // available = context.symbolInfo.dataCapacity - context.getCodewordCount();
                }
            }
            if (restInAscii) {
                context.resetSymbolInfo();
                context.pos -= restChars;
            } else {
                context.writeCodewords(encoded);
            }
        } finally{
            context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
        }
    };
    EdifactEncoder.prototype.encodeChar = function(c, sb) {
        if (c >= ' '.charCodeAt(0) && c <= '?'.charCodeAt(0)) {
            sb.append(c);
        } else if (c >= '@'.charCodeAt(0) && c <= '^'.charCodeAt(0)) {
            sb.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(c - 64));
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].illegalCharacter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(c));
        }
    };
    EdifactEncoder.prototype.encodeToCodewords = function(sb) {
        var len = sb.length;
        if (len === 0) {
            throw new Error('StringBuilder must not be empty');
        }
        var c1 = sb.charAt(0).charCodeAt(0);
        var c2 = len >= 2 ? sb.charAt(1).charCodeAt(0) : 0;
        var c3 = len >= 3 ? sb.charAt(2).charCodeAt(0) : 0;
        var c4 = len >= 4 ? sb.charAt(3).charCodeAt(0) : 0;
        var v = (c1 << 18) + (c2 << 12) + (c3 << 6) + c4;
        var cw1 = v >> 16 & 255;
        var cw2 = v >> 8 & 255;
        var cw3 = v & 255;
        var res = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        res.append(cw1);
        if (len >= 2) {
            res.append(cw2);
        }
        if (len >= 3) {
            res.append(cw3);
        }
        return res.toString();
    };
    return EdifactEncoder;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PROD_SYMBOLS": ()=>PROD_SYMBOLS,
    "default": ()=>__TURBOPACK__default__export__
});
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
/**
 * Symbol info table for DataMatrix.
 */ var SymbolInfo = function() {
    function SymbolInfo(rectangular, dataCapacity, errorCodewords, matrixWidth, matrixHeight, dataRegions, rsBlockData, rsBlockError) {
        if (rsBlockData === void 0) {
            rsBlockData = 0;
        }
        if (rsBlockError === void 0) {
            rsBlockError = 0;
        }
        this.rectangular = rectangular;
        this.dataCapacity = dataCapacity;
        this.errorCodewords = errorCodewords;
        this.matrixWidth = matrixWidth;
        this.matrixHeight = matrixHeight;
        this.dataRegions = dataRegions;
        this.rsBlockData = rsBlockData;
        this.rsBlockError = rsBlockError;
    }
    SymbolInfo.lookup = function(dataCodewords, shape, minSize, maxSize, fail) {
        var e_1, _a;
        if (shape === void 0) {
            shape = 0 /* FORCE_NONE */ ;
        }
        if (minSize === void 0) {
            minSize = null;
        }
        if (maxSize === void 0) {
            maxSize = null;
        }
        if (fail === void 0) {
            fail = true;
        }
        try {
            for(var PROD_SYMBOLS_1 = __values(PROD_SYMBOLS), PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next(); !PROD_SYMBOLS_1_1.done; PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next()){
                var symbol = PROD_SYMBOLS_1_1.value;
                if (shape === 1 /* FORCE_SQUARE */  && symbol.rectangular) {
                    continue;
                }
                if (shape === 2 /* FORCE_RECTANGLE */  && !symbol.rectangular) {
                    continue;
                }
                if (minSize != null && (symbol.getSymbolWidth() < minSize.getWidth() || symbol.getSymbolHeight() < minSize.getHeight())) {
                    continue;
                }
                if (maxSize != null && (symbol.getSymbolWidth() > maxSize.getWidth() || symbol.getSymbolHeight() > maxSize.getHeight())) {
                    continue;
                }
                if (dataCodewords <= symbol.dataCapacity) {
                    return symbol;
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (PROD_SYMBOLS_1_1 && !PROD_SYMBOLS_1_1.done && (_a = PROD_SYMBOLS_1.return)) _a.call(PROD_SYMBOLS_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        if (fail) {
            throw new Error("Can't find a symbol arrangement that matches the message. Data codewords: " + dataCodewords);
        }
        return null;
    };
    SymbolInfo.prototype.getHorizontalDataRegions = function() {
        switch(this.dataRegions){
            case 1:
                return 1;
            case 2:
            case 4:
                return 2;
            case 16:
                return 4;
            case 36:
                return 6;
            default:
                throw new Error('Cannot handle this number of data regions');
        }
    };
    SymbolInfo.prototype.getVerticalDataRegions = function() {
        switch(this.dataRegions){
            case 1:
            case 2:
                return 1;
            case 4:
                return 2;
            case 16:
                return 4;
            case 36:
                return 6;
            default:
                throw new Error('Cannot handle this number of data regions');
        }
    };
    SymbolInfo.prototype.getSymbolDataWidth = function() {
        return this.getHorizontalDataRegions() * this.matrixWidth;
    };
    SymbolInfo.prototype.getSymbolDataHeight = function() {
        return this.getVerticalDataRegions() * this.matrixHeight;
    };
    SymbolInfo.prototype.getSymbolWidth = function() {
        return this.getSymbolDataWidth() + this.getHorizontalDataRegions() * 2;
    };
    SymbolInfo.prototype.getSymbolHeight = function() {
        return this.getSymbolDataHeight() + this.getVerticalDataRegions() * 2;
    };
    SymbolInfo.prototype.getCodewordCount = function() {
        return this.dataCapacity + this.errorCodewords;
    };
    SymbolInfo.prototype.getInterleavedBlockCount = function() {
        if (!this.rsBlockData) return 1;
        return this.dataCapacity / this.rsBlockData;
    };
    SymbolInfo.prototype.getDataCapacity = function() {
        return this.dataCapacity;
    };
    SymbolInfo.prototype.getErrorCodewords = function() {
        return this.errorCodewords;
    };
    SymbolInfo.prototype.getDataLengthForInterleavedBlock = function(index) {
        return this.rsBlockData;
    };
    SymbolInfo.prototype.getErrorLengthForInterleavedBlock = function(index) {
        return this.rsBlockError;
    };
    return SymbolInfo;
}();
const __TURBOPACK__default__export__ = SymbolInfo;
var DataMatrixSymbolInfo144 = function(_super) {
    __extends(DataMatrixSymbolInfo144, _super);
    function DataMatrixSymbolInfo144() {
        return _super.call(this, false, 1558, 620, 22, 22, 36, -1, 62) || this;
    }
    DataMatrixSymbolInfo144.prototype.getInterleavedBlockCount = function() {
        return 10;
    };
    DataMatrixSymbolInfo144.prototype.getDataLengthForInterleavedBlock = function(index) {
        return index <= 8 ? 156 : 155;
    };
    return DataMatrixSymbolInfo144;
}(SymbolInfo);
var PROD_SYMBOLS = [
    new SymbolInfo(false, 3, 5, 8, 8, 1),
    new SymbolInfo(false, 5, 7, 10, 10, 1),
    /*rect*/ new SymbolInfo(true, 5, 7, 16, 6, 1),
    new SymbolInfo(false, 8, 10, 12, 12, 1),
    /*rect*/ new SymbolInfo(true, 10, 11, 14, 6, 2),
    new SymbolInfo(false, 12, 12, 14, 14, 1),
    /*rect*/ new SymbolInfo(true, 16, 14, 24, 10, 1),
    new SymbolInfo(false, 18, 14, 16, 16, 1),
    new SymbolInfo(false, 22, 18, 18, 18, 1),
    /*rect*/ new SymbolInfo(true, 22, 18, 16, 10, 2),
    new SymbolInfo(false, 30, 20, 20, 20, 1),
    /*rect*/ new SymbolInfo(true, 32, 24, 16, 14, 2),
    new SymbolInfo(false, 36, 24, 22, 22, 1),
    new SymbolInfo(false, 44, 28, 24, 24, 1),
    /*rect*/ new SymbolInfo(true, 49, 28, 22, 14, 2),
    new SymbolInfo(false, 62, 36, 14, 14, 4),
    new SymbolInfo(false, 86, 42, 16, 16, 4),
    new SymbolInfo(false, 114, 48, 18, 18, 4),
    new SymbolInfo(false, 144, 56, 20, 20, 4),
    new SymbolInfo(false, 174, 68, 22, 22, 4),
    new SymbolInfo(false, 204, 84, 24, 24, 4, 102, 42),
    new SymbolInfo(false, 280, 112, 14, 14, 16, 140, 56),
    new SymbolInfo(false, 368, 144, 16, 16, 16, 92, 36),
    new SymbolInfo(false, 456, 192, 18, 18, 16, 114, 48),
    new SymbolInfo(false, 576, 224, 20, 20, 16, 144, 56),
    new SymbolInfo(false, 696, 272, 22, 22, 16, 174, 68),
    new SymbolInfo(false, 816, 336, 24, 24, 16, 136, 56),
    new SymbolInfo(false, 1050, 408, 18, 18, 36, 175, 68),
    new SymbolInfo(false, 1304, 496, 20, 20, 36, 163, 62),
    new DataMatrixSymbolInfo144()
];
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EncoderContext.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "EncoderContext": ()=>EncoderContext
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
;
;
var EncoderContext = function() {
    function EncoderContext(msg) {
        this.msg = msg;
        this.pos = 0;
        this.skipAtEnd = 0;
        // From this point on Strings are not Unicode anymore!
        var msgBinary = msg.split('').map(function(c) {
            return c.charCodeAt(0);
        });
        var sb = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        for(var i = 0, c = msgBinary.length; i < c; i++){
            var ch = String.fromCharCode(msgBinary[i] & 0xff);
            if (ch === '?' && msg.charAt(i) !== '?') {
                throw new Error('Message contains characters outside ISO-8859-1 encoding.');
            }
            sb.append(ch);
        }
        this.msg = sb.toString(); // Not Unicode here!
        this.shape = 0 /* FORCE_NONE */ ;
        this.codewords = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.newEncoding = -1;
    }
    EncoderContext.prototype.setSymbolShape = function(shape) {
        this.shape = shape;
    };
    EncoderContext.prototype.setSizeConstraints = function(minSize, maxSize) {
        this.minSize = minSize;
        this.maxSize = maxSize;
    };
    EncoderContext.prototype.getMessage = function() {
        return this.msg;
    };
    EncoderContext.prototype.setSkipAtEnd = function(count) {
        this.skipAtEnd = count;
    };
    EncoderContext.prototype.getCurrentChar = function() {
        return this.msg.charCodeAt(this.pos);
    };
    EncoderContext.prototype.getCurrent = function() {
        return this.msg.charCodeAt(this.pos);
    };
    EncoderContext.prototype.getCodewords = function() {
        return this.codewords;
    };
    EncoderContext.prototype.writeCodewords = function(codewords) {
        this.codewords.append(codewords);
    };
    EncoderContext.prototype.writeCodeword = function(codeword) {
        this.codewords.append(codeword);
    };
    EncoderContext.prototype.getCodewordCount = function() {
        return this.codewords.length();
    };
    EncoderContext.prototype.getNewEncoding = function() {
        return this.newEncoding;
    };
    EncoderContext.prototype.signalEncoderChange = function(encoding) {
        this.newEncoding = encoding;
    };
    EncoderContext.prototype.resetEncoderSignal = function() {
        this.newEncoding = -1;
    };
    EncoderContext.prototype.hasMoreCharacters = function() {
        return this.pos < this.getTotalMessageCharCount();
    };
    EncoderContext.prototype.getTotalMessageCharCount = function() {
        return this.msg.length - this.skipAtEnd;
    };
    EncoderContext.prototype.getRemainingCharacters = function() {
        return this.getTotalMessageCharCount() - this.pos;
    };
    EncoderContext.prototype.getSymbolInfo = function() {
        return this.symbolInfo;
    };
    EncoderContext.prototype.updateSymbolInfo = function(len) {
        if (len === void 0) {
            len = this.getCodewordCount();
        }
        if (this.symbolInfo == null || len > this.symbolInfo.getDataCapacity()) {
            this.symbolInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookup(len, this.shape, this.minSize, this.maxSize, true);
        }
    };
    EncoderContext.prototype.resetSymbolInfo = function() {
        this.symbolInfo = null;
    };
    return EncoderContext;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/X12Encoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "X12Encoder": ()=>X12Encoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
var X12Encoder = function(_super) {
    __extends(X12Encoder, _super);
    function X12Encoder() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    X12Encoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"];
    };
    X12Encoder.prototype.encode = function(context) {
        // step C
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        while(context.hasMoreCharacters()){
            var c = context.getCurrentChar();
            context.pos++;
            this.encodeChar(c, buffer);
            var count = buffer.length();
            if (count % 3 === 0) {
                this.writeNextTriplet(context, buffer);
                var newMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());
                if (newMode !== this.getEncodingMode()) {
                    // Return to ASCII encodation, which will actually handle latch to new mode
                    context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
                    break;
                }
            }
        }
        this.handleEOD(context, buffer);
    };
    X12Encoder.prototype.encodeChar = function(c, sb) {
        switch(c){
            case 13:
                sb.append(0);
                break;
            case '*'.charCodeAt(0):
                sb.append(1);
                break;
            case '>'.charCodeAt(0):
                sb.append(2);
                break;
            case ' '.charCodeAt(0):
                sb.append(3);
                break;
            default:
                if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {
                    sb.append(c - 48 + 4);
                } else if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {
                    sb.append(c - 65 + 14);
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].illegalCharacter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharAt(c));
                }
                break;
        }
        return 1;
    };
    X12Encoder.prototype.handleEOD = function(context, buffer) {
        context.updateSymbolInfo();
        var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();
        var count = buffer.length();
        context.pos -= count;
        if (context.getRemainingCharacters() > 1 || available > 1 || context.getRemainingCharacters() !== available) {
            context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_UNLATCH"]);
        }
        if (context.getNewEncoding() < 0) {
            context.signalEncoderChange(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]);
        }
    };
    return X12Encoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40Encoder"]);
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/TextEncoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TextEncoder": ()=>TextEncoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var TextEncoder = function(_super) {
    __extends(TextEncoder, _super);
    function TextEncoder() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TextEncoder.prototype.getEncodingMode = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"];
    };
    TextEncoder.prototype.encodeChar = function(c, sb) {
        if (c === ' '.charCodeAt(0)) {
            sb.append(3);
            return 1;
        }
        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {
            sb.append(c - 48 + 4);
            return 1;
        }
        if (c >= 'a'.charCodeAt(0) && c <= 'z'.charCodeAt(0)) {
            sb.append(c - 97 + 14);
            return 1;
        }
        if (c < ' '.charCodeAt(0)) {
            sb.append(0); // Shift 1 Set
            sb.append(c);
            return 2;
        }
        if (c <= '/'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 33);
            return 2;
        }
        if (c <= '@'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 58 + 15);
            return 2;
        }
        if (c >= '['.charCodeAt(0) && c <= '_'.charCodeAt(0)) {
            sb.append(1); // Shift 2 Set
            sb.append(c - 91 + 22);
            return 2;
        }
        if (c === '`'.charCodeAt(0)) {
            sb.append(2); // Shift 3 Set
            sb.append(0); // '`' - 96 == 0
            return 2;
        }
        if (c <= 'Z'.charCodeAt(0)) {
            sb.append(2); // Shift 3 Set
            sb.append(c - 65 + 1);
            return 2;
        }
        if (c <= 127) {
            sb.append(2); // Shift 3 Set
            sb.append(c - 123 + 27);
            return 2;
        }
        sb.append(1 + "\u001E"); // Shift 2, Upper Shift
        var len = 2;
        len += this.encodeChar(c - 128, sb);
        return len;
    };
    return TextEncoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40Encoder"]);
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// tslint:disable-next-line:no-circular-imports
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ASCIIEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$Base256Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EdifactEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EncoderContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EncoderContext.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$X12Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/X12Encoder.js [app-ssr] (ecmascript)");
// tslint:disable-next-line:no-circular-imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$TextEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/TextEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
/**
 * DataMatrix ECC 200 data encoder following the algorithm described in ISO/IEC 16022:200(E) in
 * annex S.
 */ var HighLevelEncoder = function() {
    function HighLevelEncoder() {}
    HighLevelEncoder.randomize253State = function(codewordPosition) {
        var pseudoRandom = 149 * codewordPosition % 253 + 1;
        var tempVariable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PAD"] + pseudoRandom;
        return tempVariable <= 254 ? tempVariable : tempVariable - 254;
    };
    /**
     * Performs message encoding of a DataMatrix message using the algorithm described in annex P
     * of ISO/IEC 16022:2000(E).
     *
     * @param msg     the message
     * @param shape   requested shape. May be {@code SymbolShapeHint.FORCE_NONE},
     *                {@code SymbolShapeHint.FORCE_SQUARE} or {@code SymbolShapeHint.FORCE_RECTANGLE}.
     * @param minSize the minimum symbol size constraint or null for no constraint
     * @param maxSize the maximum symbol size constraint or null for no constraint
     * @param forceC40 enforce C40 encoding
     * @return the encoded message (the char values range from 0 to 255)
     */ HighLevelEncoder.encodeHighLevel = function(msg, shape, minSize, maxSize, forceC40) {
        if (shape === void 0) {
            shape = 0 /* FORCE_NONE */ ;
        }
        if (minSize === void 0) {
            minSize = null;
        }
        if (maxSize === void 0) {
            maxSize = null;
        }
        if (forceC40 === void 0) {
            forceC40 = false;
        }
        // the codewords 0..255 are encoded as Unicode characters
        var c40Encoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40Encoder"]();
        var encoders = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ASCIIEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCIIEncoder"](),
            c40Encoder,
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$TextEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextEncoder"](),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$X12Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12Encoder"](),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EdifactEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EdifactEncoder"](),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$Base256Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Base256Encoder"]()
        ];
        var context = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EncoderContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EncoderContext"](msg);
        context.setSymbolShape(shape);
        context.setSizeConstraints(minSize, maxSize);
        if (msg.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_05_HEADER"]) && msg.endsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_TRAILER"])) {
            context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_05"]);
            context.setSkipAtEnd(2);
            context.pos += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_05_HEADER"].length;
        } else if (msg.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_06_HEADER"]) && msg.endsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_TRAILER"])) {
            context.writeCodeword(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_06"]);
            context.setSkipAtEnd(2);
            context.pos += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_06_HEADER"].length;
        }
        var encodingMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]; // Default mode
        if (forceC40) {
            c40Encoder.encodeMaximal(context);
            encodingMode = context.getNewEncoding();
            context.resetEncoderSignal();
        }
        while(context.hasMoreCharacters()){
            encoders[encodingMode].encode(context);
            if (context.getNewEncoding() >= 0) {
                encodingMode = context.getNewEncoding();
                context.resetEncoderSignal();
            }
        }
        var len = context.getCodewordCount();
        context.updateSymbolInfo();
        var capacity = context.getSymbolInfo().getDataCapacity();
        if (len < capacity && encodingMode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"] && encodingMode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"] && encodingMode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]) {
            context.writeCodeword('\u00fe'); // Unlatch (254)
        }
        // Padding
        var codewords = context.getCodewords();
        if (codewords.length() < capacity) {
            codewords.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PAD"]);
        }
        while(codewords.length() < capacity){
            codewords.append(this.randomize253State(codewords.length() + 1));
        }
        return context.getCodewords().toString();
    };
    HighLevelEncoder.lookAheadTest = function(msg, startpos, currentMode) {
        var newMode = this.lookAheadTestIntern(msg, startpos, currentMode);
        if (currentMode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"] && newMode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]) {
            var endpos = Math.min(startpos + 3, msg.length);
            for(var i = startpos; i < endpos; i++){
                if (!this.isNativeX12(msg.charCodeAt(i))) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"];
                }
            }
        } else if (currentMode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"] && newMode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]) {
            var endpos = Math.min(startpos + 4, msg.length);
            for(var i = startpos; i < endpos; i++){
                if (!this.isNativeEDIFACT(msg.charCodeAt(i))) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"];
                }
            }
        }
        return newMode;
    };
    HighLevelEncoder.lookAheadTestIntern = function(msg, startpos, currentMode) {
        if (startpos >= msg.length) {
            return currentMode;
        }
        var charCounts;
        // step J
        if (currentMode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]) {
            charCounts = [
                0,
                1,
                1,
                1,
                1,
                1.25
            ];
        } else {
            charCounts = [
                1,
                2,
                2,
                2,
                2,
                2.25
            ];
            charCounts[currentMode] = 0;
        }
        var charsProcessed = 0;
        var mins = new Uint8Array(6);
        var intCharCounts = [];
        while(true){
            // step K
            if (startpos + charsProcessed === msg.length) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(mins, 0);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(intCharCounts, 0);
                var min = this.findMinimums(charCounts, intCharCounts, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MAX_VALUE, mins);
                var minCount = this.getMinimumCount(mins);
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] === min) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"];
                }
                if (minCount === 1) {
                    if (mins[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]] > 0) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"];
                    }
                    if (mins[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]] > 0) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"];
                    }
                    if (mins[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]] > 0) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"];
                    }
                    if (mins[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]] > 0) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"];
                    }
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"];
            }
            var c = msg.charCodeAt(startpos + charsProcessed);
            charsProcessed++;
            // step L
            if (this.isDigit(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] += 0.5;
            } else if (this.isExtendedASCII(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] = Math.ceil(charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]]);
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] += 2.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] = Math.ceil(charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]]);
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]]++;
            }
            // step M
            if (this.isNativeC40(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] += 2.0 / 3.0;
            } else if (this.isExtendedASCII(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] += 8.0 / 3.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] += 4.0 / 3.0;
            }
            // step N
            if (this.isNativeText(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]] += 2.0 / 3.0;
            } else if (this.isExtendedASCII(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]] += 8.0 / 3.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]] += 4.0 / 3.0;
            }
            // step O
            if (this.isNativeX12(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]] += 2.0 / 3.0;
            } else if (this.isExtendedASCII(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]] += 13.0 / 3.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]] += 10.0 / 3.0;
            }
            // step P
            if (this.isNativeEDIFACT(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]] += 3.0 / 4.0;
            } else if (this.isExtendedASCII(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]] += 17.0 / 4.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]] += 13.0 / 4.0;
            }
            // step Q
            if (this.isSpecialB256(c)) {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]] += 4.0;
            } else {
                charCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]]++;
            }
            // step R
            if (charsProcessed >= 4) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(mins, 0);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(intCharCounts, 0);
                this.findMinimums(charCounts, intCharCounts, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MAX_VALUE, mins);
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"];
                }
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]] < intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]] || intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]] + 1 < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"];
                }
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]] + 1 < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"];
                }
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]] + 1 < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"];
                }
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]] + 1 < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"];
                }
                if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] + 1 < this.min(intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ASCII_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BASE256_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EDIFACT_ENCODATION"]], intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TEXT_ENCODATION"]])) {
                    if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] < intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]]) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"];
                    }
                    if (intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"]] === intCharCounts[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"]]) {
                        var p = startpos + charsProcessed + 1;
                        while(p < msg.length){
                            var tc = msg.charCodeAt(p);
                            if (this.isX12TermSep(tc)) {
                                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["X12_ENCODATION"];
                            }
                            if (!this.isNativeX12(tc)) {
                                break;
                            }
                            p++;
                        }
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["C40_ENCODATION"];
                    }
                }
            }
        }
    };
    HighLevelEncoder.min = function(f1, f2, f3, f4, f5) {
        var val = Math.min(f1, Math.min(f2, Math.min(f3, f4)));
        if (f5 === undefined) {
            return val;
        } else {
            return Math.min(val, f5);
        }
    };
    HighLevelEncoder.findMinimums = function(charCounts, intCharCounts, min, mins) {
        for(var i = 0; i < 6; i++){
            var current = intCharCounts[i] = Math.ceil(charCounts[i]);
            if (min > current) {
                min = current;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fill(mins, 0);
            }
            if (min === current) {
                mins[i] = mins[i] + 1;
            }
        }
        return min;
    };
    HighLevelEncoder.getMinimumCount = function(mins) {
        var minCount = 0;
        for(var i = 0; i < 6; i++){
            minCount += mins[i];
        }
        return minCount || 0;
    };
    HighLevelEncoder.isDigit = function(ch) {
        return ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0);
    };
    HighLevelEncoder.isExtendedASCII = function(ch) {
        return ch >= 128 && ch <= 255;
    };
    HighLevelEncoder.isNativeC40 = function(ch) {
        return ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0);
    };
    HighLevelEncoder.isNativeText = function(ch) {
        return ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'a'.charCodeAt(0) && ch <= 'z'.charCodeAt(0);
    };
    HighLevelEncoder.isNativeX12 = function(ch) {
        return this.isX12TermSep(ch) || ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0);
    };
    HighLevelEncoder.isX12TermSep = function(ch) {
        return ch === 13 || // CR
        ch === '*'.charCodeAt(0) || ch === '>'.charCodeAt(0);
    };
    HighLevelEncoder.isNativeEDIFACT = function(ch) {
        return ch >= ' '.charCodeAt(0) && ch <= '^'.charCodeAt(0);
    };
    HighLevelEncoder.isSpecialB256 = function(ch) {
        return false; // TODO NOT IMPLEMENTED YET!!!
    };
    /**
     * Determines the number of consecutive characters that are encodable using numeric compaction.
     *
     * @param msg      the message
     * @param startpos the start position within the message
     * @return the requested character count
     */ HighLevelEncoder.determineConsecutiveDigitCount = function(msg, startpos) {
        if (startpos === void 0) {
            startpos = 0;
        }
        var len = msg.length;
        var idx = startpos;
        while(idx < len && this.isDigit(msg.charCodeAt(idx))){
            idx++;
        }
        return idx - startpos;
    };
    HighLevelEncoder.illegalCharacter = function(singleCharacter) {
        var hex = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].toHexString(singleCharacter.charCodeAt(0));
        hex = '0000'.substring(0, 4 - hex.length) + hex;
        throw new Error('Illegal character: ' + singleCharacter + ' (0x' + hex + ')');
    };
    return HighLevelEncoder;
}();
const __TURBOPACK__default__export__ = HighLevelEncoder;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MinimalEncoder": ()=>MinimalEncoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$MinimalECIInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/MinimalECIInput.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spread = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__spread || function() {
    for(var ar = [], i = 0; i < arguments.length; i++)ar = ar.concat(__read(arguments[i]));
    return ar;
};
;
;
;
;
var Mode;
(function(Mode) {
    Mode[Mode["ASCII"] = 0] = "ASCII";
    Mode[Mode["C40"] = 1] = "C40";
    Mode[Mode["TEXT"] = 2] = "TEXT";
    Mode[Mode["X12"] = 3] = "X12";
    Mode[Mode["EDF"] = 4] = "EDF";
    Mode[Mode["B256"] = 5] = "B256";
})(Mode || (Mode = {}));
var C40_SHIFT2_CHARS = [
    '!',
    '"',
    '#',
    '$',
    '%',
    '&',
    "'",
    '(',
    ')',
    '*',
    '+',
    ',',
    '-',
    '.',
    '/',
    ':',
    ';',
    '<',
    '=',
    '>',
    '?',
    '@',
    '[',
    '\\',
    ']',
    '^',
    '_'
];
var MinimalEncoder = function() {
    function MinimalEncoder() {}
    MinimalEncoder.isExtendedASCII = function(ch, fnc1) {
        return ch !== fnc1 && ch >= 128 && ch <= 255;
    };
    MinimalEncoder.isInC40Shift1Set = function(ch) {
        return ch <= 31;
    };
    MinimalEncoder.isInC40Shift2Set = function(ch, fnc1) {
        var e_1, _a;
        try {
            for(var C40_SHIFT2_CHARS_1 = __values(C40_SHIFT2_CHARS), C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next(); !C40_SHIFT2_CHARS_1_1.done; C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next()){
                var c40Shift2Char = C40_SHIFT2_CHARS_1_1.value;
                if (c40Shift2Char.charCodeAt(0) === ch) {
                    return true;
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (C40_SHIFT2_CHARS_1_1 && !C40_SHIFT2_CHARS_1_1.done && (_a = C40_SHIFT2_CHARS_1.return)) _a.call(C40_SHIFT2_CHARS_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return ch === fnc1;
    };
    MinimalEncoder.isInTextShift1Set = function(ch) {
        return this.isInC40Shift1Set(ch);
    };
    MinimalEncoder.isInTextShift2Set = function(ch, fnc1) {
        return this.isInC40Shift2Set(ch, fnc1);
    };
    /**
     * Performs message encoding of a DataMatrix message
     *
     * @param msg the message
     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm
     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority
     *   charset to encode any character in the input that can be encoded by it if the charset is among the
     *   supported charsets.
     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1
     *   bar code. If the value is not -1 then a FNC1 is also prepended.
     * @param shape requested shape.
     * @return the encoded message (the char values range from 0 to 255)
     */ MinimalEncoder.encodeHighLevel = function(msg, priorityCharset, fnc1, shape) {
        if (priorityCharset === void 0) {
            priorityCharset = null;
        }
        if (fnc1 === void 0) {
            fnc1 = -1;
        }
        if (shape === void 0) {
            shape = 0 /* FORCE_NONE */ ;
        }
        var macroId = 0;
        if (msg.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_05_HEADER"]) && msg.endsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_TRAILER"])) {
            macroId = 5;
            msg = msg.substring(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_05_HEADER"].length, msg.length - 2);
        } else if (msg.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_06_HEADER"]) && msg.endsWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_TRAILER"])) {
            macroId = 6;
            msg = msg.substring(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MACRO_06_HEADER"].length, msg.length - 2);
        }
        return decodeURIComponent(escape(String.fromCharCode.apply(String, __spread(this.encode(msg, priorityCharset, fnc1, shape, macroId)))));
    };
    /**
     * Encodes input minimally and returns an array of the codewords
     *
     * @param input The string to encode
     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm
     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority
     *   charset to encode any character in the input that can be encoded by it if the charset is among the
     *   supported charsets.
     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1
     *   bar code. If the value is not -1 then a FNC1 is also prepended.
     * @param shape requested shape.
     * @param macroId Prepends the specified macro function in case that a value of 5 or 6 is specified.
     * @return An array of bytes representing the codewords of a minimal encoding.
     */ MinimalEncoder.encode = function(input, priorityCharset, fnc1, shape, macroId) {
        return this.encodeMinimally(new Input(input, priorityCharset, fnc1, shape, macroId)).getBytes();
    };
    MinimalEncoder.addEdge = function(edges, edge) {
        var vertexIndex = edge.fromPosition + edge.characterLength;
        if (edges[vertexIndex][edge.getEndMode()] === null || edges[vertexIndex][edge.getEndMode()].cachedTotalSize > edge.cachedTotalSize) {
            edges[vertexIndex][edge.getEndMode()] = edge;
        }
    };
    /** @return the number of words in which the string starting at from can be encoded in c40 or text mode.
     *  The number of characters encoded is returned in characterLength.
     *  The number of characters encoded is also minimal in the sense that the algorithm stops as soon
     *  as a character encoding fills a C40 word competely (three C40 values). An exception is at the
     *  end of the string where two C40 values are allowed (according to the spec the third c40 value
     *  is filled  with 0 (Shift 1) in this case).
     */ MinimalEncoder.getNumberOfC40Words = function(input, from, c40, characterLength) {
        var thirdsCount = 0;
        for(var i = from; i < input.length(); i++){
            if (input.isECI(i)) {
                characterLength[0] = 0;
                return 0;
            }
            var ci = input.charAt(i);
            if (c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeC40(ci) || !c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeText(ci)) {
                thirdsCount++; // native
            } else if (!MinimalEncoder.isExtendedASCII(ci, input.getFNC1Character())) {
                thirdsCount += 2; // shift
            } else {
                var asciiValue = ci & 0xff;
                if (asciiValue >= 128 && (c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeC40(asciiValue - 128) || !c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeText(asciiValue - 128))) {
                    thirdsCount += 3; // shift, Upper shift
                } else {
                    thirdsCount += 4; // shift, Upper shift, shift
                }
            }
            if (thirdsCount % 3 === 0 || (thirdsCount - 2) % 3 === 0 && i + 1 === input.length()) {
                characterLength[0] = i - from + 1;
                return Math.ceil(thirdsCount / 3.0);
            }
        }
        characterLength[0] = 0;
        return 0;
    };
    MinimalEncoder.addEdges = function(input, edges, from, previous) {
        var e_2, _a;
        if (input.isECI(from)) {
            this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));
            return;
        }
        var ch = input.charAt(from);
        if (previous === null || previous.getEndMode() !== Mode.EDF) {
            // not possible to unlatch a full EDF edge to something
            // else
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(ch) && input.haveNCharacters(from, 2) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(input.charAt(from + 1))) {
                // two digits ASCII encoded
                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 2, previous));
            } else {
                // one ASCII encoded character or an extended character via Upper Shift
                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));
            }
            var modes = [
                Mode.C40,
                Mode.TEXT
            ];
            try {
                for(var modes_1 = __values(modes), modes_1_1 = modes_1.next(); !modes_1_1.done; modes_1_1 = modes_1.next()){
                    var mode = modes_1_1.value;
                    var characterLength = [];
                    if (MinimalEncoder.getNumberOfC40Words(input, from, mode === Mode.C40, characterLength) > 0) {
                        this.addEdge(edges, new Edge(input, mode, from, characterLength[0], previous));
                    }
                }
            } catch (e_2_1) {
                e_2 = {
                    error: e_2_1
                };
            } finally{
                try {
                    if (modes_1_1 && !modes_1_1.done && (_a = modes_1.return)) _a.call(modes_1);
                } finally{
                    if (e_2) throw e_2.error;
                }
            }
            if (input.haveNCharacters(from, 3) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeX12(input.charAt(from)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeX12(input.charAt(from + 1)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeX12(input.charAt(from + 2))) {
                this.addEdge(edges, new Edge(input, Mode.X12, from, 3, previous));
            }
            this.addEdge(edges, new Edge(input, Mode.B256, from, 1, previous));
        }
        // We create 4 EDF edges,  with 1, 2 3 or 4 characters length. The fourth normally doesn't have a latch to ASCII
        // unless it is 2 characters away from the end of the input.
        var i;
        for(i = 0; i < 3; i++){
            var pos = from + i;
            if (input.haveNCharacters(pos, 1) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeEDIFACT(input.charAt(pos))) {
                this.addEdge(edges, new Edge(input, Mode.EDF, from, i + 1, previous));
            } else {
                break;
            }
        }
        if (i === 3 && input.haveNCharacters(from, 4) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeEDIFACT(input.charAt(from + 3))) {
            this.addEdge(edges, new Edge(input, Mode.EDF, from, 4, previous));
        }
    };
    MinimalEncoder.encodeMinimally = function(input) {
        /* The minimal encoding is computed by Dijkstra. The acyclic graph is modeled as follows:
         * A vertex represents a combination of a position in the input and an encoding mode where position 0
         * denotes the position left of the first character, 1 the position left of the second character and so on.
         * Likewise the end vertices are located after the last character at position input.length().
         * For any position there might be up to six vertices, one for each of the encoding types ASCII, C40, TEXT, X12,
         * EDF and B256.
         *
         * As an example consider the input string "ABC123" then at position 0 there is only one vertex with the default
         * ASCII encodation. At position 3 there might be vertices for the types ASCII, C40, X12, EDF and B256.
         *
         * An edge leading to such a vertex encodes one or more of the characters left of the position that the vertex
         * represents. It encodes the characters in the encoding mode of the vertex that it ends on. In other words,
         * all edges leading to a particular vertex encode the same characters (the length of the suffix can vary) using the same
         * encoding mode.
         * As an example consider the input string "ABC123" and the vertex (4,EDF). Possible edges leading to this vertex
         * are:
         *   (0,ASCII)  --EDF(ABC1)--> (4,EDF)
         *   (1,ASCII)  --EDF(BC1)-->  (4,EDF)
         *   (1,B256)   --EDF(BC1)-->  (4,EDF)
         *   (1,EDF)    --EDF(BC1)-->  (4,EDF)
         *   (2,ASCII)  --EDF(C1)-->   (4,EDF)
         *   (2,B256)   --EDF(C1)-->   (4,EDF)
         *   (2,EDF)    --EDF(C1)-->   (4,EDF)
         *   (3,ASCII)  --EDF(1)-->    (4,EDF)
         *   (3,B256)   --EDF(1)-->    (4,EDF)
         *   (3,EDF)    --EDF(1)-->    (4,EDF)
         *   (3,C40)    --EDF(1)-->    (4,EDF)
         *   (3,X12)    --EDF(1)-->    (4,EDF)
         *
         * The edges leading to a vertex are stored in such a way that there is a fast way to enumerate the edges ending
         * on a particular vertex.
         *
         * The algorithm processes the vertices in order of their position thereby performing the following:
         *
         * For every vertex at position i the algorithm enumerates the edges ending on the vertex and removes all but the
         * shortest from that list.
         * Then it processes the vertices for the position i+1. If i+1 == input.length() then the algorithm ends
         * and chooses the the edge with the smallest size from any of the edges leading to vertices at this position.
         * Otherwise the algorithm computes all possible outgoing edges for the vertices at the position i+1
         *
         * Examples:
         * The process is illustrated by showing the graph (edges) after each iteration from left to right over the input:
         * An edge is drawn as follows "(" + fromVertex + ") -- " + encodingMode + "(" + encodedInput + ") (" +
         * accumulatedSize + ") --> (" + toVertex + ")"
         *
         * Example 1 encoding the string "ABCDEFG":
         *
         *
         * Situation after adding edges to the start vertex (0,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF)
         * (0,ASCII) C40(ABC) (3) --> (3,C40)
         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)
         * (0,ASCII) X12(ABC) (3) --> (3,X12)
         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)
         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)
         *
         * Situation after adding edges to vertices at position 1
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF)
         * (0,ASCII) C40(ABC) (3) --> (3,C40)
         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)
         * (0,ASCII) X12(ABC) (3) --> (3,X12)
         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)
         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) B256(B) (4) --> (2,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) ASCII(B) (4) --> (2,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)
         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)
         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)
         *
         * Edge "(1,ASCII) ASCII(B) (2) --> (2,ASCII)" is minimal for the vertex (2,ASCII) so that edge "(1,B256) ASCII(B) (4) --> (2,ASCII)" is removed.
         * Edge "(1,B256) B256(B) (3) --> (2,B256)" is minimal for the vertext (2,B256) so that the edge "(1,ASCII) B256(B) (4) --> (2,B256)" is removed.
         *
         * Situation after adding edges to vertices at position 2
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF)
         * (0,ASCII) C40(ABC) (3) --> (3,C40)
         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)
         * (0,ASCII) X12(ABC) (3) --> (3,X12)
         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)
         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)
         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)
         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) ASCII(C) (5) --> (3,ASCII)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) B256(C) (6) --> (3,B256)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CD) (7) --> (4,EDF)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) C40(CDE) (6) --> (5,C40)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) TEXT(CDE) (8) --> (5,TEXT)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) X12(CDE) (6) --> (5,X12)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDE) (7) --> (5,EDF)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDEF) (7) --> (6,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) B256(C) (5) --> (3,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CD) (6) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDE) (6) --> (5,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) ASCII(C) (4) --> (3,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CD) (6) --> (4,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) C40(CDE) (5) --> (5,C40)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) TEXT(CDE) (7) --> (5,TEXT)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) X12(CDE) (5) --> (5,X12)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDE) (6) --> (5,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDEF) (6) --> (6,EDF)
         *
         * Edge "(2,ASCII) ASCII(C) (3) --> (3,ASCII)" is minimal for the vertex (3,ASCII) so that edges "(2,EDF) ASCII(C) (5) --> (3,ASCII)"
         * and "(2,B256) ASCII(C) (4) --> (3,ASCII)" can be removed.
         * Edge "(0,ASCII) EDF(ABC) (4) --> (3,EDF)" is minimal for the vertex (3,EDF) so that edges "(1,ASCII) EDF(BC) (5) --> (3,EDF)"
         * and "(1,B256) EDF(BC) (6) --> (3,EDF)" can be removed.
         * Edge "(2,B256) B256(C) (4) --> (3,B256)" is minimal for the vertex (3,B256) so that edges "(2,ASCII) B256(C) (5) --> (3,B256)"
         * and "(2,EDF) B256(C) (6) --> (3,B256)" can be removed.
         *
         * This continues for vertices 3 thru 7
         *
         * Situation after adding edges to vertices at position 7
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256)
         * (0,ASCII) EDF(AB) (4) --> (2,EDF)
         * (0,ASCII) C40(ABC) (3) --> (3,C40)
         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)
         * (0,ASCII) X12(ABC) (3) --> (3,X12)
         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)
         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)
         * (0,ASCII) C40(ABC) (3) --> (3,C40) C40(DEF) (5) --> (6,C40)
         * (0,ASCII) X12(ABC) (3) --> (3,X12) X12(DEF) (5) --> (6,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40) C40(EFG) (6) --> (7,C40)    //Solution 1
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12) X12(EFG) (6) --> (7,X12)    //Solution 2
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) TEXT(DEF) (8) --> (6,TEXT)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) EDF(DEFG) (7) --> (7,EDF)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) TEXT(EFG) (9) --> (7,TEXT)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256)
         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII) ASCII(G) (7) --> (7,ASCII)
         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256) B256(G) (8) --> (7,B256)
         *
         * Hence a minimal encoding of "ABCDEFG" is either ASCII(A),C40(BCDEFG) or ASCII(A), X12(BCDEFG) with a size of 5 bytes.
         */ var inputLength = input.length();
        // Array that represents vertices. There is a vertex for every character and mode.
        // The last dimension in the array below encodes the 6 modes ASCII, C40, TEXT, X12, EDF and B256
        var edges = Array(inputLength + 1).fill(null).map(function() {
            return Array(6).fill(0);
        });
        this.addEdges(input, edges, 0, null);
        for(var i = 1; i <= inputLength; i++){
            for(var j = 0; j < 6; j++){
                if (edges[i][j] !== null && i < inputLength) {
                    this.addEdges(input, edges, i, edges[i][j]);
                }
            }
            // optimize memory by removing edges that have been passed.
            for(var j = 0; j < 6; j++){
                edges[i - 1][j] = null;
            }
        }
        var minimalJ = -1;
        var minimalSize = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MAX_VALUE;
        for(var j = 0; j < 6; j++){
            if (edges[inputLength][j] !== null) {
                var edge = edges[inputLength][j];
                var size = j >= 1 && j <= 3 ? edge.cachedTotalSize + 1 : edge.cachedTotalSize; // C40, TEXT and X12 need an
                // extra unlatch at the end
                if (size < minimalSize) {
                    minimalSize = size;
                    minimalJ = j;
                }
            }
        }
        if (minimalJ < 0) {
            throw new Error('Failed to encode "' + input + '"');
        }
        return new Result(edges[inputLength][minimalJ]);
    };
    return MinimalEncoder;
}();
;
var Result = function() {
    function Result(solution) {
        var input = solution.input;
        var size = 0;
        var bytesAL = [];
        var randomizePostfixLength = [];
        var randomizeLengths = [];
        if ((solution.mode === Mode.C40 || solution.mode === Mode.TEXT || solution.mode === Mode.X12) && solution.getEndMode() !== Mode.ASCII) {
            size += this.prepend(Edge.getBytes(254), bytesAL);
        }
        var current = solution;
        while(current !== null){
            size += this.prepend(current.getDataBytes(), bytesAL);
            if (current.previous === null || current.getPreviousStartMode() !== current.getMode()) {
                if (current.getMode() === Mode.B256) {
                    if (size <= 249) {
                        bytesAL.unshift(size);
                        size++;
                    } else {
                        bytesAL.unshift(size % 250);
                        bytesAL.unshift(size / 250 + 249);
                        size += 2;
                    }
                    randomizePostfixLength.push(bytesAL.length);
                    randomizeLengths.push(size);
                }
                this.prepend(current.getLatchBytes(), bytesAL);
                size = 0;
            }
            current = current.previous;
        }
        if (input.getMacroId() === 5) {
            size += this.prepend(Edge.getBytes(236), bytesAL);
        } else if (input.getMacroId() === 6) {
            size += this.prepend(Edge.getBytes(237), bytesAL);
        }
        if (input.getFNC1Character() > 0) {
            size += this.prepend(Edge.getBytes(232), bytesAL);
        }
        for(var i = 0; i < randomizePostfixLength.length; i++){
            this.applyRandomPattern(bytesAL, bytesAL.length - randomizePostfixLength[i], randomizeLengths[i]);
        }
        // add padding
        var capacity = solution.getMinSymbolSize(bytesAL.length);
        if (bytesAL.length < capacity) {
            bytesAL.push(129);
        }
        while(bytesAL.length < capacity){
            bytesAL.push(this.randomize253State(bytesAL.length + 1));
        }
        this.bytes = new Uint8Array(bytesAL.length);
        for(var i = 0; i < this.bytes.length; i++){
            this.bytes[i] = bytesAL[i];
        }
    }
    Result.prototype.prepend = function(bytes, into) {
        for(var i = bytes.length - 1; i >= 0; i--){
            into.unshift(bytes[i]);
        }
        return bytes.length;
    };
    Result.prototype.randomize253State = function(codewordPosition) {
        var pseudoRandom = 149 * codewordPosition % 253 + 1;
        var tempVariable = 129 + pseudoRandom;
        return tempVariable <= 254 ? tempVariable : tempVariable - 254;
    };
    Result.prototype.applyRandomPattern = function(bytesAL, startPosition, length) {
        for(var i = 0; i < length; i++){
            // See "B.1 253-state algorithm
            var Pad_codeword_position = startPosition + i;
            var Pad_codeword_value = bytesAL[Pad_codeword_position] & 0xff;
            var pseudo_random_number = 149 * (Pad_codeword_position + 1) % 255 + 1;
            var temp_variable = Pad_codeword_value + pseudo_random_number;
            bytesAL[Pad_codeword_position] = temp_variable <= 255 ? temp_variable : temp_variable - 256;
        }
    };
    Result.prototype.getBytes = function() {
        return this.bytes;
    };
    return Result;
}();
var Edge = function() {
    function Edge(input, mode, fromPosition, characterLength, previous) {
        this.input = input;
        this.mode = mode;
        this.fromPosition = fromPosition;
        this.characterLength = characterLength;
        this.previous = previous;
        this.allCodewordCapacities = [
            3,
            5,
            8,
            10,
            12,
            16,
            18,
            22,
            30,
            32,
            36,
            44,
            49,
            62,
            86,
            114,
            144,
            174,
            204,
            280,
            368,
            456,
            576,
            696,
            816,
            1050,
            1304,
            1558
        ];
        this.squareCodewordCapacities = [
            3,
            5,
            8,
            12,
            18,
            22,
            30,
            36,
            44,
            62,
            86,
            114,
            144,
            174,
            204,
            280,
            368,
            456,
            576,
            696,
            816,
            1050,
            1304,
            1558
        ];
        this.rectangularCodewordCapacities = [
            5,
            10,
            16,
            33,
            32,
            49
        ];
        if (!(fromPosition + characterLength <= input.length())) {
            throw new Error('Invalid edge');
        }
        var size = previous !== null ? previous.cachedTotalSize : 0;
        var previousMode = this.getPreviousMode();
        /*
         * Switching modes
         * ASCII -> C40: latch 230
         * ASCII -> TEXT: latch 239
         * ASCII -> X12: latch 238
         * ASCII -> EDF: latch 240
         * ASCII -> B256: latch 231
         * C40 -> ASCII: word(c1,c2,c3), 254
         * TEXT -> ASCII: word(c1,c2,c3), 254
         * X12 -> ASCII: word(c1,c2,c3), 254
         * EDIFACT -> ASCII: Unlatch character,0,0,0 or c1,Unlatch character,0,0 or c1,c2,Unlatch character,0 or
         * c1,c2,c3,Unlatch character
         * B256 -> ASCII: without latch after n bytes
         */ switch(mode){
            case Mode.ASCII:
                size++;
                if (input.isECI(fromPosition) || MinimalEncoder.isExtendedASCII(input.charAt(fromPosition), input.getFNC1Character())) {
                    size++;
                }
                if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {
                    size++; // unlatch 254 to ASCII
                }
                break;
            case Mode.B256:
                size++;
                if (previousMode !== Mode.B256) {
                    size++; // byte count
                } else if (this.getB256Size() === 250) {
                    size++; // extra byte count
                }
                if (previousMode === Mode.ASCII) {
                    size++; // latch to B256
                } else if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {
                    size += 2; // unlatch to ASCII, latch to B256
                }
                break;
            case Mode.C40:
            case Mode.TEXT:
            case Mode.X12:
                if (mode === Mode.X12) {
                    size += 2;
                } else {
                    var charLen = [];
                    size += MinimalEncoder.getNumberOfC40Words(input, fromPosition, mode === Mode.C40, charLen) * 2;
                }
                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {
                    size++; // additional byte for latch from ASCII to this mode
                } else if (previousMode !== mode && (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12)) {
                    size += 2; // unlatch 254 to ASCII followed by latch to this mode
                }
                break;
            case Mode.EDF:
                size += 3;
                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {
                    size++; // additional byte for latch from ASCII to this mode
                } else if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {
                    size += 2; // unlatch 254 to ASCII followed by latch to this mode
                }
                break;
        }
        this.cachedTotalSize = size;
    }
    // does not count beyond 250
    Edge.prototype.getB256Size = function() {
        var cnt = 0;
        var current = this;
        while(current !== null && current.mode === Mode.B256 && cnt <= 250){
            cnt++;
            current = current.previous;
        }
        return cnt;
    };
    Edge.prototype.getPreviousStartMode = function() {
        return this.previous === null ? Mode.ASCII : this.previous.mode;
    };
    Edge.prototype.getPreviousMode = function() {
        return this.previous === null ? Mode.ASCII : this.previous.getEndMode();
    };
    /** Returns Mode.ASCII in case that:
     *  - Mode is EDIFACT and characterLength is less than 4 or the remaining characters can be encoded in at most 2
     *    ASCII bytes.
     *  - Mode is C40, TEXT or X12 and the remaining characters can be encoded in at most 1 ASCII byte.
     *  Returns mode in all other cases.
     * */ Edge.prototype.getEndMode = function() {
        if (this.mode === Mode.EDF) {
            if (this.characterLength < 4) {
                return Mode.ASCII;
            }
            var lastASCII = this.getLastASCII(); // see 5.2.8.2 EDIFACT encodation Rules
            if (lastASCII > 0 && this.getCodewordsRemaining(this.cachedTotalSize + lastASCII) <= 2 - lastASCII) {
                return Mode.ASCII;
            }
        }
        if (this.mode === Mode.C40 || this.mode === Mode.TEXT || this.mode === Mode.X12) {
            // see 5.2.5.2 C40 encodation rules and 5.2.7.2 ANSI X12 encodation rules
            if (this.fromPosition + this.characterLength >= this.input.length() && this.getCodewordsRemaining(this.cachedTotalSize) === 0) {
                return Mode.ASCII;
            }
            var lastASCII = this.getLastASCII();
            if (lastASCII === 1 && this.getCodewordsRemaining(this.cachedTotalSize + 1) === 0) {
                return Mode.ASCII;
            }
        }
        return this.mode;
    };
    Edge.prototype.getMode = function() {
        return this.mode;
    };
    /** Peeks ahead and returns 1 if the postfix consists of exactly two digits, 2 if the postfix consists of exactly
     *  two consecutive digits and a non extended character or of 4 digits.
     *  Returns 0 in any other case
     **/ Edge.prototype.getLastASCII = function() {
        var length = this.input.length();
        var from = this.fromPosition + this.characterLength;
        if (length - from > 4 || from >= length) {
            return 0;
        }
        if (length - from === 1) {
            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {
                return 0;
            }
            return 1;
        }
        if (length - from === 2) {
            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character()) || MinimalEncoder.isExtendedASCII(this.input.charAt(from + 1), this.input.getFNC1Character())) {
                return 0;
            }
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 1))) {
                return 1;
            }
            return 2;
        }
        if (length - from === 3) {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 1)) && !MinimalEncoder.isExtendedASCII(this.input.charAt(from + 2), this.input.getFNC1Character())) {
                return 2;
            }
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 1)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 2)) && !MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {
                return 2;
            }
            return 0;
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 1)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 2)) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isDigit(this.input.charAt(from + 3))) {
            return 2;
        }
        return 0;
    };
    /** Returns the capacity in codewords of the smallest symbol that has enough capacity to fit the given minimal
     * number of codewords.
     **/ Edge.prototype.getMinSymbolSize = function(minimum) {
        var e_3, _a, e_4, _b, e_5, _c;
        switch(this.input.getShapeHint()){
            case 1 /* FORCE_SQUARE */ :
                try {
                    for(var _d = __values(this.squareCodewordCapacities), _e = _d.next(); !_e.done; _e = _d.next()){
                        var capacity = _e.value;
                        if (capacity >= minimum) {
                            return capacity;
                        }
                    }
                } catch (e_3_1) {
                    e_3 = {
                        error: e_3_1
                    };
                } finally{
                    try {
                        if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
                    } finally{
                        if (e_3) throw e_3.error;
                    }
                }
                break;
            case 2 /* FORCE_RECTANGLE */ :
                try {
                    for(var _f = __values(this.rectangularCodewordCapacities), _g = _f.next(); !_g.done; _g = _f.next()){
                        var capacity = _g.value;
                        if (capacity >= minimum) {
                            return capacity;
                        }
                    }
                } catch (e_4_1) {
                    e_4 = {
                        error: e_4_1
                    };
                } finally{
                    try {
                        if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
                    } finally{
                        if (e_4) throw e_4.error;
                    }
                }
                break;
        }
        try {
            for(var _h = __values(this.allCodewordCapacities), _j = _h.next(); !_j.done; _j = _h.next()){
                var capacity = _j.value;
                if (capacity >= minimum) {
                    return capacity;
                }
            }
        } catch (e_5_1) {
            e_5 = {
                error: e_5_1
            };
        } finally{
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            } finally{
                if (e_5) throw e_5.error;
            }
        }
        return this.allCodewordCapacities[this.allCodewordCapacities.length - 1];
    };
    /** Returns the remaining capacity in codewords of the smallest symbol that has enough capacity to fit the given
     * minimal number of codewords.
     **/ Edge.prototype.getCodewordsRemaining = function(minimum) {
        return this.getMinSymbolSize(minimum) - minimum;
    };
    Edge.getBytes = function(c1, c2) {
        var result = new Uint8Array(c2 ? 2 : 1);
        result[0] = c1;
        if (c2) {
            result[1] = c2;
        }
        return result;
    };
    Edge.prototype.setC40Word = function(bytes, offset, c1, c2, c3) {
        var val16 = 1600 * (c1 & 0xff) + 40 * (c2 & 0xff) + (c3 & 0xff) + 1;
        bytes[offset] = val16 / 256;
        bytes[offset + 1] = val16 % 256;
    };
    Edge.prototype.getX12Value = function(c) {
        return c === 13 ? 0 : c === 42 ? 1 : c === 62 ? 2 : c === 32 ? 3 : c >= 48 && c <= 57 ? c - 44 : c >= 65 && c <= 90 ? c - 51 : c;
    };
    Edge.prototype.getX12Words = function() {
        if (!(this.characterLength % 3 === 0)) {
            throw new Error('X12 words must be a multiple of 3');
        }
        var result = new Uint8Array(this.characterLength / 3 * 2);
        for(var i = 0; i < result.length; i += 2){
            this.setC40Word(result, i, this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3)), this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3 + 1)), this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3 + 2)));
        }
        return result;
    };
    Edge.prototype.getShiftValue = function(c, c40, fnc1) {
        return c40 && MinimalEncoder.isInC40Shift1Set(c) || !c40 && MinimalEncoder.isInTextShift1Set(c) ? 0 : c40 && MinimalEncoder.isInC40Shift2Set(c, fnc1) || !c40 && MinimalEncoder.isInTextShift2Set(c, fnc1) ? 1 : 2;
    };
    Edge.prototype.getC40Value = function(c40, setIndex, c, fnc1) {
        if (c === fnc1) {
            if (!(setIndex === 2)) {
                throw new Error('FNC1 cannot be used in C40 shift 2');
            }
            return 27;
        }
        if (c40) {
            return c <= 31 ? c : c === 32 ? 3 : c <= 47 ? c - 33 : c <= 57 ? c - 44 : c <= 64 ? c - 43 : c <= 90 ? c - 51 : c <= 95 ? c - 69 : c <= 127 ? c - 96 : c;
        } else {
            return c === 0 ? 0 : setIndex === 0 && c <= 3 ? c - 1 // is this a bug in the spec?
             : setIndex === 1 && c <= 31 ? c : c === 32 ? 3 : c >= 33 && c <= 47 ? c - 33 : c >= 48 && c <= 57 ? c - 44 : c >= 58 && c <= 64 ? c - 43 : c >= 65 && c <= 90 ? c - 64 : c >= 91 && c <= 95 ? c - 69 : c === 96 ? 0 : c >= 97 && c <= 122 ? c - 83 : c >= 123 && c <= 127 ? c - 96 : c;
        }
    };
    Edge.prototype.getC40Words = function(c40, fnc1) {
        var c40Values = [];
        for(var i = 0; i < this.characterLength; i++){
            var ci = this.input.charAt(this.fromPosition + i);
            if (c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeC40(ci) || !c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeText(ci)) {
                c40Values.push(this.getC40Value(c40, 0, ci, fnc1));
            } else if (!MinimalEncoder.isExtendedASCII(ci, fnc1)) {
                var shiftValue = this.getShiftValue(ci, c40, fnc1);
                c40Values.push(shiftValue); // Shift[123]
                c40Values.push(this.getC40Value(c40, shiftValue, ci, fnc1));
            } else {
                var asciiValue = (ci & 0xff) - 128;
                if (c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeC40(asciiValue) || !c40 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isNativeText(asciiValue)) {
                    c40Values.push(1); // Shift 2
                    c40Values.push(30); // Upper Shift
                    c40Values.push(this.getC40Value(c40, 0, asciiValue, fnc1));
                } else {
                    c40Values.push(1); // Shift 2
                    c40Values.push(30); // Upper Shift
                    var shiftValue = this.getShiftValue(asciiValue, c40, fnc1);
                    c40Values.push(shiftValue); // Shift[123]
                    c40Values.push(this.getC40Value(c40, shiftValue, asciiValue, fnc1));
                }
            }
        }
        if (c40Values.length % 3 !== 0) {
            if (!((c40Values.length - 2) % 3 === 0 && this.fromPosition + this.characterLength === this.input.length())) {
                throw new Error('C40 words must be a multiple of 3');
            }
            c40Values.push(0); // pad with 0 (Shift 1)
        }
        var result = new Uint8Array(c40Values.length / 3 * 2);
        var byteIndex = 0;
        for(var i = 0; i < c40Values.length; i += 3){
            this.setC40Word(result, byteIndex, c40Values[i] & 0xff, c40Values[i + 1] & 0xff, c40Values[i + 2] & 0xff);
            byteIndex += 2;
        }
        return result;
    };
    Edge.prototype.getEDFBytes = function() {
        var numberOfThirds = Math.ceil(this.characterLength / 4.0);
        var result = new Uint8Array(numberOfThirds * 3);
        var pos = this.fromPosition;
        var endPos = Math.min(this.fromPosition + this.characterLength - 1, this.input.length() - 1);
        for(var i = 0; i < numberOfThirds; i += 3){
            var edfValues = [];
            for(var j = 0; j < 4; j++){
                if (pos <= endPos) {
                    edfValues[j] = this.input.charAt(pos++) & 0x3f;
                } else {
                    edfValues[j] = pos === endPos + 1 ? 0x1f : 0;
                }
            }
            var val24 = edfValues[0] << 18;
            val24 |= edfValues[1] << 12;
            val24 |= edfValues[2] << 6;
            val24 |= edfValues[3];
            result[i] = val24 >> 16 & 0xff;
            result[i + 1] = val24 >> 8 & 0xff;
            result[i + 2] = val24 & 0xff;
        }
        return result;
    };
    Edge.prototype.getLatchBytes = function() {
        switch(this.getPreviousMode()){
            case Mode.ASCII:
            case Mode.B256:
                switch(this.mode){
                    case Mode.B256:
                        return Edge.getBytes(231);
                    case Mode.C40:
                        return Edge.getBytes(230);
                    case Mode.TEXT:
                        return Edge.getBytes(239);
                    case Mode.X12:
                        return Edge.getBytes(238);
                    case Mode.EDF:
                        return Edge.getBytes(240);
                }
                break;
            case Mode.C40:
            case Mode.TEXT:
            case Mode.X12:
                if (this.mode !== this.getPreviousMode()) {
                    switch(this.mode){
                        case Mode.ASCII:
                            return Edge.getBytes(254);
                        case Mode.B256:
                            return Edge.getBytes(254, 231);
                        case Mode.C40:
                            return Edge.getBytes(254, 230);
                        case Mode.TEXT:
                            return Edge.getBytes(254, 239);
                        case Mode.X12:
                            return Edge.getBytes(254, 238);
                        case Mode.EDF:
                            return Edge.getBytes(254, 240);
                    }
                }
                break;
            case Mode.EDF:
                // The rightmost EDIFACT edge always contains an unlatch character
                if (this.mode !== Mode.EDF) {
                    throw new Error('Cannot switch from EDF to ' + this.mode);
                }
                break;
        }
        return new Uint8Array(0);
    };
    // Important: The function does not return the length bytes (one or two) in case of B256 encoding
    Edge.prototype.getDataBytes = function() {
        switch(this.mode){
            case Mode.ASCII:
                if (this.input.isECI(this.fromPosition)) {
                    return Edge.getBytes(241, this.input.getECIValue(this.fromPosition) + 1);
                } else if (MinimalEncoder.isExtendedASCII(this.input.charAt(this.fromPosition), this.input.getFNC1Character())) {
                    return Edge.getBytes(235, this.input.charAt(this.fromPosition) - 127);
                } else if (this.characterLength === 2) {
                    return Edge.getBytes(this.input.charAt(this.fromPosition) * 10 + this.input.charAt(this.fromPosition + 1) + 130);
                } else if (this.input.isFNC1(this.fromPosition)) {
                    return Edge.getBytes(232);
                } else {
                    return Edge.getBytes(this.input.charAt(this.fromPosition) + 1);
                }
            case Mode.B256:
                return Edge.getBytes(this.input.charAt(this.fromPosition));
            case Mode.C40:
                return this.getC40Words(true, this.input.getFNC1Character());
            case Mode.TEXT:
                return this.getC40Words(false, this.input.getFNC1Character());
            case Mode.X12:
                return this.getX12Words();
            case Mode.EDF:
                return this.getEDFBytes();
        }
    };
    return Edge;
}();
var Input = function(_super) {
    __extends(Input, _super);
    function Input(stringToEncode, priorityCharset, fnc1, shape, macroId) {
        var _this = _super.call(this, stringToEncode, priorityCharset, fnc1) || this;
        _this.shape = shape;
        _this.macroId = macroId;
        return _this;
    }
    Input.prototype.getMacroId = function() {
        return this.macroId;
    };
    Input.prototype.getShapeHint = function() {
        return this.shape;
    };
    return Input;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$MinimalECIInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MinimalECIInput"]);
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ASCIIEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$Base256Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EdifactEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EncoderContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EncoderContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$MinimalEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$TextEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/TextEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$X12Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/X12Encoder.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ASCIIEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$Base256Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$C40Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EdifactEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$EncoderContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/EncoderContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$MinimalEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$TextEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/TextEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$X12Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/X12Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/index.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript) <export default as DefaultPlacement>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DefaultPlacement": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)");
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript) <export default as ErrorCorrection>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ErrorCorrection": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)");
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript) <export default as HighLevelEncoder>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "HighLevelEncoder": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript) <export default as SymbolInfo>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SymbolInfo": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
}),
"[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixWriter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Charset.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DefaultPlacement$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript) <export default as DefaultPlacement>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ErrorCorrection$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript) <export default as ErrorCorrection>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HighLevelEncoder$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript) <export default as HighLevelEncoder>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$MinimalEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SymbolInfo$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript) <export default as SymbolInfo>");
;
;
;
;
;
;
var DataMatrixWriter = function() {
    function DataMatrixWriter() {}
    DataMatrixWriter.prototype.encode = function(contents, format, width, height, hints) {
        if (hints === void 0) {
            hints = null;
        }
        if (contents.trim() === '') {
            throw new Error('Found empty contents');
        }
        if (format !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX) {
            throw new Error('Can only encode DATA_MATRIX, but got ' + format);
        }
        if (width < 0 || height < 0) {
            throw new Error('Requested dimensions can\'t be negative: ' + width + 'x' + height);
        }
        // Try to get force shape & min / max size
        var shape = 0 /* FORCE_NONE */ ;
        var minSize = null;
        var maxSize = null;
        if (hints != null) {
            var requestedShape = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX_SHAPE);
            if (requestedShape != null) {
                shape = requestedShape;
            }
            var requestedMinSize = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MIN_SIZE);
            if (requestedMinSize != null) {
                minSize = requestedMinSize;
            }
            var requestedMaxSize = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MAX_SIZE);
            if (requestedMaxSize != null) {
                maxSize = requestedMaxSize;
            }
        }
        // 1. step: Data encodation
        var encoded;
        var hasCompactionHint = hints != null && hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX_COMPACT) && Boolean(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX_COMPACT).toString());
        if (hasCompactionHint) {
            var hasGS1FormatHint = hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GS1_FORMAT) && Boolean(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GS1_FORMAT).toString());
            var charset = null;
            var hasEncodingHint = hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET);
            if (hasEncodingHint) {
                charset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forName(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET).toString());
            }
            encoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$MinimalEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MinimalEncoder"].encodeHighLevel(contents, charset, hasGS1FormatHint ? 0x1d : -1, shape);
        } else {
            var hasForceC40Hint = hints != null && hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FORCE_C40) && Boolean(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FORCE_C40).toString());
            encoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HighLevelEncoder$3e$__["HighLevelEncoder"].encodeHighLevel(contents, shape, minSize, maxSize, hasForceC40Hint);
        }
        var symbolInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SymbolInfo$3e$__["SymbolInfo"].lookup(encoded.length, shape, minSize, maxSize, true);
        // 2. step: ECC generation
        var codewords = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ErrorCorrection$3e$__["ErrorCorrection"].encodeECC200(encoded, symbolInfo);
        // 3. step: Module placement in Matrix
        var placement = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DefaultPlacement$3e$__["DefaultPlacement"](codewords, symbolInfo.getSymbolDataWidth(), symbolInfo.getSymbolDataHeight());
        placement.place();
        // 4. step: low-level encoding
        return this.encodeLowLevel(placement, symbolInfo, width, height);
    };
    /**
     * Encode the given symbol info to a bit matrix.
     *
     * @param placement  The DataMatrix placement.
     * @param symbolInfo The symbol info to encode.
     * @return The bit matrix generated.
     */ DataMatrixWriter.prototype.encodeLowLevel = function(placement, symbolInfo, width, height) {
        var symbolWidth = symbolInfo.getSymbolDataWidth();
        var symbolHeight = symbolInfo.getSymbolDataHeight();
        var matrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](symbolInfo.getSymbolWidth(), symbolInfo.getSymbolHeight());
        var matrixY = 0;
        for(var y = 0; y < symbolHeight; y++){
            // Fill the top edge with alternate 0 / 1
            var matrixX = void 0;
            if (y % symbolInfo.matrixHeight === 0) {
                matrixX = 0;
                for(var x = 0; x < symbolInfo.getSymbolWidth(); x++){
                    matrix.setBoolean(matrixX, matrixY, x % 2 === 0);
                    matrixX++;
                }
                matrixY++;
            }
            matrixX = 0;
            for(var x = 0; x < symbolWidth; x++){
                // Fill the right edge with full 1
                if (x % symbolInfo.matrixWidth === 0) {
                    matrix.setBoolean(matrixX, matrixY, true);
                    matrixX++;
                }
                matrix.setBoolean(matrixX, matrixY, placement.getBit(x, y));
                matrixX++;
                // Fill the right edge with alternate 0 / 1
                if (x % symbolInfo.matrixWidth === symbolInfo.matrixWidth - 1) {
                    matrix.setBoolean(matrixX, matrixY, y % 2 === 0);
                    matrixX++;
                }
            }
            matrixY++;
            // Fill the bottom edge with full 1
            if (y % symbolInfo.matrixHeight === symbolInfo.matrixHeight - 1) {
                matrixX = 0;
                for(var x = 0; x < symbolInfo.getSymbolWidth(); x++){
                    matrix.setBoolean(matrixX, matrixY, true);
                    matrixX++;
                }
                matrixY++;
            }
        }
        return this.convertByteMatrixToBitMatrix(matrix, width, height);
    };
    /**
     * Convert the ByteMatrix to BitMatrix.
     *
     * @param reqHeight The requested height of the image (in pixels) with the Datamatrix code
     * @param reqWidth The requested width of the image (in pixels) with the Datamatrix code
     * @param matrix The input matrix.
     * @return The output matrix.
     */ DataMatrixWriter.prototype.convertByteMatrixToBitMatrix = function(matrix, reqWidth, reqHeight) {
        var matrixWidth = matrix.getWidth();
        var matrixHeight = matrix.getHeight();
        var outputWidth = Math.max(reqWidth, matrixWidth);
        var outputHeight = Math.max(reqHeight, matrixHeight);
        var multiple = Math.min(outputWidth / matrixWidth, outputHeight / matrixHeight);
        var leftPadding = (outputWidth - matrixWidth * multiple) / 2;
        var topPadding = (outputHeight - matrixHeight * multiple) / 2;
        var output;
        // remove padding if requested width and height are too small
        if (reqHeight < matrixHeight || reqWidth < matrixWidth) {
            leftPadding = 0;
            topPadding = 0;
            output = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](matrixWidth, matrixHeight);
        } else {
            output = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](reqWidth, reqHeight);
        }
        output.clear();
        for(var inputY = 0, outputY = topPadding; inputY < matrixHeight; inputY++, outputY += multiple){
            // Write the contents of this row of the bytematrix
            for(var inputX = 0, outputX = leftPadding; inputX < matrixWidth; inputX++, outputX += multiple){
                if (matrix.get(inputX, inputY) === 1) {
                    output.setRegion(outputX, outputY, multiple, multiple);
                }
            }
        }
        return output;
    };
    return DataMatrixWriter;
}();
const __TURBOPACK__default__export__ = DataMatrixWriter;
}),

};

//# sourceMappingURL=node_modules_%40zxing_library_esm_core_datamatrix_fbd45644._.js.map