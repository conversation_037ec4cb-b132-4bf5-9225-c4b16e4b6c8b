{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n  attribute?: \"class\" | \"data-theme\" | \"data-mode\"\n  defaultTheme?: string\n  enableSystem?: boolean\n  disableTransitionOnChange?: boolean\n}\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/providers/session-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { ReactNode } from \"react\"\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBAAO,8OAAC,qIAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B", "debugId": null}}]}