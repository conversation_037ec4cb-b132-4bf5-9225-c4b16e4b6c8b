module.exports = {

"[project]/lib/types/student.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Student Management Types
__turbopack_context__.s({
    "DEFAULT_PAGINATION": ()=>DEFAULT_PAGINATION,
    "DEFAULT_STUDENT": ()=>DEFAULT_STUDENT,
    "GENDERS": ()=>GENDERS,
    "GRADE_LEVELS": ()=>GRADE_LEVELS,
    "GUARDIAN_RELATIONSHIPS": ()=>GUARDIAN_RELATIONSHIPS,
    "STUDENT_STATUSES": ()=>STUDENT_STATUSES,
    "getFullName": ()=>getFullName
});
const getFullName = (student)=>{
    const parts = [
        student.firstName,
        student.middleName,
        student.lastName
    ].filter(Boolean);
    return parts.join(' ');
};
const GRADE_LEVELS = [
    '7',
    '8',
    '9',
    '10',
    '11',
    '12'
];
const STUDENT_STATUSES = [
    'Active',
    'Inactive',
    'Transferred',
    'Graduated'
];
const GUARDIAN_RELATIONSHIPS = [
    'Father',
    'Mother',
    'Guardian',
    'Grandparent',
    'Sibling',
    'Other'
];
const GENDERS = [
    'Male',
    'Female'
];
const DEFAULT_STUDENT = {
    status: 'Active',
    emergencyContacts: [],
    country: 'Philippines'
};
const DEFAULT_PAGINATION = {
    page: 1,
    pageSize: 20,
    total: 0
};
}),
"[project]/lib/utils/qr-code.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "QRCodeGenerator": ()=>QRCodeGenerator,
    "QRCodeManager": ()=>QRCodeManager,
    "QRCodePrinter": ()=>QRCodePrinter,
    "qrGenerator": ()=>qrGenerator,
    "qrManager": ()=>qrManager,
    "qrPrinter": ()=>qrPrinter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/types/student.ts [app-ssr] (ecmascript)");
;
class QRCodeGenerator {
    static instance;
    static getInstance() {
        if (!QRCodeGenerator.instance) {
            QRCodeGenerator.instance = new QRCodeGenerator();
        }
        return QRCodeGenerator.instance;
    }
    // Generate QR code data for a student
    generateQRData(student) {
        return {
            studentId: student.id,
            name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFullName"])(student),
            grade: student.grade,
            section: student.section,
            validUntil: this.getValidUntilDate()
        };
    }
    // Generate QR code string (what gets encoded in the QR code)
    generateQRString(student) {
        const data = this.generateQRData(student);
        return JSON.stringify({
            id: data.studentId,
            name: data.name,
            grade: data.grade,
            section: data.section,
            school: "Tanauan School of Arts and Trade",
            type: "student_id",
            validUntil: data.validUntil,
            generated: new Date().toISOString()
        });
    }
    // Generate a unique QR code identifier
    generateQRCodeId(student) {
        const timestamp = Date.now();
        const year = new Date().getFullYear();
        return `QR_${student.id}_${year}_${timestamp.toString().slice(-6)}`;
    }
    // Get validity date (1 year from now)
    getValidUntilDate() {
        const date = new Date();
        date.setFullYear(date.getFullYear() + 1);
        return date.toISOString().split('T')[0];
    }
    // Validate QR code data
    validateQRData(qrString) {
        try {
            const data = JSON.parse(qrString);
            if (!data.id || !data.name || !data.type) {
                return {
                    valid: false,
                    error: 'Missing required fields'
                };
            }
            if (data.type !== 'student_id') {
                return {
                    valid: false,
                    error: 'Invalid QR code type'
                };
            }
            if (data.validUntil && new Date(data.validUntil) < new Date()) {
                return {
                    valid: false,
                    error: 'QR code has expired'
                };
            }
            return {
                valid: true,
                data
            };
        } catch (error) {
            return {
                valid: false,
                error: 'Invalid QR code format'
            };
        }
    }
}
class QRCodePrinter {
    static instance;
    static getInstance() {
        if (!QRCodePrinter.instance) {
            QRCodePrinter.instance = new QRCodePrinter();
        }
        return QRCodePrinter.instance;
    }
    // Get QR code size in pixels
    getQRSize(size) {
        switch(size){
            case 'small':
                return 96 // 1 inch at 96 DPI
                ;
            case 'medium':
                return 144 // 1.5 inches at 96 DPI
                ;
            case 'large':
                return 192 // 2 inches at 96 DPI
                ;
            default:
                return 144;
        }
    }
    // Generate print layout for multiple QR codes
    generatePrintLayout(students, options) {
        const qrGenerator = QRCodeGenerator.getInstance();
        const qrSize = this.getQRSize(options.size);
        const qrCodes = students.map((student)=>({
                student,
                qrData: qrGenerator.generateQRString(student),
                qrId: qrGenerator.generateQRCodeId(student)
            }));
        const sheets = this.layoutQRCodes(qrCodes, options);
        return {
            sheets,
            totalCodes: qrCodes.length,
            totalSheets: sheets.length,
            options
        };
    }
    // Layout QR codes on sheets
    layoutQRCodes(qrCodes, options) {
        const sheets = [];
        const codesPerSheet = options.codesPerSheet;
        for(let i = 0; i < qrCodes.length; i += codesPerSheet){
            const sheetCodes = qrCodes.slice(i, i + codesPerSheet);
            sheets.push({
                id: `sheet_${Math.floor(i / codesPerSheet) + 1}`,
                codes: sheetCodes,
                paperSize: options.paperSize
            });
        }
        return sheets;
    }
    // Generate CSS for print styles
    generatePrintCSS(options) {
        const qrSize = this.getQRSize(options.size);
        const margin = 10 // 10px margin
        ;
        return `
      @media print {
        @page {
          size: ${options.paperSize};
          margin: 0.5in;
        }
        
        .qr-sheet {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(${qrSize + margin * 2}px, 1fr));
          gap: ${margin}px;
          page-break-after: always;
        }
        
        .qr-code-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: ${margin}px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        
        .qr-code-image {
          width: ${qrSize}px;
          height: ${qrSize}px;
          margin-bottom: 8px;
        }
        
        .student-info {
          font-size: 10px;
          line-height: 1.2;
        }
        
        .student-name {
          font-weight: bold;
          margin-bottom: 2px;
        }
        
        .student-details {
          color: #666;
        }
      }
    `;
    }
}
class QRCodeManager {
    static instance;
    static getInstance() {
        if (!QRCodeManager.instance) {
            QRCodeManager.instance = new QRCodeManager();
        }
        return QRCodeManager.instance;
    }
    // Generate QR code for a student
    async generateForStudent(student) {
        const generator = QRCodeGenerator.getInstance();
        const qrString = generator.generateQRString(student);
        const qrId = generator.generateQRCodeId(student);
        // In a real implementation, you would:
        // 1. Generate the actual QR code image using a library like qrcode
        // 2. Save it to storage (file system, cloud storage, etc.)
        // 3. Update the student record with the QR code ID
        // For now, we'll simulate this
        await new Promise((resolve)=>setTimeout(resolve, 100));
        return qrId;
    }
    // Generate QR codes for multiple students
    async generateBatch(students, onProgress) {
        const qrIds = [];
        for(let i = 0; i < students.length; i++){
            const qrId = await this.generateForStudent(students[i]);
            qrIds.push(qrId);
            if (onProgress) {
                onProgress((i + 1) / students.length * 100);
            }
        }
        return qrIds;
    }
    // Regenerate QR code for a student
    async regenerateForStudent(student) {
        // Invalidate old QR code and generate new one
        return this.generateForStudent(student);
    }
    // Validate and decode QR code
    validateAndDecode(qrString) {
        const generator = QRCodeGenerator.getInstance();
        const result = generator.validateQRData(qrString);
        if (result.valid && result.data) {
            return {
                valid: true,
                studentId: result.data.id
            };
        }
        return {
            valid: false,
            error: result.error
        };
    }
}
const qrGenerator = QRCodeGenerator.getInstance();
const qrPrinter = QRCodePrinter.getInstance();
const qrManager = QRCodeManager.getInstance();
}),
"[project]/lib/validations/student.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bulkImportSchema": ()=>bulkImportSchema,
    "getDefaultFormValues": ()=>getDefaultFormValues,
    "quickStudentSchema": ()=>quickStudentSchema,
    "studentRegistrationSchema": ()=>studentRegistrationSchema,
    "studentUpdateSchema": ()=>studentUpdateSchema,
    "validateDepEdId": ()=>validateDepEdId,
    "validateEmail": ()=>validateEmail,
    "validatePhoneNumber": ()=>validatePhoneNumber
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/types/student.ts [app-ssr] (ecmascript)");
;
;
// Emergency contact schema
const emergencyContactSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Name must be at least 2 characters"),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
    relationship: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Relationship is required"),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
// Address schema
const addressSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    street: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(5, "Street address must be at least 5 characters"),
    barangay: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Barangay is required"),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "City is required"),
    province: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Province is required"),
    zipCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(4, "ZIP code must be at least 4 characters").max(10, "ZIP code must be at most 10 characters"),
    country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("Philippines")
});
// Guardian schema
const guardianSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Guardian name must be at least 2 characters"),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email("Invalid email format").optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("")),
    relationship: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GUARDIAN_RELATIONSHIPS"], {
        errorMap: ()=>({
                message: "Please select a valid relationship"
            })
    }),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
const studentRegistrationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // Basic Information
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(12, "DepEd ID must be 12 digits").max(12, "DepEd ID must be 12 digits").regex(/^\d{12}$/, "DepEd ID must contain only numbers"),
    firstName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "First name must be at least 2 characters").max(50, "First name must be at most 50 characters"),
    middleName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().max(50, "Middle name must be at most 50 characters").optional(),
    lastName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Last name must be at least 2 characters").max(50, "Last name must be at most 50 characters"),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email("Invalid email format"),
    dateOfBirth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    gender: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GENDERS"]).optional(),
    // Academic Information
    course: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Course is required"),
    year: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Year level is required"),
    section: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    grade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GRADE_LEVELS"], {
        errorMap: ()=>({
                message: "Please select a valid grade level"
            })
    }),
    // Guardian Information
    guardianName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Guardian name must be at least 2 characters"),
    guardianPhone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
    guardianEmail: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email("Invalid email format").optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("")),
    guardianRelationship: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GUARDIAN_RELATIONSHIPS"], {
        errorMap: ()=>({
                message: "Please select a valid relationship"
            })
    }),
    guardianAddress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // Emergency Contacts
    emergencyContacts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(emergencyContactSchema).min(1, "At least one emergency contact is required").max(3, "Maximum 3 emergency contacts allowed"),
    // Address Information
    street: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(5, "Street address must be at least 5 characters"),
    barangay: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Barangay is required"),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "City is required"),
    province: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Province is required"),
    zipCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(4, "ZIP code must be at least 4 characters").max(10, "ZIP code must be at most 10 characters"),
    country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("Philippines"),
    // Photo (optional for now)
    photo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].any().optional()
});
const studentUpdateSchema = studentRegistrationSchema.partial().extend({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(12, "DepEd ID must be 12 digits").max(12, "DepEd ID must be 12 digits").regex(/^\d{12}$/, "DepEd ID must contain only numbers")
});
const quickStudentSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(12, "DepEd ID must be 12 digits").max(12, "DepEd ID must be 12 digits").regex(/^\d{12}$/, "DepEd ID must contain only numbers"),
    firstName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "First name is required"),
    lastName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Last name is required"),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email("Invalid email format"),
    grade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GRADE_LEVELS"]),
    course: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Course is required"),
    year: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Year level is required"),
    guardianName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, "Guardian name is required"),
    guardianPhone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(10, "Guardian phone is required").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
    guardianRelationship: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GUARDIAN_RELATIONSHIPS"])
});
const bulkImportSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(studentRegistrationSchema);
const validateDepEdId = (id)=>{
    return /^\d{12}$/.test(id);
};
const validatePhoneNumber = (phone)=>{
    return /^[\+]?[0-9\s\-\(\)]+$/.test(phone) && phone.replace(/\D/g, '').length >= 10;
};
const validateEmail = (email)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email().safeParse(email).success;
};
const getDefaultFormValues = ()=>({
        country: "Philippines",
        province: "Batangas",
        city: "Tanauan City",
        emergencyContacts: [
            {
                name: "",
                phone: "",
                relationship: "",
                address: ""
            }
        ]
    });
}),
"[project]/lib/utils/photo-management.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Photo management utilities for student photos
__turbopack_context__.s({
    "PhotoManager": ()=>PhotoManager,
    "compressPhoto": ()=>compressPhoto,
    "photoManager": ()=>photoManager,
    "processPhoto": ()=>processPhoto,
    "validatePhotoFile": ()=>validatePhotoFile
});
class PhotoManager {
    static instance;
    static getInstance() {
        if (!PhotoManager.instance) {
            PhotoManager.instance = new PhotoManager();
        }
        return PhotoManager.instance;
    }
    // Default compression options
    defaultOptions = {
        maxWidth: 800,
        maxHeight: 800,
        quality: 0.8,
        format: 'jpeg'
    };
    // Validate photo file
    validatePhoto(file) {
        const result = {
            valid: true,
            warnings: []
        };
        // Check file type
        const allowedTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/webp'
        ];
        if (!allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.'
            };
        }
        // Check file size (max 10MB)
        const maxSize = 10 * 1024 * 1024 // 10MB
        ;
        if (file.size > maxSize) {
            return {
                valid: false,
                error: 'File size too large. Please upload an image smaller than 10MB.'
            };
        }
        // Add warnings for large files
        if (file.size > 5 * 1024 * 1024) {
            result.warnings?.push('Large file size detected. Consider compressing the image.');
        }
        // Check file name
        if (file.name.length > 100) {
            result.warnings?.push('File name is very long. It will be shortened during upload.');
        }
        return result;
    }
    // Get image dimensions
    async getImageDimensions(file) {
        return new Promise((resolve, reject)=>{
            const img = new Image();
            const url = URL.createObjectURL(file);
            img.onload = ()=>{
                URL.revokeObjectURL(url);
                resolve({
                    width: img.width,
                    height: img.height
                });
            };
            img.onerror = ()=>{
                URL.revokeObjectURL(url);
                reject(new Error('Failed to load image'));
            };
            img.src = url;
        });
    }
    // Compress image
    async compressImage(file, options = {}) {
        const opts = {
            ...this.defaultOptions,
            ...options
        };
        return new Promise(async (resolve, reject)=>{
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    reject(new Error('Canvas context not available'));
                    return;
                }
                const img = new Image();
                const url = URL.createObjectURL(file);
                img.onload = ()=>{
                    URL.revokeObjectURL(url);
                    // Calculate new dimensions
                    let { width, height } = img;
                    const aspectRatio = width / height;
                    if (width > opts.maxWidth) {
                        width = opts.maxWidth;
                        height = width / aspectRatio;
                    }
                    if (height > opts.maxHeight) {
                        height = opts.maxHeight;
                        width = height * aspectRatio;
                    }
                    // Set canvas dimensions
                    canvas.width = width;
                    canvas.height = height;
                    // Draw and compress image
                    ctx.drawImage(img, 0, 0, width, height);
                    canvas.toBlob((blob)=>{
                        if (!blob) {
                            reject(new Error('Failed to compress image'));
                            return;
                        }
                        // Create new file
                        const compressedFile = new File([
                            blob
                        ], this.generateFileName(file.name, opts.format), {
                            type: `image/${opts.format}`
                        });
                        const metadata = {
                            originalName: file.name,
                            size: compressedFile.size,
                            type: compressedFile.type,
                            dimensions: {
                                width,
                                height
                            },
                            lastModified: Date.now()
                        };
                        resolve({
                            file: compressedFile,
                            metadata
                        });
                    }, `image/${opts.format}`, opts.quality);
                };
                img.onerror = ()=>{
                    URL.revokeObjectURL(url);
                    reject(new Error('Failed to load image for compression'));
                };
                img.src = url;
            } catch (error) {
                reject(error);
            }
        });
    }
    // Generate unique filename
    generateFileName(originalName, format) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const baseName = originalName.split('.')[0].substring(0, 20);
        return `${baseName}_${timestamp}_${random}.${format}`;
    }
    // Create photo preview URL
    createPreviewUrl(file) {
        return URL.createObjectURL(file);
    }
    // Revoke preview URL
    revokePreviewUrl(url) {
        URL.revokeObjectURL(url);
    }
    // Process photo for upload
    async processPhotoForUpload(file, options = {}) {
        // Validate photo
        const validationResult = this.validatePhoto(file);
        if (!validationResult.valid) {
            throw new Error(validationResult.error);
        }
        // Get original dimensions
        const originalDimensions = await this.getImageDimensions(file);
        // Compress if needed
        let processedFile = file;
        let metadata = {
            originalName: file.name,
            size: file.size,
            type: file.type,
            dimensions: originalDimensions,
            lastModified: file.lastModified
        };
        // Compress if image is large or if compression is requested
        const shouldCompress = file.size > 1024 * 1024 || // > 1MB
        originalDimensions.width > 1200 || originalDimensions.height > 1200 || Object.keys(options).length > 0;
        if (shouldCompress) {
            const compressed = await this.compressImage(file, options);
            processedFile = compressed.file;
            metadata = compressed.metadata;
        }
        // Create preview URL
        const previewUrl = this.createPreviewUrl(processedFile);
        return {
            processedFile,
            metadata,
            previewUrl,
            validationResult
        };
    }
    // Upload photo (mock implementation)
    async uploadPhoto(file, studentId) {
        // In a real implementation, this would upload to a storage service
        // For now, we'll simulate the upload and return a mock URL
        await new Promise((resolve)=>setTimeout(resolve, 1000)); // Simulate upload delay
        const timestamp = Date.now();
        const mockUrl = `https://storage.qrsams.edu.ph/students/${studentId}/photo_${timestamp}.jpg`;
        return mockUrl;
    }
    // Delete photo (mock implementation)
    async deletePhoto(photoUrl) {
        // In a real implementation, this would delete from storage service
        await new Promise((resolve)=>setTimeout(resolve, 500));
        console.log('Photo deleted:', photoUrl);
    }
    // Get photo info from URL
    getPhotoInfo(url) {
        try {
            const urlParts = url.split('/');
            const filename = urlParts[urlParts.length - 1];
            const studentId = urlParts[urlParts.length - 2];
            const timestampMatch = filename.match(/_(\d+)\./);
            const timestamp = timestampMatch ? parseInt(timestampMatch[1]) : undefined;
            return {
                studentId,
                timestamp,
                filename
            };
        } catch (error) {
            return {};
        }
    }
    // Batch process photos
    async batchProcessPhotos(files, options = {}) {
        const results = [];
        for (const file of files){
            try {
                const result = await this.processPhotoForUpload(file, options);
                results.push({
                    originalFile: file,
                    ...result
                });
            } catch (error) {
                results.push({
                    originalFile: file,
                    processedFile: file,
                    metadata: {
                        originalName: file.name,
                        size: file.size,
                        type: file.type,
                        dimensions: {
                            width: 0,
                            height: 0
                        },
                        lastModified: file.lastModified
                    },
                    previewUrl: '',
                    validationResult: {
                        valid: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    },
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        return results;
    }
}
const photoManager = PhotoManager.getInstance();
const validatePhotoFile = (file)=>{
    return photoManager.validatePhoto(file);
};
const compressPhoto = async (file, options)=>{
    return photoManager.compressImage(file, options);
};
const processPhoto = async (file, options)=>{
    return photoManager.processPhotoForUpload(file, options);
};
}),
"[project]/lib/data/students-mock-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "findStudentById": ()=>findStudentById,
    "findStudentByQRCode": ()=>findStudentByQRCode,
    "getStudentsByGrade": ()=>getStudentsByGrade,
    "getStudentsBySection": ()=>getStudentsBySection,
    "getStudentsByStatus": ()=>getStudentsByStatus,
    "getUniqueCourses": ()=>getUniqueCourses,
    "getUniqueGrades": ()=>getUniqueGrades,
    "getUniqueSections": ()=>getUniqueSections,
    "getUniqueYears": ()=>getUniqueYears,
    "mockStudentsData": ()=>mockStudentsData,
    "searchStudents": ()=>searchStudents
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/types/student.ts [app-ssr] (ecmascript)");
;
const mockStudentsData = [
    {
        id: "123456789001",
        firstName: "John",
        middleName: "Michael",
        lastName: "Doe",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "3rd Year",
        section: "IT-3A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_123456789001_2025",
        dateOfBirth: "2007-05-15",
        gender: "Male",
        guardian: {
            name: "Robert Doe",
            phone: "+63 ************",
            email: "<EMAIL>",
            relationship: "Father",
            address: "123 Main St, Tanauan City"
        },
        emergencyContacts: [
            {
                name: "Mary Doe",
                phone: "+63 ************",
                relationship: "Mother",
                address: "123 Main St, Tanauan City"
            },
            {
                name: "James Doe",
                phone: "+63 ************",
                relationship: "Uncle"
            }
        ],
        address: {
            street: "123 Main Street",
            barangay: "Poblacion",
            city: "Tanauan City",
            province: "Batangas",
            zipCode: "4232",
            country: "Philippines"
        },
        enrollmentDate: "2023-08-15",
        lastUpdated: "2025-01-15",
        attendanceStats: {
            totalDays: 120,
            presentDays: 110,
            lateDays: 8,
            absentDays: 2,
            attendanceRate: 91.7,
            lastAttendance: "2025-01-15"
        }
    },
    {
        id: "123456789002",
        firstName: "Jane",
        middleName: "Marie",
        lastName: "Smith",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "2nd Year",
        section: "CS-2B",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_123456789002_2025",
        dateOfBirth: "2008-03-22",
        gender: "Female",
        guardian: {
            name: "Patricia Smith",
            phone: "+63 ************",
            email: "<EMAIL>",
            relationship: "Mother",
            address: "456 Oak Ave, Tanauan City"
        },
        emergencyContacts: [
            {
                name: "David Smith",
                phone: "+63 ************",
                relationship: "Father",
                address: "456 Oak Ave, Tanauan City"
            }
        ],
        address: {
            street: "456 Oak Avenue",
            barangay: "San Jose",
            city: "Tanauan City",
            province: "Batangas",
            zipCode: "4232",
            country: "Philippines"
        },
        enrollmentDate: "2023-08-15",
        lastUpdated: "2025-01-14",
        attendanceStats: {
            totalDays: 120,
            presentDays: 115,
            lateDays: 3,
            absentDays: 2,
            attendanceRate: 95.8,
            lastAttendance: "2025-01-14"
        }
    },
    {
        id: "123456789003",
        firstName: "Mike",
        lastName: "Johnson",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "1st Year",
        section: "IT-1C",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_123456789003_2025",
        dateOfBirth: "2009-11-08",
        gender: "Male",
        guardian: {
            name: "Linda Johnson",
            phone: "+63 ************",
            email: "<EMAIL>",
            relationship: "Mother",
            address: "789 Pine St, Tanauan City"
        },
        emergencyContacts: [
            {
                name: "Mark Johnson",
                phone: "+63 ************",
                relationship: "Father"
            },
            {
                name: "Susan Johnson",
                phone: "+63 ************",
                relationship: "Grandmother",
                address: "321 Elm St, Tanauan City"
            }
        ],
        address: {
            street: "789 Pine Street",
            barangay: "Natatas",
            city: "Tanauan City",
            province: "Batangas",
            zipCode: "4232",
            country: "Philippines"
        },
        enrollmentDate: "2024-08-15",
        lastUpdated: "2025-01-13",
        attendanceStats: {
            totalDays: 120,
            presentDays: 105,
            lateDays: 12,
            absentDays: 3,
            attendanceRate: 87.5,
            lastAttendance: "2025-01-13"
        }
    },
    {
        id: "123456789004",
        firstName: "Sarah",
        middleName: "Grace",
        lastName: "Wilson",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "4th Year",
        section: "CS-4A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_123456789004_2025",
        dateOfBirth: "2006-09-12",
        gender: "Female",
        guardian: {
            name: "Thomas Wilson",
            phone: "+63 ************",
            email: "<EMAIL>",
            relationship: "Father",
            address: "654 Maple Dr, Tanauan City"
        },
        emergencyContacts: [
            {
                name: "Helen Wilson",
                phone: "+63 ************",
                relationship: "Mother",
                address: "654 Maple Dr, Tanauan City"
            }
        ],
        address: {
            street: "654 Maple Drive",
            barangay: "Sambat",
            city: "Tanauan City",
            province: "Batangas",
            zipCode: "4232",
            country: "Philippines"
        },
        enrollmentDate: "2021-08-15",
        lastUpdated: "2025-01-12",
        attendanceStats: {
            totalDays: 120,
            presentDays: 118,
            lateDays: 1,
            absentDays: 1,
            attendanceRate: 98.3,
            lastAttendance: "2025-01-12"
        }
    },
    {
        id: "123456789005",
        firstName: "Alex",
        lastName: "Rodriguez",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "2nd Year",
        section: "IT-2A",
        grade: "10",
        status: "Inactive",
        photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_123456789005_2025",
        dateOfBirth: "2008-07-25",
        gender: "Male",
        guardian: {
            name: "Maria Rodriguez",
            phone: "+63 ************",
            email: "<EMAIL>",
            relationship: "Mother",
            address: "987 Cedar Ln, Tanauan City"
        },
        emergencyContacts: [
            {
                name: "Carlos Rodriguez",
                phone: "+63 ************",
                relationship: "Father"
            }
        ],
        address: {
            street: "987 Cedar Lane",
            barangay: "Ulango",
            city: "Tanauan City",
            province: "Batangas",
            zipCode: "4232",
            country: "Philippines"
        },
        enrollmentDate: "2023-08-15",
        lastUpdated: "2024-12-15",
        attendanceStats: {
            totalDays: 120,
            presentDays: 85,
            lateDays: 15,
            absentDays: 20,
            attendanceRate: 70.8,
            lastAttendance: "2024-12-10"
        }
    }
];
const findStudentById = (id)=>{
    return mockStudentsData.find((student)=>student.id === id);
};
const findStudentByQRCode = (qrCode)=>{
    return mockStudentsData.find((student)=>student.qrCode === qrCode);
};
const getStudentsByGrade = (grade)=>{
    return mockStudentsData.filter((student)=>student.grade === grade);
};
const getStudentsBySection = (section)=>{
    return mockStudentsData.filter((student)=>student.section === section);
};
const getStudentsByStatus = (status)=>{
    return mockStudentsData.filter((student)=>student.status === status);
};
const getUniqueGrades = ()=>{
    return [
        ...new Set(mockStudentsData.map((s)=>s.grade))
    ].sort();
};
const getUniqueSections = ()=>{
    return [
        ...new Set(mockStudentsData.map((s)=>s.section).filter(Boolean))
    ].sort();
};
const getUniqueCourses = ()=>{
    return [
        ...new Set(mockStudentsData.map((s)=>s.course))
    ].sort();
};
const getUniqueYears = ()=>{
    return [
        ...new Set(mockStudentsData.map((s)=>s.year))
    ].sort();
};
const searchStudents = (query)=>{
    if (!query.trim()) return mockStudentsData;
    const searchTerm = query.toLowerCase();
    return mockStudentsData.filter((student)=>{
        const fullName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFullName"])(student).toLowerCase();
        return fullName.includes(searchTerm) || student.id.toLowerCase().includes(searchTerm) || student.email.toLowerCase().includes(searchTerm) || student.course.toLowerCase().includes(searchTerm) || student.section?.toLowerCase().includes(searchTerm) || student.guardian.name.toLowerCase().includes(searchTerm);
    });
};
}),
"[project]/app/(dashboard)/students/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>StudentsPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-check.js [app-ssr] (ecmascript) <export default as UserCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserX$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-x.js [app-ssr] (ecmascript) <export default as UserX>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-ssr] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$qr$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__QrCode$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/qr-code.js [app-ssr] (ecmascript) <export default as QrCode>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/types/student.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$student$2d$filters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/student-filters.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$students$2d$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/students-table.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$bulk$2d$actions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/bulk-actions.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/pagination.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$student$2d$registration$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/student-registration-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$csv$2d$import$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/csv-import-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$qr$2d$batch$2d$generator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/students/qr-batch-generator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/students-mock-data.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function StudentsPage() {
    const [students, setStudents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockStudentsData"]);
    const [selectedStudents, setSelectedStudents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [sortConfig, setSortConfig] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        field: 'name',
        direction: 'asc'
    });
    const [pagination, setPagination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        page: 1,
        pageSize: 20,
        total: 0
    });
    const [showAddDialog, setShowAddDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Filter and sort students
    const filteredAndSortedStudents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        let filtered = students;
        // Apply search filter
        if (filters.search) {
            filtered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["searchStudents"])(filters.search);
        }
        // Apply other filters
        if (filters.grade?.length) {
            filtered = filtered.filter((s)=>filters.grade.includes(s.grade));
        }
        if (filters.section?.length) {
            filtered = filtered.filter((s)=>s.section && filters.section.includes(s.section));
        }
        if (filters.status?.length) {
            filtered = filtered.filter((s)=>filters.status.includes(s.status));
        }
        if (filters.course?.length) {
            filtered = filtered.filter((s)=>filters.course.includes(s.course));
        }
        if (filters.year?.length) {
            filtered = filtered.filter((s)=>filters.year.includes(s.year));
        }
        // Apply sorting
        const sorted = [
            ...filtered
        ].sort((a, b)=>{
            let aValue;
            let bValue;
            if (sortConfig.field === 'name') {
                aValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFullName"])(a).toLowerCase();
                bValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$types$2f$student$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFullName"])(b).toLowerCase();
            } else {
                aValue = a[sortConfig.field];
                bValue = b[sortConfig.field];
            }
            if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
            return 0;
        });
        return sorted;
    }, [
        students,
        filters,
        sortConfig
    ]);
    // Paginate students
    const paginatedStudents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const startIndex = (pagination.page - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        return filteredAndSortedStudents.slice(startIndex, endIndex);
    }, [
        filteredAndSortedStudents,
        pagination.page,
        pagination.pageSize
    ]);
    // Update pagination total when filtered students change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setPagination((prev)=>({
                ...prev,
                total: filteredAndSortedStudents.length,
                page: 1 // Reset to first page when filters change
            }));
    }, [
        filteredAndSortedStudents.length
    ]);
    // Statistics
    const stats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const total = students.length;
        const active = students.filter((s)=>s.status === 'Active').length;
        const inactive = students.filter((s)=>s.status === 'Inactive').length;
        const avgAttendance = students.filter((s)=>s.attendanceStats).reduce((sum, s)=>sum + (s.attendanceStats?.attendanceRate || 0), 0) / students.filter((s)=>s.attendanceStats).length || 0;
        return {
            total,
            active,
            inactive,
            avgAttendance
        };
    }, [
        students
    ]);
    const handleStudentCreated = (student)=>{
        setStudents((prev)=>[
                ...prev,
                student
            ]);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Student registered successfully!");
    };
    const handleStudentUpdated = (updatedStudent)=>{
        setStudents((prev)=>prev.map((s)=>s.id === updatedStudent.id ? updatedStudent : s));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Student updated successfully!");
    };
    const handleStudentDeleted = (studentId)=>{
        setStudents((prev)=>prev.filter((s)=>s.id !== studentId));
        setSelectedStudents((prev)=>prev.filter((id)=>id !== studentId));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Student deleted successfully!");
    };
    const handleBulkStudentsUpdated = (updatedStudents)=>{
        setStudents(updatedStudents);
        setSelectedStudents([]);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl sm:text-3xl font-bold tracking-tight",
                                        children: "Students"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-muted-foreground",
                                        children: "Manage student records and information"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 139,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$student$2d$registration$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StudentRegistrationDialog"], {
                                open: showAddDialog,
                                onOpenChange: setShowAddDialog,
                                onStudentCreated: handleStudentCreated,
                                trigger: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    className: "w-full sm:w-auto",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                            className: "mr-2 h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                                            lineNumber: 151,
                                            columnNumber: 17
                                        }, void 0),
                                        "Add Student"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/students/page.tsx",
                                    lineNumber: 150,
                                    columnNumber: 15
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 145,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 136,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col sm:flex-row gap-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-1 gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$csv$2d$import$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSVImportDialog"], {
                                    onStudentsImported: (importedStudents)=>{
                                        setStudents((prev)=>[
                                                ...prev,
                                                ...importedStudents
                                            ]);
                                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Imported ${importedStudents.length} students`);
                                    },
                                    trigger: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        className: "flex-1 sm:flex-none",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                className: "mr-2 h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 168,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Import CSV"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 169,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "Import"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 170,
                                                columnNumber: 19
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 167,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/students/page.tsx",
                                    lineNumber: 161,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$qr$2d$batch$2d$generator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QRBatchGenerator"], {
                                    students: students,
                                    trigger: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        className: "flex-1 sm:flex-none",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$qr$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__QrCode$3e$__["QrCode"], {
                                                className: "mr-2 h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 179,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Generate QR"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 180,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "QR"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 181,
                                                columnNumber: 19
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 178,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/students/page.tsx",
                                    lineNumber: 175,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    className: "flex-1 sm:flex-none",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                            className: "mr-2 h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                                            lineNumber: 187,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "hidden sm:inline",
                                            children: "Export All"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                                            lineNumber: 188,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sm:hidden",
                                            children: "Export"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                                            lineNumber: 189,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/students/page.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/students/page.tsx",
                lineNumber: 135,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-xs sm:text-sm font-medium",
                                        children: "Total Students"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 199,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                        className: "h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 200,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 198,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xl sm:text-2xl font-bold",
                                        children: stats.total
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 203,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Registered in system"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 205,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "Registered"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 206,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 204,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 197,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-xs sm:text-sm font-medium",
                                        children: "Active Students"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 213,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__["UserCheck"], {
                                        className: "h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 214,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 212,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xl sm:text-2xl font-bold text-green-600",
                                        children: stats.active
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 217,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Currently enrolled"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 219,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "Enrolled"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 220,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 218,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 216,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 211,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-xs sm:text-sm font-medium",
                                        children: "Inactive Students"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 227,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserX$3e$__["UserX"], {
                                        className: "h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 228,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 226,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xl sm:text-2xl font-bold text-yellow-600",
                                        children: stats.inactive
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 231,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Not currently active"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 233,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "Inactive"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 234,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 232,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 230,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 225,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-xs sm:text-sm font-medium",
                                        children: "Avg. Attendance"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 241,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 242,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 240,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xl sm:text-2xl font-bold",
                                        children: [
                                            stats.avgAttendance.toFixed(1),
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 245,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Overall attendance rate"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 247,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "sm:hidden",
                                                children: "Attendance"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                                lineNumber: 248,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 246,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 244,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 239,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/students/page.tsx",
                lineNumber: 196,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$student$2d$filters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StudentFiltersComponent"], {
                filters: filters,
                onFiltersChange: setFilters,
                availableGrades: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUniqueGrades"])(),
                availableSections: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUniqueSections"])(),
                availableCourses: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUniqueCourses"])(),
                availableYears: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$students$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUniqueYears"])()
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/students/page.tsx",
                lineNumber: 255,
                columnNumber: 7
            }, this),
            selectedStudents.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$bulk$2d$actions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BulkActions"], {
                selectedStudents: selectedStudents,
                students: students,
                onClearSelection: ()=>setSelectedStudents([]),
                onStudentsUpdated: handleBulkStudentsUpdated
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/students/page.tsx",
                lineNumber: 266,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Student Directory"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 278,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                        variant: "secondary",
                                        children: [
                                            filteredAndSortedStudents.length,
                                            " student",
                                            filteredAndSortedStudents.length !== 1 ? 's' : ''
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 279,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 277,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                                children: filters.search || Object.keys(filters).length > 1 ? `Filtered results from ${students.length} total students` : "Complete list of registered students"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 283,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 276,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "p-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$students$2d$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StudentsTable"], {
                                students: paginatedStudents,
                                selectedStudents: selectedStudents,
                                onSelectionChange: setSelectedStudents,
                                sortConfig: sortConfig,
                                onSortChange: setSortConfig,
                                onStudentUpdated: handleStudentUpdated,
                                onStudentDeleted: handleStudentDeleted
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/students/page.tsx",
                                lineNumber: 291,
                                columnNumber: 11
                            }, this),
                            filteredAndSortedStudents.length > pagination.pageSize && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 303,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$students$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pagination"], {
                                            pagination: pagination,
                                            onPaginationChange: setPagination
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/students/page.tsx",
                                            lineNumber: 305,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                                        lineNumber: 304,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/students/page.tsx",
                        lineNumber: 290,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/students/page.tsx",
                lineNumber: 275,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/students/page.tsx",
        lineNumber: 133,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=_e7debd78._.js.map