{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/Exception.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { CustomError } from 'ts-custom-error';\n/**\n * Custom Error class of type Exception.\n */\nvar Exception = /** @class */ (function (_super) {\n    __extends(Exception, _super);\n    /**\n     * Allows Exception to be constructed directly\n     * with some message and prototype definition.\n     */\n    function Exception(message) {\n        if (message === void 0) { message = undefined; }\n        var _this = _super.call(this, message) || this;\n        _this.message = message;\n        return _this;\n    }\n    Exception.prototype.getKind = function () {\n        var ex = this.constructor;\n        return ex.kind;\n    };\n    /**\n     * It's typed as string so it can be extended and overriden.\n     */\n    Exception.kind = 'Exception';\n    return Exception;\n}(CustomError));\nexport default Exception;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,YAA2B,SAAU,MAAM;IAC3C,UAAU,WAAW;IACrB;;;KAGC,GACD,SAAS,UAAU,OAAO;QACtB,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAW;QAC/C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,YAAY,IAAI;QAC9C,MAAM,OAAO,GAAG;QAChB,OAAO;IACX;IACA,UAAU,SAAS,CAAC,OAAO,GAAG;QAC1B,IAAI,KAAK,IAAI,CAAC,WAAW;QACzB,OAAO,GAAG,IAAI;IAClB;IACA;;KAEC,GACD,UAAU,IAAI,GAAG;IACjB,OAAO;AACX,EAAE,iKAAA,CAAA,cAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ArgumentException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar ArgumentException = /** @class */ (function (_super) {\n    __extends(ArgumentException, _super);\n    function ArgumentException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ArgumentException.kind = 'ArgumentException';\n    return ArgumentException;\n}(Exception));\nexport default ArgumentException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,kBAAkB,IAAI,GAAG;IACzB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/IllegalArgumentException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar IllegalArgumentException = /** @class */ (function (_super) {\n    __extends(IllegalArgumentException, _super);\n    function IllegalArgumentException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    IllegalArgumentException.kind = 'IllegalArgumentException';\n    return IllegalArgumentException;\n}(Exception));\nexport default IllegalArgumentException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,2BAA0C,SAAU,MAAM;IAC1D,UAAU,0BAA0B;IACpC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,yBAAyB,IAAI,GAAG;IAChC,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/BinaryBitmap.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from './IllegalArgumentException';\nvar BinaryBitmap = /** @class */ (function () {\n    function BinaryBitmap(binarizer) {\n        this.binarizer = binarizer;\n        if (binarizer === null) {\n            throw new IllegalArgumentException('Binarizer must be non-null.');\n        }\n    }\n    /**\n     * @return The width of the bitmap.\n     */\n    BinaryBitmap.prototype.getWidth = function () {\n        return this.binarizer.getWidth();\n    };\n    /**\n     * @return The height of the bitmap.\n     */\n    BinaryBitmap.prototype.getHeight = function () {\n        return this.binarizer.getHeight();\n    };\n    /**\n     * Converts one row of luminance data to 1 bit data. May actually do the conversion, or return\n     * cached data. Callers should assume this method is expensive and call it as seldom as possible.\n     * This method is intended for decoding 1D barcodes and may choose to apply sharpening.\n     *\n     * @param y The row to fetch, which must be in [0, bitmap height)\n     * @param row An optional preallocated array. If null or too small, it will be ignored.\n     *            If used, the Binarizer will call BitArray.clear(). Always use the returned object.\n     * @return The array of bits for this row (true means black).\n     * @throws NotFoundException if row can't be binarized\n     */\n    BinaryBitmap.prototype.getBlackRow = function (y /*int*/, row) {\n        return this.binarizer.getBlackRow(y, row);\n    };\n    /**\n     * Converts a 2D array of luminance data to 1 bit. As above, assume this method is expensive\n     * and do not call it repeatedly. This method is intended for decoding 2D barcodes and may or\n     * may not apply sharpening. Therefore, a row from this matrix may not be identical to one\n     * fetched using getBlackRow(), so don't mix and match between them.\n     *\n     * @return The 2D array of bits for the image (true means black).\n     * @throws NotFoundException if image can't be binarized to make a matrix\n     */\n    BinaryBitmap.prototype.getBlackMatrix = function () {\n        // The matrix is created on demand the first time it is requested, then cached. There are two\n        // reasons for this:\n        // 1. This work will never be done if the caller only installs 1D Reader objects, or if a\n        //    1D Reader finds a barcode before the 2D Readers run.\n        // 2. This work will only be done once even if the caller installs multiple 2D Readers.\n        if (this.matrix === null || this.matrix === undefined) {\n            this.matrix = this.binarizer.getBlackMatrix();\n        }\n        return this.matrix;\n    };\n    /**\n     * @return Whether this bitmap can be cropped.\n     */\n    BinaryBitmap.prototype.isCropSupported = function () {\n        return this.binarizer.getLuminanceSource().isCropSupported();\n    };\n    /**\n     * Returns a new object with cropped image data. Implementations may keep a reference to the\n     * original data rather than a copy. Only callable if isCropSupported() is true.\n     *\n     * @param left The left coordinate, which must be in [0,getWidth())\n     * @param top The top coordinate, which must be in [0,getHeight())\n     * @param width The width of the rectangle to crop.\n     * @param height The height of the rectangle to crop.\n     * @return A cropped version of this object.\n     */\n    BinaryBitmap.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        var newSource = this.binarizer.getLuminanceSource().crop(left, top, width, height);\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /**\n     * @return Whether this bitmap supports counter-clockwise rotation.\n     */\n    BinaryBitmap.prototype.isRotateSupported = function () {\n        return this.binarizer.getLuminanceSource().isRotateSupported();\n    };\n    /**\n     * Returns a new object with rotated image data by 90 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    BinaryBitmap.prototype.rotateCounterClockwise = function () {\n        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise();\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /**\n     * Returns a new object with rotated image data by 45 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    BinaryBitmap.prototype.rotateCounterClockwise45 = function () {\n        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise45();\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /*@Override*/\n    BinaryBitmap.prototype.toString = function () {\n        try {\n            return this.getBlackMatrix().toString();\n        }\n        catch (e /*: NotFoundException*/) {\n            return '';\n        }\n    };\n    return BinaryBitmap;\n}());\nexport default BinaryBitmap;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA,IAAI,eAA8B;IAC9B,SAAS,aAAa,SAAS;QAC3B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,cAAc,MAAM;YACpB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;IACJ;IACA;;KAEC,GACD,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;IACA;;KAEC,GACD,aAAa,SAAS,CAAC,SAAS,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACnC;IACA;;;;;;;;;;KAUC,GACD,aAAa,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QACzD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG;IACzC;IACA;;;;;;;;KAQC,GACD,aAAa,SAAS,CAAC,cAAc,GAAG;QACpC,6FAA6F;QAC7F,oBAAoB;QACpB,yFAAyF;QACzF,0DAA0D;QAC1D,uFAAuF;QACvF,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK,WAAW;YACnD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc;QAC/C;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,aAAa,SAAS,CAAC,eAAe,GAAG;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,eAAe;IAC9D;IACA;;;;;;;;;KASC,GACD,aAAa,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACpF,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,KAAK,OAAO;QAC3E,OAAO,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IAC3D;IACA;;KAEC,GACD,aAAa,SAAS,CAAC,iBAAiB,GAAG;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,iBAAiB;IAChE;IACA;;;;;KAKC,GACD,aAAa,SAAS,CAAC,sBAAsB,GAAG;QAC5C,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,sBAAsB;QAC1E,OAAO,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IAC3D;IACA;;;;;KAKC,GACD,aAAa,SAAS,CAAC,wBAAwB,GAAG;QAC9C,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,wBAAwB;QAC5E,OAAO,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IAC3D;IACA,WAAW,GACX,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,IAAI;YACA,OAAO,IAAI,CAAC,cAAc,GAAG,QAAQ;QACzC,EACA,OAAO,EAAE,qBAAqB,KAAI;YAC9B,OAAO;QACX;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ChecksumException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar ChecksumException = /** @class */ (function (_super) {\n    __extends(ChecksumException, _super);\n    function ChecksumException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ChecksumException.getChecksumInstance = function () {\n        return new ChecksumException();\n    };\n    ChecksumException.kind = 'ChecksumException';\n    return ChecksumException;\n}(Exception));\nexport default ChecksumException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,kBAAkB,mBAAmB,GAAG;QACpC,OAAO,IAAI;IACf;IACA,kBAAkB,IAAI,GAAG;IACzB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/Binarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This class hierarchy provides a set of methods to convert luminance data to 1 bit data.\n * It allows the algorithm to vary polymorphically, for example allowing a very expensive\n * thresholding technique for servers and a fast one for mobile. It also permits the implementation\n * to vary, e.g. a JNI version for Android and a Java fallback version for other platforms.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar Binarizer = /** @class */ (function () {\n    function Binarizer(source) {\n        this.source = source;\n    }\n    Binarizer.prototype.getLuminanceSource = function () {\n        return this.source;\n    };\n    Binarizer.prototype.getWidth = function () {\n        return this.source.getWidth();\n    };\n    Binarizer.prototype.getHeight = function () {\n        return this.source.getHeight();\n    };\n    return Binarizer;\n}());\nexport default Binarizer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD;;;;;;;CAOC;;;AACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,MAAM;QACrB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,UAAU,SAAS,CAAC,kBAAkB,GAAG;QACrC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;IAC/B;IACA,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;IAChC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/System.js"], "sourcesContent": ["var System = /** @class */ (function () {\n    function System() {\n    }\n    // public static void arraycopy(Object src, int srcPos, Object dest, int destPos, int length)\n    /**\n     * Makes a copy of a array.\n     */\n    System.arraycopy = function (src, srcPos, dest, destPos, length) {\n        // TODO: better use split or set?\n        while (length--) {\n            dest[destPos++] = src[srcPos++];\n        }\n    };\n    /**\n     * Returns the current time in milliseconds.\n     */\n    System.currentTimeMillis = function () {\n        return Date.now();\n    };\n    return System;\n}());\nexport default System;\n"], "names": [], "mappings": ";;;AAAA,IAAI,SAAwB;IACxB,SAAS,UACT;IACA,6FAA6F;IAC7F;;KAEC,GACD,OAAO,SAAS,GAAG,SAAU,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM;QAC3D,iCAAiC;QACjC,MAAO,SAAU;YACb,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS;QACnC;IACJ;IACA;;KAEC,GACD,OAAO,iBAAiB,GAAG;QACvB,OAAO,KAAK,GAAG;IACnB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Arrays.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport System from './System';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport ArrayIndexOutOfBoundsException from '../ArrayIndexOutOfBoundsException';\nvar Arrays = /** @class */ (function () {\n    function Arrays() {\n    }\n    /**\n     * Assigns the specified int value to each element of the specified array\n     * of ints.\n     *\n     * @param a the array to be filled\n     * @param val the value to be stored in all elements of the array\n     */\n    Arrays.fill = function (a, val) {\n        for (var i = 0, len = a.length; i < len; i++)\n            a[i] = val;\n    };\n    /**\n     * Assigns the specified int value to each element of the specified\n     * range of the specified array of ints.  The range to be filled\n     * extends from index {@code fromIndex}, inclusive, to index\n     * {@code toIndex}, exclusive.  (If {@code fromIndex==toIndex}, the\n     * range to be filled is empty.)\n     *\n     * @param a the array to be filled\n     * @param fromIndex the index of the first element (inclusive) to be\n     *        filled with the specified value\n     * @param toIndex the index of the last element (exclusive) to be\n     *        filled with the specified value\n     * @param val the value to be stored in all elements of the array\n     * @throws IllegalArgumentException if {@code fromIndex > toIndex}\n     * @throws ArrayIndexOutOfBoundsException if {@code fromIndex < 0} or\n     *         {@code toIndex > a.length}\n     */\n    Arrays.fillWithin = function (a, fromIndex, toIndex, val) {\n        Arrays.rangeCheck(a.length, fromIndex, toIndex);\n        for (var i = fromIndex; i < toIndex; i++)\n            a[i] = val;\n    };\n    /**\n     * Checks that {@code fromIndex} and {@code toIndex} are in\n     * the range and throws an exception if they aren't.\n     */\n    Arrays.rangeCheck = function (arrayLength, fromIndex, toIndex) {\n        if (fromIndex > toIndex) {\n            throw new IllegalArgumentException('fromIndex(' + fromIndex + ') > toIndex(' + toIndex + ')');\n        }\n        if (fromIndex < 0) {\n            throw new ArrayIndexOutOfBoundsException(fromIndex);\n        }\n        if (toIndex > arrayLength) {\n            throw new ArrayIndexOutOfBoundsException(toIndex);\n        }\n    };\n    Arrays.asList = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return args;\n    };\n    Arrays.create = function (rows, cols, value) {\n        var arr = Array.from({ length: rows });\n        return arr.map(function (x) { return Array.from({ length: cols }).fill(value); });\n    };\n    Arrays.createInt32Array = function (rows, cols, value) {\n        var arr = Array.from({ length: rows });\n        return arr.map(function (x) { return Int32Array.from({ length: cols }).fill(value); });\n    };\n    Arrays.equals = function (first, second) {\n        if (!first) {\n            return false;\n        }\n        if (!second) {\n            return false;\n        }\n        if (!first.length) {\n            return false;\n        }\n        if (!second.length) {\n            return false;\n        }\n        if (first.length !== second.length) {\n            return false;\n        }\n        for (var i = 0, length_1 = first.length; i < length_1; i++) {\n            if (first[i] !== second[i]) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Arrays.hashCode = function (a) {\n        var e_1, _a;\n        if (a === null) {\n            return 0;\n        }\n        var result = 1;\n        try {\n            for (var a_1 = __values(a), a_1_1 = a_1.next(); !a_1_1.done; a_1_1 = a_1.next()) {\n                var element = a_1_1.value;\n                result = 31 * result + element;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (a_1_1 && !a_1_1.done && (_a = a_1.return)) _a.call(a_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return result;\n    };\n    Arrays.fillUint8Array = function (a, value) {\n        for (var i = 0; i !== a.length; i++) {\n            a[i] = value;\n        }\n    };\n    Arrays.copyOf = function (original, newLength) {\n        return original.slice(0, newLength);\n    };\n    Arrays.copyOfUint8Array = function (original, newLength) {\n        if (original.length <= newLength) {\n            var newArray = new Uint8Array(newLength);\n            newArray.set(original);\n            return newArray;\n        }\n        return original.slice(0, newLength);\n    };\n    Arrays.copyOfRange = function (original, from, to) {\n        var newLength = to - from;\n        var copy = new Int32Array(newLength);\n        System.arraycopy(original, from, copy, 0, newLength);\n        return copy;\n    };\n    /*\n    * Returns the index of of the element in a sorted array or (-n-1) where n is the insertion point\n    * for the new element.\n    * Parameters:\n    *     ar - A sorted array\n    *     el - An element to search for\n    *     comparator - A comparator function. The function takes two arguments: (a, b) and returns:\n    *        a negative number  if a is less than b;\n    *        0 if a is equal to b;\n    *        a positive number of a is greater than b.\n    * The array may contain duplicate elements. If there are more than one equal elements in the array,\n    * the returned value can be the index of any one of the equal elements.\n    *\n    * http://jsfiddle.net/aryzhov/pkfst550/\n    */\n    Arrays.binarySearch = function (ar, el, comparator) {\n        if (undefined === comparator) {\n            comparator = Arrays.numberComparator;\n        }\n        var m = 0;\n        var n = ar.length - 1;\n        while (m <= n) {\n            var k = (n + m) >> 1;\n            var cmp = comparator(el, ar[k]);\n            if (cmp > 0) {\n                m = k + 1;\n            }\n            else if (cmp < 0) {\n                n = k - 1;\n            }\n            else {\n                return k;\n            }\n        }\n        return -m - 1;\n    };\n    Arrays.numberComparator = function (a, b) {\n        return a - b;\n    };\n    return Arrays;\n}());\nexport default Arrays;\n"], "names": [], "mappings": ";;;AAWA;AACA;AACA;AAbA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;AAIA,IAAI,SAAwB;IACxB,SAAS,UACT;IACA;;;;;;KAMC,GACD,OAAO,IAAI,GAAG,SAAU,CAAC,EAAE,GAAG;QAC1B,IAAK,IAAI,IAAI,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,KAAK,IACrC,CAAC,CAAC,EAAE,GAAG;IACf;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,OAAO,UAAU,GAAG,SAAU,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QACpD,OAAO,UAAU,CAAC,EAAE,MAAM,EAAE,WAAW;QACvC,IAAK,IAAI,IAAI,WAAW,IAAI,SAAS,IACjC,CAAC,CAAC,EAAE,GAAG;IACf;IACA;;;KAGC,GACD,OAAO,UAAU,GAAG,SAAU,WAAW,EAAE,SAAS,EAAE,OAAO;QACzD,IAAI,YAAY,SAAS;YACrB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,eAAe,YAAY,iBAAiB,UAAU;QAC7F;QACA,IAAI,YAAY,GAAG;YACf,MAAM,IAAI,mLAAA,CAAA,UAA8B,CAAC;QAC7C;QACA,IAAI,UAAU,aAAa;YACvB,MAAM,IAAI,mLAAA,CAAA,UAA8B,CAAC;QAC7C;IACJ;IACA,OAAO,MAAM,GAAG;QACZ,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC5B;QACA,OAAO;IACX;IACA,OAAO,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK;QACvC,IAAI,MAAM,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK;QACpC,OAAO,IAAI,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAK,GAAG,IAAI,CAAC;QAAQ;IACnF;IACA,OAAO,gBAAgB,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK;QACjD,IAAI,MAAM,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK;QACpC,OAAO,IAAI,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,WAAW,IAAI,CAAC;gBAAE,QAAQ;YAAK,GAAG,IAAI,CAAC;QAAQ;IACxF;IACA,OAAO,MAAM,GAAG,SAAU,KAAK,EAAE,MAAM;QACnC,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,IAAI,CAAC,QAAQ;YACT,OAAO;QACX;QACA,IAAI,CAAC,MAAM,MAAM,EAAE;YACf,OAAO;QACX;QACA,IAAI,CAAC,OAAO,MAAM,EAAE;YAChB,OAAO;QACX;QACA,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,EAAE;YAChC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,WAAW,MAAM,MAAM,EAAE,IAAI,UAAU,IAAK;YACxD,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE;gBACxB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,OAAO,QAAQ,GAAG,SAAU,CAAC;QACzB,IAAI,KAAK;QACT,IAAI,MAAM,MAAM;YACZ,OAAO;QACX;QACA,IAAI,SAAS;QACb,IAAI;YACA,IAAK,IAAI,MAAM,SAAS,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,QAAQ,IAAI,IAAI,GAAI;gBAC7E,IAAI,UAAU,MAAM,KAAK;gBACzB,SAAS,KAAK,SAAS;YAC3B;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC;YAC3D,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,OAAO,cAAc,GAAG,SAAU,CAAC,EAAE,KAAK;QACtC,IAAK,IAAI,IAAI,GAAG,MAAM,EAAE,MAAM,EAAE,IAAK;YACjC,CAAC,CAAC,EAAE,GAAG;QACX;IACJ;IACA,OAAO,MAAM,GAAG,SAAU,QAAQ,EAAE,SAAS;QACzC,OAAO,SAAS,KAAK,CAAC,GAAG;IAC7B;IACA,OAAO,gBAAgB,GAAG,SAAU,QAAQ,EAAE,SAAS;QACnD,IAAI,SAAS,MAAM,IAAI,WAAW;YAC9B,IAAI,WAAW,IAAI,WAAW;YAC9B,SAAS,GAAG,CAAC;YACb,OAAO;QACX;QACA,OAAO,SAAS,KAAK,CAAC,GAAG;IAC7B;IACA,OAAO,WAAW,GAAG,SAAU,QAAQ,EAAE,IAAI,EAAE,EAAE;QAC7C,IAAI,YAAY,KAAK;QACrB,IAAI,OAAO,IAAI,WAAW;QAC1B,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,MAAM,MAAM,GAAG;QAC1C,OAAO;IACX;IACA;;;;;;;;;;;;;;IAcA,GACA,OAAO,YAAY,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,UAAU;QAC9C,IAAI,cAAc,YAAY;YAC1B,aAAa,OAAO,gBAAgB;QACxC;QACA,IAAI,IAAI;QACR,IAAI,IAAI,GAAG,MAAM,GAAG;QACpB,MAAO,KAAK,EAAG;YACX,IAAI,IAAI,AAAC,IAAI,KAAM;YACnB,IAAI,MAAM,WAAW,IAAI,EAAE,CAAC,EAAE;YAC9B,IAAI,MAAM,GAAG;gBACT,IAAI,IAAI;YACZ,OACK,IAAI,MAAM,GAAG;gBACd,IAAI,IAAI;YACZ,OACK;gBACD,OAAO;YACX;QACJ;QACA,OAAO,CAAC,IAAI;IAChB;IACA,OAAO,gBAAgB,GAAG,SAAU,CAAC,EAAE,CAAC;QACpC,OAAO,IAAI;IACf;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Integer.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Integer class.\n */\nvar Integer = /** @class */ (function () {\n    function Integer() {\n    }\n    Integer.numberOfTrailingZeros = function (i) {\n        var y;\n        if (i === 0)\n            return 32;\n        var n = 31;\n        y = i << 16;\n        if (y !== 0) {\n            n -= 16;\n            i = y;\n        }\n        y = i << 8;\n        if (y !== 0) {\n            n -= 8;\n            i = y;\n        }\n        y = i << 4;\n        if (y !== 0) {\n            n -= 4;\n            i = y;\n        }\n        y = i << 2;\n        if (y !== 0) {\n            n -= 2;\n            i = y;\n        }\n        return n - ((i << 1) >>> 31);\n    };\n    Integer.numberOfLeadingZeros = function (i) {\n        // HD, Figure 5-6\n        if (i === 0) {\n            return 32;\n        }\n        var n = 1;\n        if (i >>> 16 === 0) {\n            n += 16;\n            i <<= 16;\n        }\n        if (i >>> 24 === 0) {\n            n += 8;\n            i <<= 8;\n        }\n        if (i >>> 28 === 0) {\n            n += 4;\n            i <<= 4;\n        }\n        if (i >>> 30 === 0) {\n            n += 2;\n            i <<= 2;\n        }\n        n -= i >>> 31;\n        return n;\n    };\n    Integer.toHexString = function (i) {\n        return i.toString(16);\n    };\n    Integer.toBinaryString = function (intNumber) {\n        return String(parseInt(String(intNumber), 2));\n    };\n    // Returns the number of one-bits in the two's complement binary representation of the specified int value. This function is sometimes referred to as the population count.\n    // Returns:\n    // the number of one-bits in the two's complement binary representation of the specified int value.\n    Integer.bitCount = function (i) {\n        // HD, Figure 5-2\n        i = i - ((i >>> 1) & 0x55555555);\n        i = (i & 0x33333333) + ((i >>> 2) & 0x33333333);\n        i = (i + (i >>> 4)) & 0x0f0f0f0f;\n        i = i + (i >>> 8);\n        i = i + (i >>> 16);\n        return i & 0x3f;\n    };\n    Integer.truncDivision = function (dividend, divisor) {\n        return Math.trunc(dividend / divisor);\n    };\n    /**\n     * Converts A string to an integer.\n     * @param s A string to convert into a number.\n     * @param radix A value between 2 and 36 that specifies the base of the number in numString. If this argument is not supplied, strings with a prefix of '0x' are considered hexadecimal. All other strings are considered decimal.\n     */\n    Integer.parseInt = function (num, radix) {\n        if (radix === void 0) { radix = undefined; }\n        return parseInt(num, radix);\n    };\n    Integer.MIN_VALUE_32_BITS = -2147483648;\n    Integer.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n    return Integer;\n}());\nexport default Integer;\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,IAAI,UAAyB;IACzB,SAAS,WACT;IACA,QAAQ,qBAAqB,GAAG,SAAU,CAAC;QACvC,IAAI;QACJ,IAAI,MAAM,GACN,OAAO;QACX,IAAI,IAAI;QACR,IAAI,KAAK;QACT,IAAI,MAAM,GAAG;YACT,KAAK;YACL,IAAI;QACR;QACA,IAAI,KAAK;QACT,IAAI,MAAM,GAAG;YACT,KAAK;YACL,IAAI;QACR;QACA,IAAI,KAAK;QACT,IAAI,MAAM,GAAG;YACT,KAAK;YACL,IAAI;QACR;QACA,IAAI,KAAK;QACT,IAAI,MAAM,GAAG;YACT,KAAK;YACL,IAAI;QACR;QACA,OAAO,IAAI,CAAC,AAAC,KAAK,MAAO,EAAE;IAC/B;IACA,QAAQ,oBAAoB,GAAG,SAAU,CAAC;QACtC,iBAAiB;QACjB,IAAI,MAAM,GAAG;YACT,OAAO;QACX;QACA,IAAI,IAAI;QACR,IAAI,MAAM,OAAO,GAAG;YAChB,KAAK;YACL,MAAM;QACV;QACA,IAAI,MAAM,OAAO,GAAG;YAChB,KAAK;YACL,MAAM;QACV;QACA,IAAI,MAAM,OAAO,GAAG;YAChB,KAAK;YACL,MAAM;QACV;QACA,IAAI,MAAM,OAAO,GAAG;YAChB,KAAK;YACL,MAAM;QACV;QACA,KAAK,MAAM;QACX,OAAO;IACX;IACA,QAAQ,WAAW,GAAG,SAAU,CAAC;QAC7B,OAAO,EAAE,QAAQ,CAAC;IACtB;IACA,QAAQ,cAAc,GAAG,SAAU,SAAS;QACxC,OAAO,OAAO,SAAS,OAAO,YAAY;IAC9C;IACA,2KAA2K;IAC3K,WAAW;IACX,mGAAmG;IACnG,QAAQ,QAAQ,GAAG,SAAU,CAAC;QAC1B,iBAAiB;QACjB,IAAI,IAAI,CAAC,AAAC,MAAM,IAAK,UAAU;QAC/B,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,AAAC,MAAM,IAAK,UAAU;QAC9C,IAAI,AAAC,IAAI,CAAC,MAAM,CAAC,IAAK;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;QACjB,OAAO,IAAI;IACf;IACA,QAAQ,aAAa,GAAG,SAAU,QAAQ,EAAE,OAAO;QAC/C,OAAO,KAAK,KAAK,CAAC,WAAW;IACjC;IACA;;;;KAIC,GACD,QAAQ,QAAQ,GAAG,SAAU,GAAG,EAAE,KAAK;QACnC,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAW;QAC3C,OAAO,SAAS,KAAK;IACzB;IACA,QAAQ,iBAAiB,GAAG,CAAC;IAC7B,QAAQ,SAAS,GAAG,OAAO,gBAAgB;IAC3C,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/StringEncoding.js"], "sourcesContent": ["import UnsupportedOperationException from '../UnsupportedOperationException';\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Responsible for en/decoding strings.\n */\nvar StringEncoding = /** @class */ (function () {\n    function StringEncoding() {\n    }\n    /**\n     * Decodes some Uint8Array to a string format.\n     */\n    StringEncoding.decode = function (bytes, encoding) {\n        var encodingName = this.encodingName(encoding);\n        if (this.customDecoder) {\n            return this.customDecoder(bytes, encodingName);\n        }\n        // Increases browser support.\n        if (typeof TextDecoder === 'undefined' || this.shouldDecodeOnFallback(encodingName)) {\n            return this.decodeFallback(bytes, encodingName);\n        }\n        return new TextDecoder(encodingName).decode(bytes);\n    };\n    /**\n     * Checks if the decoding method should use the fallback for decoding\n     * once Node TextDecoder doesn't support all encoding formats.\n     *\n     * @param encodingName\n     */\n    StringEncoding.shouldDecodeOnFallback = function (encodingName) {\n        return !StringEncoding.isBrowser() && encodingName === 'ISO-8859-1';\n    };\n    /**\n     * Encodes some string into a Uint8Array.\n     */\n    StringEncoding.encode = function (s, encoding) {\n        var encodingName = this.encodingName(encoding);\n        if (this.customEncoder) {\n            return this.customEncoder(s, encodingName);\n        }\n        // Increases browser support.\n        if (typeof TextEncoder === 'undefined') {\n            return this.encodeFallback(s);\n        }\n        // TextEncoder only encodes to UTF8 by default as specified by encoding.spec.whatwg.org\n        return new TextEncoder().encode(s);\n    };\n    StringEncoding.isBrowser = function () {\n        return (typeof window !== 'undefined' && {}.toString.call(window) === '[object Window]');\n    };\n    /**\n     * Returns the string value from some encoding character set.\n     */\n    StringEncoding.encodingName = function (encoding) {\n        return typeof encoding === 'string'\n            ? encoding\n            : encoding.getName();\n    };\n    /**\n     * Returns character set from some encoding character set.\n     */\n    StringEncoding.encodingCharacterSet = function (encoding) {\n        if (encoding instanceof CharacterSetECI) {\n            return encoding;\n        }\n        return CharacterSetECI.getCharacterSetECIByName(encoding);\n    };\n    /**\n     * Runs a fallback for the native decoding funcion.\n     */\n    StringEncoding.decodeFallback = function (bytes, encoding) {\n        var characterSet = this.encodingCharacterSet(encoding);\n        if (StringEncoding.isDecodeFallbackSupported(characterSet)) {\n            var s = '';\n            for (var i = 0, length_1 = bytes.length; i < length_1; i++) {\n                var h = bytes[i].toString(16);\n                if (h.length < 2) {\n                    h = '0' + h;\n                }\n                s += '%' + h;\n            }\n            return decodeURIComponent(s);\n        }\n        if (characterSet.equals(CharacterSetECI.UnicodeBigUnmarked)) {\n            return String.fromCharCode.apply(null, new Uint16Array(bytes.buffer));\n        }\n        throw new UnsupportedOperationException(\"Encoding \" + this.encodingName(encoding) + \" not supported by fallback.\");\n    };\n    StringEncoding.isDecodeFallbackSupported = function (characterSet) {\n        return characterSet.equals(CharacterSetECI.UTF8) ||\n            characterSet.equals(CharacterSetECI.ISO8859_1) ||\n            characterSet.equals(CharacterSetECI.ASCII);\n    };\n    /**\n     * Runs a fallback for the native encoding funcion.\n     *\n     * @see https://stackoverflow.com/a/17192845/4367683\n     */\n    StringEncoding.encodeFallback = function (s) {\n        var encodedURIstring = btoa(unescape(encodeURIComponent(s)));\n        var charList = encodedURIstring.split('');\n        var uintArray = [];\n        for (var i = 0; i < charList.length; i++) {\n            uintArray.push(charList[i].charCodeAt(0));\n        }\n        return new Uint8Array(uintArray);\n    };\n    return StringEncoding;\n}());\nexport default StringEncoding;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA;;CAEC,GACD,IAAI,iBAAgC;IAChC,SAAS,kBACT;IACA;;KAEC,GACD,eAAe,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ;QAC7C,IAAI,eAAe,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO;QACrC;QACA,6BAA6B;QAC7B,IAAI,OAAO,gBAAgB,eAAe,IAAI,CAAC,sBAAsB,CAAC,eAAe;YACjF,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;QACtC;QACA,OAAO,IAAI,YAAY,cAAc,MAAM,CAAC;IAChD;IACA;;;;;KAKC,GACD,eAAe,sBAAsB,GAAG,SAAU,YAAY;QAC1D,OAAO,CAAC,eAAe,SAAS,MAAM,iBAAiB;IAC3D;IACA;;KAEC,GACD,eAAe,MAAM,GAAG,SAAU,CAAC,EAAE,QAAQ;QACzC,IAAI,eAAe,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG;QACjC;QACA,6BAA6B;QAC7B,IAAI,OAAO,gBAAgB,aAAa;YACpC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B;QACA,uFAAuF;QACvF,OAAO,IAAI,cAAc,MAAM,CAAC;IACpC;IACA,eAAe,SAAS,GAAG;QACvB,OAAQ,gBAAkB,eAAe,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY;IAC1E;IACA;;KAEC,GACD,eAAe,YAAY,GAAG,SAAU,QAAQ;QAC5C,OAAO,OAAO,aAAa,WACrB,WACA,SAAS,OAAO;IAC1B;IACA;;KAEC,GACD,eAAe,oBAAoB,GAAG,SAAU,QAAQ;QACpD,IAAI,oBAAoB,8KAAA,CAAA,UAAe,EAAE;YACrC,OAAO;QACX;QACA,OAAO,8KAAA,CAAA,UAAe,CAAC,wBAAwB,CAAC;IACpD;IACA;;KAEC,GACD,eAAe,cAAc,GAAG,SAAU,KAAK,EAAE,QAAQ;QACrD,IAAI,eAAe,IAAI,CAAC,oBAAoB,CAAC;QAC7C,IAAI,eAAe,yBAAyB,CAAC,eAAe;YACxD,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,WAAW,MAAM,MAAM,EAAE,IAAI,UAAU,IAAK;gBACxD,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC;gBAC1B,IAAI,EAAE,MAAM,GAAG,GAAG;oBACd,IAAI,MAAM;gBACd;gBACA,KAAK,MAAM;YACf;YACA,OAAO,mBAAmB;QAC9B;QACA,IAAI,aAAa,MAAM,CAAC,8KAAA,CAAA,UAAe,CAAC,kBAAkB,GAAG;YACzD,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,YAAY,MAAM,MAAM;QACvE;QACA,MAAM,IAAI,kLAAA,CAAA,UAA6B,CAAC,cAAc,IAAI,CAAC,YAAY,CAAC,YAAY;IACxF;IACA,eAAe,yBAAyB,GAAG,SAAU,YAAY;QAC7D,OAAO,aAAa,MAAM,CAAC,8KAAA,CAAA,UAAe,CAAC,IAAI,KAC3C,aAAa,MAAM,CAAC,8KAAA,CAAA,UAAe,CAAC,SAAS,KAC7C,aAAa,MAAM,CAAC,8KAAA,CAAA,UAAe,CAAC,KAAK;IACjD;IACA;;;;KAIC,GACD,eAAe,cAAc,GAAG,SAAU,CAAC;QACvC,IAAI,mBAAmB,KAAK,SAAS,mBAAmB;QACxD,IAAI,WAAW,iBAAiB,KAAK,CAAC;QACtC,IAAI,YAAY,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;QAC1C;QACA,OAAO,IAAI,WAAW;IAC1B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/StringBuilder.js"], "sourcesContent": ["import StringUtils from '../common/StringUtils';\nvar StringBuilder = /** @class */ (function () {\n    function StringBuilder(value) {\n        if (value === void 0) { value = ''; }\n        this.value = value;\n    }\n    StringBuilder.prototype.enableDecoding = function (encoding) {\n        this.encoding = encoding;\n        return this;\n    };\n    StringBuilder.prototype.append = function (s) {\n        if (typeof s === 'string') {\n            this.value += s.toString();\n        }\n        else if (this.encoding) {\n            // use passed format (fromCharCode will return UTF8 encoding)\n            this.value += StringUtils.castAsNonUtf8Char(s, this.encoding);\n        }\n        else {\n            // correctly converts from UTF-8, but not other encodings\n            this.value += String.fromCharCode(s);\n        }\n        return this;\n    };\n    StringBuilder.prototype.appendChars = function (str, offset, len) {\n        for (var i = offset; offset < offset + len; i++) {\n            this.append(str[i]);\n        }\n        return this;\n    };\n    StringBuilder.prototype.length = function () {\n        return this.value.length;\n    };\n    StringBuilder.prototype.charAt = function (n) {\n        return this.value.charAt(n);\n    };\n    StringBuilder.prototype.deleteCharAt = function (n) {\n        this.value = this.value.substr(0, n) + this.value.substring(n + 1);\n    };\n    StringBuilder.prototype.setCharAt = function (n, c) {\n        this.value = this.value.substr(0, n) + c + this.value.substr(n + 1);\n    };\n    StringBuilder.prototype.substring = function (start, end) {\n        return this.value.substring(start, end);\n    };\n    /**\n     * @note helper method for RSS Expanded\n     */\n    StringBuilder.prototype.setLengthToZero = function () {\n        this.value = '';\n    };\n    StringBuilder.prototype.toString = function () {\n        return this.value;\n    };\n    StringBuilder.prototype.insert = function (n, c) {\n        this.value = this.value.substring(0, n) + c + this.value.substring(n);\n    };\n    return StringBuilder;\n}());\nexport default StringBuilder;\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,gBAA+B;IAC/B,SAAS,cAAc,KAAK;QACxB,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAI;QACpC,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,QAAQ;QACvD,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,IAAI;IACf;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACxC,IAAI,OAAO,MAAM,UAAU;YACvB,IAAI,CAAC,KAAK,IAAI,EAAE,QAAQ;QAC5B,OACK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpB,6DAA6D;YAC7D,IAAI,CAAC,KAAK,IAAI,0KAAA,CAAA,UAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,QAAQ;QAChE,OACK;YACD,yDAAyD;YACzD,IAAI,CAAC,KAAK,IAAI,OAAO,YAAY,CAAC;QACtC;QACA,OAAO,IAAI;IACf;IACA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE,MAAM,EAAE,GAAG;QAC5D,IAAK,IAAI,IAAI,QAAQ,SAAS,SAAS,KAAK,IAAK;YAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QACtB;QACA,OAAO,IAAI;IACf;IACA,cAAc,SAAS,CAAC,MAAM,GAAG;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC5B;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC7B;IACA,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;IACpE;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,CAAC,EAAE,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;IACrE;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,KAAK,EAAE,GAAG;QACpD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO;IACvC;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,eAAe,GAAG;QACtC,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC,EAAE,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IACvE;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Float.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Float class.\n */\nvar Float = /** @class */ (function () {\n    function Float() {\n    }\n    /**\n     * SincTS has no difference between int and float, there's all numbers,\n     * this is used only to polyfill Java code.\n     */\n    Float.floatToIntBits = function (f) {\n        return f;\n    };\n    /**\n     * The float max value in JS is the number max value.\n     */\n    Float.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n    return Float;\n}());\nexport default Float;\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,IAAI,QAAuB;IACvB,SAAS,SACT;IACA;;;KAGC,GACD,MAAM,cAAc,GAAG,SAAU,CAAC;QAC9B,OAAO;IACX;IACA;;KAEC,GACD,MAAM,SAAS,GAAG,OAAO,gBAAgB;IACzC,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Formatter.js"], "sourcesContent": ["/**\n * Java Formatter class polyfill that works in the JS way.\n */\nvar Formatter = /** @class */ (function () {\n    function Formatter() {\n        this.buffer = '';\n    }\n    /**\n     *\n     * @see https://stackoverflow.com/a/13439711/4367683\n     *\n     * @param str\n     * @param arr\n     */\n    Formatter.form = function (str, arr) {\n        var i = -1;\n        function callback(exp, p0, p1, p2, p3, p4) {\n            if (exp === '%%')\n                return '%';\n            if (arr[++i] === undefined)\n                return undefined;\n            exp = p2 ? parseInt(p2.substr(1)) : undefined;\n            var base = p3 ? parseInt(p3.substr(1)) : undefined;\n            var val;\n            switch (p4) {\n                case 's':\n                    val = arr[i];\n                    break;\n                case 'c':\n                    val = arr[i][0];\n                    break;\n                case 'f':\n                    val = parseFloat(arr[i]).toFixed(exp);\n                    break;\n                case 'p':\n                    val = parseFloat(arr[i]).toPrecision(exp);\n                    break;\n                case 'e':\n                    val = parseFloat(arr[i]).toExponential(exp);\n                    break;\n                case 'x':\n                    val = parseInt(arr[i]).toString(base ? base : 16);\n                    break;\n                case 'd':\n                    val = parseFloat(parseInt(arr[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n                    break;\n            }\n            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n            var size = parseInt(p1); /* padding size */\n            var ch = p1 && (p1[0] + '') === '0' ? '0' : ' '; /* isnull? */\n            while (val.length < size)\n                val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n            return val;\n        }\n        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n        return str.replace(regex, callback);\n    };\n    /**\n     *\n     * @param append The new string to append.\n     * @param args Argumets values to be formated.\n     */\n    Formatter.prototype.format = function (append) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        this.buffer += Formatter.form(append, args);\n    };\n    /**\n     * Returns the Formatter string value.\n     */\n    Formatter.prototype.toString = function () {\n        return this.buffer;\n    };\n    return Formatter;\n}());\nexport default Formatter;\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,IAAI,YAA2B;IAC3B,SAAS;QACL,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;;;KAMC,GACD,UAAU,IAAI,GAAG,SAAU,GAAG,EAAE,GAAG;QAC/B,IAAI,IAAI,CAAC;QACT,SAAS,SAAS,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YACrC,IAAI,QAAQ,MACR,OAAO;YACX,IAAI,GAAG,CAAC,EAAE,EAAE,KAAK,WACb,OAAO;YACX,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM;YACpC,IAAI,OAAO,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM;YACzC,IAAI;YACJ,OAAQ;gBACJ,KAAK;oBACD,MAAM,GAAG,CAAC,EAAE;oBACZ;gBACJ,KAAK;oBACD,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;oBACf;gBACJ,KAAK;oBACD,MAAM,WAAW,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC;oBACjC;gBACJ,KAAK;oBACD,MAAM,WAAW,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC;oBACrC;gBACJ,KAAK;oBACD,MAAM,WAAW,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC;oBACvC;gBACJ,KAAK;oBACD,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,OAAO;oBAC9C;gBACJ,KAAK;oBACD,MAAM,WAAW,SAAS,GAAG,CAAC,EAAE,EAAE,OAAO,OAAO,IAAI,WAAW,CAAC,MAAM,OAAO,CAAC;oBAC9E;YACR;YACA,MAAM,OAAO,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC;YACtE,IAAI,OAAO,SAAS,KAAK,gBAAgB;YACzC,IAAI,KAAK,MAAM,AAAC,EAAE,CAAC,EAAE,GAAG,OAAQ,MAAM,MAAM,KAAK,WAAW;YAC5D,MAAO,IAAI,MAAM,GAAG,KAChB,MAAM,OAAO,YAAY,MAAM,KAAK,KAAK,KAAK,YAAY;YAC9D,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,OAAO,CAAC,OAAO;IAC9B;IACA;;;;KAIC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM;QACzC,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QAChC;QACA,IAAI,CAAC,MAAM,IAAI,UAAU,IAAI,CAAC,QAAQ;IAC1C;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Long.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Long class.\n */\nvar Long = /** @class */ (function () {\n    function Long() {\n    }\n    /**\n     * Parses a string to a number, since JS has no really Int64.\n     *\n     * @param num Numeric string.\n     * @param radix Destination radix.\n     */\n    Long.parseLong = function (num, radix) {\n        if (radix === void 0) { radix = undefined; }\n        return parseInt(num, radix);\n    };\n    return Long;\n}());\nexport default Long;\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,IAAI,OAAsB;IACtB,SAAS,QACT;IACA;;;;;KAKC,GACD,KAAK,SAAS,GAAG,SAAU,GAAG,EAAE,KAAK;QACjC,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAW;QAC3C,OAAO,SAAS,KAAK;IACzB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/OutputStream.js"], "sourcesContent": ["import IndexOutOfBoundsException from '../IndexOutOfBoundsException';\nimport NullPointerException from '../NullPointerException';\n/*\n * Copyright (c) 1994, 2004, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by <PERSON> in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of ME<PERSON>HANT<PERSON>ILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\n// package java.io;\n/**\n * This abstract class is the superclass of all classes representing\n * an output stream of bytes. An output stream accepts output bytes\n * and sends them to some sink.\n * <p>\n * Applications that need to define a subclass of\n * <code>OutputStream</code> must always provide at least a method\n * that writes one byte of output.\n *\n * <AUTHOR> van Hoff\n * @see     java.io.BufferedOutputStream\n * @see     java.io.ByteArrayOutputStream\n * @see     java.io.DataOutputStream\n * @see     java.io.FilterOutputStream\n * @see     java.io.InputStream\n * @see     java.io.OutputStream#write(int)\n * @since   JDK1.0\n */\nvar OutputStream /*implements Closeable, Flushable*/ = /** @class */ (function () {\n    function OutputStream() {\n    }\n    /**\n     * Writes <code>b.length</code> bytes from the specified byte array\n     * to this output stream. The general contract for <code>write(b)</code>\n     * is that it should have exactly the same effect as the call\n     * <code>write(b, 0, b.length)</code>.\n     *\n     * @param      b   the data.\n     * @exception  IOException  if an I/O error occurs.\n     * @see        java.io.OutputStream#write(byte[], int, int)\n     */\n    OutputStream.prototype.writeBytes = function (b) {\n        this.writeBytesOffset(b, 0, b.length);\n    };\n    /**\n     * Writes <code>len</code> bytes from the specified byte array\n     * starting at offset <code>off</code> to this output stream.\n     * The general contract for <code>write(b, off, len)</code> is that\n     * some of the bytes in the array <code>b</code> are written to the\n     * output stream in order; element <code>b[off]</code> is the first\n     * byte written and <code>b[off+len-1]</code> is the last byte written\n     * by this operation.\n     * <p>\n     * The <code>write</code> method of <code>OutputStream</code> calls\n     * the write method of one argument on each of the bytes to be\n     * written out. Subclasses are encouraged to override this method and\n     * provide a more efficient implementation.\n     * <p>\n     * If <code>b</code> is <code>null</code>, a\n     * <code>NullPointerException</code> is thrown.\n     * <p>\n     * If <code>off</code> is negative, or <code>len</code> is negative, or\n     * <code>off+len</code> is greater than the length of the array\n     * <code>b</code>, then an <tt>IndexOutOfBoundsException</tt> is thrown.\n     *\n     * @param      b     the data.\n     * @param      off   the start offset in the data.\n     * @param      len   the number of bytes to write.\n     * @exception  IOException  if an I/O error occurs. In particular,\n     *             an <code>IOException</code> is thrown if the output\n     *             stream is closed.\n     */\n    OutputStream.prototype.writeBytesOffset = function (b, off, len) {\n        if (b == null) {\n            throw new NullPointerException();\n        }\n        else if ((off < 0) || (off > b.length) || (len < 0) ||\n            ((off + len) > b.length) || ((off + len) < 0)) {\n            throw new IndexOutOfBoundsException();\n        }\n        else if (len === 0) {\n            return;\n        }\n        for (var i = 0; i < len; i++) {\n            this.write(b[off + i]);\n        }\n    };\n    /**\n     * Flushes this output stream and forces any buffered output bytes\n     * to be written out. The general contract of <code>flush</code> is\n     * that calling it is an indication that, if any bytes previously\n     * written have been buffered by the implementation of the output\n     * stream, such bytes should immediately be written to their\n     * intended destination.\n     * <p>\n     * If the intended destination of this stream is an abstraction provided by\n     * the underlying operating system, for example a file, then flushing the\n     * stream guarantees only that bytes previously written to the stream are\n     * passed to the operating system for writing; it does not guarantee that\n     * they are actually written to a physical device such as a disk drive.\n     * <p>\n     * The <code>flush</code> method of <code>OutputStream</code> does nothing.\n     *\n     * @exception  IOException  if an I/O error occurs.\n     */\n    OutputStream.prototype.flush = function () {\n    };\n    /**\n     * Closes this output stream and releases any system resources\n     * associated with this stream. The general contract of <code>close</code>\n     * is that it closes the output stream. A closed stream cannot perform\n     * output operations and cannot be reopened.\n     * <p>\n     * The <code>close</code> method of <code>OutputStream</code> does nothing.\n     *\n     * @exception  IOException  if an I/O error occurs.\n     */\n    OutputStream.prototype.close = function () {\n    };\n    return OutputStream;\n}());\nexport default OutputStream;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,mBAAmB;AACnB;;;;;;;;;;;;;;;;;CAiBC,GACD,IAAI,aAAa,iCAAiC,MAAoB;IAClE,SAAS,gBACT;IACA;;;;;;;;;KASC,GACD,aAAa,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAAE,MAAM;IACxC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,aAAa,SAAS,CAAC,gBAAgB,GAAG,SAAU,CAAC,EAAE,GAAG,EAAE,GAAG;QAC3D,IAAI,KAAK,MAAM;YACX,MAAM,IAAI,yKAAA,CAAA,UAAoB;QAClC,OACK,IAAI,AAAC,MAAM,KAAO,MAAM,EAAE,MAAM,IAAM,MAAM,KAC5C,AAAC,MAAM,MAAO,EAAE,MAAM,IAAM,AAAC,MAAM,MAAO,GAAI;YAC/C,MAAM,IAAI,8KAAA,CAAA,UAAyB;QACvC,OACK,IAAI,QAAQ,GAAG;YAChB;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE;QACzB;IACJ;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,aAAa,SAAS,CAAC,KAAK,GAAG,YAC/B;IACA;;;;;;;;;KASC,GACD,aAAa,SAAS,CAAC,KAAK,GAAG,YAC/B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/ByteArrayOutputStream.js"], "sourcesContent": ["/*\n * Copyright (c) 1994, 2010, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by Oracle in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n// package java.io;\n// import java.util.Arrays;\nimport Arrays from './Arrays';\nimport OutputStream from './OutputStream';\nimport Integer from './Integer';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport OutOfMemoryError from '../OutOfMemoryError';\nimport System from './System';\nimport IndexOutOfBoundsException from '../IndexOutOfBoundsException';\n/**\n * This class implements an output stream in which the data is\n * written into a byte array. The buffer automatically grows as data\n * is written to it.\n * The data can be retrieved using <code>toByteArray()</code> and\n * <code>toString()</code>.\n * <p>\n * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n * this class can be called after the stream has been closed without\n * generating an <tt>IOException</tt>.\n *\n * <AUTHOR> van Hoff\n * @since   JDK1.0\n */\nvar ByteArrayOutputStream = /** @class */ (function (_super) {\n    __extends(ByteArrayOutputStream, _super);\n    /**\n     * Creates a new byte array output stream. The buffer capacity is\n     * initially 32 bytes, though its size increases if necessary.\n     */\n    // public constructor() {\n    //     this(32);\n    // }\n    /**\n     * Creates a new byte array output stream, with a buffer capacity of\n     * the specified size, in bytes.\n     *\n     * @param   size   the initial size.\n     * @exception  IllegalArgumentException if size is negative.\n     */\n    function ByteArrayOutputStream(size) {\n        if (size === void 0) { size = 32; }\n        var _this = _super.call(this) || this;\n        /**\n         * The number of valid bytes in the buffer.\n         */\n        _this.count = 0;\n        if (size < 0) {\n            throw new IllegalArgumentException('Negative initial size: '\n                + size);\n        }\n        _this.buf = new Uint8Array(size);\n        return _this;\n    }\n    /**\n     * Increases the capacity if necessary to ensure that it can hold\n     * at least the number of elements specified by the minimum\n     * capacity argument.\n     *\n     * @param minCapacity the desired minimum capacity\n     * @throws OutOfMemoryError if {@code minCapacity < 0}.  This is\n     * interpreted as a request for the unsatisfiably large capacity\n     * {@code (long) Integer.MAX_VALUE + (minCapacity - Integer.MAX_VALUE)}.\n     */\n    ByteArrayOutputStream.prototype.ensureCapacity = function (minCapacity) {\n        // overflow-conscious code\n        if (minCapacity - this.buf.length > 0)\n            this.grow(minCapacity);\n    };\n    /**\n     * Increases the capacity to ensure that it can hold at least the\n     * number of elements specified by the minimum capacity argument.\n     *\n     * @param minCapacity the desired minimum capacity\n     */\n    ByteArrayOutputStream.prototype.grow = function (minCapacity) {\n        // overflow-conscious code\n        var oldCapacity = this.buf.length;\n        var newCapacity = oldCapacity << 1;\n        if (newCapacity - minCapacity < 0)\n            newCapacity = minCapacity;\n        if (newCapacity < 0) {\n            if (minCapacity < 0) // overflow\n                throw new OutOfMemoryError();\n            newCapacity = Integer.MAX_VALUE;\n        }\n        this.buf = Arrays.copyOfUint8Array(this.buf, newCapacity);\n    };\n    /**\n     * Writes the specified byte to this byte array output stream.\n     *\n     * @param   b   the byte to be written.\n     */\n    ByteArrayOutputStream.prototype.write = function (b) {\n        this.ensureCapacity(this.count + 1);\n        this.buf[this.count] = /*(byte)*/ b;\n        this.count += 1;\n    };\n    /**\n     * Writes <code>len</code> bytes from the specified byte array\n     * starting at offset <code>off</code> to this byte array output stream.\n     *\n     * @param   b     the data.\n     * @param   off   the start offset in the data.\n     * @param   len   the number of bytes to write.\n     */\n    ByteArrayOutputStream.prototype.writeBytesOffset = function (b, off, len) {\n        if ((off < 0) || (off > b.length) || (len < 0) ||\n            ((off + len) - b.length > 0)) {\n            throw new IndexOutOfBoundsException();\n        }\n        this.ensureCapacity(this.count + len);\n        System.arraycopy(b, off, this.buf, this.count, len);\n        this.count += len;\n    };\n    /**\n     * Writes the complete contents of this byte array output stream to\n     * the specified output stream argument, as if by calling the output\n     * stream's write method using <code>out.write(buf, 0, count)</code>.\n     *\n     * @param      out   the output stream to which to write the data.\n     * @exception  IOException  if an I/O error occurs.\n     */\n    ByteArrayOutputStream.prototype.writeTo = function (out) {\n        out.writeBytesOffset(this.buf, 0, this.count);\n    };\n    /**\n     * Resets the <code>count</code> field of this byte array output\n     * stream to zero, so that all currently accumulated output in the\n     * output stream is discarded. The output stream can be used again,\n     * reusing the already allocated buffer space.\n     *\n     * @see     java.io.ByteArrayInputStream#count\n     */\n    ByteArrayOutputStream.prototype.reset = function () {\n        this.count = 0;\n    };\n    /**\n     * Creates a newly allocated byte array. Its size is the current\n     * size of this output stream and the valid contents of the buffer\n     * have been copied into it.\n     *\n     * @return  the current contents of this output stream, as a byte array.\n     * @see     java.io.ByteArrayOutputStream#size()\n     */\n    ByteArrayOutputStream.prototype.toByteArray = function () {\n        return Arrays.copyOfUint8Array(this.buf, this.count);\n    };\n    /**\n     * Returns the current size of the buffer.\n     *\n     * @return  the value of the <code>count</code> field, which is the number\n     *          of valid bytes in this output stream.\n     * @see     java.io.ByteArrayOutputStream#count\n     */\n    ByteArrayOutputStream.prototype.size = function () {\n        return this.count;\n    };\n    ByteArrayOutputStream.prototype.toString = function (param) {\n        if (!param) {\n            return this.toString_void();\n        }\n        if (typeof param === 'string') {\n            return this.toString_string(param);\n        }\n        return this.toString_number(param);\n    };\n    /**\n     * Converts the buffer's contents into a string decoding bytes using the\n     * platform's default character set. The length of the new <tt>String</tt>\n     * is a function of the character set, and hence may not be equal to the\n     * size of the buffer.\n     *\n     * <p> This method always replaces malformed-input and unmappable-character\n     * sequences with the default replacement string for the platform's\n     * default character set. The {@linkplain java.nio.charset.CharsetDecoder}\n     * class should be used when more control over the decoding process is\n     * required.\n     *\n     * @return String decoded from the buffer's contents.\n     * @since  JDK1.1\n     */\n    ByteArrayOutputStream.prototype.toString_void = function () {\n        return new String(this.buf /*, 0, this.count*/).toString();\n    };\n    /**\n     * Converts the buffer's contents into a string by decoding the bytes using\n     * the specified {@link java.nio.charset.Charset charsetName}. The length of\n     * the new <tt>String</tt> is a function of the charset, and hence may not be\n     * equal to the length of the byte array.\n     *\n     * <p> This method always replaces malformed-input and unmappable-character\n     * sequences with this charset's default replacement string. The {@link\n     * java.nio.charset.CharsetDecoder} class should be used when more control\n     * over the decoding process is required.\n     *\n     * @param  charsetName  the name of a supported\n     *              {@linkplain java.nio.charset.Charset </code>charset<code>}\n     * @return String decoded from the buffer's contents.\n     * @exception  UnsupportedEncodingException\n     *             If the named charset is not supported\n     * @since   JDK1.1\n     */\n    ByteArrayOutputStream.prototype.toString_string = function (charsetName) {\n        return new String(this.buf /*, 0, this.count, charsetName*/).toString();\n    };\n    /**\n     * Creates a newly allocated string. Its size is the current size of\n     * the output stream and the valid contents of the buffer have been\n     * copied into it. Each character <i>c</i> in the resulting string is\n     * constructed from the corresponding element <i>b</i> in the byte\n     * array such that:\n     * <blockquote><pre>\n     *     c == (char)(((hibyte &amp; 0xff) &lt;&lt; 8) | (b &amp; 0xff))\n     * </pre></blockquote>\n     *\n     * @deprecated This method does not properly convert bytes into characters.\n     * As of JDK&nbsp;1.1, the preferred way to do this is via the\n     * <code>toString(String enc)</code> method, which takes an encoding-name\n     * argument, or the <code>toString()</code> method, which uses the\n     * platform's default character encoding.\n     *\n     * @param      hibyte    the high byte of each resulting Unicode character.\n     * @return     the current contents of the output stream, as a string.\n     * @see        java.io.ByteArrayOutputStream#size()\n     * @see        java.io.ByteArrayOutputStream#toString(String)\n     * @see        java.io.ByteArrayOutputStream#toString()\n     */\n    // @Deprecated\n    ByteArrayOutputStream.prototype.toString_number = function (hibyte) {\n        return new String(this.buf /*, hibyte, 0, this.count*/).toString();\n    };\n    /**\n     * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n     * this class can be called after the stream has been closed without\n     * generating an <tt>IOException</tt>.\n     * <p>\n     *\n     * @throws IOException\n     */\n    ByteArrayOutputStream.prototype.close = function () {\n    };\n    return ByteArrayOutputStream;\n}(OutputStream));\nexport default ByteArrayOutputStream;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;AAcD,mBAAmB;AACnB,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AArBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;;;AAUA;;;;;;;;;;;;;CAaC,GACD,IAAI,wBAAuC,SAAU,MAAM;IACvD,UAAU,uBAAuB;IACjC;;;KAGC,GACD,yBAAyB;IACzB,gBAAgB;IAChB,IAAI;IACJ;;;;;;KAMC,GACD,SAAS,sBAAsB,IAAI;QAC/B,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO;QAAI;QAClC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC;;SAEC,GACD,MAAM,KAAK,GAAG;QACd,IAAI,OAAO,GAAG;YACV,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,4BAC7B;QACV;QACA,MAAM,GAAG,GAAG,IAAI,WAAW;QAC3B,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,sBAAsB,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW;QAClE,0BAA0B;QAC1B,IAAI,cAAc,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAChC,IAAI,CAAC,IAAI,CAAC;IAClB;IACA;;;;;KAKC,GACD,sBAAsB,SAAS,CAAC,IAAI,GAAG,SAAU,WAAW;QACxD,0BAA0B;QAC1B,IAAI,cAAc,IAAI,CAAC,GAAG,CAAC,MAAM;QACjC,IAAI,cAAc,eAAe;QACjC,IAAI,cAAc,cAAc,GAC5B,cAAc;QAClB,IAAI,cAAc,GAAG;YACjB,IAAI,cAAc,GACd,MAAM,IAAI,qKAAA,CAAA,UAAgB;YAC9B,cAAc,oKAAA,CAAA,UAAO,CAAC,SAAS;QACnC;QACA,IAAI,CAAC,GAAG,GAAG,mKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE;IACjD;IACA;;;;KAIC,GACD,sBAAsB,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG;QACjC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG;QAClC,IAAI,CAAC,KAAK,IAAI;IAClB;IACA;;;;;;;KAOC,GACD,sBAAsB,SAAS,CAAC,gBAAgB,GAAG,SAAU,CAAC,EAAE,GAAG,EAAE,GAAG;QACpE,IAAI,AAAC,MAAM,KAAO,MAAM,EAAE,MAAM,IAAM,MAAM,KACvC,AAAC,MAAM,MAAO,EAAE,MAAM,GAAG,GAAI;YAC9B,MAAM,IAAI,8KAAA,CAAA,UAAyB;QACvC;QACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG;QACjC,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE;QAC/C,IAAI,CAAC,KAAK,IAAI;IAClB;IACA;;;;;;;KAOC,GACD,sBAAsB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QACnD,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK;IAChD;IACA;;;;;;;KAOC,GACD,sBAAsB,SAAS,CAAC,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;;;;;KAOC,GACD,sBAAsB,SAAS,CAAC,WAAW,GAAG;QAC1C,OAAO,mKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK;IACvD;IACA;;;;;;KAMC,GACD,sBAAsB,SAAS,CAAC,IAAI,GAAG;QACnC,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,sBAAsB,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QACtD,IAAI,CAAC,OAAO;YACR,OAAO,IAAI,CAAC,aAAa;QAC7B;QACA,IAAI,OAAO,UAAU,UAAU;YAC3B,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC;QACA,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC;IACA;;;;;;;;;;;;;;KAcC,GACD,sBAAsB,SAAS,CAAC,aAAa,GAAG;QAC5C,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,KAAI,QAAQ;IAC5D;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,sBAAsB,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW;QACnE,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,8BAA8B,KAAI,QAAQ;IACzE;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,cAAc;IACd,sBAAsB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM;QAC9D,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,yBAAyB,KAAI,QAAQ;IACpE;IACA;;;;;;;KAOC,GACD,sBAAsB,SAAS,CAAC,KAAK,GAAG,YACxC;IACA,OAAO;AACX,EAAE,yKAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Charset.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar Charset = /** @class */ (function (_super) {\n    __extends(Charset, _super);\n    function Charset() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Charset.forName = function (name) {\n        return this.getCharacterSetECIByName(name);\n    };\n    return Charset;\n}(CharacterSetECI));\nexport default Charset;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,UAAyB,SAAU,MAAM;IACzC,UAAU,SAAS;IACnB,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,QAAQ,OAAO,GAAG,SAAU,IAAI;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,OAAO;AACX,EAAE,8KAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/StandardCharsets.js"], "sourcesContent": ["import CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar StandardCharsets = /** @class */ (function () {\n    function StandardCharsets() {\n    }\n    StandardCharsets.ISO_8859_1 = CharacterSetECI.ISO8859_1;\n    return StandardCharsets;\n}());\nexport default StandardCharsets;\n"], "names": [], "mappings": ";;;AAAA;;AACA;;CAEC,GACD,IAAI,mBAAkC;IAClC,SAAS,oBACT;IACA,iBAAiB,UAAU,GAAG,8KAAA,CAAA,UAAe,CAAC,SAAS;IACvD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/util/Collections.js"], "sourcesContent": ["var Collections = /** @class */ (function () {\n    function Collections() {\n    }\n    /**\n     * The singletonList(T) method is used to return an immutable list containing only the specified object.\n     */\n    Collections.singletonList = function (item) {\n        return [item];\n    };\n    /**\n     * The min(Collection<? extends T>, Comparator<? super T>) method is used to return the minimum element of the given collection, according to the order induced by the specified comparator.\n     */\n    Collections.min = function (collection, comparator) {\n        return collection.sort(comparator)[0];\n    };\n    return Collections;\n}());\nexport default Collections;\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA;;KAEC,GACD,YAAY,aAAa,GAAG,SAAU,IAAI;QACtC,OAAO;YAAC;SAAK;IACjB;IACA;;KAEC,GACD,YAAY,GAAG,GAAG,SAAU,UAAU,EAAE,UAAU;QAC9C,OAAO,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE;IACzC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/IndexOutOfBoundsException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar IndexOutOfBoundsException = /** @class */ (function (_super) {\n    __extends(IndexOutOfBoundsException, _super);\n    function IndexOutOfBoundsException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    IndexOutOfBoundsException.kind = 'IndexOutOfBoundsException';\n    return IndexOutOfBoundsException;\n}(Exception));\nexport default IndexOutOfBoundsException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,4BAA2C,SAAU,MAAM;IAC3D,UAAU,2BAA2B;IACrC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,0BAA0B,IAAI,GAAG;IACjC,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ArrayIndexOutOfBoundsException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport IndexOutOfBoundsException from './IndexOutOfBoundsException';\n/**\n * Custom Error class of type Exception.\n */\nvar ArrayIndexOutOfBoundsException = /** @class */ (function (_super) {\n    __extends(ArrayIndexOutOfBoundsException, _super);\n    function ArrayIndexOutOfBoundsException(index, message) {\n        if (index === void 0) { index = undefined; }\n        if (message === void 0) { message = undefined; }\n        var _this = _super.call(this, message) || this;\n        _this.index = index;\n        _this.message = message;\n        return _this;\n    }\n    ArrayIndexOutOfBoundsException.kind = 'ArrayIndexOutOfBoundsException';\n    return ArrayIndexOutOfBoundsException;\n}(IndexOutOfBoundsException));\nexport default ArrayIndexOutOfBoundsException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,iCAAgD,SAAU,MAAM;IAChE,UAAU,gCAAgC;IAC1C,SAAS,+BAA+B,KAAK,EAAE,OAAO;QAClD,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAW;QAC3C,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAW;QAC/C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,YAAY,IAAI;QAC9C,MAAM,KAAK,GAAG;QACd,MAAM,OAAO,GAAG;QAChB,OAAO;IACX;IACA,+BAA+B,IAAI,GAAG;IACtC,OAAO;AACX,EAAE,8KAAA,CAAA,UAAyB;uCACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/DecodeHintType.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Encapsulates a type of hint that a caller may pass to a barcode reader to help it\n * more quickly or accurately decode it. It is up to implementations to decide what,\n * if anything, to do with the information that is supplied.\n *\n * <AUTHOR>\n * <AUTHOR> (<PERSON>)\n * @see Reader#decode(BinaryBitmap,java.util.Map)\n */\nvar DecodeHintType;\n(function (DecodeHintType) {\n    /**\n     * Unspecified, application-specific hint. Maps to an unspecified {@link Object}.\n     */\n    DecodeHintType[DecodeHintType[\"OTHER\"] = 0] = \"OTHER\"; /*(Object.class)*/\n    /**\n     * Image is a pure monochrome image of a barcode. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"PURE_BARCODE\"] = 1] = \"PURE_BARCODE\"; /*(Void.class)*/\n    /**\n     * Image is known to be of one of a few possible formats.\n     * Maps to a {@link List} of {@link BarcodeFormat}s.\n     */\n    DecodeHintType[DecodeHintType[\"POSSIBLE_FORMATS\"] = 2] = \"POSSIBLE_FORMATS\"; /*(List.class)*/\n    /**\n     * Spend more time to try to find a barcode; optimize for accuracy, not speed.\n     * Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"TRY_HARDER\"] = 3] = \"TRY_HARDER\"; /*(Void.class)*/\n    /**\n     * Specifies what character encoding to use when decoding, where applicable (type String)\n     */\n    DecodeHintType[DecodeHintType[\"CHARACTER_SET\"] = 4] = \"CHARACTER_SET\"; /*(String.class)*/\n    /**\n     * Allowed lengths of encoded data -- reject anything else. Maps to an {@code Int32Array}.\n     */\n    DecodeHintType[DecodeHintType[\"ALLOWED_LENGTHS\"] = 5] = \"ALLOWED_LENGTHS\"; /*(Int32Array.class)*/\n    /**\n     * Assume Code 39 codes employ a check digit. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ASSUME_CODE_39_CHECK_DIGIT\"] = 6] = \"ASSUME_CODE_39_CHECK_DIGIT\"; /*(Void.class)*/\n    /**\n     * Enable extended mode for Code 39 codes. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ENABLE_CODE_39_EXTENDED_MODE\"] = 7] = \"ENABLE_CODE_39_EXTENDED_MODE\"; /*(Void.class)*/\n    /**\n     * Assume the barcode is being processed as a GS1 barcode, and modify behavior as needed.\n     * For example this affects FNC1 handling for Code 128 (aka GS1-128). Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ASSUME_GS1\"] = 8] = \"ASSUME_GS1\"; /*(Void.class)*/\n    /**\n     * If true, return the start and end digits in a Codabar barcode instead of stripping them. They\n     * are alpha, whereas the rest are numeric. By default, they are stripped, but this causes them\n     * to not be. Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"RETURN_CODABAR_START_END\"] = 9] = \"RETURN_CODABAR_START_END\"; /*(Void.class)*/\n    /**\n     * The caller needs to be notified via callback when a possible {@link ResultPoint}\n     * is found. Maps to a {@link ResultPointCallback}.\n     */\n    DecodeHintType[DecodeHintType[\"NEED_RESULT_POINT_CALLBACK\"] = 10] = \"NEED_RESULT_POINT_CALLBACK\"; /*(ResultPointCallback.class)*/\n    /**\n     * Allowed extension lengths for EAN or UPC barcodes. Other formats will ignore this.\n     * Maps to an {@code Int32Array} of the allowed extension lengths, for example [2], [5], or [2, 5].\n     * If it is optional to have an extension, do not set this hint. If this is set,\n     * and a UPC or EAN barcode is found but an extension is not, then no result will be returned\n     * at all.\n     */\n    DecodeHintType[DecodeHintType[\"ALLOWED_EAN_EXTENSIONS\"] = 11] = \"ALLOWED_EAN_EXTENSIONS\"; /*(Int32Array.class)*/\n    // End of enumeration values.\n    /**\n     * Data type the hint is expecting.\n     * Among the possible values the {@link Void} stands out as being used for\n     * hints that do not expect a value to be supplied (flag hints). Such hints\n     * will possibly have their value ignored, or replaced by a\n     * {@link Boolean#TRUE}. Hint suppliers should probably use\n     * {@link Boolean#TRUE} as directed by the actual hint documentation.\n     */\n    // private valueType: Class<?>\n    // DecodeHintType(valueType: Class<?>) {\n    //   this.valueType = valueType\n    // }\n    // public getValueType(): Class<?> {\n    //   return valueType\n    // }\n})(DecodeHintType || (DecodeHintType = {}));\nexport default DecodeHintType;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8BAA8B,GAC9B;;;;;;;;CAQC;;;AACD,IAAI;AACJ,CAAC,SAAU,cAAc;IACrB;;KAEC,GACD,cAAc,CAAC,cAAc,CAAC,QAAQ,GAAG,EAAE,GAAG,SAAS,gBAAgB;IACvE;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,eAAe,GAAG,EAAE,GAAG,gBAAgB,cAAc;IACnF;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,mBAAmB,GAAG,EAAE,GAAG,oBAAoB,cAAc;IAC3F;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,aAAa,GAAG,EAAE,GAAG,cAAc,cAAc;IAC/E;;KAEC,GACD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG,iBAAiB,gBAAgB;IACvF;;KAEC,GACD,cAAc,CAAC,cAAc,CAAC,kBAAkB,GAAG,EAAE,GAAG,mBAAmB,oBAAoB;IAC/F;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,6BAA6B,GAAG,EAAE,GAAG,8BAA8B,cAAc;IAC/G;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,+BAA+B,GAAG,EAAE,GAAG,gCAAgC,cAAc;IACnH;;;;KAIC,GACD,cAAc,CAAC,cAAc,CAAC,aAAa,GAAG,EAAE,GAAG,cAAc,cAAc;IAC/E;;;;KAIC,GACD,cAAc,CAAC,cAAc,CAAC,2BAA2B,GAAG,EAAE,GAAG,4BAA4B,cAAc;IAC3G;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,6BAA6B,GAAG,GAAG,GAAG,8BAA8B,6BAA6B;IAC/H;;;;;;KAMC,GACD,cAAc,CAAC,cAAc,CAAC,yBAAyB,GAAG,GAAG,GAAG,0BAA0B,oBAAoB;AAC9G,6BAA6B;AAC7B;;;;;;;KAOC,GACD,8BAA8B;AAC9B,wCAAwC;AACxC,+BAA+B;AAC/B,IAAI;AACJ,oCAAoC;AACpC,qBAAqB;AACrB,IAAI;AACR,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;uCAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/FormatException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar FormatException = /** @class */ (function (_super) {\n    __extends(FormatException, _super);\n    function FormatException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FormatException.getFormatInstance = function () {\n        return new FormatException();\n    };\n    FormatException.kind = 'FormatException';\n    return FormatException;\n}(Exception));\nexport default FormatException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,gBAAgB,iBAAiB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,gBAAgB,IAAI,GAAG;IACvB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1699, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/UnsupportedOperationException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar UnsupportedOperationException = /** @class */ (function (_super) {\n    __extends(UnsupportedOperationException, _super);\n    function UnsupportedOperationException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    UnsupportedOperationException.kind = 'UnsupportedOperationException';\n    return UnsupportedOperationException;\n}(Exception));\nexport default UnsupportedOperationException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,gCAA+C,SAAU,MAAM;IAC/D,UAAU,+BAA+B;IACzC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,8BAA8B,IAAI,GAAG;IACrC,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/NotFoundException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar NotFoundException = /** @class */ (function (_super) {\n    __extends(NotFoundException, _super);\n    function NotFoundException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NotFoundException.getNotFoundInstance = function () {\n        return new NotFoundException();\n    };\n    NotFoundException.kind = 'NotFoundException';\n    return NotFoundException;\n}(Exception));\nexport default NotFoundException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,kBAAkB,mBAAmB,GAAG;QACpC,OAAO,IAAI;IACf;IACA,kBAAkB,IAAI,GAAG;IACzB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/LuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from './util/StringBuilder';\nimport UnsupportedOperationException from './UnsupportedOperationException';\n/*namespace com.google.zxing {*/\n/**\n * The purpose of this class hierarchy is to abstract different bitmap implementations across\n * platforms into a standard interface for requesting greyscale luminance values. The interface\n * only provides immutable methods; therefore crop and rotation create copies. This is to ensure\n * that one Reader does not modify the original luminance source and leave it in an unknown state\n * for other Readers in the chain.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar LuminanceSource = /** @class */ (function () {\n    function LuminanceSource(width /*int*/, height /*int*/) {\n        this.width = width;\n        this.height = height;\n    }\n    /**\n     * @return The width of the bitmap.\n     */\n    LuminanceSource.prototype.getWidth = function () {\n        return this.width;\n    };\n    /**\n     * @return The height of the bitmap.\n     */\n    LuminanceSource.prototype.getHeight = function () {\n        return this.height;\n    };\n    /**\n     * @return Whether this subclass supports cropping.\n     */\n    LuminanceSource.prototype.isCropSupported = function () {\n        return false;\n    };\n    /**\n     * Returns a new object with cropped image data. Implementations may keep a reference to the\n     * original data rather than a copy. Only callable if isCropSupported() is true.\n     *\n     * @param left The left coordinate, which must be in [0,getWidth())\n     * @param top The top coordinate, which must be in [0,getHeight())\n     * @param width The width of the rectangle to crop.\n     * @param height The height of the rectangle to crop.\n     * @return A cropped version of this object.\n     */\n    LuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        throw new UnsupportedOperationException('This luminance source does not support cropping.');\n    };\n    /**\n     * @return Whether this subclass supports counter-clockwise rotation.\n     */\n    LuminanceSource.prototype.isRotateSupported = function () {\n        return false;\n    };\n    /**\n     * Returns a new object with rotated image data by 90 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    LuminanceSource.prototype.rotateCounterClockwise = function () {\n        throw new UnsupportedOperationException('This luminance source does not support rotation by 90 degrees.');\n    };\n    /**\n     * Returns a new object with rotated image data by 45 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    LuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        throw new UnsupportedOperationException('This luminance source does not support rotation by 45 degrees.');\n    };\n    /*@Override*/\n    LuminanceSource.prototype.toString = function () {\n        var row = new Uint8ClampedArray(this.width);\n        var result = new StringBuilder();\n        for (var y = 0; y < this.height; y++) {\n            var sourceRow = this.getRow(y, row);\n            for (var x = 0; x < this.width; x++) {\n                var luminance = sourceRow[x] & 0xFF;\n                var c = void 0;\n                if (luminance < 0x40) {\n                    c = '#';\n                }\n                else if (luminance < 0x80) {\n                    c = '+';\n                }\n                else if (luminance < 0xC0) {\n                    c = '.';\n                }\n                else {\n                    c = ' ';\n                }\n                result.append(c);\n            }\n            result.append('\\n');\n        }\n        return result.toString();\n    };\n    return LuminanceSource;\n}());\nexport default LuminanceSource;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;;;AACA,8BAA8B,GAC9B;;;;;;;;CAQC,GACD,IAAI,kBAAiC;IACjC,SAAS,gBAAgB,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QAC1C,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;KAEC,GACD,gBAAgB,SAAS,CAAC,QAAQ,GAAG;QACjC,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,gBAAgB,SAAS,CAAC,SAAS,GAAG;QAClC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,gBAAgB,SAAS,CAAC,eAAe,GAAG;QACxC,OAAO;IACX;IACA;;;;;;;;;KAS<PERSON>,GACD,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACvF,MAAM,IAAI,kLAAA,CAAA,UAA6B,CAAC;IAC5C;IACA;;KAEC,GACD,gBAAgB,SAAS,CAAC,iBAAiB,GAAG;QAC1C,OAAO;IACX;IACA;;;;;KAKC,GACD,gBAAgB,SAAS,CAAC,sBAAsB,GAAG;QAC/C,MAAM,IAAI,kLAAA,CAAA,UAA6B,CAAC;IAC5C;IACA;;;;;KAKC,GACD,gBAAgB,SAAS,CAAC,wBAAwB,GAAG;QACjD,MAAM,IAAI,kLAAA,CAAA,UAA6B,CAAC;IAC5C;IACA,WAAW,GACX,gBAAgB,SAAS,CAAC,QAAQ,GAAG;QACjC,IAAI,MAAM,IAAI,kBAAkB,IAAI,CAAC,KAAK;QAC1C,IAAI,SAAS,IAAI,0KAAA,CAAA,UAAa;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;YAClC,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,IAAK;gBACjC,IAAI,YAAY,SAAS,CAAC,EAAE,GAAG;gBAC/B,IAAI,IAAI,KAAK;gBACb,IAAI,YAAY,MAAM;oBAClB,IAAI;gBACR,OACK,IAAI,YAAY,MAAM;oBACvB,IAAI;gBACR,OACK,IAAI,YAAY,MAAM;oBACvB,IAAI;gBACR,OACK;oBACD,IAAI;gBACR;gBACA,OAAO,MAAM,CAAC;YAClB;YACA,OAAO,MAAM,CAAC;QAClB;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/InvertedLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport LuminanceSource from './LuminanceSource';\n/*namespace com.google.zxing {*/\n/**\n * A wrapper implementation of {@link LuminanceSource} which inverts the luminances it returns -- black becomes\n * white and vice versa, and each value becomes (255-value).\n *\n * <AUTHOR> Owen\n */\nvar InvertedLuminanceSource = /** @class */ (function (_super) {\n    __extends(InvertedLuminanceSource, _super);\n    function InvertedLuminanceSource(delegate) {\n        var _this = _super.call(this, delegate.getWidth(), delegate.getHeight()) || this;\n        _this.delegate = delegate;\n        return _this;\n    }\n    /*@Override*/\n    InvertedLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        var sourceRow = this.delegate.getRow(y, row);\n        var width = this.getWidth();\n        for (var i = 0; i < width; i++) {\n            sourceRow[i] = /*(byte)*/ (255 - (sourceRow[i] & 0xFF));\n        }\n        return sourceRow;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.getMatrix = function () {\n        var matrix = this.delegate.getMatrix();\n        var length = this.getWidth() * this.getHeight();\n        var invertedMatrix = new Uint8ClampedArray(length);\n        for (var i = 0; i < length; i++) {\n            invertedMatrix[i] = /*(byte)*/ (255 - (matrix[i] & 0xFF));\n        }\n        return invertedMatrix;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.isCropSupported = function () {\n        return this.delegate.isCropSupported();\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new InvertedLuminanceSource(this.delegate.crop(left, top, width, height));\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.isRotateSupported = function () {\n        return this.delegate.isRotateSupported();\n    };\n    /**\n     * @return original delegate {@link LuminanceSource} since invert undoes itself\n     */\n    /*@Override*/\n    InvertedLuminanceSource.prototype.invert = function () {\n        return this.delegate;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.rotateCounterClockwise = function () {\n        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise());\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise45());\n    };\n    return InvertedLuminanceSource;\n}(LuminanceSource));\nexport default InvertedLuminanceSource;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,8BAA8B,GAC9B;;;;;CAKC,GACD,IAAI,0BAAyC,SAAU,MAAM;IACzD,UAAU,yBAAyB;IACnC,SAAS,wBAAwB,QAAQ;QACrC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,QAAQ,IAAI,SAAS,SAAS,OAAO,IAAI;QAChF,MAAM,QAAQ,GAAG;QACjB,OAAO;IACX;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QAC/D,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG;QACxC,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,SAAS,CAAC,EAAE,GAAe,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI;QACzD;QACA,OAAO;IACX;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,SAAS,GAAG;QAC1C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS;QACpC,IAAI,SAAS,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS;QAC7C,IAAI,iBAAiB,IAAI,kBAAkB;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,cAAc,CAAC,EAAE,GAAe,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI;QAC3D;QACA,OAAO;IACX;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,eAAe,GAAG;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe;IACxC;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QAC/F,OAAO,IAAI,wBAAwB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO;IAC5E;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,iBAAiB,GAAG;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB;IAC1C;IACA;;KAEC,GACD,WAAW,GACX,wBAAwB,SAAS,CAAC,MAAM,GAAG;QACvC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,sBAAsB,GAAG;QACvD,OAAO,IAAI,wBAAwB,IAAI,CAAC,QAAQ,CAAC,sBAAsB;IAC3E;IACA,WAAW,GACX,wBAAwB,SAAS,CAAC,wBAAwB,GAAG;QACzD,OAAO,IAAI,wBAAwB,IAAI,CAAC,QAAQ,CAAC,wBAAwB;IAC7E;IACA,OAAO;AACX,EAAE,oKAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/Result.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport System from './util/System';\n/**\n * <p>Encapsulates the result of decoding a barcode within an image.</p>\n *\n * <AUTHOR>\n */\nvar Result = /** @class */ (function () {\n    // public constructor(private text: string,\n    //               Uint8Array rawBytes,\n    //               ResultPoconst resultPoints: Int32Array,\n    //               BarcodeFormat format) {\n    //   this(text, rawBytes, resultPoints, format, System.currentTimeMillis())\n    // }\n    // public constructor(text: string,\n    //               Uint8Array rawBytes,\n    //               ResultPoconst resultPoints: Int32Array,\n    //               BarcodeFormat format,\n    //               long timestamp) {\n    //   this(text, rawBytes, rawBytes == null ? 0 : 8 * rawBytes.length,\n    //        resultPoints, format, timestamp)\n    // }\n    function Result(text, rawBytes, numBits, resultPoints, format, timestamp) {\n        if (numBits === void 0) { numBits = rawBytes == null ? 0 : 8 * rawBytes.length; }\n        if (timestamp === void 0) { timestamp = System.currentTimeMillis(); }\n        this.text = text;\n        this.rawBytes = rawBytes;\n        this.numBits = numBits;\n        this.resultPoints = resultPoints;\n        this.format = format;\n        this.timestamp = timestamp;\n        this.text = text;\n        this.rawBytes = rawBytes;\n        if (undefined === numBits || null === numBits) {\n            this.numBits = (rawBytes === null || rawBytes === undefined) ? 0 : 8 * rawBytes.length;\n        }\n        else {\n            this.numBits = numBits;\n        }\n        this.resultPoints = resultPoints;\n        this.format = format;\n        this.resultMetadata = null;\n        if (undefined === timestamp || null === timestamp) {\n            this.timestamp = System.currentTimeMillis();\n        }\n        else {\n            this.timestamp = timestamp;\n        }\n    }\n    /**\n     * @return raw text encoded by the barcode\n     */\n    Result.prototype.getText = function () {\n        return this.text;\n    };\n    /**\n     * @return raw bytes encoded by the barcode, if applicable, otherwise {@code null}\n     */\n    Result.prototype.getRawBytes = function () {\n        return this.rawBytes;\n    };\n    /**\n     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n     * @since 3.3.0\n     */\n    Result.prototype.getNumBits = function () {\n        return this.numBits;\n    };\n    /**\n     * @return points related to the barcode in the image. These are typically points\n     *         identifying finder patterns or the corners of the barcode. The exact meaning is\n     *         specific to the type of barcode that was decoded.\n     */\n    Result.prototype.getResultPoints = function () {\n        return this.resultPoints;\n    };\n    /**\n     * @return {@link BarcodeFormat} representing the format of the barcode that was decoded\n     */\n    Result.prototype.getBarcodeFormat = function () {\n        return this.format;\n    };\n    /**\n     * @return {@link Map} mapping {@link ResultMetadataType} keys to values. May be\n     *   {@code null}. This contains optional metadata about what was detected about the barcode,\n     *   like orientation.\n     */\n    Result.prototype.getResultMetadata = function () {\n        return this.resultMetadata;\n    };\n    Result.prototype.putMetadata = function (type, value) {\n        if (this.resultMetadata === null) {\n            this.resultMetadata = new Map();\n        }\n        this.resultMetadata.set(type, value);\n    };\n    Result.prototype.putAllMetadata = function (metadata) {\n        if (metadata !== null) {\n            if (this.resultMetadata === null) {\n                this.resultMetadata = metadata;\n            }\n            else {\n                this.resultMetadata = new Map(metadata);\n            }\n        }\n    };\n    Result.prototype.addResultPoints = function (newPoints) {\n        var oldPoints = this.resultPoints;\n        if (oldPoints === null) {\n            this.resultPoints = newPoints;\n        }\n        else if (newPoints !== null && newPoints.length > 0) {\n            var allPoints = new Array(oldPoints.length + newPoints.length);\n            System.arraycopy(oldPoints, 0, allPoints, 0, oldPoints.length);\n            System.arraycopy(newPoints, 0, allPoints, oldPoints.length, newPoints.length);\n            this.resultPoints = allPoints;\n        }\n    };\n    Result.prototype.getTimestamp = function () {\n        return this.timestamp;\n    };\n    /*@Override*/\n    Result.prototype.toString = function () {\n        return this.text;\n    };\n    return Result;\n}());\nexport default Result;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA;;;;CAIC,GACD,IAAI,SAAwB;IACxB,2CAA2C;IAC3C,qCAAqC;IACrC,wDAAwD;IACxD,wCAAwC;IACxC,2EAA2E;IAC3E,IAAI;IACJ,mCAAmC;IACnC,qCAAqC;IACrC,wDAAwD;IACxD,sCAAsC;IACtC,kCAAkC;IAClC,qEAAqE;IACrE,0CAA0C;IAC1C,IAAI;IACJ,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS;QACpE,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU,YAAY,OAAO,IAAI,IAAI,SAAS,MAAM;QAAE;QAChF,IAAI,cAAc,KAAK,GAAG;YAAE,YAAY,mKAAA,CAAA,UAAM,CAAC,iBAAiB;QAAI;QACpE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,cAAc,WAAW,SAAS,SAAS;YAC3C,IAAI,CAAC,OAAO,GAAG,AAAC,aAAa,QAAQ,aAAa,YAAa,IAAI,IAAI,SAAS,MAAM;QAC1F,OACK;YACD,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,cAAc,aAAa,SAAS,WAAW;YAC/C,IAAI,CAAC,SAAS,GAAG,mKAAA,CAAA,UAAM,CAAC,iBAAiB;QAC7C,OACK;YACD,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,OAAO,GAAG;QACvB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,WAAW,GAAG;QAC3B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,UAAU,GAAG;QAC1B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,eAAe,GAAG;QAC/B,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,gBAAgB,GAAG;QAChC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,iBAAiB,GAAG;QACjC,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,KAAK;QAChD,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI;QAC9B;QACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM;IAClC;IACA,OAAO,SAAS,CAAC,cAAc,GAAG,SAAU,QAAQ;QAChD,IAAI,aAAa,MAAM;YACnB,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM;gBAC9B,IAAI,CAAC,cAAc,GAAG;YAC1B,OACK;gBACD,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI;YAClC;QACJ;IACJ;IACA,OAAO,SAAS,CAAC,eAAe,GAAG,SAAU,SAAS;QAClD,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,cAAc,MAAM;YACpB,IAAI,CAAC,YAAY,GAAG;QACxB,OACK,IAAI,cAAc,QAAQ,UAAU,MAAM,GAAG,GAAG;YACjD,IAAI,YAAY,IAAI,MAAM,UAAU,MAAM,GAAG,UAAU,MAAM;YAC7D,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW,GAAG,UAAU,MAAM;YAC7D,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW,UAAU,MAAM,EAAE,UAAU,MAAM;YAC5E,IAAI,CAAC,YAAY,GAAG;QACxB;IACJ;IACA,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,WAAW,GACX,OAAO,SAAS,CAAC,QAAQ,GAAG;QACxB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/BarcodeFormat.js"], "sourcesContent": ["/*\n * Direct port to TypeScript of ZXing by <PERSON>\n */\n/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Enumerates barcode formats known to this package. Please keep alphabetized.\n *\n * <AUTHOR>\n */\nvar BarcodeFormat;\n(function (BarcodeFormat) {\n    /** Aztec 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"AZTEC\"] = 0] = \"AZTEC\";\n    /** CODABAR 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODABAR\"] = 1] = \"CODABAR\";\n    /** Code 39 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_39\"] = 2] = \"CODE_39\";\n    /** Code 93 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_93\"] = 3] = \"CODE_93\";\n    /** Code 128 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_128\"] = 4] = \"CODE_128\";\n    /** Data Matrix 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"DATA_MATRIX\"] = 5] = \"DATA_MATRIX\";\n    /** EAN-8 1D format. */\n    BarcodeFormat[BarcodeFormat[\"EAN_8\"] = 6] = \"EAN_8\";\n    /** EAN-13 1D format. */\n    BarcodeFormat[BarcodeFormat[\"EAN_13\"] = 7] = \"EAN_13\";\n    /** ITF (Interleaved Two of Five) 1D format. */\n    BarcodeFormat[BarcodeFormat[\"ITF\"] = 8] = \"ITF\";\n    /** MaxiCode 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"MAXICODE\"] = 9] = \"MAXICODE\";\n    /** PDF417 format. */\n    BarcodeFormat[BarcodeFormat[\"PDF_417\"] = 10] = \"PDF_417\";\n    /** QR Code 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"QR_CODE\"] = 11] = \"QR_CODE\";\n    /** RSS 14 */\n    BarcodeFormat[BarcodeFormat[\"RSS_14\"] = 12] = \"RSS_14\";\n    /** RSS EXPANDED */\n    BarcodeFormat[BarcodeFormat[\"RSS_EXPANDED\"] = 13] = \"RSS_EXPANDED\";\n    /** UPC-A 1D format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_A\"] = 14] = \"UPC_A\";\n    /** UPC-E 1D format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_E\"] = 15] = \"UPC_E\";\n    /** UPC/EAN extension format. Not a stand-alone format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_EAN_EXTENSION\"] = 16] = \"UPC_EAN_EXTENSION\";\n})(BarcodeFormat || (BarcodeFormat = {}));\nexport default BarcodeFormat;\n"], "names": [], "mappings": "AAAA;;CAEC,GACD;;;;;;;;;;;;;;CAcC,GACD,8BAA8B,GAC9B;;;;CAIC;;;AACD,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB,6BAA6B,GAC7B,aAAa,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5C,uBAAuB,GACvB,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,uBAAuB,GACvB,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,uBAAuB,GACvB,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,wBAAwB,GACxB,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,GAAG;IAC/C,mCAAmC,GACnC,aAAa,CAAC,aAAa,CAAC,cAAc,GAAG,EAAE,GAAG;IAClD,qBAAqB,GACrB,aAAa,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC5C,sBAAsB,GACtB,aAAa,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE,GAAG;IAC7C,6CAA6C,GAC7C,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,GAAG;IAC1C,gCAAgC,GAChC,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,GAAG;IAC/C,mBAAmB,GACnB,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,GAAG,GAAG;IAC/C,+BAA+B,GAC/B,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,GAAG,GAAG;IAC/C,WAAW,GACX,aAAa,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,GAAG;IAC9C,iBAAiB,GACjB,aAAa,CAAC,aAAa,CAAC,eAAe,GAAG,GAAG,GAAG;IACpD,qBAAqB,GACrB,aAAa,CAAC,aAAa,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC7C,qBAAqB,GACrB,aAAa,CAAC,aAAa,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC7C,wDAAwD,GACxD,aAAa,CAAC,aAAa,CAAC,oBAAoB,GAAG,GAAG,GAAG;AAC7D,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;uCACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ResultMetadataType.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Represents some type of metadata about the result of the decoding that the decoder\n * wishes to communicate back to the caller.\n *\n * <AUTHOR> Owen\n */\nvar ResultMetadataType;\n(function (ResultMetadataType) {\n    /**\n     * Unspecified, application-specific metadata. Maps to an unspecified {@link Object}.\n     */\n    ResultMetadataType[ResultMetadataType[\"OTHER\"] = 0] = \"OTHER\";\n    /**\n     * Denotes the likely approximate orientation of the barcode in the image. This value\n     * is given as degrees rotated clockwise from the normal, upright orientation.\n     * For example a 1D barcode which was found by reading top-to-bottom would be\n     * said to have orientation \"90\". This key maps to an {@link Integer} whose\n     * value is in the range [0,360).\n     */\n    ResultMetadataType[ResultMetadataType[\"ORIENTATION\"] = 1] = \"ORIENTATION\";\n    /**\n     * <p>2D barcode formats typically encode text, but allow for a sort of 'byte mode'\n     * which is sometimes used to encode binary data. While {@link Result} makes available\n     * the complete raw bytes in the barcode for these formats, it does not offer the bytes\n     * from the byte segments alone.</p>\n     *\n     * <p>This maps to a {@link java.util.List} of byte arrays corresponding to the\n     * raw bytes in the byte segments in the barcode, in order.</p>\n     */\n    ResultMetadataType[ResultMetadataType[\"BYTE_SEGMENTS\"] = 2] = \"BYTE_SEGMENTS\";\n    /**\n     * Error correction level used, if applicable. The value type depends on the\n     * format, but is typically a String.\n     */\n    ResultMetadataType[ResultMetadataType[\"ERROR_CORRECTION_LEVEL\"] = 3] = \"ERROR_CORRECTION_LEVEL\";\n    /**\n     * For some periodicals, indicates the issue number as an {@link Integer}.\n     */\n    ResultMetadataType[ResultMetadataType[\"ISSUE_NUMBER\"] = 4] = \"ISSUE_NUMBER\";\n    /**\n     * For some products, indicates the suggested retail price in the barcode as a\n     * formatted {@link String}.\n     */\n    ResultMetadataType[ResultMetadataType[\"SUGGESTED_PRICE\"] = 5] = \"SUGGESTED_PRICE\";\n    /**\n     * For some products, the possible country of manufacture as a {@link String} denoting the\n     * ISO country code. Some map to multiple possible countries, like \"US/CA\".\n     */\n    ResultMetadataType[ResultMetadataType[\"POSSIBLE_COUNTRY\"] = 6] = \"POSSIBLE_COUNTRY\";\n    /**\n     * For some products, the extension text\n     */\n    ResultMetadataType[ResultMetadataType[\"UPC_EAN_EXTENSION\"] = 7] = \"UPC_EAN_EXTENSION\";\n    /**\n     * PDF417-specific metadata\n     */\n    ResultMetadataType[ResultMetadataType[\"PDF417_EXTRA_METADATA\"] = 8] = \"PDF417_EXTRA_METADATA\";\n    /**\n     * If the code format supports structured append and the current scanned code is part of one then the\n     * sequence number is given with it.\n     */\n    ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_SEQUENCE\"] = 9] = \"STRUCTURED_APPEND_SEQUENCE\";\n    /**\n     * If the code format supports structured append and the current scanned code is part of one then the\n     * parity is given with it.\n     */\n    ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_PARITY\"] = 10] = \"STRUCTURED_APPEND_PARITY\";\n})(ResultMetadataType || (ResultMetadataType = {}));\nexport default ResultMetadataType;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8BAA8B,GAC9B;;;;;CAKC;;;AACD,IAAI;AACJ,CAAC,SAAU,kBAAkB;IACzB;;KAEC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtD;;;;;;KAMC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,GAAG,EAAE,GAAG;IAC5D;;;;;;;;KAQC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC9D;;;KAGC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,yBAAyB,GAAG,EAAE,GAAG;IACvE;;KAEC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,eAAe,GAAG,EAAE,GAAG;IAC7D;;;KAGC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,EAAE,GAAG;IAChE;;;KAGC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACjE;;KAEC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAClE;;KAEC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,wBAAwB,GAAG,EAAE,GAAG;IACtE;;;KAGC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,6BAA6B,GAAG,EAAE,GAAG;IAC3E;;;KAGC,GACD,kBAAkB,CAAC,kBAAkB,CAAC,2BAA2B,GAAG,GAAG,GAAG;AAC9E,CAAC,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;uCAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ArithmeticException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar ArithmeticException = /** @class */ (function (_super) {\n    __extends(ArithmeticException, _super);\n    function ArithmeticException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ArithmeticException.kind = 'ArithmeticException';\n    return ArithmeticException;\n}(Exception));\nexport default ArithmeticException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,sBAAqC,SAAU,MAAM;IACrD,UAAU,qBAAqB;IAC/B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,oBAAoB,IAAI,GAAG;IAC3B,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ReedSolomonException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar ReedSolomonException = /** @class */ (function (_super) {\n    __extends(ReedSolomonException, _super);\n    function ReedSolomonException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ReedSolomonException.kind = 'ReedSolomonException';\n    return ReedSolomonException;\n}(Exception));\nexport default ReedSolomonException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,uBAAsC,SAAU,MAAM;IACtD,UAAU,sBAAsB;IAChC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,qBAAqB,IAAI,GAAG;IAC5B,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/IllegalStateException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar IllegalStateException = /** @class */ (function (_super) {\n    __extends(IllegalStateException, _super);\n    function IllegalStateException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    IllegalStateException.kind = 'IllegalStateException';\n    return IllegalStateException;\n}(Exception));\nexport default IllegalStateException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,wBAAuC,SAAU,MAAM;IACvD,UAAU,uBAAuB;IACjC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,sBAAsB,IAAI,GAAG;IAC7B,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ResultPoint.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\nimport MathUtils from './common/detector/MathUtils';\nimport Float from './util/Float';\n/**\n * <p>Encapsulates a point of interest in an image containing a barcode. Typically, this\n * would be the location of a finder pattern or the corner of the barcode, for example.</p>\n *\n * <AUTHOR> <PERSON>\n */\nvar ResultPoint = /** @class */ (function () {\n    function ResultPoint(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    ResultPoint.prototype.getX = function () {\n        return this.x;\n    };\n    ResultPoint.prototype.getY = function () {\n        return this.y;\n    };\n    /*@Override*/\n    ResultPoint.prototype.equals = function (other) {\n        if (other instanceof ResultPoint) {\n            var otherPoint = other;\n            return this.x === otherPoint.x && this.y === otherPoint.y;\n        }\n        return false;\n    };\n    /*@Override*/\n    ResultPoint.prototype.hashCode = function () {\n        return 31 * Float.floatToIntBits(this.x) + Float.floatToIntBits(this.y);\n    };\n    /*@Override*/\n    ResultPoint.prototype.toString = function () {\n        return '(' + this.x + ',' + this.y + ')';\n    };\n    /**\n     * Orders an array of three ResultPoints in an order [A,B,C] such that AB is less than AC\n     * and BC is less than AC, and the angle between BC and BA is less than 180 degrees.\n     *\n     * @param patterns array of three {@code ResultPoint} to order\n     */\n    ResultPoint.orderBestPatterns = function (patterns) {\n        // Find distances between pattern centers\n        var zeroOneDistance = this.distance(patterns[0], patterns[1]);\n        var oneTwoDistance = this.distance(patterns[1], patterns[2]);\n        var zeroTwoDistance = this.distance(patterns[0], patterns[2]);\n        var pointA;\n        var pointB;\n        var pointC;\n        // Assume one closest to other two is B; A and C will just be guesses at first\n        if (oneTwoDistance >= zeroOneDistance && oneTwoDistance >= zeroTwoDistance) {\n            pointB = patterns[0];\n            pointA = patterns[1];\n            pointC = patterns[2];\n        }\n        else if (zeroTwoDistance >= oneTwoDistance && zeroTwoDistance >= zeroOneDistance) {\n            pointB = patterns[1];\n            pointA = patterns[0];\n            pointC = patterns[2];\n        }\n        else {\n            pointB = patterns[2];\n            pointA = patterns[0];\n            pointC = patterns[1];\n        }\n        // Use cross product to figure out whether A and C are correct or flipped.\n        // This asks whether BC x BA has a positive z component, which is the arrangement\n        // we want for A, B, C. If it's negative, then we've got it flipped around and\n        // should swap A and C.\n        if (this.crossProductZ(pointA, pointB, pointC) < 0.0) {\n            var temp = pointA;\n            pointA = pointC;\n            pointC = temp;\n        }\n        patterns[0] = pointA;\n        patterns[1] = pointB;\n        patterns[2] = pointC;\n    };\n    /**\n     * @param pattern1 first pattern\n     * @param pattern2 second pattern\n     * @return distance between two points\n     */\n    ResultPoint.distance = function (pattern1, pattern2) {\n        return MathUtils.distance(pattern1.x, pattern1.y, pattern2.x, pattern2.y);\n    };\n    /**\n     * Returns the z component of the cross product between vectors BC and BA.\n     */\n    ResultPoint.crossProductZ = function (pointA, pointB, pointC) {\n        var bX = pointB.x;\n        var bY = pointB.y;\n        return ((pointC.x - bX) * (pointA.y - bY)) - ((pointC.y - bY) * (pointA.x - bX));\n    };\n    return ResultPoint;\n}());\nexport default ResultPoint;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8BAA8B;;;AAC9B;AACA;;;AACA;;;;;CAKC,GACD,IAAI,cAA6B;IAC7B,SAAS,YAAY,CAAC,EAAE,CAAC;QACrB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACb;IACA,YAAY,SAAS,CAAC,IAAI,GAAG;QACzB,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,YAAY,SAAS,CAAC,IAAI,GAAG;QACzB,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,WAAW,GACX,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QAC1C,IAAI,iBAAiB,aAAa;YAC9B,IAAI,aAAa;YACjB,OAAO,IAAI,CAAC,CAAC,KAAK,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,WAAW,CAAC;QAC7D;QACA,OAAO;IACX;IACA,WAAW,GACX,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,KAAK,kKAAA,CAAA,UAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,kKAAA,CAAA,UAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC1E;IACA,WAAW,GACX,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,GAAG;IACzC;IACA;;;;;KAKC,GACD,YAAY,iBAAiB,GAAG,SAAU,QAAQ;QAC9C,yCAAyC;QACzC,IAAI,kBAAkB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE;QAC5D,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE;QAC3D,IAAI,kBAAkB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE;QAC5D,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,8EAA8E;QAC9E,IAAI,kBAAkB,mBAAmB,kBAAkB,iBAAiB;YACxE,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;QACxB,OACK,IAAI,mBAAmB,kBAAkB,mBAAmB,iBAAiB;YAC9E,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;QACxB,OACK;YACD,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;YACpB,SAAS,QAAQ,CAAC,EAAE;QACxB;QACA,0EAA0E;QAC1E,iFAAiF;QACjF,8EAA8E;QAC9E,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,QAAQ,UAAU,KAAK;YAClD,IAAI,OAAO;YACX,SAAS;YACT,SAAS;QACb;QACA,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;IAClB;IACA;;;;KAIC,GACD,YAAY,QAAQ,GAAG,SAAU,QAAQ,EAAE,QAAQ;QAC/C,OAAO,oLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC;IAC5E;IACA;;KAEC,GACD,YAAY,aAAa,GAAG,SAAU,MAAM,EAAE,MAAM,EAAE,MAAM;QACxD,IAAI,KAAK,OAAO,CAAC;QACjB,IAAI,KAAK,OAAO,CAAC;QACjB,OAAO,AAAC,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAM,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;IAClF;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/NullPointerException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar NullPointerException = /** @class */ (function (_super) {\n    __extends(NullPointerException, _super);\n    function NullPointerException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NullPointerException.kind = 'NullPointerException';\n    return NullPointerException;\n}(Exception));\nexport default NullPointerException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,uBAAsC,SAAU,MAAM;IACtD,UAAU,sBAAsB;IAChC,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,qBAAqB,IAAI,GAAG;IAC5B,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/OutOfMemoryError.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar OutOfMemoryError = /** @class */ (function (_super) {\n    __extends(OutOfMemoryError, _super);\n    function OutOfMemoryError() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return OutOfMemoryError;\n}(Exception));\nexport default OutOfMemoryError;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,mBAAkC,SAAU,MAAM;IAClD,UAAU,kBAAkB;IAC5B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/ReaderException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar ReaderException = /** @class */ (function (_super) {\n    __extends(ReaderException, _super);\n    function ReaderException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ReaderException.kind = 'ReaderException';\n    return ReaderException;\n}(Exception));\nexport default ReaderException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,gBAAgB,IAAI,GAAG;IACvB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/MultiFormatReader.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport DecodeHintType from './DecodeHintType';\nimport BarcodeFormat from './BarcodeFormat';\nimport QRCodeReader from './qrcode/QRCodeReader';\nimport AztecReader from './aztec/AztecReader';\nimport MultiFormatOneDReader from './oned/MultiFormatOneDReader';\nimport DataMatrixReader from './datamatrix/DataMatrixReader';\nimport NotFoundException from './NotFoundException';\nimport PDF417Reader from './pdf417/PDF417Reader';\nimport ReaderException from './ReaderException';\n/*namespace com.google.zxing {*/\n/**\n * MultiFormatReader is a convenience class and the main entry point into the library for most uses.\n * By default it attempts to decode all barcode formats that the library supports. Optionally, you\n * can provide a hints object to request different behavior, for example only decoding QR codes.\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatReader = /** @class */ (function () {\n    function MultiFormatReader() {\n    }\n    /**\n     * This version of decode honors the intent of Reader.decode(BinaryBitmap) in that it\n     * passes null as a hint to the decoders. However, that makes it inefficient to call repeatedly.\n     * Use setHints() followed by decodeWithState() for continuous scan applications.\n     *\n     * @param image The pixel data to decode\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    /*@Override*/\n    // public decode(image: BinaryBitmap): Result {\n    //   setHints(null)\n    //   return decodeInternal(image)\n    // }\n    /**\n     * Decode an image using the hints provided. Does not honor existing state.\n     *\n     * @param image The pixel data to decode\n     * @param hints The hints to use, clearing the previous state.\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    /*@Override*/\n    MultiFormatReader.prototype.decode = function (image, hints) {\n        this.setHints(hints);\n        return this.decodeInternal(image);\n    };\n    /**\n     * Decode an image using the state set up by calling setHints() previously. Continuous scan\n     * clients will get a <b>large</b> speed increase by using this instead of decode().\n     *\n     * @param image The pixel data to decode\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    MultiFormatReader.prototype.decodeWithState = function (image) {\n        // Make sure to set up the default state so we don't crash\n        if (this.readers === null || this.readers === undefined) {\n            this.setHints(null);\n        }\n        return this.decodeInternal(image);\n    };\n    /**\n     * This method adds state to the MultiFormatReader. By setting the hints once, subsequent calls\n     * to decodeWithState(image) can reuse the same set of readers without reallocating memory. This\n     * is important for performance in continuous scan clients.\n     *\n     * @param hints The set of hints to use for subsequent calls to decode(image)\n     */\n    MultiFormatReader.prototype.setHints = function (hints) {\n        this.hints = hints;\n        var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n        /*@SuppressWarnings(\"unchecked\")*/\n        var formats = hints === null || hints === undefined ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n        var readers = new Array();\n        if (formats !== null && formats !== undefined) {\n            var addOneDReader = formats.some(function (f) {\n                return f === BarcodeFormat.UPC_A ||\n                    f === BarcodeFormat.UPC_E ||\n                    f === BarcodeFormat.EAN_13 ||\n                    f === BarcodeFormat.EAN_8 ||\n                    f === BarcodeFormat.CODABAR ||\n                    f === BarcodeFormat.CODE_39 ||\n                    f === BarcodeFormat.CODE_93 ||\n                    f === BarcodeFormat.CODE_128 ||\n                    f === BarcodeFormat.ITF ||\n                    f === BarcodeFormat.RSS_14 ||\n                    f === BarcodeFormat.RSS_EXPANDED;\n            });\n            // Put 1D readers upfront in \"normal\" mode\n            // TYPESCRIPTPORT: TODO: uncomment below as they are ported\n            if (addOneDReader && !tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n            if (formats.includes(BarcodeFormat.QR_CODE)) {\n                readers.push(new QRCodeReader());\n            }\n            if (formats.includes(BarcodeFormat.DATA_MATRIX)) {\n                readers.push(new DataMatrixReader());\n            }\n            if (formats.includes(BarcodeFormat.AZTEC)) {\n                readers.push(new AztecReader());\n            }\n            if (formats.includes(BarcodeFormat.PDF_417)) {\n                readers.push(new PDF417Reader());\n            }\n            // if (formats.includes(BarcodeFormat.MAXICODE)) {\n            //    readers.push(new MaxiCodeReader())\n            // }\n            // At end in \"try harder\" mode\n            if (addOneDReader && tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n        }\n        if (readers.length === 0) {\n            if (!tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n            readers.push(new QRCodeReader());\n            readers.push(new DataMatrixReader());\n            readers.push(new AztecReader());\n            readers.push(new PDF417Reader());\n            // readers.push(new MaxiCodeReader())\n            if (tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n        }\n        this.readers = readers; // .toArray(new Reader[readers.size()])\n    };\n    /*@Override*/\n    MultiFormatReader.prototype.reset = function () {\n        var e_1, _a;\n        if (this.readers !== null) {\n            try {\n                for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var reader = _c.value;\n                    reader.reset();\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n    };\n    /**\n     * @throws NotFoundException\n     */\n    MultiFormatReader.prototype.decodeInternal = function (image) {\n        var e_2, _a;\n        if (this.readers === null) {\n            throw new ReaderException('No readers where selected, nothing can be read.');\n        }\n        try {\n            for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var reader = _c.value;\n                // Trying to decode with ${reader} reader.\n                try {\n                    return reader.decode(image, this.hints);\n                }\n                catch (ex) {\n                    if (ex instanceof ReaderException) {\n                        continue;\n                    }\n                    // Bad Exception.\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        throw new NotFoundException('No MultiFormat Readers were able to detect the code.');\n    };\n    return MultiFormatReader;\n}());\nexport default MultiFormatReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;AAUA,8BAA8B,GAC9B;;;;;;;CAOC,GACD,IAAI,oBAAmC;IACnC,SAAS,qBACT;IACA;;;;;;;;;KASC,GACD,WAAW,GACX,+CAA+C;IAC/C,mBAAmB;IACnB,iCAAiC;IACjC,IAAI;IACJ;;;;;;;;KAQC,GACD,WAAW,GACX,kBAAkB,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QACvD,IAAI,CAAC,QAAQ,CAAC;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK;QACzD,0DAA0D;QAC1D,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,KAAK,WAAW;YACrD,IAAI,CAAC,QAAQ,CAAC;QAClB;QACA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA;;;;;;KAMC,GACD,kBAAkB,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAClD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,YAAY,UAAU,QAAQ,UAAU,aAAa,cAAc,MAAM,GAAG,CAAC,mKAAA,CAAA,UAAc,CAAC,UAAU;QAC1G,gCAAgC,GAChC,IAAI,UAAU,UAAU,QAAQ,UAAU,YAAY,OAAO,MAAM,GAAG,CAAC,mKAAA,CAAA,UAAc,CAAC,gBAAgB;QACtG,IAAI,UAAU,IAAI;QAClB,IAAI,YAAY,QAAQ,YAAY,WAAW;YAC3C,IAAI,gBAAgB,QAAQ,IAAI,CAAC,SAAU,CAAC;gBACxC,OAAO,MAAM,kKAAA,CAAA,UAAa,CAAC,KAAK,IAC5B,MAAM,kKAAA,CAAA,UAAa,CAAC,KAAK,IACzB,MAAM,kKAAA,CAAA,UAAa,CAAC,MAAM,IAC1B,MAAM,kKAAA,CAAA,UAAa,CAAC,KAAK,IACzB,MAAM,kKAAA,CAAA,UAAa,CAAC,OAAO,IAC3B,MAAM,kKAAA,CAAA,UAAa,CAAC,OAAO,IAC3B,MAAM,kKAAA,CAAA,UAAa,CAAC,OAAO,IAC3B,MAAM,kKAAA,CAAA,UAAa,CAAC,QAAQ,IAC5B,MAAM,kKAAA,CAAA,UAAa,CAAC,GAAG,IACvB,MAAM,kKAAA,CAAA,UAAa,CAAC,MAAM,IAC1B,MAAM,kKAAA,CAAA,UAAa,CAAC,YAAY;YACxC;YACA,0CAA0C;YAC1C,2DAA2D;YAC3D,IAAI,iBAAiB,CAAC,WAAW;gBAC7B,QAAQ,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAqB,CAAC;YAC3C;YACA,IAAI,QAAQ,QAAQ,CAAC,kKAAA,CAAA,UAAa,CAAC,OAAO,GAAG;gBACzC,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAY;YACjC;YACA,IAAI,QAAQ,QAAQ,CAAC,kKAAA,CAAA,UAAa,CAAC,WAAW,GAAG;gBAC7C,QAAQ,IAAI,CAAC,IAAI,mLAAA,CAAA,UAAgB;YACrC;YACA,IAAI,QAAQ,QAAQ,CAAC,kKAAA,CAAA,UAAa,CAAC,KAAK,GAAG;gBACvC,QAAQ,IAAI,CAAC,IAAI,yKAAA,CAAA,UAAW;YAChC;YACA,IAAI,QAAQ,QAAQ,CAAC,kKAAA,CAAA,UAAa,CAAC,OAAO,GAAG;gBACzC,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAY;YACjC;YACA,kDAAkD;YAClD,wCAAwC;YACxC,IAAI;YACJ,8BAA8B;YAC9B,IAAI,iBAAiB,WAAW;gBAC5B,QAAQ,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAqB,CAAC;YAC3C;QACJ;QACA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACtB,IAAI,CAAC,WAAW;gBACZ,QAAQ,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAqB,CAAC;YAC3C;YACA,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAY;YAC7B,QAAQ,IAAI,CAAC,IAAI,mLAAA,CAAA,UAAgB;YACjC,QAAQ,IAAI,CAAC,IAAI,yKAAA,CAAA,UAAW;YAC5B,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAY;YAC7B,qCAAqC;YACrC,IAAI,WAAW;gBACX,QAAQ,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAqB,CAAC;YAC3C;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,SAAS,uCAAuC;IACnE;IACA,WAAW,GACX,kBAAkB,SAAS,CAAC,KAAK,GAAG;QAChC,IAAI,KAAK;QACT,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACvB,IAAI;gBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;oBAC5E,IAAI,SAAS,GAAG,KAAK;oBACrB,OAAO,KAAK;gBAChB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;gBACpD,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QACxD,IAAI,KAAK;QACT,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACvB,MAAM,IAAI,oKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC5E,IAAI,SAAS,GAAG,KAAK;gBACrB,0CAA0C;gBAC1C,IAAI;oBACA,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK;gBAC1C,EACA,OAAO,IAAI;oBACP,IAAI,cAAc,oKAAA,CAAA,UAAe,EAAE;wBAC/B;oBACJ;gBACA,iBAAiB;gBACrB;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,MAAM,IAAI,sKAAA,CAAA,UAAiB,CAAC;IAChC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/EncodeHintType.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * These are a set of hints that you may pass to Writers to specify their behavior.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar EncodeHintType;\n(function (EncodeHintType) {\n    /**\n     * Specifies what degree of error correction to use, for example in QR Codes.\n     * Type depends on the encoder. For example for QR codes it's type\n     * {@link com.google.zxing.qrcode.decoder.ErrorCorrectionLevel ErrorCorrectionLevel}.\n     * For Aztec it is of type {@link Integer}, representing the minimal percentage of error correction words.\n     * For PDF417 it is of type {@link Integer}, valid values being 0 to 8.\n     * In all cases, it can also be a {@link String} representation of the desired value as well.\n     * Note: an Aztec symbol should have a minimum of 25% EC words.\n     */\n    EncodeHintType[EncodeHintType[\"ERROR_CORRECTION\"] = 0] = \"ERROR_CORRECTION\";\n    /**\n     * Specifies what character encoding to use where applicable (type {@link String})\n     */\n    EncodeHintType[EncodeHintType[\"CHARACTER_SET\"] = 1] = \"CHARACTER_SET\";\n    /**\n     * Specifies the matrix shape for Data Matrix (type {@link com.google.zxing.datamatrix.encoder.SymbolShapeHint})\n     */\n    EncodeHintType[EncodeHintType[\"DATA_MATRIX_SHAPE\"] = 2] = \"DATA_MATRIX_SHAPE\";\n    /**\n     * Specifies whether to use compact mode for Data Matrix (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String } value).\n     * The compact encoding mode also supports the encoding of characters that are not in the ISO-8859-1\n     * character set via ECIs.\n     * Please note that in that case, the most compact character encoding is chosen for characters in\n     * the input that are not in the ISO-8859-1 character set. Based on experience, some scanners do not\n     * support encodings like cp-1256 (Arabic). In such cases the encoding can be forced to UTF-8 by\n     * means of the {@link #CHARACTER_SET} encoding hint.\n     * Compact encoding also provides GS1-FNC1 support when {@link #GS1_FORMAT} is selected. In this case\n     * group-separator character (ASCII 29 decimal) can be used to encode the positions of FNC1 codewords\n     * for the purpose of delimiting AIs.\n     * This option and {@link #FORCE_C40} are mutually exclusive.\n     */\n    EncodeHintType[EncodeHintType[\"DATA_MATRIX_COMPACT\"] = 3] = \"DATA_MATRIX_COMPACT\";\n    /**\n     * Specifies a minimum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n     *\n     * @deprecated use width/height params in\n     * {@link com.google.zxing.datamatrix.DataMatrixWriter#encode(String, BarcodeFormat, int, int)}\n     */\n    /*@Deprecated*/\n    EncodeHintType[EncodeHintType[\"MIN_SIZE\"] = 4] = \"MIN_SIZE\";\n    /**\n     * Specifies a maximum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n     *\n     * @deprecated without replacement\n     */\n    /*@Deprecated*/\n    EncodeHintType[EncodeHintType[\"MAX_SIZE\"] = 5] = \"MAX_SIZE\";\n    /**\n     * Specifies margin, in pixels, to use when generating the barcode. The meaning can vary\n     * by format; for example it controls margin before and after the barcode horizontally for\n     * most 1D formats. (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"MARGIN\"] = 6] = \"MARGIN\";\n    /**\n     * Specifies whether to use compact mode for PDF417 (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String} value).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_COMPACT\"] = 7] = \"PDF417_COMPACT\";\n    /**\n     * Specifies what compaction mode to use for PDF417 (type\n     * {@link com.google.zxing.pdf417.encoder.Compaction Compaction} or {@link String} value of one of its\n     * enum values).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_COMPACTION\"] = 8] = \"PDF417_COMPACTION\";\n    /**\n     * Specifies the minimum and maximum number of rows and columns for PDF417 (type\n     * {@link com.google.zxing.pdf417.encoder.Dimensions Dimensions}).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_DIMENSIONS\"] = 9] = \"PDF417_DIMENSIONS\";\n    /**\n     * Specifies the required number of layers for an Aztec code.\n     * A negative number (-1, -2, -3, -4) specifies a compact Aztec code.\n     * 0 indicates to use the minimum number of layers (the default).\n     * A positive number (1, 2, .. 32) specifies a normal (non-compact) Aztec code.\n     * (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"AZTEC_LAYERS\"] = 10] = \"AZTEC_LAYERS\";\n    /**\n     * Specifies the exact version of QR code to be encoded.\n     * (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"QR_VERSION\"] = 11] = \"QR_VERSION\";\n    /**\n     * Specifies whether the data should be encoded to the GS1 standard (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String } value).\n     */\n    EncodeHintType[EncodeHintType[\"GS1_FORMAT\"] = 12] = \"GS1_FORMAT\";\n    /**\n     * Forces C40 encoding for data-matrix (type {@link Boolean}, or \"true\" or \"false\") {@link String } value). This\n     * option and {@link #DATA_MATRIX_COMPACT} are mutually exclusive.\n     */\n    EncodeHintType[EncodeHintType[\"FORCE_C40\"] = 13] = \"FORCE_C40\";\n})(EncodeHintType || (EncodeHintType = {}));\nexport default EncodeHintType;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8BAA8B,GAC9B;;;;CAIC;;;AACD,IAAI;AACJ,CAAC,SAAU,cAAc;IACrB;;;;;;;;KAQC,GACD,cAAc,CAAC,cAAc,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACzD;;KAEC,GACD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD;;KAEC,GACD,cAAc,CAAC,cAAc,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAC1D;;;;;;;;;;;;;KAaC,GACD,cAAc,CAAC,cAAc,CAAC,sBAAsB,GAAG,EAAE,GAAG;IAC5D;;;;;KAKC,GACD,aAAa,GACb,cAAc,CAAC,cAAc,CAAC,WAAW,GAAG,EAAE,GAAG;IACjD;;;;KAIC,GACD,aAAa,GACb,cAAc,CAAC,cAAc,CAAC,WAAW,GAAG,EAAE,GAAG;IACjD;;;;KAIC,GACD,cAAc,CAAC,cAAc,CAAC,SAAS,GAAG,EAAE,GAAG;IAC/C;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACvD;;;;KAIC,GACD,cAAc,CAAC,cAAc,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAC1D;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAC1D;;;;;;KAMC,GACD,cAAc,CAAC,cAAc,CAAC,eAAe,GAAG,GAAG,GAAG;IACtD;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,aAAa,GAAG,GAAG,GAAG;IACpD;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,aAAa,GAAG,GAAG,GAAG;IACpD;;;KAGC,GACD,cAAc,CAAC,cAAc,CAAC,YAAY,GAAG,GAAG,GAAG;AACvD,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;uCAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/WriterException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Exception from './Exception';\n/**\n * Custom Error class of type Exception.\n */\nvar WriterException = /** @class */ (function (_super) {\n    __extends(WriterException, _super);\n    function WriterException() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    WriterException.kind = 'WriterException';\n    return WriterException;\n}(Exception));\nexport default WriterException;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA;;CAEC,GACD,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,gBAAgB,IAAI,GAAG;IACvB,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/MultiFormatWriter.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// import DataMatrixWriter from './datamatrix/DataMatrixWriter'\n// import CodaBarWriter from './oned/CodaBarWriter'\n// import Code128Writer from './oned/Code128Writer'\n// import Code39Writer from './oned/Code39Writer'\n// import Code93Writer from './oned/Code93Writer'\n// import EAN13Writer from './oned/EAN13Writer'\n// import EAN8Writer from './oned/EAN8Writer'\n// import ITFWriter from './oned/ITFWriter'\n// import UPCAWriter from './oned/UPCAWriter'\n// import UPCEWriter from './oned/UPCEWriter'\n// import PDF417Writer from './pdf417/PDF417Writer'\nimport QRCodeWriter from './qrcode/QRCodeWriter';\nimport BarcodeFormat from './BarcodeFormat';\nimport IllegalArgumentException from './IllegalArgumentException';\n/*import java.util.Map;*/\n/**\n * This is a factory class which finds the appropriate Writer subclass for the BarcodeFormat\n * requested and encodes the barcode with the supplied contents.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatWriter = /** @class */ (function () {\n    function MultiFormatWriter() {\n    }\n    /*@Override*/\n    // public encode(contents: string,\n    //                         format: BarcodeFormat,\n    //                         width: number /*int*/,\n    //                         height: number /*int*/): BitMatrix /*throws WriterException */ {\n    //   return encode(contents, format, width, height, null)\n    // }\n    /*@Override*/\n    MultiFormatWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n        var writer;\n        switch (format) {\n            // case BarcodeFormat.EAN_8:\n            //   writer = new EAN8Writer()\n            //   break\n            // case BarcodeFormat.UPC_E:\n            //   writer = new UPCEWriter()\n            //   break\n            // case BarcodeFormat.EAN_13:\n            //   writer = new EAN13Writer()\n            //   break\n            // case BarcodeFormat.UPC_A:\n            //   writer = new UPCAWriter()\n            //   break\n            case BarcodeFormat.QR_CODE:\n                writer = new QRCodeWriter();\n                break;\n            // case BarcodeFormat.CODE_39:\n            //   writer = new Code39Writer()\n            //   break\n            // case BarcodeFormat.CODE_93:\n            //   writer = new Code93Writer()\n            //   break\n            // case BarcodeFormat.CODE_128:\n            //   writer = new Code128Writer()\n            //   break\n            // case BarcodeFormat.ITF:\n            //   writer = new ITFWriter()\n            //   break\n            // case BarcodeFormat.PDF_417:\n            //   writer = new PDF417Writer()\n            //   break\n            // case BarcodeFormat.CODABAR:\n            //   writer = new CodaBarWriter()\n            //   break\n            // case BarcodeFormat.DATA_MATRIX:\n            //   writer = new DataMatrixWriter()\n            //   break\n            // case BarcodeFormat.AZTEC:\n            //   writer = new AztecWriter()\n            //   break\n            default:\n                throw new IllegalArgumentException('No encoder available for format ' + format);\n        }\n        return writer.encode(contents, format, width, height, hints);\n    };\n    return MultiFormatWriter;\n}());\nexport default MultiFormatWriter;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,+DAA+D;AAC/D,mDAAmD;AACnD,mDAAmD;AACnD,iDAAiD;AACjD,iDAAiD;AACjD,+CAA+C;AAC/C,6CAA6C;AAC7C,2CAA2C;AAC3C,6CAA6C;AAC7C,6CAA6C;AAC7C,mDAAmD;;;;AACnD;AACA;AACA;;;;AACA,uBAAuB,GACvB;;;;;CAKC,GACD,IAAI,oBAAmC;IACnC,SAAS,qBACT;IACA,WAAW,GACX,kCAAkC;IAClC,iDAAiD;IACjD,iDAAiD;IACjD,2FAA2F;IAC3F,yDAAyD;IACzD,IAAI;IACJ,WAAW,GACX,kBAAkB,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,KAAK;QACjG,IAAI;QACJ,OAAQ;YACJ,4BAA4B;YAC5B,8BAA8B;YAC9B,UAAU;YACV,4BAA4B;YAC5B,8BAA8B;YAC9B,UAAU;YACV,6BAA6B;YAC7B,+BAA+B;YAC/B,UAAU;YACV,4BAA4B;YAC5B,8BAA8B;YAC9B,UAAU;YACV,KAAK,kKAAA,CAAA,UAAa,CAAC,OAAO;gBACtB,SAAS,IAAI,2KAAA,CAAA,UAAY;gBACzB;YACJ,8BAA8B;YAC9B,gCAAgC;YAChC,UAAU;YACV,8BAA8B;YAC9B,gCAAgC;YAChC,UAAU;YACV,+BAA+B;YAC/B,iCAAiC;YACjC,UAAU;YACV,0BAA0B;YAC1B,6BAA6B;YAC7B,UAAU;YACV,8BAA8B;YAC9B,gCAAgC;YAChC,UAAU;YACV,8BAA8B;YAC9B,iCAAiC;YACjC,UAAU;YACV,kCAAkC;YAClC,oCAAoC;YACpC,UAAU;YACV,4BAA4B;YAC5B,+BAA+B;YAC/B,UAAU;YACV;gBACI,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,qCAAqC;QAChF;QACA,OAAO,OAAO,MAAM,CAAC,UAAU,QAAQ,OAAO,QAAQ;IAC1D;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/PlanarYUVLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing {*/\nimport System from './util/System';\nimport LuminanceSource from './LuminanceSource';\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This object extends LuminanceSource around an array of YUV data returned from the camera driver,\n * with the option to crop to a rectangle within the full data. This can be used to exclude\n * superfluous pixels around the perimeter and speed up decoding.\n *\n * It works for any pixel format where the Y channel is planar and appears first, including\n * YCbCr_420_SP and YCbCr_422_SP.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar PlanarYUVLuminanceSource = /** @class */ (function (_super) {\n    __extends(PlanarYUVLuminanceSource, _super);\n    function PlanarYUVLuminanceSource(yuvData, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/, width /*int*/, height /*int*/, reverseHorizontal) {\n        var _this = _super.call(this, width, height) || this;\n        _this.yuvData = yuvData;\n        _this.dataWidth = dataWidth;\n        _this.dataHeight = dataHeight;\n        _this.left = left;\n        _this.top = top;\n        if (left + width > dataWidth || top + height > dataHeight) {\n            throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n        }\n        if (reverseHorizontal) {\n            _this.reverseHorizontal(width, height);\n        }\n        return _this;\n    }\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        if (row === null || row === undefined || row.length < width) {\n            row = new Uint8ClampedArray(width);\n        }\n        var offset = (y + this.top) * this.dataWidth + this.left;\n        System.arraycopy(this.yuvData, offset, row, 0, width);\n        return row;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.getMatrix = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        // If the caller asks for the entire underlying image, save the copy and give them the\n        // original data. The docs specifically warn that result.length must be ignored.\n        if (width === this.dataWidth && height === this.dataHeight) {\n            return this.yuvData;\n        }\n        var area = width * height;\n        var matrix = new Uint8ClampedArray(area);\n        var inputOffset = this.top * this.dataWidth + this.left;\n        // If the width matches the full width of the underlying data, perform a single copy.\n        if (width === this.dataWidth) {\n            System.arraycopy(this.yuvData, inputOffset, matrix, 0, area);\n            return matrix;\n        }\n        // Otherwise copy one cropped row at a time.\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            System.arraycopy(this.yuvData, inputOffset, matrix, outputOffset, width);\n            inputOffset += this.dataWidth;\n        }\n        return matrix;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new PlanarYUVLuminanceSource(this.yuvData, this.dataWidth, this.dataHeight, this.left + left, this.top + top, width, height, false);\n    };\n    PlanarYUVLuminanceSource.prototype.renderThumbnail = function () {\n        var width = this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        var height = this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        var pixels = new Int32Array(width * height);\n        var yuv = this.yuvData;\n        var inputOffset = this.top * this.dataWidth + this.left;\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            for (var x = 0; x < width; x++) {\n                var grey = yuv[inputOffset + x * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR] & 0xff;\n                pixels[outputOffset + x] = 0xFF000000 | (grey * 0x00010101);\n            }\n            inputOffset += this.dataWidth * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        }\n        return pixels;\n    };\n    /**\n     * @return width of image from {@link #renderThumbnail()}\n     */\n    PlanarYUVLuminanceSource.prototype.getThumbnailWidth = function () {\n        return this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    };\n    /**\n     * @return height of image from {@link #renderThumbnail()}\n     */\n    PlanarYUVLuminanceSource.prototype.getThumbnailHeight = function () {\n        return this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    };\n    PlanarYUVLuminanceSource.prototype.reverseHorizontal = function (width /*int*/, height /*int*/) {\n        var yuvData = this.yuvData;\n        for (var y = 0, rowStart = this.top * this.dataWidth + this.left; y < height; y++, rowStart += this.dataWidth) {\n            var middle = rowStart + width / 2;\n            for (var x1 = rowStart, x2 = rowStart + width - 1; x1 < middle; x1++, x2--) {\n                var temp = yuvData[x1];\n                yuvData[x1] = yuvData[x2];\n                yuvData[x2] = temp;\n            }\n        }\n    };\n    PlanarYUVLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR = 2;\n    return PlanarYUVLuminanceSource;\n}(LuminanceSource));\nexport default PlanarYUVLuminanceSource;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,8BAA8B,GAC9B;AACA;AACA;AACA;AAjBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;AAMA;;;;;;;;;CASC,GACD,IAAI,2BAA0C,SAAU,MAAM;IAC1D,UAAU,0BAA0B;IACpC,SAAS,yBAAyB,OAAO,EAAE,UAAU,KAAK,GAAN,EAAU,WAAW,KAAK,GAAN,EAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,iBAAiB;QACzJ,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,WAAW,IAAI;QACpD,MAAM,OAAO,GAAG;QAChB,MAAM,SAAS,GAAG;QAClB,MAAM,UAAU,GAAG;QACnB,MAAM,IAAI,GAAG;QACb,MAAM,GAAG,GAAG;QACZ,IAAI,OAAO,QAAQ,aAAa,MAAM,SAAS,YAAY;YACvD,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,mBAAmB;YACnB,MAAM,iBAAiB,CAAC,OAAO;QACnC;QACA,OAAO;IACX;IACA,WAAW,GACX,yBAAyB,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QAChE,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAI;YAChC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,yCAAyC;QAChF;QACA,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,QAAQ,QAAQ,QAAQ,aAAa,IAAI,MAAM,GAAG,OAAO;YACzD,MAAM,IAAI,kBAAkB;QAChC;QACA,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;QACxD,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,KAAK,GAAG;QAC/C,OAAO;IACX;IACA,WAAW,GACX,yBAAyB,SAAS,CAAC,SAAS,GAAG;QAC3C,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,SAAS,IAAI,CAAC,SAAS;QAC3B,sFAAsF;QACtF,gFAAgF;QAChF,IAAI,UAAU,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YACxD,OAAO,IAAI,CAAC,OAAO;QACvB;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,SAAS,IAAI,kBAAkB;QACnC,IAAI,cAAc,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;QACvD,qFAAqF;QACrF,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE;YAC1B,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,QAAQ,GAAG;YACvD,OAAO;QACX;QACA,4CAA4C;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,eAAe,IAAI;YACvB,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,QAAQ,cAAc;YAClE,eAAe,IAAI,CAAC,SAAS;QACjC;QACA,OAAO;IACX;IACA,WAAW,GACX,yBAAyB,SAAS,CAAC,eAAe,GAAG;QACjD,OAAO;IACX;IACA,WAAW,GACX,yBAAyB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QAChG,OAAO,IAAI,yBAAyB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG,KAAK,OAAO,QAAQ;IACxI;IACA,yBAAyB,SAAS,CAAC,eAAe,GAAG;QACjD,IAAI,QAAQ,IAAI,CAAC,QAAQ,KAAK,yBAAyB,sBAAsB;QAC7E,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,yBAAyB,sBAAsB;QAC/E,IAAI,SAAS,IAAI,WAAW,QAAQ;QACpC,IAAI,MAAM,IAAI,CAAC,OAAO;QACtB,IAAI,cAAc,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,eAAe,IAAI;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,OAAO,GAAG,CAAC,cAAc,IAAI,yBAAyB,sBAAsB,CAAC,GAAG;gBACpF,MAAM,CAAC,eAAe,EAAE,GAAG,aAAc,OAAO;YACpD;YACA,eAAe,IAAI,CAAC,SAAS,GAAG,yBAAyB,sBAAsB;QACnF;QACA,OAAO;IACX;IACA;;KAEC,GACD,yBAAyB,SAAS,CAAC,iBAAiB,GAAG;QACnD,OAAO,IAAI,CAAC,QAAQ,KAAK,yBAAyB,sBAAsB;IAC5E;IACA;;KAEC,GACD,yBAAyB,SAAS,CAAC,kBAAkB,GAAG;QACpD,OAAO,IAAI,CAAC,SAAS,KAAK,yBAAyB,sBAAsB;IAC7E;IACA,yBAAyB,SAAS,CAAC,iBAAiB,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QAClF,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAK,IAAI,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,QAAQ,KAAK,YAAY,IAAI,CAAC,SAAS,CAAE;YAC3G,IAAI,SAAS,WAAW,QAAQ;YAChC,IAAK,IAAI,KAAK,UAAU,KAAK,WAAW,QAAQ,GAAG,KAAK,QAAQ,MAAM,KAAM;gBACxE,IAAI,OAAO,OAAO,CAAC,GAAG;gBACtB,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG;gBACzB,OAAO,CAAC,GAAG,GAAG;YAClB;QACJ;IACJ;IACA,yBAAyB,SAAS,CAAC,MAAM,GAAG;QACxC,OAAO,IAAI,4KAAA,CAAA,UAAuB,CAAC,IAAI;IAC3C;IACA,yBAAyB,sBAAsB,GAAG;IAClD,OAAO;AACX,EAAE,oKAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/RGBLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing {*/\nimport './InvertedLuminanceSource'; // required because of circular dependencies between LuminanceSource and InvertedLuminanceSource\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport LuminanceSource from './LuminanceSource';\nimport System from './util/System';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This class is used to help decode images from files which arrive as RGB data from\n * an ARGB pixel array. It does not support rotation.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR>\n */\nvar RGBLuminanceSource = /** @class */ (function (_super) {\n    __extends(RGBLuminanceSource, _super);\n    function RGBLuminanceSource(luminances, width /*int*/, height /*int*/, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/) {\n        var _this = _super.call(this, width, height) || this;\n        _this.dataWidth = dataWidth;\n        _this.dataHeight = dataHeight;\n        _this.left = left;\n        _this.top = top;\n        if (luminances.BYTES_PER_ELEMENT === 4) { // Int32Array\n            var size = width * height;\n            var luminancesUint8Array = new Uint8ClampedArray(size);\n            for (var offset = 0; offset < size; offset++) {\n                var pixel = luminances[offset];\n                var r = (pixel >> 16) & 0xff; // red\n                var g2 = (pixel >> 7) & 0x1fe; // 2 * green\n                var b = pixel & 0xff; // blue\n                // Calculate green-favouring average cheaply\n                luminancesUint8Array[offset] = /*(byte) */ ((r + g2 + b) / 4) & 0xFF;\n            }\n            _this.luminances = luminancesUint8Array;\n        }\n        else {\n            _this.luminances = luminances;\n        }\n        if (undefined === dataWidth) {\n            _this.dataWidth = width;\n        }\n        if (undefined === dataHeight) {\n            _this.dataHeight = height;\n        }\n        if (undefined === left) {\n            _this.left = 0;\n        }\n        if (undefined === top) {\n            _this.top = 0;\n        }\n        if (_this.left + width > _this.dataWidth || _this.top + height > _this.dataHeight) {\n            throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n        }\n        return _this;\n    }\n    /*@Override*/\n    RGBLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        if (row === null || row === undefined || row.length < width) {\n            row = new Uint8ClampedArray(width);\n        }\n        var offset = (y + this.top) * this.dataWidth + this.left;\n        System.arraycopy(this.luminances, offset, row, 0, width);\n        return row;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.getMatrix = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        // If the caller asks for the entire underlying image, save the copy and give them the\n        // original data. The docs specifically warn that result.length must be ignored.\n        if (width === this.dataWidth && height === this.dataHeight) {\n            return this.luminances;\n        }\n        var area = width * height;\n        var matrix = new Uint8ClampedArray(area);\n        var inputOffset = this.top * this.dataWidth + this.left;\n        // If the width matches the full width of the underlying data, perform a single copy.\n        if (width === this.dataWidth) {\n            System.arraycopy(this.luminances, inputOffset, matrix, 0, area);\n            return matrix;\n        }\n        // Otherwise copy one cropped row at a time.\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            System.arraycopy(this.luminances, inputOffset, matrix, outputOffset, width);\n            inputOffset += this.dataWidth;\n        }\n        return matrix;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new RGBLuminanceSource(this.luminances, width, height, this.dataWidth, this.dataHeight, this.left + left, this.top + top);\n    };\n    RGBLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    return RGBLuminanceSource;\n}(LuminanceSource));\nexport default RGBLuminanceSource;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,8BAA8B,GAC9B,gTAAoC,gGAAgG;AAEpI;AACA;AACA;AAlBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;AAOA;;;;;;CAMC,GACD,IAAI,qBAAoC,SAAU,MAAM;IACpD,UAAU,oBAAoB;IAC9B,SAAS,mBAAmB,UAAU,EAAE,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN,EAAU,WAAW,KAAK,GAAN,EAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN;QAC3H,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,WAAW,IAAI;QACpD,MAAM,SAAS,GAAG;QAClB,MAAM,UAAU,GAAG;QACnB,MAAM,IAAI,GAAG;QACb,MAAM,GAAG,GAAG;QACZ,IAAI,WAAW,iBAAiB,KAAK,GAAG;YACpC,IAAI,OAAO,QAAQ;YACnB,IAAI,uBAAuB,IAAI,kBAAkB;YACjD,IAAK,IAAI,SAAS,GAAG,SAAS,MAAM,SAAU;gBAC1C,IAAI,QAAQ,UAAU,CAAC,OAAO;gBAC9B,IAAI,IAAI,AAAC,SAAS,KAAM,MAAM,MAAM;gBACpC,IAAI,KAAK,AAAC,SAAS,IAAK,OAAO,YAAY;gBAC3C,IAAI,IAAI,QAAQ,MAAM,OAAO;gBAC7B,4CAA4C;gBAC5C,oBAAoB,CAAC,OAAO,GAAG,SAAS,GAAG,AAAC,CAAC,IAAI,KAAK,CAAC,IAAI,IAAK;YACpE;YACA,MAAM,UAAU,GAAG;QACvB,OACK;YACD,MAAM,UAAU,GAAG;QACvB;QACA,IAAI,cAAc,WAAW;YACzB,MAAM,SAAS,GAAG;QACtB;QACA,IAAI,cAAc,YAAY;YAC1B,MAAM,UAAU,GAAG;QACvB;QACA,IAAI,cAAc,MAAM;YACpB,MAAM,IAAI,GAAG;QACjB;QACA,IAAI,cAAc,KAAK;YACnB,MAAM,GAAG,GAAG;QAChB;QACA,IAAI,MAAM,IAAI,GAAG,QAAQ,MAAM,SAAS,IAAI,MAAM,GAAG,GAAG,SAAS,MAAM,UAAU,EAAE;YAC/E,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,OAAO;IACX;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QAC1D,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAI;YAChC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,yCAAyC;QAChF;QACA,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,QAAQ,QAAQ,QAAQ,aAAa,IAAI,MAAM,GAAG,OAAO;YACzD,MAAM,IAAI,kBAAkB;QAChC;QACA,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;QACxD,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,KAAK,GAAG;QAClD,OAAO;IACX;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,SAAS,GAAG;QACrC,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,SAAS,IAAI,CAAC,SAAS;QAC3B,sFAAsF;QACtF,gFAAgF;QAChF,IAAI,UAAU,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YACxD,OAAO,IAAI,CAAC,UAAU;QAC1B;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,SAAS,IAAI,kBAAkB;QACnC,IAAI,cAAc,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;QACvD,qFAAqF;QACrF,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE;YAC1B,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,QAAQ,GAAG;YAC1D,OAAO;QACX;QACA,4CAA4C;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,eAAe,IAAI;YACvB,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,QAAQ,cAAc;YACrE,eAAe,IAAI,CAAC,SAAS;QACjC;QACA,OAAO;IACX;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,eAAe,GAAG;QAC3C,OAAO;IACX;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QAC1F,OAAO,IAAI,mBAAmB,IAAI,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG;IAChI;IACA,mBAAmB,SAAS,CAAC,MAAM,GAAG;QAClC,OAAO,IAAI,4KAAA,CAAA,UAAuB,CAAC,IAAI;IAC3C;IACA,OAAO;AACX,EAAE,oKAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}]}