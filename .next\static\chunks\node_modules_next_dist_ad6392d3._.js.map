{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/build/polyfills/process.ts"], "sourcesContent": ["module.exports =\n  global.process?.env && typeof global.process?.env === 'object'\n    ? global.process\n    : (require('next/dist/compiled/process') as typeof import('next/dist/compiled/process'))\n"], "names": ["global", "module", "exports", "process", "env", "require"], "mappings": "IACEA,iBAA8BA;AADhCC,OAAOC,OAAO,GACZF,CAAAA,CAAAA,kBAAAA,4CAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,gBAAgBI,GAAG,KAAI,OAAA,CAAA,CAAOJ,mBAAAA,4CAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,iBAAgBI,GAAG,MAAK,WAClDJ,4CAAOG,OAAO,GACbE,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/dist/build/polyfills/polyfill-module.js"], "sourcesContent": ["\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n"], "names": [], "mappings": "AAAA,eAAc,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,SAAS,GAAC,OAAO,SAAS,CAAC,QAAQ,GAAE,aAAY,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,OAAO,GAAC,OAAO,SAAS,CAAC,SAAS,GAAE,iBAAgB,OAAO,SAAS,IAAE,OAAO,cAAc,CAAC,OAAO,SAAS,EAAC,eAAc;IAAC,cAAa,CAAC;IAAE,KAAI;QAAW,IAAI,IAAE,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QAAI,OAAO,IAAE,CAAC,CAAC,EAAE,GAAC,KAAK;IAAC;AAAC,IAAG,MAAM,SAAS,CAAC,IAAI,IAAE,CAAC,MAAM,SAAS,CAAC,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAC,IAAI,GAAE,IAAE,KAAG,EAAE,IAAI,CAAC,MAAM,OAAO,IAAE,EAAE,IAAI,CAAC,IAAE,KAAG;AAAC,GAAE,MAAM,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAE,GAAG,IAAI;AAAE,CAAC,GAAE,QAAQ,SAAS,CAAC,OAAO,IAAE,CAAC,QAAQ,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC;IAAE,IAAG,cAAY,OAAO,GAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAE;IAAG,IAAI,IAAE,IAAI,CAAC,WAAW,IAAE;IAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,OAAO;QAAC;IAAE,GAAE,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,MAAM;QAAC;IAAE;AAAE,CAAC,GAAE,OAAO,WAAW,IAAE,CAAC,OAAO,WAAW,GAAC,SAAS,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAC,CAAC;QAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,EAAC;IAAC,GAAE,CAAC;AAAE,CAAC,GAAE,MAAM,SAAS,CAAC,EAAE,IAAE,CAAC,MAAM,SAAS,CAAC,EAAE,GAAC,SAAS,CAAC;IAAE,IAAI,IAAE,KAAK,KAAK,CAAC,MAAI;IAAE,IAAG,IAAE,KAAG,CAAC,KAAG,IAAI,CAAC,MAAM,GAAE,CAAC,CAAC,IAAE,KAAG,KAAG,IAAI,CAAC,MAAM,GAAE,OAAO,IAAI,CAAC,EAAE;AAAA,CAAC,GAAE,OAAO,MAAM,IAAE,CAAC,OAAO,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG,QAAM,GAAE,MAAM,IAAI,UAAU;IAA8C,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,IAAG;AAAE,CAAC,GAAE,cAAa,OAAK,CAAC,IAAI,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG;QAAC,OAAM,CAAC,CAAC,IAAI,IAAI,GAAE;IAAE,EAAC,OAAM,GAAE;QAAC,OAAM,CAAC;IAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/build/deployment-id.ts"], "sourcesContent": ["export function getDeploymentIdQueryOrEmptyString(): string {\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    return `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n  }\n  return ''\n}\n"], "names": ["getDeploymentIdQueryOrEmptyString", "process", "env", "NEXT_DEPLOYMENT_ID"], "mappings": "AACMC,QAAQC,GAAG,CAACC,kBAAkB,EAAE;;;;;+BADtBH,qCAAAA;;;eAAAA;;;AAAT,SAASA;IACd;;IAGA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS =\n  'private-next-rsc-track-dynamic-import'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcaA,aAAa,EAAA;eAAbA;;IAuCAC,aAAa,EAAA;eAAbA;;IAnBAC,cAAc,EAAA;eAAdA;;IAiBAC,cAAc,EAAA;eAAdA;;IAwCAC,mBAAmB,EAAA;eAAnBA;;IAfAC,qBAAqB,EAAA;eAArBA;;IASAC,2BAA2B,EAAA;eAA3BA;;IAPAC,sBAAsB,EAAA;eAAtBA;;IAvCAC,cAAc,EAAA;eAAdA;;IAOAC,6BAA6B,EAAA;eAA7BA;;IAzCAC,mBAAmB,EAAA;eAAnBA;;IAqCAC,mBAAmB,EAAA;eAAnBA;;IACAC,0BAA0B,EAAA;eAA1BA;;IA1BAC,gBAAgB,EAAA;eAAhBA;;IAcAC,0BAA0B,EAAA;eAA1BA;;IAXAC,kCAAkC,EAAA;eAAlCA;;IACAC,sCAAsC,EAAA;eAAtCA;;IASAC,8BAA8B,EAAA;eAA9BA;;IAXAC,sBAAsB,EAAA;eAAtBA;;IASAC,wBAAwB,EAAA;eAAxBA;;IACAC,yBAAyB,EAAA;eAAzBA;;IAdAC,gBAAgB,EAAA;eAAhBA;;IAZAC,+BAA+B,EAAA;eAA/BA;;IAaAC,gBAAgB,EAAA;eAAhBA;;IAdAC,uBAAuB,EAAA;eAAvBA;;IAsBAC,kBAAkB,EAAA;eAAlBA;;IA+DAC,qBAAqB,EAAA;eAArBA;;IArCAC,eAAe,EAAA;eAAfA;;IA5CAC,2BAA2B,EAAA;eAA3BA;;IACAC,0CAA0C,EAAA;eAA1CA;;IAyDAC,8BAA8B,EAAA;eAA9BA;;IAZAC,cAAc,EAAA;eAAdA;;IASAC,+BAA+B,EAAA;eAA/BA;;IADAC,2BAA2B,EAAA;eAA3BA;;IAJAC,sBAAsB,EAAA;eAAtBA;;IADAC,yBAAyB,EAAA;eAAzBA;;IAEAC,uBAAuB,EAAA;eAAvBA;;IACAC,gCAAgC,EAAA;eAAhCA;;IAJAC,uBAAuB,EAAA;eAAvBA;;IA5CAC,mBAAmB,EAAA;eAAnBA;;IACAC,uBAAuB,EAAA;eAAvBA;;IACAC,kBAAkB,EAAA;eAAlBA;;IACAC,UAAU,EAAA;eAAVA;;IA6DAC,yBAAyB,EAAA;eAAzBA;;IANAC,oCAAoC,EAAA;eAApCA;;IAEAC,yBAAyB,EAAA;eAAzBA;;IAuBAC,cAAc,EAAA;eAAdA;;IAJAC,yBAAyB,EAAA;eAAzBA;;IAvBAC,8BAA8B,EAAA;eAA9BA;;IAMAC,0CAA0C,EAAA;eAA1CA;;IASAC,gCAAgC,EAAA;eAAhCA;;IAiIJC,cAAc,EAAA;eAAdA;;IAAgBC,wBAAwB,EAAA;eAAxBA;;;AAhNlB,MAAM5B,0BAA0B;AAChC,MAAMF,kCAAkC;AAExC,MAAMZ,sBAAsB;AAC5B,MAAMkB,8BAA8B;AACpC,MAAMC,6CACX;AAEK,MAAMU,sBAAsB;AAC5B,MAAMC,0BAA0B;AAChC,MAAMC,qBAAqB;AAC3B,MAAMC,aAAa;AACnB,MAAM1C,gBAAgB;AACtB,MAAMqB,mBAAmB;AACzB,MAAME,mBAAmB;AACzB,MAAMV,mBAAmB;AAEzB,MAAMK,yBAAyB;AAC/B,MAAMH,qCAAqC;AAC3C,MAAMC,yCACX;AAEK,MAAMS,qBAAqB;AAI3B,MAAMN,2BAA2B;AACjC,MAAMC,4BAA4B;AAClC,MAAMH,iCAAiC;AACvC,MAAMH,6BAA6B;AAGnC,MAAMZ,iBAAiB;AAKvB,MAAMM,iBAAiB;AAGvB,MAAMG,sBAAsB;AAC5B,MAAMC,6BAA6B,AAAC,SAAS,GAAED,CAAqB,MAArBA;AAG/C,MAAMF,gCAAgC;AAItC,MAAMkB,kBAAkB;AACxB,MAAMxB,iBAAiB;AACvB,MAAM4B,iBAAiB;AACvB,MAAM9B,gBAAgB;AACtB,MAAMqC,0BAA0B;AAChC,MAAMH,4BAA4B;AAClC,MAAMD,yBAAyB;AAC/B,MAAME,0BAA0B;AAChC,MAAMC,mCACX;AACK,MAAMJ,8BAA8B;AACpC,MAAMD,kCACX;AAEK,MAAMF,iCAAkC,AAAD,6KAA8K,CAAC;AAEtN,MAAMkB,iCAAiC,AAAC,mGAAmG,CAAC;AAE5I,MAAMJ,uCAAuC,AAAC,uFAAuF,CAAC;AAEtI,MAAMC,4BAA4B,AAAC,sHAAsH,CAAC;AAE1J,MAAMI,6CAA6C,AAAC,uGAAuG,CAAC;AAE5J,MAAMN,4BAA4B,AAAC,uHAAuH,CAAC;AAE3J,MAAMtC,wBACX;AACK,MAAME,yBACX;AAEK,MAAM2C,mCACX,uEACA;AAEK,MAAM5C,8BAA8B,AAAC,wJAAwJ,CAAC;AAE9L,MAAMoB,wBAAwB,AAAC,iNAAiN,CAAC;AAEjP,MAAMqB,4BAA4B,AAAC,wJAAwJ,CAAC;AAE5L,MAAM3C,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM;AAExE,MAAM0C,iBAAgD;IAC3DO,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV;AAEA;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,cAAc;IACd;;GAEC,GACDC,cAAc;AAChB;AAKA,MAAMlB,iBAAiB;IACrB,GAAGK,oBAAoB;IACvBc,OAAO;QACLC,cAAc;YACZf,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDY,YAAY;YACVhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDU,eAAe;YACb,YAAY;YACZjB,qBAAqBK,OAAO;YAC5BL,qBAAqBM,OAAO;SAC7B;QACDY,YAAY;YACVlB,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;SACrC;QACDS,SAAS;YACPnB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBC,MAAM;YAC3BD,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDa,UAAU;YACR,+BAA+B;YAC/BpB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMR,2BAA2B;IAC/ByB,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/lib/is-error.ts"], "sourcesContent": ["import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n"], "names": ["isError", "getProperError", "err", "safeStringify", "obj", "seen", "WeakSet", "JSON", "stringify", "_key", "value", "has", "add", "process", "env", "NODE_ENV", "Error", "isPlainObject"], "mappings": "AAyCMa,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;IA9B/B;;;CAGC,GACD,OAIC,EAAA;eAJuBf;;IAqBRC,cAAc,EAAA;eAAdA;;;+BApCc;AAef,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,SAASC,cAAcC,GAAQ;IAC7B,MAAMC,OAAO,IAAIC;IAEjB,OAAOC,KAAKC,SAAS,CAACJ,KAAK,CAACK,MAAMC;QAChC,oEAAoE;QACpE,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM;YAC/C,IAAIL,KAAKM,GAAG,CAACD,QAAQ;gBACnB,OAAO;YACT;YACAL,KAAKO,GAAG,CAACF;QACX;QACA,OAAOA;IACT;AACF;AAEO,SAAST,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,wCAA4C;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOA,QAAQ,aAAa;YAC9B,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,oCACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;QAEA,IAAId,QAAQ,MAAM;YAChB,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,8BACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;IACF;IAEA,OAAO,OAAA,cAA6D,CAA7D,IAAIA,MAAMC,CAAAA,GAAAA,eAAAA,aAAa,EAACf,OAAOC,cAAcD,OAAOA,MAAM,KAA1D,qBAAA;eAAA;oBAAA;sBAAA;IAA4D;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/lib/is-api-route.ts"], "sourcesContent": ["export function isAPIRoute(value?: string) {\n  return value === '/api' || Boolean(value?.startsWith('/api/'))\n}\n"], "names": ["isAPIRoute", "value", "Boolean", "startsWith"], "mappings": ";;;+BAAgBA,cAAAA;;;eAAAA;;;AAAT,SAASA,WAAWC,KAAc;IACvC,OAAOA,UAAU,UAAUC,QAAQD,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,UAAU,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/lib/require-instrumentation-client.ts"], "sourcesContent": ["/**\n * This module imports the client instrumentation hook from the project root.\n *\n * The `private-next-instrumentation-client` module is automatically aliased to\n * the `instrumentation-client.ts` file in the project root by webpack or turbopack.\n */\nif (process.env.NODE_ENV === 'development') {\n  const measureName = 'Client Instrumentation Hook'\n  const startTime = performance.now()\n  // eslint-disable-next-line @next/internal/typechecked-require -- Not a module.\n  module.exports = require('private-next-instrumentation-client')\n  const endTime = performance.now()\n  const duration = endTime - startTime\n\n  // Using 16ms threshold as it represents one frame (1000ms/60fps)\n  // This helps identify if the instrumentation hook initialization\n  // could potentially cause frame drops during development.\n  const THRESHOLD = 16\n  if (duration > THRESHOLD) {\n    console.log(\n      `[${measureName}] Slow execution detected: ${duration.toFixed(0)}ms (Note: Code download overhead is not included in this measurement)`\n    )\n  }\n} else {\n  // eslint-disable-next-line @next/internal/typechecked-require -- Not a module.\n  module.exports = require('private-next-instrumentation-client')\n}\n"], "names": ["process", "env", "NODE_ENV", "measureName", "startTime", "performance", "now", "module", "exports", "require", "endTime", "duration", "THRESHOLD", "console", "log", "toFixed"], "mappings": "AAAA;;;;;CAKC,GACGA,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAD5B;AACD,wCAA4C;IAC1C,MAAMC,cAAc;IACpB,MAAMC,YAAYC,YAAYC,GAAG;IACjC,+EAA+E;IAC/EC,OAAOC,OAAO,GAAGC,QAAQ;IACzB,MAAMC,UAAUL,YAAYC,GAAG;IAC/B,MAAMK,WAAWD,UAAUN;IAE3B,iEAAiE;IACjE,iEAAiE;IACjE,0DAA0D;IAC1D,MAAMQ,YAAY;IAClB,IAAID,WAAWC,WAAW;QACxBC,QAAQC,GAAG,CACT,AAAC,CAAC,UAAEX,aAAY,2BAA2B,WAAEQ,SAASI,OAAO,CAAC,IAAG,qEAAqE,CAAC;IAE3I;AACF,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/app/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../../lib/is-error'\n\nconst ownerStacks = new WeakMap<Error, string | null>()\nconst componentStacks = new WeakMap<Error, string>()\n\nexport function getComponentStack(error: Error): string | undefined {\n  return componentStacks.get(error)\n}\nexport function setComponentStack(error: Error, stack: string) {\n  componentStacks.set(error, stack)\n}\n\nexport function getOwnerStack(error: Error): string | null | undefined {\n  return ownerStacks.get(error)\n}\nexport function setOwnerStack(error: Error, stack: string | null) {\n  ownerStacks.set(error, stack)\n}\n\nexport function coerceError(value: unknown): Error {\n  return isError(value) ? value : new Error('' + value)\n}\n\nexport function setOwnerStackIfAvailable(error: Error): void {\n  // React 18 and prod does not have `captureOwnerStack`\n  if ('captureOwnerStack' in React) {\n    setOwnerStack(error, React.captureOwnerStack())\n  }\n}\n\nexport function decorateDevError(\n  thrownValue: unknown,\n  errorInfo: React.ErrorInfo\n) {\n  const error = coerceError(thrownValue)\n  setOwnerStackIfAvailable(error)\n  // TODO: change to passing down errorInfo later\n  // In development mode, pass along the component stack to the error\n  if (errorInfo.componentStack) {\n    setComponentStack(error, errorInfo.componentStack)\n  }\n  return error\n}\n"], "names": ["coerceError", "decorateDevError", "getComponentStack", "getOwnerStack", "setComponentStack", "setOwnerStack", "setOwnerStackIfAvailable", "ownerStacks", "WeakMap", "componentStacks", "error", "get", "stack", "set", "value", "isError", "Error", "React", "captureOwnerStack", "thrownValue", "errorInfo", "componentStack"], "mappings": ";;;;;;;;;;;;;;;;;;;IAoBgBA,WAAW,EAAA;eAAXA;;IAWAC,gBAAgB,EAAA;eAAhBA;;IAzBAC,iBAAiB,EAAA;eAAjBA;;IAOAC,aAAa,EAAA;eAAbA;;IAJAC,iBAAiB,EAAA;eAAjBA;;IAOAC,aAAa,EAAA;eAAbA;;IAQAC,wBAAwB,EAAA;eAAxBA;;;;gEAxBE;kEACE;AAEpB,MAAMC,cAAc,IAAIC;AACxB,MAAMC,kBAAkB,IAAID;AAErB,SAASN,kBAAkBQ,KAAY;IAC5C,OAAOD,gBAAgBE,GAAG,CAACD;AAC7B;AACO,SAASN,kBAAkBM,KAAY,EAAEE,KAAa;IAC3DH,gBAAgBI,GAAG,CAACH,OAAOE;AAC7B;AAEO,SAAST,cAAcO,KAAY;IACxC,OAAOH,YAAYI,GAAG,CAACD;AACzB;AACO,SAASL,cAAcK,KAAY,EAAEE,KAAoB;IAC9DL,YAAYM,GAAG,CAACH,OAAOE;AACzB;AAEO,SAASZ,YAAYc,KAAc;IACxC,OAAOC,CAAAA,GAAAA,SAAAA,OAAO,EAACD,SAASA,QAAQ,OAAA,cAAqB,CAArB,IAAIE,MAAM,KAAKF,QAAf,qBAAA;eAAA;oBAAA;sBAAA;IAAoB;AACtD;AAEO,SAASR,yBAAyBI,KAAY;IACnD,sDAAsD;IACtD,IAAI,uBAAuBO,OAAAA,OAAK,EAAE;QAChCZ,cAAcK,OAAOO,OAAAA,OAAK,CAACC,iBAAiB;IAC9C;AACF;AAEO,SAASjB,iBACdkB,WAAoB,EACpBC,SAA0B;IAE1B,MAAMV,QAAQV,YAAYmB;IAC1Bb,yBAAyBI;IACzB,+CAA+C;IAC/C,mEAAmE;IACnE,IAAIU,UAAUC,cAAc,EAAE;QAC5BjB,kBAAkBM,OAAOU,UAAUC,cAAc;IACnD;IACA,OAAOX;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/shared/react-18-hydration-error.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nexport function isHydrationError(error: unknown): boolean {\n  return (\n    isError(error) &&\n    (error.message ===\n      'Hydration failed because the initial UI does not match what was rendered on the server.' ||\n      error.message === 'Text content does not match server-rendered HTML.')\n  )\n}\n\nexport function isHydrationWarning(message: unknown): message is string {\n  return (\n    isHtmlTagsWarning(message) ||\n    isTextInTagsMismatchWarning(message) ||\n    isTextWarning(message)\n  )\n}\n\ntype NullableText = string | null | undefined\n\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n  'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s',\n])\nconst textAndTagsMismatchWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n  'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s',\n])\nconst textWarnings = new Set([\n  'Warning: Text content did not match. Server: \"%s\" Client: \"%s\"%s',\n])\n\nexport const getHydrationWarningType = (\n  message: NullableText\n): 'tag' | 'text' | 'text-in-tag' => {\n  if (typeof message !== 'string') {\n    // TODO: Doesn't make sense to treat no message as a hydration error message.\n    // We should bail out somewhere earlier.\n    return 'text'\n  }\n\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  if (isHtmlTagsWarning(normalizedMessage)) return 'tag'\n  if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag'\n\n  return 'text'\n}\n\nconst isHtmlTagsWarning = (message: unknown) =>\n  typeof message === 'string' && htmlTagsWarnings.has(message)\n\nconst isTextInTagsMismatchWarning = (msg: unknown) =>\n  typeof msg === 'string' && textAndTagsMismatchWarnings.has(msg)\n\nconst isTextWarning = (msg: unknown) =>\n  typeof msg === 'string' && textWarnings.has(msg)\n"], "names": ["getHydrationWarningType", "isHydrationError", "isHydrationWarning", "error", "isError", "message", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "isTextWarning", "htmlTagsWarnings", "Set", "textAndTagsMismatchWarnings", "textWarnings", "normalizedMessage", "startsWith", "has", "msg"], "mappings": ";;;;;;;;;;;;;;;IAkCaA,uBAAuB,EAAA;eAAvBA;;IAhCGC,gBAAgB,EAAA;eAAhBA;;IASAC,kBAAkB,EAAA;eAAlBA;;;;kEAXI;AAEb,SAASD,iBAAiBE,KAAc;IAC7C,OACEC,CAAAA,GAAAA,SAAAA,OAAO,EAACD,UACPA,CAAAA,MAAME,OAAO,KACZ,6FACAF,MAAME,OAAO,KAAK,mDAAkD;AAE1E;AAEO,SAASH,mBAAmBG,OAAgB;IACjD,OACEC,kBAAkBD,YAClBE,4BAA4BF,YAC5BG,cAAcH;AAElB;AAIA,iIAAiI;AACjI,MAAMI,mBAAmB,IAAIC,IAAI;IAC/B;IACA;CACD;AACD,MAAMC,8BAA8B,IAAID,IAAI;IAC1C;IACA;CACD;AACD,MAAME,eAAe,IAAIF,IAAI;IAC3B;CACD;AAEM,MAAMV,0BAA0B,CACrCK;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,6EAA6E;QAC7E,wCAAwC;QACxC,OAAO;IACT;IAEA,MAAMQ,oBAAoBR,QAAQS,UAAU,CAAC,eACzCT,UACC,cAAWA;IAEhB,IAAIC,kBAAkBO,oBAAoB,OAAO;IACjD,IAAIN,4BAA4BM,oBAAoB,OAAO;IAE3D,OAAO;AACT;AAEA,MAAMP,oBAAoB,CAACD,UACzB,OAAOA,YAAY,YAAYI,iBAAiBM,GAAG,CAACV;AAEtD,MAAME,8BAA8B,CAACS,MACnC,OAAOA,QAAQ,YAAYL,4BAA4BI,GAAG,CAACC;AAE7D,MAAMR,gBAAgB,CAACQ,MACrB,OAAOA,QAAQ,YAAYJ,aAAaG,GAAG,CAACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/shared/react-19-hydration-error.ts"], "sourcesContent": ["export const REACT_HYDRATION_ERROR_LINK =\n  'https://react.dev/link/hydration-mismatch'\nexport const NEXTJS_HYDRATION_ERROR_LINK =\n  'https://nextjs.org/docs/messages/react-hydration-error'\n\n/**\n * Only React 19+ contains component stack diff in the error message\n */\nconst errorMessagesWithComponentStackDiff = [\n  /^In HTML, (.+?) cannot be a child of <(.+?)>\\.(.*)\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, (.+?) cannot be a descendant of <(.+?)>\\.\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, text nodes cannot be a child of <(.+?)>\\.\\nThis will cause a hydration error\\./,\n  /^In HTML, whitespace text nodes cannot be a child of <(.+?)>\\. Make sure you don't have any extra whitespace between tags on each line of your source code\\.\\nThis will cause a hydration error\\./,\n]\n\nexport function isHydrationError(error: Error): boolean {\n  return (\n    isErrorMessageWithComponentStackDiff(error.message) ||\n    /Hydration failed because the server rendered (text|HTML) didn't match the client\\./.test(\n      error.message\n    ) ||\n    /A tree hydrated but some attributes of the server rendered HTML didn't match the client properties./.test(\n      error.message\n    )\n  )\n}\n\nexport function isErrorMessageWithComponentStackDiff(msg: string): boolean {\n  return errorMessagesWithComponentStackDiff.some((regex) => regex.test(msg))\n}\n\nexport function getHydrationErrorStackInfo(error: Error): {\n  message: string | null\n  notes: string | null\n  diff: string | null\n} {\n  const errorMessage = error.message\n  if (isErrorMessageWithComponentStackDiff(errorMessage)) {\n    const [message, diffLog = ''] = errorMessage.split('\\n\\n')\n    const diff = diffLog.trim()\n    return {\n      message: diff === '' ? errorMessage.trim() : message.trim(),\n      diff,\n      notes: null,\n    }\n  }\n\n  const [message, maybeComponentStackDiff] = errorMessage.split(\n    `${REACT_HYDRATION_ERROR_LINK}`\n  )\n  const trimmedMessage = message.trim()\n  // React built-in hydration diff starts with a newline\n  if (\n    maybeComponentStackDiff !== undefined &&\n    maybeComponentStackDiff.length > 1\n  ) {\n    const diffs: string[] = []\n    maybeComponentStackDiff.split('\\n').forEach((line) => {\n      if (line.trim() === '') return\n      if (!line.trim().startsWith('at ')) {\n        diffs.push(line)\n      }\n    })\n\n    const [displayedMessage, ...notes] = trimmedMessage.split('\\n\\n')\n    return {\n      message: displayedMessage,\n      diff: diffs.join('\\n'),\n      notes: notes.join('\\n\\n') || null,\n    }\n  } else {\n    const [displayedMessage, ...notes] = trimmedMessage.split('\\n\\n')\n    return {\n      message: displayedMessage,\n      diff: null,\n      notes: notes.join('\\n\\n'),\n    }\n  }\n}\n"], "names": ["NEXTJS_HYDRATION_ERROR_LINK", "REACT_HYDRATION_ERROR_LINK", "getHydrationErrorStackInfo", "isErrorMessageWithComponentStackDiff", "isHydrationError", "errorMessagesWithComponentStackDiff", "error", "message", "test", "msg", "some", "regex", "errorMessage", "diffLog", "split", "diff", "trim", "notes", "maybeComponentStackDiff", "trimmedMessage", "undefined", "length", "diffs", "for<PERSON>ach", "line", "startsWith", "push", "displayedMessage", "join"], "mappings": ";;;;;;;;;;;;;;;;;IAEaA,2BAA2B,EAAA;eAA3BA;;IAFAC,0BAA0B,EAAA;eAA1BA;;IA+BGC,0BAA0B,EAAA;eAA1BA;;IAJAC,oCAAoC,EAAA;eAApCA;;IAZAC,gBAAgB,EAAA;eAAhBA;;;AAfT,MAAMH,6BACX;AACK,MAAMD,8BACX;AAEF;;CAEC,GACD,MAAMK,sCAAsC;IAC1C;IACA;IACA;IACA;CACD;AAEM,SAASD,iBAAiBE,KAAY;IAC3C,OACEH,qCAAqCG,MAAMC,OAAO,KAClD,qFAAqFC,IAAI,CACvFF,MAAMC,OAAO,KAEf,sGAAsGC,IAAI,CACxGF,MAAMC,OAAO;AAGnB;AAEO,SAASJ,qCAAqCM,GAAW;IAC9D,OAAOJ,oCAAoCK,IAAI,CAAC,CAACC,QAAUA,MAAMH,IAAI,CAACC;AACxE;AAEO,SAASP,2BAA2BI,KAAY;IAKrD,MAAMM,eAAeN,MAAMC,OAAO;IAClC,IAAIJ,qCAAqCS,eAAe;QACtD,MAAM,CAACL,SAASM,UAAU,EAAE,CAAC,GAAGD,aAAaE,KAAK,CAAC;QACnD,MAAMC,OAAOF,QAAQG,IAAI;QACzB,OAAO;YACLT,SAASQ,SAAS,KAAKH,aAAaI,IAAI,KAAKT,QAAQS,IAAI;YACzDD;YACAE,OAAO;QACT;IACF;IAEA,MAAM,CAACV,SAASW,wBAAwB,GAAGN,aAAaE,KAAK,CAC1D,KAAEb;IAEL,MAAMkB,iBAAiBZ,QAAQS,IAAI;IACnC,sDAAsD;IACtD,IACEE,4BAA4BE,aAC5BF,wBAAwBG,MAAM,GAAG,GACjC;QACA,MAAMC,QAAkB,EAAE;QAC1BJ,wBAAwBJ,KAAK,CAAC,MAAMS,OAAO,CAAC,CAACC;YAC3C,IAAIA,KAAKR,IAAI,OAAO,IAAI;YACxB,IAAI,CAACQ,KAAKR,IAAI,GAAGS,UAAU,CAAC,QAAQ;gBAClCH,MAAMI,IAAI,CAACF;YACb;QACF;QAEA,MAAM,CAACG,kBAAkB,GAAGV,MAAM,GAAGE,eAAeL,KAAK,CAAC;QAC1D,OAAO;YACLP,SAASoB;YACTZ,MAAMO,MAAMM,IAAI,CAAC;YACjBX,OAAOA,MAAMW,IAAI,CAAC,WAAW;QAC/B;IACF,OAAO;QACL,MAAM,CAACD,kBAAkB,GAAGV,MAAM,GAAGE,eAAeL,KAAK,CAAC;QAC1D,OAAO;YACLP,SAASoB;YACTZ,MAAM;YACNE,OAAOA,MAAMW,IAAI,CAAC;QACpB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/pages/hydration-error-state.ts"], "sourcesContent": ["import {\n  getHydrationWarningType,\n  isHydrationError as isReact18HydrationError,\n  isHydrationWarning as isReact18HydrationWarning,\n} from '../../shared/react-18-hydration-error'\nimport {\n  isHydrationError as isReact19HydrationError,\n  isErrorMessageWithComponentStackDiff as isReact19HydrationWarning,\n} from '../../shared/react-19-hydration-error'\nimport type { HydrationErrorState } from '../../shared/hydration-error'\n\n// We only need this for React 18 or hydration console errors in React 19.\n// Once we surface console.error in the dev overlay in pages router, we should only\n// use this for React 18.\nlet hydrationErrorState: HydrationErrorState = {}\n\nconst squashedHydrationErrorDetails = new WeakMap<Error, HydrationErrorState>()\n\nexport function getSquashedHydrationErrorDetails(\n  error: Error\n): HydrationErrorState | null {\n  return squashedHydrationErrorDetails.has(error)\n    ? squashedHydrationErrorDetails.get(error)!\n    : null\n}\n\nexport function attachHydrationErrorState(error: Error) {\n  if (!isReact18HydrationError(error) && !isReact19HydrationError(error)) {\n    return\n  }\n\n  let parsedHydrationErrorState: typeof hydrationErrorState = {}\n\n  // If there's any extra information in the error message to display,\n  // append it to the error message details property\n  if (hydrationErrorState.warning) {\n    // The patched console.error found hydration errors logged by React\n    // Append the logged warning to the error message\n    parsedHydrationErrorState = {\n      // It contains the warning, component stack, server and client tag names\n      ...hydrationErrorState,\n    }\n\n    // Consume the cached hydration diff.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (hydrationErrorState.reactOutputComponentDiff) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n\n    squashedHydrationErrorDetails.set(error, parsedHydrationErrorState)\n  }\n}\n\n// TODO: Only handle React 18. Once we surface console.error in the dev overlay in pages router,\n// we can use the same behavior as App Router.\nexport function storeHydrationErrorStateFromConsoleArgs(...args: any[]) {\n  let [message, firstContent, secondContent, ...rest] = args\n  if (isReact18HydrationWarning(message)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning = message\n      .replace(/Warning: /, '')\n      .replace('%s', firstContent)\n      .replace('%s', secondContent)\n      // remove the last %s from the message\n      .replace(/%s/g, '')\n\n    const lastArg = (rest[rest.length - 1] || '').trim()\n\n    hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(\n      message,\n      firstContent,\n      secondContent,\n      lastArg\n    )\n\n    hydrationErrorState.warning = warning\n  } else if (isReact19HydrationWarning(message)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning = message\n      .replace('%s', firstContent)\n      .replace('%s', secondContent)\n      // remove the last %s from the message\n      .replace(/%s/g, '')\n\n    const lastArg = (args[args.length - 1] || '').trim()\n\n    hydrationErrorState.reactOutputComponentDiff = lastArg\n    hydrationErrorState.warning = warning\n  }\n}\n\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */\nfunction generateHydrationDiffReact18(\n  message: string,\n  firstContent: string,\n  secondContent: string,\n  lastArg: string\n) {\n  const componentStack = lastArg\n  let firstIndex = -1\n  let secondIndex = -1\n  const hydrationWarningType = getHydrationWarningType(message)\n\n  // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n  const components = componentStack\n    .split('\\n')\n    // .reverse()\n    .map((line: string, index: number) => {\n      // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n      line = line.trim()\n      // extract `<space>at <component>` to `<<component>>`\n      // e.g. `  at Foo` -> `<Foo>`\n      const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || []\n      // If there's no location then it's user-land stack frame\n      if (!location) {\n        if (component === firstContent && firstIndex === -1) {\n          firstIndex = index\n        } else if (component === secondContent && secondIndex === -1) {\n          secondIndex = index\n        }\n      }\n      return location ? '' : component\n    })\n    .filter(Boolean)\n    .reverse()\n\n  let diff = ''\n  for (let i = 0; i < components.length; i++) {\n    const component = components[i]\n    const matchFirstContent =\n      hydrationWarningType === 'tag' && i === components.length - firstIndex - 1\n    const matchSecondContent =\n      hydrationWarningType === 'tag' &&\n      i === components.length - secondIndex - 1\n    if (matchFirstContent || matchSecondContent) {\n      const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2)\n      diff += `> ${spaces}<${component}>\\n`\n    } else {\n      const spaces = ' '.repeat(i * 2 + 2)\n      diff += `${spaces}<${component}>\\n`\n    }\n  }\n  if (hydrationWarningType === 'text') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `+ ${spaces}\"${firstContent}\"\\n`\n    diff += `- ${spaces}\"${secondContent}\"\\n`\n  } else if (hydrationWarningType === 'text-in-tag') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `> ${spaces}<${secondContent}>\\n`\n    diff += `>   ${spaces}\"${firstContent}\"\\n`\n  }\n  return diff\n}\n"], "names": ["attachHydrationErrorState", "getSquashedHydrationErrorDetails", "storeHydrationErrorStateFromConsoleArgs", "hydrationErrorState", "squashedHydrationErrorDetails", "WeakMap", "error", "has", "get", "isReact18HydrationError", "isReact19HydrationError", "parsedHydrationErrorState", "warning", "reactOutputComponentDiff", "set", "args", "message", "firstContent", "second<PERSON><PERSON>nt", "rest", "isReact18HydrationWarning", "length", "replace", "lastArg", "trim", "generateHydrationDiffReact18", "isReact19HydrationWarning", "componentStack", "firstIndex", "secondIndex", "hydrationWarningType", "getHydrationWarningType", "components", "split", "map", "line", "index", "component", "location", "exec", "filter", "Boolean", "reverse", "diff", "i", "match<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matchSecondContent", "spaces", "repeat", "Math", "max"], "mappings": ";;;;;;;;;;;;;;;IA0BgBA,yBAAyB,EAAA;eAAzBA;;IARAC,gCAAgC,EAAA;eAAhCA;;IAuCAC,uCAAuC,EAAA;eAAvCA;;;uCArDT;uCAIA;AAGP,0EAA0E;AAC1E,mFAAmF;AACnF,yBAAyB;AACzB,IAAIC,sBAA2C,CAAC;AAEhD,MAAMC,gCAAgC,IAAIC;AAEnC,SAASJ,iCACdK,KAAY;IAEZ,OAAOF,8BAA8BG,GAAG,CAACD,SACrCF,8BAA8BI,GAAG,CAACF,SAClC;AACN;AAEO,SAASN,0BAA0BM,KAAY;IACpD,IAAI,CAACG,CAAAA,GAAAA,uBAAAA,gBAAuB,EAACH,UAAU,CAACI,CAAAA,GAAAA,uBAAAA,gBAAuB,EAACJ,QAAQ;QACtE;IACF;IAEA,IAAIK,4BAAwD,CAAC;IAE7D,oEAAoE;IACpE,kDAAkD;IAClD,IAAIR,oBAAoBS,OAAO,EAAE;QAC/B,mEAAmE;QACnE,iDAAiD;QACjDD,4BAA4B;YAC1B,wEAAwE;YACxE,GAAGR,mBAAmB;QACxB;QAEA,qCAAqC;QACrC,oGAAoG;QACpG,2EAA2E;QAC3E,IAAIA,oBAAoBU,wBAAwB,EAAE;YAChDF,0BAA0BE,wBAAwB,GAChDV,oBAAoBU,wBAAwB;QAChD;QAEAT,8BAA8BU,GAAG,CAACR,OAAOK;IAC3C;AACF;AAIO,SAAST;IAAwC,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGa,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAc;;IACpE,IAAI,CAACC,SAASC,cAAcC,eAAe,GAAGC,KAAK,GAAGJ;IACtD,IAAIK,CAAAA,GAAAA,uBAAAA,kBAAyB,EAACJ,UAAU;QACtC,qFAAqF;QACrF,uEAAuE;QACvE,2DAA2D;QAC3D,sDAAsD;QACtD,IAAID,KAAKM,MAAM,KAAK,GAAG;YACrBH,gBAAgB;QAClB;QAEA,MAAMN,UAAUI,QACbM,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,MAAML,cACdK,OAAO,CAAC,MAAMJ,eACf,sCAAsC;SACrCI,OAAO,CAAC,OAAO;QAElB,MAAMC,UAAWJ,CAAAA,IAAI,CAACA,KAAKE,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAElDrB,oBAAoBU,wBAAwB,GAAGY,6BAC7CT,SACAC,cACAC,eACAK;QAGFpB,oBAAoBS,OAAO,GAAGA;IAChC,OAAO,IAAIc,CAAAA,GAAAA,uBAAAA,oCAAyB,EAACV,UAAU;QAC7C,qFAAqF;QACrF,uEAAuE;QACvE,2DAA2D;QAC3D,sDAAsD;QACtD,IAAID,KAAKM,MAAM,KAAK,GAAG;YACrBH,gBAAgB;QAClB;QAEA,MAAMN,UAAUI,QACbM,OAAO,CAAC,MAAML,cACdK,OAAO,CAAC,MAAMJ,eACf,sCAAsC;SACrCI,OAAO,CAAC,OAAO;QAElB,MAAMC,UAAWR,CAAAA,IAAI,CAACA,KAAKM,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAElDrB,oBAAoBU,wBAAwB,GAAGU;QAC/CpB,oBAAoBS,OAAO,GAAGA;IAChC;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASa,6BACPT,OAAe,EACfC,YAAoB,EACpBC,aAAqB,EACrBK,OAAe;IAEf,MAAMI,iBAAiBJ;IACvB,IAAIK,aAAa,CAAC;IAClB,IAAIC,cAAc,CAAC;IACnB,MAAMC,uBAAuBC,CAAAA,GAAAA,uBAAAA,uBAAuB,EAACf;IAErD,kDAAkD;IAClD,MAAMgB,aAAaL,eAChBM,KAAK,CAAC,MACP,aAAa;KACZC,GAAG,CAAC,CAACC,MAAcC;QAClB,wEAAwE;QACxED,OAAOA,KAAKX,IAAI;QAChB,qDAAqD;QACrD,6BAA6B;QAC7B,MAAM,GAAGa,WAAWC,SAAS,GAAG,uBAAuBC,IAAI,CAACJ,SAAS,EAAE;QACvE,yDAAyD;QACzD,IAAI,CAACG,UAAU;YACb,IAAID,cAAcpB,gBAAgBW,eAAe,CAAC,GAAG;gBACnDA,aAAaQ;YACf,OAAO,IAAIC,cAAcnB,iBAAiBW,gBAAgB,CAAC,GAAG;gBAC5DA,cAAcO;YAChB;QACF;QACA,OAAOE,WAAW,KAAKD;IACzB,GACCG,MAAM,CAACC,SACPC,OAAO;IAEV,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIZ,WAAWX,MAAM,EAAEuB,IAAK;QAC1C,MAAMP,YAAYL,UAAU,CAACY,EAAE;QAC/B,MAAMC,oBACJf,yBAAyB,SAASc,MAAMZ,WAAWX,MAAM,GAAGO,aAAa;QAC3E,MAAMkB,qBACJhB,yBAAyB,SACzBc,MAAMZ,WAAWX,MAAM,GAAGQ,cAAc;QAC1C,IAAIgB,qBAAqBC,oBAAoB;YAC3C,MAAMC,SAAS,IAAIC,MAAM,CAACC,KAAKC,GAAG,CAACN,IAAI,IAAI,GAAG,KAAK;YACnDD,QAAS,OAAII,SAAO,MAAGV,YAAU;QACnC,OAAO;YACL,MAAMU,SAAS,IAAIC,MAAM,CAACJ,IAAI,IAAI;YAClCD,QAAWI,SAAO,MAAGV,YAAU;QACjC;IACF;IACA,IAAIP,yBAAyB,QAAQ;QACnC,MAAMiB,SAAS,IAAIC,MAAM,CAAChB,WAAWX,MAAM,GAAG;QAC9CsB,QAAS,OAAII,SAAO,MAAG9B,eAAa;QACpC0B,QAAS,OAAII,SAAO,MAAG7B,gBAAc;IACvC,OAAO,IAAIY,yBAAyB,eAAe;QACjD,MAAMiB,SAAS,IAAIC,MAAM,CAAChB,WAAWX,MAAM,GAAG;QAC9CsB,QAAS,OAAII,SAAO,MAAG7B,gBAAc;QACrCyB,QAAS,SAAMI,SAAO,MAAG9B,eAAa;IACxC;IACA,OAAO0B;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/pages/pages-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import React from 'react'\n\ntype PagesDevOverlayErrorBoundaryProps = {\n  children?: React.ReactNode\n}\ntype PagesDevOverlayErrorBoundaryState = { error: Error | null }\n\nexport class PagesDevOverlayErrorBoundary extends React.PureComponent<\n  PagesDevOverlayErrorBoundaryProps,\n  PagesDevOverlayErrorBoundaryState\n> {\n  state = { error: null }\n\n  static getDerivedStateFromError(error: Error) {\n    return { error }\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    // The component has to be unmounted or else it would continue to error\n    return this.state.error ? null : this.props.children\n  }\n}\n"], "names": ["PagesDevOverlayErrorBoundary", "React", "PureComponent", "getDerivedStateFromError", "error", "render", "state", "props", "children"], "mappings": ";;;+BAOaA,gCAAAA;;;eAAAA;;;;gEAPK;AAOX,MAAMA,qCAAqCC,OAAAA,OAAK,CAACC,aAAa;IAMnE,OAAOC,yBAAyBC,KAAY,EAAE;QAC5C,OAAO;YAAEA;QAAM;IACjB;IAEA,yIAAyI;IACzIC,SAA0B;QACxB,uEAAuE;QACvE,OAAO,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,OAAO,IAAI,CAACG,KAAK,CAACC,QAAQ;IACtD;;QAdK,KAAA,IAAA,OAAA,IAAA,CAILF,KAAAA,GAAQ;YAAEF,OAAO;QAAK;;AAWxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/app/terminal-logging-config.ts"], "sourcesContent": ["export function getTerminalLoggingConfig():\n  | false\n  | boolean\n  | {\n      depthLimit?: number\n      edgeLimit?: number\n      showSourceLocation?: boolean\n    } {\n  try {\n    return JSON.parse(\n      process.env.__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL || 'false'\n    )\n  } catch {\n    return false\n  }\n}\n\nexport function getIsTerminalLoggingEnabled(): boolean {\n  const config = getTerminalLoggingConfig()\n  return Boolean(config)\n}\n"], "names": ["getIsTerminalLoggingEnabled", "getTerminalLoggingConfig", "JSON", "parse", "process", "env", "__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL", "config", "Boolean"], "mappings": "AAUMI,QAAQC,GAAG,CAACC,qCAAqC;;;;;;;;;;;;;;;;IAOvCN,2BAA2B,EAAA;eAA3BA;;IAjBAC,wBAAwB,EAAA;eAAxBA;;;AAAT,SAASA;IAQd,IAAI;QACF,OAAOC,KAAKC,KAAK,8CACsC;IAEzD,EAAE,OAAA,GAAM;QACN,OAAO;IACT;AACF;AAEO,SAASH;IACd,MAAMO,SAASN;IACf,OAAOO,QAAQD;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/shared/forward-logs-shared.ts"], "sourcesContent": ["export type LogMethod =\n  | 'log'\n  | 'info'\n  | 'debug'\n  | 'table'\n  | 'error'\n  | 'assert'\n  | 'dir'\n  | 'dirxml'\n  | 'group'\n  | 'groupCollapsed'\n  | 'groupEnd'\n  | 'trace'\n  | 'warn'\n\nexport type ConsoleEntry<T> = {\n  kind: 'console'\n  method: LogMethod\n  consoleMethodStack: string | null\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string\n      }\n  >\n}\n\nexport type ConsoleErrorEntry<T> = {\n  kind: 'any-logged-error'\n  method: 'error'\n  consoleErrorStack: string\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n        isRejectionMessage?: boolean\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string | null\n      }\n  >\n}\n\nexport type FormattedErrorEntry = {\n  kind: 'formatted-error'\n  prefix: string\n  stack: string\n  method: 'error'\n}\n\nexport type ClientLogEntry =\n  | ConsoleEntry<unknown>\n  | ConsoleErrorEntry<unknown>\n  | FormattedErrorEntry\nexport type ServerLogEntry =\n  | ConsoleEntry<string>\n  | ConsoleErrorEntry<string>\n  | FormattedErrorEntry\n\nexport const UNDEFINED_MARKER = '__next_tagged_undefined'\n\n// Based on https://github.com/facebook/react/blob/28dc0776be2e1370fe217549d32aee2519f0cf05/packages/react-server/src/ReactFlightServer.js#L248\nexport function patchConsoleMethod<T extends keyof Console>(\n  methodName: T,\n  wrapper: (\n    methodName: T,\n    ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n  ) => void\n): () => void {\n  const descriptor = Object.getOwnPropertyDescriptor(console, methodName)\n  if (\n    descriptor &&\n    (descriptor.configurable || descriptor.writable) &&\n    typeof descriptor.value === 'function'\n  ) {\n    const originalMethod = descriptor.value as Console[T] extends (\n      ...args: any[]\n    ) => any\n      ? Console[T]\n      : never\n    const originalName = Object.getOwnPropertyDescriptor(originalMethod, 'name')\n    const wrapperMethod = function (\n      this: typeof console,\n      ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n    ) {\n      wrapper(methodName, ...args)\n\n      originalMethod.apply(this, args)\n    }\n    if (originalName) {\n      Object.defineProperty(wrapperMethod, 'name', originalName)\n    }\n    Object.defineProperty(console, methodName, {\n      value: wrapperMethod,\n    })\n\n    return () => {\n      Object.defineProperty(console, methodName, {\n        value: originalMethod,\n        writable: descriptor.writable,\n        configurable: descriptor.configurable,\n      })\n    }\n  }\n\n  return () => {}\n}\n"], "names": ["UNDEFINED_MARKER", "patchConsoleMethod", "methodName", "wrapper", "descriptor", "Object", "getOwnPropertyDescriptor", "console", "configurable", "writable", "value", "originalMethod", "originalName", "wrapperMethod", "args", "apply", "defineProperty"], "mappings": ";;;;;;;;;;;;;;IAkEaA,gBAAgB,EAAA;eAAhBA;;IAGGC,kBAAkB,EAAA;eAAlBA;;;AAHT,MAAMD,mBAAmB;AAGzB,SAASC,mBACdC,UAAa,EACbC,OAGS;IAET,MAAMC,aAAaC,OAAOC,wBAAwB,CAACC,SAASL;IAC5D,IACEE,cACCA,CAAAA,WAAWI,YAAY,IAAIJ,WAAWK,QAAO,KAC9C,OAAOL,WAAWM,KAAK,KAAK,YAC5B;QACA,MAAMC,iBAAiBP,WAAWM,KAAK;QAKvC,MAAME,eAAeP,OAAOC,wBAAwB,CAACK,gBAAgB;QACrE,MAAME,gBAAgB;YAEpB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;gBAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAmE;;YAEnEX,QAAQD,eAAeY;YAEvBH,eAAeI,KAAK,CAAC,IAAI,EAAED;QAC7B;QACA,IAAIF,cAAc;YAChBP,OAAOW,cAAc,CAACH,eAAe,QAAQD;QAC/C;QACAP,OAAOW,cAAc,CAACT,SAASL,YAAY;YACzCQ,OAAOG;QACT;QAEA,OAAO;YACLR,OAAOW,cAAc,CAACT,SAASL,YAAY;gBACzCQ,OAAOC;gBACPF,UAAUL,WAAWK,QAAQ;gBAC7BD,cAAcJ,WAAWI,YAAY;YACvC;QACF;IACF;IAEA,OAAO,KAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/app/forward-logs.ts"], "sourcesContent": ["import { configure } from 'next/dist/compiled/safe-stable-stringify'\nimport {\n  getOwnerStack,\n  setOwnerStackIfAvailable,\n} from './errors/stitched-error'\nimport { getErrorSource } from '../../../shared/lib/error-source'\nimport {\n  getTerminalLoggingConfig,\n  getIsTerminalLoggingEnabled,\n} from './terminal-logging-config'\nimport {\n  type ConsoleEntry,\n  type ConsoleErrorEntry,\n  type FormattedErrorEntry,\n  type ClientLogEntry,\n  type LogMethod,\n  patchConsoleMethod,\n  UNDEFINED_MARKER,\n} from '../../shared/forward-logs-shared'\n\nconst terminalLoggingConfig = getTerminalLoggingConfig()\nexport const PROMISE_MARKER = 'Promise {}'\nexport const UNAVAILABLE_MARKER = '[Unable to view]'\n\nconst maximumDepth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.depthLimit\n    ? terminalLoggingConfig.depthLimit\n    : 5\nconst maximumBreadth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.edgeLimit\n    ? terminalLoggingConfig.edgeLimit\n    : 100\n\nconst stringify = configure({\n  maximumDepth,\n  maximumBreadth,\n})\n\nexport const isTerminalLoggingEnabled = getIsTerminalLoggingEnabled()\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n  'trace',\n]\n/**\n * allows us to:\n * - revive the undefined log in the server as it would look in the browser\n * - not read/attempt to serialize promises (next will console error if you do that, and will cause this program to infinitely recurse)\n * - if we read a proxy that throws (no way to detect if something is a proxy), explain to the user we can't read this data\n */\nexport function preLogSerializationClone<T>(\n  value: T,\n  seen = new WeakMap()\n): any {\n  if (value === undefined) return UNDEFINED_MARKER\n  if (value === null || typeof value !== 'object') return value\n  if (seen.has(value as object)) return seen.get(value as object)\n\n  try {\n    Object.keys(value as object)\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  try {\n    if (typeof (value as any).then === 'function') return PROMISE_MARKER\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  if (Array.isArray(value)) {\n    const out: any[] = []\n    seen.set(value, out)\n    for (const item of value) {\n      try {\n        out.push(preLogSerializationClone(item, seen))\n      } catch {\n        out.push(UNAVAILABLE_MARKER)\n      }\n    }\n    return out\n  }\n\n  const proto = Object.getPrototypeOf(value)\n  if (proto === Object.prototype || proto === null) {\n    const out: Record<string, unknown> = {}\n    seen.set(value as object, out)\n    for (const key of Object.keys(value as object)) {\n      try {\n        out[key] = preLogSerializationClone((value as any)[key], seen)\n      } catch {\n        out[key] = UNAVAILABLE_MARKER\n      }\n    }\n    return out\n  }\n\n  return Object.prototype.toString.call(value)\n}\n\n// only safe if passed safeClone data\nexport const logStringify = (data: unknown): string => {\n  try {\n    const result = stringify(data)\n    return result ?? `\"${UNAVAILABLE_MARKER}\"`\n  } catch {\n    return `\"${UNAVAILABLE_MARKER}\"`\n  }\n}\n\nconst afterThisFrame = (cb: () => void) => {\n  let timeout: ReturnType<typeof setTimeout> | undefined\n\n  const rafId = requestAnimationFrame(() => {\n    timeout = setTimeout(() => {\n      cb()\n    })\n  })\n\n  return () => {\n    cancelAnimationFrame(rafId)\n    clearTimeout(timeout)\n  }\n}\n\nlet isPatched = false\n\nconst serializeEntries = (entries: Array<ClientLogEntry>) =>\n  entries.map((clientEntry) => {\n    switch (clientEntry.kind) {\n      case 'any-logged-error':\n      case 'console': {\n        return {\n          ...clientEntry,\n          args: clientEntry.args.map(stringifyUserArg),\n        }\n      }\n      case 'formatted-error': {\n        return clientEntry\n      }\n      default: {\n        return null!\n      }\n    }\n  })\n\nexport const logQueue: {\n  entries: Array<ClientLogEntry>\n  onSocketReady: (socket: WebSocket) => void\n  flushScheduled: boolean\n  socket: WebSocket | null\n  cancelFlush: (() => void) | null\n  sourceType?: 'server' | 'edge-server'\n  router: 'app' | 'pages' | null\n  scheduleLogSend: (entry: ClientLogEntry) => void\n} = {\n  entries: [],\n  flushScheduled: false,\n  cancelFlush: null,\n  socket: null,\n  sourceType: undefined,\n  router: null,\n  scheduleLogSend: (entry: ClientLogEntry) => {\n    logQueue.entries.push(entry)\n    if (logQueue.flushScheduled) {\n      return\n    }\n    // safe to deref and use in setTimeout closure since we cancel on new socket\n    const socket = logQueue.socket\n    if (!socket) {\n      return\n    }\n\n    // we probably dont need this\n    logQueue.flushScheduled = true\n\n    // non blocking log flush, runs at most once per frame\n    logQueue.cancelFlush = afterThisFrame(() => {\n      logQueue.flushScheduled = false\n\n      // just incase\n      try {\n        const payload = JSON.stringify({\n          event: 'browser-logs',\n          entries: serializeEntries(logQueue.entries),\n          router: logQueue.router,\n          // needed for source mapping, we just assign the sourceType from the last error for the whole batch\n          sourceType: logQueue.sourceType,\n        })\n\n        socket.send(payload)\n        logQueue.entries = []\n        logQueue.sourceType = undefined\n      } catch {\n        // error (make sure u don't infinite loop)\n        /* noop */\n      }\n    })\n  },\n  onSocketReady: (socket: WebSocket) => {\n    if (socket.readyState !== WebSocket.OPEN) {\n      // invariant\n      return\n    }\n\n    // incase an existing timeout was going to run with a stale socket\n    logQueue.cancelFlush?.()\n    logQueue.socket = socket\n    try {\n      const payload = JSON.stringify({\n        event: 'browser-logs',\n        entries: serializeEntries(logQueue.entries),\n        router: logQueue.router,\n        sourceType: logQueue.sourceType,\n      })\n\n      socket.send(payload)\n      logQueue.entries = []\n      logQueue.sourceType = undefined\n    } catch {\n      /** noop just incase */\n    }\n  },\n}\n\nconst stringifyUserArg = (\n  arg:\n    | {\n        kind: 'arg'\n        data: unknown\n      }\n    | {\n        kind: 'formatted-error-arg'\n      }\n) => {\n  if (arg.kind !== 'arg') {\n    return arg\n  }\n  return {\n    ...arg,\n    data: logStringify(arg.data),\n  }\n}\n\nconst createErrorArg = (error: Error) => {\n  const stack = stackWithOwners(error)\n  return {\n    kind: 'formatted-error-arg' as const,\n    prefix: error.message ? `${error.name}: ${error.message}` : `${error.name}`,\n    stack,\n  }\n}\n\nconst createLogEntry = (level: LogMethod, args: any[]) => {\n  // do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n  // error capture stack trace maybe\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n') // this is probably ignored anyways\n  const entry: ConsoleEntry<unknown> = {\n    kind: 'console',\n    consoleMethodStack: cleanStack ?? null, // depending on browser we might not have stack\n    method: level,\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nexport const forwardErrorLog = (args: any[]) => {\n  const errorObjects = args.filter((arg) => arg instanceof Error)\n  const first = errorObjects.at(0)\n  if (first) {\n    const source = getErrorSource(first)\n    if (source) {\n      logQueue.sourceType = source\n    }\n  }\n  /**\n   * browser shows stack regardless of type of data passed to console.error, so we should do the same\n   *\n   * do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n   */\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n')\n\n  const entry: ConsoleErrorEntry<unknown> = {\n    kind: 'any-logged-error',\n    method: 'error',\n    consoleErrorStack: cleanStack ?? '',\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUncaughtErrorEntry = (\n  errorName: string,\n  errorMessage: string,\n  fullStack: string\n) => {\n  const entry: FormattedErrorEntry = {\n    kind: 'formatted-error',\n    prefix: `Uncaught ${errorName}: ${errorMessage}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst stackWithOwners = (error: Error) => {\n  let ownerStack = ''\n  setOwnerStackIfAvailable(error)\n  ownerStack = getOwnerStack(error) || ''\n  const stack = (error.stack || '') + ownerStack\n  return stack\n}\n\nexport function logUnhandledRejection(reason: unknown) {\n  if (reason instanceof Error) {\n    createUnhandledRejectionErrorEntry(reason, stackWithOwners(reason))\n    return\n  }\n  createUnhandledRejectionNonErrorEntry(reason)\n}\n\nconst createUnhandledRejectionErrorEntry = (\n  error: Error,\n  fullStack: string\n) => {\n  const source = getErrorSource(error)\n  if (source) {\n    logQueue.sourceType = source\n  }\n\n  const entry: ClientLogEntry = {\n    kind: 'formatted-error',\n    prefix: `⨯ unhandledRejection: ${error.name}: ${error.message}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUnhandledRejectionNonErrorEntry = (reason: unknown) => {\n  const entry: ClientLogEntry = {\n    kind: 'any-logged-error',\n    // we can't access the stack since the event is dispatched async and creating an inline error would be meaningless\n    consoleErrorStack: '',\n    method: 'error',\n    args: [\n      {\n        kind: 'arg',\n        data: `⨯ unhandledRejection:`,\n        isRejectionMessage: true,\n      },\n      {\n        kind: 'arg',\n        data: preLogSerializationClone(reason),\n      },\n    ],\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst isHMR = (args: any[]) => {\n  const firstArg = args[0]\n  if (typeof firstArg !== 'string') {\n    return false\n  }\n  if (firstArg.startsWith('[Fast Refresh]')) {\n    return true\n  }\n\n  if (firstArg.startsWith('[HMR]')) {\n    return true\n  }\n\n  return false\n}\n\nconst isIgnoredLog = (args: any[]) => {\n  if (args.length < 3) {\n    return false\n  }\n\n  const [format, styles, label] = args\n\n  if (\n    typeof format !== 'string' ||\n    typeof styles !== 'string' ||\n    typeof label !== 'string'\n  ) {\n    return false\n  }\n\n  // kinda hacky, we should define a common format for these strings so we can safely ignore\n  return format.startsWith('%c%s%c') && styles.includes('background:')\n}\n\nexport function forwardUnhandledError(error: Error) {\n  createUncaughtErrorEntry(error.name, error.message, stackWithOwners(error))\n}\n\n// TODO: this router check is brittle, we need to update based on the current router the user is using\nexport const initializeDebugLogForwarding = (router: 'app' | 'pages'): void => {\n  // probably don't need this\n  if (isPatched) {\n    return\n  }\n  // TODO(rob): why does this break rendering on server, important to know incase the same bug appears in browser\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  // better to be safe than sorry\n  try {\n    methods.forEach((method) =>\n      patchConsoleMethod(method, (_, ...args) => {\n        if (isHMR(args)) {\n          return\n        }\n        if (isIgnoredLog(args)) {\n          return\n        }\n        createLogEntry(method, args)\n      })\n    )\n  } catch {}\n  logQueue.router = router\n  isPatched = true\n}\n"], "names": ["PROMISE_MARKER", "UNAVAILABLE_MARKER", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardUnhandledError", "initializeDebugLogForwarding", "isTerminalLoggingEnabled", "logQueue", "logStringify", "logUnhandledRejection", "preLogSerializationClone", "terminalLoggingConfig", "getTerminalLoggingConfig", "maximumDepth", "depthLimit", "maximumBreadth", "edgeLimit", "stringify", "configure", "getIsTerminalLoggingEnabled", "methods", "value", "seen", "WeakMap", "undefined", "UNDEFINED_MARKER", "has", "get", "Object", "keys", "then", "Array", "isArray", "out", "set", "item", "push", "proto", "getPrototypeOf", "prototype", "key", "toString", "call", "data", "result", "afterT<PERSON><PERSON><PERSON>e", "cb", "timeout", "rafId", "requestAnimationFrame", "setTimeout", "cancelAnimationFrame", "clearTimeout", "isPatched", "serializeEntries", "entries", "map", "clientEntry", "kind", "args", "stringifyUserArg", "flushScheduled", "cancelFlush", "socket", "sourceType", "router", "scheduleLogSend", "entry", "payload", "JSON", "event", "send", "onSocketReady", "readyState", "WebSocket", "OPEN", "arg", "createErrorArg", "error", "stack", "stackWithOwners", "prefix", "message", "name", "createLogEntry", "level", "Error", "stackLines", "split", "cleanStack", "slice", "join", "consoleMethodStack", "method", "errorObjects", "filter", "first", "at", "source", "getErrorSource", "consoleErrorStack", "createUncaughtErrorEntry", "errorName", "errorMessage", "fullStack", "ownerStack", "setOwnerStackIfAvailable", "getOwnerStack", "reason", "createUnhandledRejectionErrorEntry", "createUnhandledRejectionNonErrorEntry", "isRejectionMessage", "isHMR", "firstArg", "startsWith", "isIgnoredLog", "length", "format", "styles", "label", "includes", "window", "for<PERSON>ach", "patchConsoleMethod", "_"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAqBaA,cAAc,EAAA;eAAdA;;IACAC,kBAAkB,EAAA;eAAlBA;;IAyQAC,eAAe,EAAA;eAAfA;;IA+IGC,qBAAqB,EAAA;eAArBA;;IAKHC,4BAA4B,EAAA;eAA5BA;;IA7YAC,wBAAwB,EAAA;eAAxBA;;IAsHAC,QAAQ,EAAA;eAARA;;IA7CAC,YAAY,EAAA;eAAZA;;IA2OGC,qBAAqB,EAAA;eAArBA;;IA9RAC,wBAAwB,EAAA;eAAxBA;;;qCA5DU;+BAInB;6BACwB;uCAIxB;mCASA;AAEP,MAAMC,wBAAwBC,CAAAA,GAAAA,uBAAAA,wBAAwB;AAC/C,MAAMX,iBAAiB;AACvB,MAAMC,qBAAqB;AAElC,MAAMW,eACJ,OAAOF,0BAA0B,YAAYA,sBAAsBG,UAAU,GACzEH,sBAAsBG,UAAU,GAChC;AACN,MAAMC,iBACJ,OAAOJ,0BAA0B,YAAYA,sBAAsBK,SAAS,GACxEL,sBAAsBK,SAAS,GAC/B;AAEN,MAAMC,YAAYC,CAAAA,GAAAA,qBAAAA,SAAS,EAAC;IAC1BL;IACAE;AACF;AAEO,MAAMT,2BAA2Ba,CAAAA,GAAAA,uBAAAA,2BAA2B;AAEnE,MAAMC,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAOM,SAASV,yBACdW,KAAQ,EACRC,IAAoB;IAApBA,IAAAA,SAAAA,KAAAA,GAAAA,OAAO,IAAIC;IAEX,IAAIF,UAAUG,WAAW,OAAOC,mBAAAA,gBAAgB;IAChD,IAAIJ,UAAU,QAAQ,OAAOA,UAAU,UAAU,OAAOA;IACxD,IAAIC,KAAKI,GAAG,CAACL,QAAkB,OAAOC,KAAKK,GAAG,CAACN;IAE/C,IAAI;QACFO,OAAOC,IAAI,CAACR;IACd,EAAE,OAAA,GAAM;QACN,OAAOnB;IACT;IAEA,IAAI;QACF,IAAI,OAAQmB,MAAcS,IAAI,KAAK,YAAY,OAAO7B;IACxD,EAAE,OAAA,GAAM;QACN,OAAOC;IACT;IAEA,IAAI6B,MAAMC,OAAO,CAACX,QAAQ;QACxB,MAAMY,MAAa,EAAE;QACrBX,KAAKY,GAAG,CAACb,OAAOY;QAChB,KAAK,MAAME,QAAQd,MAAO;YACxB,IAAI;gBACFY,IAAIG,IAAI,CAAC1B,yBAAyByB,MAAMb;YAC1C,EAAE,OAAA,GAAM;gBACNW,IAAIG,IAAI,CAAClC;YACX;QACF;QACA,OAAO+B;IACT;IAEA,MAAMI,QAAQT,OAAOU,cAAc,CAACjB;IACpC,IAAIgB,UAAUT,OAAOW,SAAS,IAAIF,UAAU,MAAM;QAChD,MAAMJ,MAA+B,CAAC;QACtCX,KAAKY,GAAG,CAACb,OAAiBY;QAC1B,KAAK,MAAMO,OAAOZ,OAAOC,IAAI,CAACR,OAAkB;YAC9C,IAAI;gBACFY,GAAG,CAACO,IAAI,GAAG9B,yBAA0BW,KAAa,CAACmB,IAAI,EAAElB;YAC3D,EAAE,OAAA,GAAM;gBACNW,GAAG,CAACO,IAAI,GAAGtC;YACb;QACF;QACA,OAAO+B;IACT;IAEA,OAAOL,OAAOW,SAAS,CAACE,QAAQ,CAACC,IAAI,CAACrB;AACxC;AAGO,MAAMb,eAAe,CAACmC;IAC3B,IAAI;QACF,MAAMC,SAAS3B,UAAU0B;QACzB,OAAOC,UAAAA,OAAAA,SAAW,MAAG1C,qBAAmB;IAC1C,EAAE,OAAA,GAAM;QACN,OAAQ,MAAGA,qBAAmB;IAChC;AACF;AAEA,MAAM2C,iBAAiB,CAACC;IACtB,IAAIC;IAEJ,MAAMC,QAAQC,sBAAsB;QAClCF,UAAUG,WAAW;YACnBJ;QACF;IACF;IAEA,OAAO;QACLK,qBAAqBH;QACrBI,aAAaL;IACf;AACF;AAEA,IAAIM,YAAY;AAEhB,MAAMC,mBAAmB,CAACC,UACxBA,QAAQC,GAAG,CAAC,CAACC;QACX,OAAQA,YAAYC,IAAI;YACtB,KAAK;YACL,KAAK;gBAAW;oBACd,OAAO;wBACL,GAAGD,WAAW;wBACdE,MAAMF,YAAYE,IAAI,CAACH,GAAG,CAACI;oBAC7B;gBACF;YACA,KAAK;gBAAmB;oBACtB,OAAOH;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;AAEK,MAAMlD,WAST;IACFgD,SAAS,EAAE;IACXM,gBAAgB;IAChBC,aAAa;IACbC,QAAQ;IACRC,YAAYxC;IACZyC,QAAQ;IACRC,iBAAiB,CAACC;QAChB5D,SAASgD,OAAO,CAACnB,IAAI,CAAC+B;QACtB,IAAI5D,SAASsD,cAAc,EAAE;YAC3B;QACF;QACA,4EAA4E;QAC5E,MAAME,SAASxD,SAASwD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,6BAA6B;QAC7BxD,SAASsD,cAAc,GAAG;QAE1B,sDAAsD;QACtDtD,SAASuD,WAAW,GAAGjB,eAAe;YACpCtC,SAASsD,cAAc,GAAG;YAE1B,cAAc;YACd,IAAI;gBACF,MAAMO,UAAUC,KAAKpD,SAAS,CAAC;oBAC7BqD,OAAO;oBACPf,SAASD,iBAAiB/C,SAASgD,OAAO;oBAC1CU,QAAQ1D,SAAS0D,MAAM;oBACvB,mGAAmG;oBACnGD,YAAYzD,SAASyD,UAAU;gBACjC;gBAEAD,OAAOQ,IAAI,CAACH;gBACZ7D,SAASgD,OAAO,GAAG,EAAE;gBACrBhD,SAASyD,UAAU,GAAGxC;YACxB,EAAE,OAAA,GAAM;YACN,0CAA0C;YAC1C,QAAQ,GACV;QACF;IACF;IACAgD,eAAe,CAACT;QACd,IAAIA,OAAOU,UAAU,KAAKC,UAAUC,IAAI,EAAE;YACxC,YAAY;YACZ;QACF;QAEA,kEAAkE;QAClEpE,SAASuD,WAAW,IAAA,OAAA,KAAA,IAApBvD,SAASuD,WAAW,CAAA,IAAA,CAApBvD;QACAA,SAASwD,MAAM,GAAGA;QAClB,IAAI;YACF,MAAMK,UAAUC,KAAKpD,SAAS,CAAC;gBAC7BqD,OAAO;gBACPf,SAASD,iBAAiB/C,SAASgD,OAAO;gBAC1CU,QAAQ1D,SAAS0D,MAAM;gBACvBD,YAAYzD,SAASyD,UAAU;YACjC;YAEAD,OAAOQ,IAAI,CAACH;YACZ7D,SAASgD,OAAO,GAAG,EAAE;YACrBhD,SAASyD,UAAU,GAAGxC;QACxB,EAAE,OAAA,GAAM;QACN,qBAAqB,GACvB;IACF;AACF;AAEA,MAAMoC,mBAAmB,CACvBgB;IASA,IAAIA,IAAIlB,IAAI,KAAK,OAAO;QACtB,OAAOkB;IACT;IACA,OAAO;QACL,GAAGA,GAAG;QACNjC,MAAMnC,aAAaoE,IAAIjC,IAAI;IAC7B;AACF;AAEA,MAAMkC,iBAAiB,CAACC;IACtB,MAAMC,QAAQC,gBAAgBF;IAC9B,OAAO;QACLpB,MAAM;QACNuB,QAAQH,MAAMI,OAAO,GAAMJ,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO,GAAM,KAAEJ,MAAMK,IAAI;QACzEJ;IACF;AACF;AAEA,MAAMK,iBAAiB,CAACC,OAAkB1B;IACxC,0IAA0I;IAC1I,kCAAkC;IAClC,MAAMoB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,SAAAA,OAAAA,KAAAA,IAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,cAAAA,OAAAA,KAAAA,IAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,mCAAmC;;IACtF,MAAMxB,QAA+B;QACnCT,MAAM;QACNkC,oBAAoBH,cAAAA,OAAAA,aAAc;QAClCI,QAAQR;QACR1B,MAAMA,KAAKH,GAAG,CAAC,CAACoB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLlB,MAAM;gBACNf,MAAMjC,yBAAyBkE;YACjC;QACF;IACF;IAEArE,SAAS2D,eAAe,CAACC;AAC3B;AAEO,MAAMhE,kBAAkB,CAACwD;IAC9B,MAAMmC,eAAenC,KAAKoC,MAAM,CAAC,CAACnB,MAAQA,eAAeU;IACzD,MAAMU,QAAQF,aAAaG,EAAE,CAAC;IAC9B,IAAID,OAAO;QACT,MAAME,SAASC,CAAAA,GAAAA,aAAAA,cAAc,EAACH;QAC9B,IAAIE,QAAQ;YACV3F,SAASyD,UAAU,GAAGkC;QACxB;IACF;IACA;;;;GAIC,GACD,MAAMnB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,SAAAA,OAAAA,KAAAA,IAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,cAAAA,OAAAA,KAAAA,IAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC;IAE7C,MAAMxB,QAAoC;QACxCT,MAAM;QACNmC,QAAQ;QACRO,mBAAmBX,cAAAA,OAAAA,aAAc;QACjC9B,MAAMA,KAAKH,GAAG,CAAC,CAACoB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLlB,MAAM;gBACNf,MAAMjC,yBAAyBkE;YACjC;QACF;IACF;IAEArE,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAMkC,2BAA2B,CAC/BC,WACAC,cACAC;IAEA,MAAMrC,QAA6B;QACjCT,MAAM;QACNuB,QAAS,cAAWqB,YAAU,OAAIC;QAClCxB,OAAOyB;QACPX,QAAQ;IACV;IAEAtF,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAMa,kBAAkB,CAACF;IACvB,IAAI2B,aAAa;IACjBC,CAAAA,GAAAA,eAAAA,wBAAwB,EAAC5B;IACzB2B,aAAaE,CAAAA,GAAAA,eAAAA,aAAa,EAAC7B,UAAU;IACrC,MAAMC,QAASD,CAAAA,MAAMC,KAAK,IAAI,EAAC,IAAK0B;IACpC,OAAO1B;AACT;AAEO,SAAStE,sBAAsBmG,MAAe;IACnD,IAAIA,kBAAkBtB,OAAO;QAC3BuB,mCAAmCD,QAAQ5B,gBAAgB4B;QAC3D;IACF;IACAE,sCAAsCF;AACxC;AAEA,MAAMC,qCAAqC,CACzC/B,OACA0B;IAEA,MAAMN,SAASC,CAAAA,GAAAA,aAAAA,cAAc,EAACrB;IAC9B,IAAIoB,QAAQ;QACV3F,SAASyD,UAAU,GAAGkC;IACxB;IAEA,MAAM/B,QAAwB;QAC5BT,MAAM;QACNuB,QAAS,2BAAwBH,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO;QAC7DH,OAAOyB;QACPX,QAAQ;IACV;IAEAtF,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAM2C,wCAAwC,CAACF;IAC7C,MAAMzC,QAAwB;QAC5BT,MAAM;QACN,kHAAkH;QAClH0C,mBAAmB;QACnBP,QAAQ;QACRlC,MAAM;YACJ;gBACED,MAAM;gBACNf,MAAO;gBACPoE,oBAAoB;YACtB;YACA;gBACErD,MAAM;gBACNf,MAAMjC,yBAAyBkG;YACjC;SACD;IACH;IAEArG,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAM6C,QAAQ,CAACrD;IACb,MAAMsD,WAAWtD,IAAI,CAAC,EAAE;IACxB,IAAI,OAAOsD,aAAa,UAAU;QAChC,OAAO;IACT;IACA,IAAIA,SAASC,UAAU,CAAC,mBAAmB;QACzC,OAAO;IACT;IAEA,IAAID,SAASC,UAAU,CAAC,UAAU;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAMC,eAAe,CAACxD;IACpB,IAAIA,KAAKyD,MAAM,GAAG,GAAG;QACnB,OAAO;IACT;IAEA,MAAM,CAACC,QAAQC,QAAQC,MAAM,GAAG5D;IAEhC,IACE,OAAO0D,WAAW,YAClB,OAAOC,WAAW,YAClB,OAAOC,UAAU,UACjB;QACA,OAAO;IACT;IAEA,0FAA0F;IAC1F,OAAOF,OAAOH,UAAU,CAAC,aAAaI,OAAOE,QAAQ,CAAC;AACxD;AAEO,SAASpH,sBAAsB0E,KAAY;IAChDuB,yBAAyBvB,MAAMK,IAAI,EAAEL,MAAMI,OAAO,EAAEF,gBAAgBF;AACtE;AAGO,MAAMzE,+BAA+B,CAAC4D;IAC3C,2BAA2B;IAC3B,IAAIZ,WAAW;QACb;IACF;IACA,+GAA+G;IAC/G,IAAI,OAAOoE,WAAW,aAAa;QACjC;IACF;IAEA,+BAA+B;IAC/B,IAAI;QACFrG,QAAQsG,OAAO,CAAC,CAAC7B,SACf8B,CAAAA,GAAAA,mBAAAA,kBAAkB,EAAC9B,QAAQ,SAAC+B,CAAAA;iDAAMjE,OAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;oBAAAA,IAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;gBAChC,IAAIqD,MAAMrD,OAAO;oBACf;gBACF;gBACA,IAAIwD,aAAaxD,OAAO;oBACtB;gBACF;gBACAyB,eAAeS,QAAQlC;YACzB;IAEJ,EAAE,OAAA,GAAM,CAAC;IACTpD,SAAS0D,MAAM,GAAGA;IAClBZ,YAAY;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/next-devtools/userspace/pages/pages-dev-overlay-setup.tsx"], "sourcesContent": ["import React from 'react'\nimport { renderPagesDevOverlay } from 'next/dist/compiled/next-devtools'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport {\n  attachHydrationErrorState,\n  storeHydrationErrorStateFromConsoleArgs,\n} from './hydration-error-state'\nimport { Router } from '../../../client/router'\nimport { getComponentStack, getOwnerStack } from '../app/errors/stitched-error'\nimport { isRecoverableError } from '../../../client/react-client-callbacks/on-recoverable-error'\nimport { getSquashedHydrationErrorDetails } from './hydration-error-state'\nimport { PagesDevOverlayErrorBoundary } from './pages-dev-overlay-error-boundary'\nimport {\n  initializeDebugLogForwarding,\n  forwardUnhandledError,\n  logUnhandledRejection,\n  forwardErrorLog,\n  isTerminalLoggingEnabled,\n} from '../app/forward-logs'\n\nconst usePagesDevOverlayBridge = () => {\n  React.useInsertionEffect(() => {\n    // NDT uses a different React instance so it's not technically a state update\n    // scheduled from useInsertionEffect.\n    renderPagesDevOverlay(\n      getComponentStack,\n      getOwnerStack,\n      getSquashedHydrationErrorDetails,\n      isRecoverableError\n    )\n  }, [])\n\n  React.useEffect(() => {\n    const { handleStaticIndicator } =\n      require('../../../client/dev/hot-reloader/pages/hot-reloader-pages') as typeof import('../../../client/dev/hot-reloader/pages/hot-reloader-pages')\n\n    Router.events.on('routeChangeComplete', handleStaticIndicator)\n\n    return function () {\n      Router.events.off('routeChangeComplete', handleStaticIndicator)\n    }\n  }, [])\n}\n\nexport type PagesDevOverlayBridgeType = typeof PagesDevOverlayBridge\n\ninterface PagesDevOverlayBridgeProps {\n  children?: React.ReactNode\n}\n\nexport function PagesDevOverlayBridge({\n  children,\n}: PagesDevOverlayBridgeProps) {\n  usePagesDevOverlayBridge()\n\n  return <PagesDevOverlayErrorBoundary>{children}</PagesDevOverlayErrorBoundary>\n}\n\nlet isRegistered = false\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    dispatcher.onUnhandledError(error)\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const maybeError = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  // TODO: Surfaces non-errors logged via `console.error`.\n  handleError(maybeError)\n  if (isTerminalLoggingEnabled) {\n    forwardErrorLog(args)\n  }\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n\n  if (error && isTerminalLoggingEnabled) {\n    forwardUnhandledError(error as Error)\n  }\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  dispatcher.onUnhandledRejection(reason)\n  if (isTerminalLoggingEnabled) {\n    logUnhandledRejection(reason)\n  }\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    Error.stackTraceLimit = 50\n  } catch {}\n\n  if (isTerminalLoggingEnabled) {\n    initializeDebugLogForwarding('pages')\n  }\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n"], "names": ["PagesDevOverlayBridge", "register", "usePagesDevOverlayBridge", "React", "useInsertionEffect", "renderPagesDevOverlay", "getComponentStack", "getOwnerStack", "getSquashedHydrationErrorDetails", "isRecoverableError", "useEffect", "handleStaticIndicator", "require", "Router", "events", "on", "off", "children", "PagesDevOverlayErrorBoundary", "isRegistered", "handleError", "error", "Error", "stack", "attachHydrationErrorState", "name", "dispatcher", "onUnhandledError", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "maybeError", "process", "env", "NODE_ENV", "storeHydrationErrorStateFromConsoleArgs", "isTerminalLoggingEnabled", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "window", "event", "forwardUnhandledError", "onUnhandledRejection", "ev", "reason", "logUnhandledRejection", "stackTraceLimit", "initializeDebugLogForwarding", "addEventListener"], "mappings": "AAiFqBiC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;IA/B9BnC,qBAAqB,EAAA;eAArBA;;IAmEAC,QAAQ,EAAA;eAARA;;;;;gEArHE;8BACoB;qCAK/B;wBACgB;+BAC0B;oCACd;8CAEU;6BAOtC;AAEP,MAAMC,2BAA2B;IAC/BC,OAAAA,OAAK,CAACC,kBAAkB;uDAAC;YACvB,6EAA6E;YAC7E,qCAAqC;YACrCC,CAAAA,GAAAA,cAAAA,qBAAqB,EACnBC,eAAAA,iBAAiB,EACjBC,eAAAA,aAAa,EACbC,qBAAAA,gCAAgC,EAChCC,oBAAAA,kBAAkB;QAEtB;sDAAG,EAAE;IAELN,OAAAA,OAAK,CAACO,SAAS;8CAAC;YACd,MAAM,EAAEC,qBAAqB,EAAE,GAC7BC,QAAQ;YAEVC,QAAAA,MAAM,CAACC,MAAM,CAACC,EAAE,CAAC,uBAAuBJ;YAExC;sDAAO;oBACLE,QAAAA,MAAM,CAACC,MAAM,CAACE,GAAG,CAAC,uBAAuBL;gBAC3C;;QACF;6CAAG,EAAE;AACP;AAQO,SAASX,sBAAsB,KAET;IAFS,IAAA,EACpCiB,QAAQ,EACmB,GAFS;IAGpCf;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACgB,8BAAAA,4BAA4B,EAAA;kBAAED;;AACxC;AAEA,IAAIE,eAAe;AAEnB,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEAC,CAAAA,GAAAA,qBAAAA,yBAAyB,EAACH;IAE1B,mGAAmG;IACnG,wFAAwF;IACxF,IACEA,MAAMI,IAAI,KAAK,sBACfJ,MAAMI,IAAI,KAAK,uBACf;QACAC,cAAAA,UAAU,CAACC,gBAAgB,CAACN;IAC9B;AACF;AAEA,IAAIO,mBAAmBC,QAAQR,KAAK;AACpC,SAASS;IAAyB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAc;;IAC9C,iJAAiJ;IACjJ,MAAMC,oDAAqDD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IAC5EK,CAAAA,GAAAA,qBAAAA,uCAAuC,KAAIL;IAC3C,wDAAwD;IACxDX,YAAYY;IACZ,IAAIK,aAAAA,wBAAwB,EAAE;QAC5BC,CAAAA,GAAAA,aAAAA,eAAe,EAACP;IAClB;IACAH,iBAAiBW,KAAK,CAACC,OAAOX,OAAO,EAAEE;AACzC;AAEA,SAASJ,iBAAiBc,KAAiB;IACzC,MAAMpB,QAAQoB,SAAAA,OAAAA,KAAAA,IAAAA,MAAOpB,KAAK;IAC1BD,YAAYC;IAEZ,IAAIA,SAASgB,aAAAA,wBAAwB,EAAE;QACrCK,CAAAA,GAAAA,aAAAA,qBAAqB,EAACrB;IACxB;AACF;AAEA,SAASsB,qBAAqBC,EAAyB;IACrD,MAAMC,SAASD,MAAAA,OAAAA,KAAAA,IAAAA,GAAIC,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBvB,KAAI,KACxB,OAAOuB,OAAOtB,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEAG,cAAAA,UAAU,CAACiB,oBAAoB,CAACE;IAChC,IAAIR,aAAAA,wBAAwB,EAAE;QAC5BS,CAAAA,GAAAA,aAAAA,qBAAqB,EAACD;IACxB;AACF;AAEO,SAAS5C;IACd,IAAIkB,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACFG,MAAMyB,eAAe,GAAG;IAC1B,EAAE,OAAA,GAAM,CAAC;IAET,IAAIV,aAAAA,wBAAwB,EAAE;QAC5BW,CAAAA,GAAAA,aAAAA,4BAA4B,EAAC;IAC/B;IACAR,OAAOS,gBAAgB,CAAC,SAAStB;IACjCa,OAAOS,gBAAgB,CAAC,sBAAsBN;IAC9CH,OAAOX,OAAO,CAACR,KAAK,GAAGS;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/server/dev/hot-reloader-types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { Duplex } from 'stream'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type getBaseWebpackConfig from '../../build/webpack-config'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Project, Update as TurbopackUpdate } from '../../build/swc/types'\nimport type { VersionInfo } from './parse-version-info'\nimport type { DebugInfo } from '../../next-devtools/shared/types'\nimport type { DevIndicatorServerState } from './dev-indicator-server-state'\n\nexport const enum HMR_ACTIONS_SENT_TO_BROWSER {\n  ADDED_PAGE = 'addedPage',\n  REMOVED_PAGE = 'removedPage',\n  RELOAD_PAGE = 'reloadPage',\n  SERVER_COMPONENT_CHANGES = 'serverComponentChanges',\n  MIDDLEWARE_CHANGES = 'middlewareChanges',\n  CLIENT_CHANGES = 'clientChanges',\n  SERVER_ONLY_CHANGES = 'serverOnlyChanges',\n  SYNC = 'sync',\n  BUILT = 'built',\n  BUILDING = 'building',\n  DEV_PAGES_MANIFEST_UPDATE = 'devPagesManifestUpdate',\n  TURBOPACK_MESSAGE = 'turbopack-message',\n  SERVER_ERROR = 'serverError',\n  TURBOPACK_CONNECTED = 'turbopack-connected',\n  ISR_MANIFEST = 'isrManifest',\n  DEV_INDICATOR = 'devIndicator',\n}\n\ninterface ServerErrorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR\n  errorJSON: string\n}\n\nexport interface TurbopackMessageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE\n  data: TurbopackUpdate | TurbopackUpdate[]\n}\n\ninterface BuildingAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING\n}\n\nexport interface CompilationError {\n  moduleName?: string\n  message: string\n  details?: string\n  moduleTrace?: Array<{ moduleName?: string }>\n  stack?: string\n}\nexport interface SyncAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  versionInfo: VersionInfo\n  updatedModules?: ReadonlyArray<string>\n  debug?: DebugInfo\n  devIndicator: DevIndicatorServerState\n}\ninterface BuiltAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  updatedModules?: ReadonlyArray<string>\n}\n\ninterface AddedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE\n  data: [page: string | null]\n}\n\ninterface RemovedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE\n  data: [page: string | null]\n}\n\nexport interface ReloadPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE\n  data: string\n}\n\ninterface ServerComponentChangesAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES\n  hash: string\n}\n\ninterface MiddlewareChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES\n}\n\ninterface ClientChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES\n}\n\ninterface ServerOnlyChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES\n  pages: ReadonlyArray<string>\n}\n\ninterface DevPagesManifestUpdateAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n  data: [\n    {\n      devPagesManifest: true\n    },\n  ]\n}\n\nexport interface TurbopackConnectedAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n  data: { sessionId: number }\n}\n\nexport interface AppIsrManifestAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST\n  data: Record<string, boolean>\n}\n\nexport interface DevIndicatorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type HMR_ACTION_TYPES =\n  | TurbopackMessageAction\n  | TurbopackConnectedAction\n  | BuildingAction\n  | SyncAction\n  | BuiltAction\n  | AddedPageAction\n  | RemovedPageAction\n  | ReloadPageAction\n  | ServerComponentChangesAction\n  | ClientChangesAction\n  | MiddlewareChangesAction\n  | ServerOnlyChangesAction\n  | DevPagesManifestUpdateAction\n  | ServerErrorAction\n  | AppIsrManifestAction\n  | DevIndicatorAction\n\nexport type TurbopackMsgToBrowser =\n  | { type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE; data: any }\n  | {\n      type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      data: { sessionId: number }\n    }\n\nexport interface NextJsHotReloaderInterface {\n  turbopackProject?: Project\n  activeWebpackConfigs?: Array<Awaited<ReturnType<typeof getBaseWebpackConfig>>>\n  serverStats: webpack.Stats | null\n  edgeServerStats: webpack.Stats | null\n  run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }>\n\n  setHmrServerError(error: Error | null): void\n  clearHmrServerError(): void\n  start(): Promise<void>\n  send(action: HMR_ACTION_TYPES): void\n  getCompilationErrors(page: string): Promise<any[]>\n  onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    onUpgrade: (client: { send(data: string): void }) => void\n  ): void\n  invalidate({\n    reloadAfterInvalidation,\n  }: {\n    reloadAfterInvalidation: boolean\n  }): Promise<void> | void\n  buildFallbackError(): Promise<void>\n  ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void>\n  close(): void\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER"], "mappings": ";;;+BAWkBA,+BAAAA;;;eAAAA;;;AAAX,IAAWA,8BAAAA,WAAAA,GAAAA,SAAAA,2BAAAA;;;;;;;;;;;;;;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/server/dev/node-stack-frames.ts"], "sourcesContent": ["import { parse } from 'next/dist/compiled/stacktrace-parser'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  decorateServerError,\n  type ErrorSourceType,\n} from '../../shared/lib/error-source'\n\nfunction getFilesystemFrame(frame: StackFrame): StackFrame {\n  const f: StackFrame = { ...frame }\n\n  if (typeof f.file === 'string') {\n    if (\n      // Posix:\n      f.file.startsWith('/') ||\n      // Win32:\n      /^[a-z]:\\\\/i.test(f.file) ||\n      // Win32 UNC:\n      f.file.startsWith('\\\\\\\\')\n    ) {\n      f.file = `file://${f.file}`\n    }\n  }\n\n  return f\n}\n\nexport function getServerError(error: Error, type: ErrorSourceType): Error {\n  if (error.name === 'TurbopackInternalError') {\n    // If this is an internal Turbopack error we shouldn't show internal details\n    // to the user. These are written to a log file instead.\n    const turbopackInternalError = new Error(\n      'An unexpected Turbopack error occurred. Please see the output of `next dev` for more details.'\n    )\n    decorateServerError(turbopackInternalError, type)\n    return turbopackInternalError\n  }\n\n  let n: Error\n  try {\n    throw new Error(error.message)\n  } catch (e) {\n    n = e as Error\n  }\n\n  n.name = error.name\n  try {\n    n.stack = `${n.toString()}\\n${parse(error.stack!)\n      .map(getFilesystemFrame)\n      .map((f) => {\n        let str = `    at ${f.methodName}`\n        if (f.file) {\n          let loc = f.file\n          if (f.lineNumber) {\n            loc += `:${f.lineNumber}`\n            if (f.column) {\n              loc += `:${f.column}`\n            }\n          }\n          str += ` (${loc})`\n        }\n        return str\n      })\n      .join('\\n')}`\n  } catch {\n    n.stack = error.stack\n  }\n\n  decorateServerError(n, type)\n  return n\n}\n"], "names": ["getServerError", "getFilesystemFrame", "frame", "f", "file", "startsWith", "test", "error", "type", "name", "turbopackInternalError", "Error", "decorateServerError", "n", "message", "e", "stack", "toString", "parse", "map", "str", "methodName", "loc", "lineNumber", "column", "join"], "mappings": ";;;+BA0BgBA,kBAAAA;;;eAAAA;;;kCA1BM;6BAKf;AAEP,SAASC,mBAAmBC,KAAiB;IAC3C,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,AACAD,EAAEC,IAAI,CAACC,EADE,QACQ,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,AAAC,OAAO,GAAQ,CAAE,MAARD,EAAEC,IAAI;QAC3B;IACF;IAEA,OAAOD;AACT;AAEO,SAASH,eAAeO,KAAY,EAAEC,IAAqB;IAChE,IAAID,MAAME,IAAI,KAAK,0BAA0B;QAC3C,4EAA4E;QAC5E,wDAAwD;QACxD,MAAMC,yBAAyB,OAAA,cAE9B,CAF8B,IAAIC,MACjC,kGAD6B,qBAAA;mBAAA;wBAAA;0BAAA;QAE/B;QACAC,CAAAA,GAAAA,aAAAA,mBAAmB,EAACF,wBAAwBF;QAC5C,OAAOE;IACT;IAEA,IAAIG;IACJ,IAAI;QACF,MAAM,OAAA,cAAwB,CAAxB,IAAIF,MAAMJ,MAAMO,OAAO,GAAvB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B,EAAE,OAAOC,GAAG;QACVF,IAAIE;IACN;IAEAF,EAAEJ,IAAI,GAAGF,MAAME,IAAI;IACnB,IAAI;QACFI,EAAEG,KAAK,GAAG,UAAGH,EAAEI,QAAQ,IAAG,EAAE,IAgBpB,CAAO,MAhBeC,CAAAA,GAAAA,kBAAAA,KAAK,EAACX,MAAMS,KAAK,EAC5CG,GAAG,CAAClB,oBACJkB,GAAG,CAAC,CAAChB;YACJ,IAAIiB,MAAM,AAAC,OAAO,GAAc,CAAE,MAAdjB,EAAEkB,UAAU;YAChC,IAAIlB,EAAEC,IAAI,EAAE;gBACV,IAAIkB,MAAMnB,EAAEC,IAAI;gBAChB,IAAID,EAAEoB,UAAU,EAAE;oBAChBD,OAAO,AAAC,CAAC,GAAc,CAAE,MAAdnB,EAAEoB,UAAU;oBACvB,IAAIpB,EAAEqB,MAAM,EAAE;wBACZF,OAAO,AAAC,CAAC,GAAU,CAAE,MAAVnB,EAAEqB,MAAM;oBACrB;gBACF;gBACAJ,OAAO,AAAC,EAAE,UAAEE,KAAI,CAAC,CAAC;YACpB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC;IACV,EAAE,UAAM;QACNZ,EAAEG,KAAK,GAAGT,MAAMS,KAAK;IACvB;IAEAJ,CAAAA,GAAAA,aAAAA,mBAAmB,EAACC,GAAGL;IACvB,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/src/pages/_app.tsx"], "sourcesContent": ["import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n"], "names": ["App", "appGetInitialProps", "Component", "ctx", "pageProps", "loadGetInitialProps", "React", "render", "props", "origGetInitialProps", "getInitialProps"], "mappings": ";;;;;;eAiCqBA;;;;;gEAjCH;uBAWkB;AAUpC;;;CAGC,GACD,eAAeC,mBAAmB,KAGrB;IAHqB,IAAA,EAChCC,SAAS,EACTC,GAAG,EACQ,GAHqB;IAIhC,MAAMC,YAAY,MAAMC,CAAAA,GAAAA,OAAAA,mBAAmB,EAACH,WAAWC;IACvD,OAAO;QAAEC;IAAU;AACrB;AAEe,MAAMJ,YAAsCM,OAAAA,OAAK,CAACJ,SAAS;IAOxEK,SAAS;QACP,MAAM,EAAEL,SAAS,EAAEE,SAAS,EAAE,GAAG,IAAI,CAACI,KAAK;QAE3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACN,WAAAA;YAAW,GAAGE,SAAS;;IACjC;AACF;AAZqBJ,IAIZS,mBAAAA,GAAsBR;AAJVD,IAKZU,eAAAA,GAAkBT", "ignoreList": [0], "debugId": null}}]}