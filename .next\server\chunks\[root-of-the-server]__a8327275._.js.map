{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/mock-data.ts"], "sourcesContent": ["import { Student, Subject, TimePeriod, AttendanceRecord } from \"@/lib/types/scanner\"\n\n// Mock student data with photos\nexport const mockStudents: Student[] = [\n  {\n    id: \"STU001\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"3rd Year\",\n    section: \"IT-3A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU001_2025\"\n  },\n  {\n    id: \"STU002\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"2nd Year\",\n    section: \"CS-2B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU002_2025\"\n  },\n  {\n    id: \"STU003\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"1st Year\",\n    section: \"IT-1C\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU003_2025\"\n  },\n  {\n    id: \"STU004\",\n    name: \"Sarah Wilson\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"4th Year\",\n    section: \"CS-4A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU004_2025\"\n  },\n  {\n    id: \"STU005\",\n    name: \"Alex Chen\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"2nd Year\",\n    section: \"IT-2A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU005_2025\"\n  },\n  {\n    id: \"STU006\",\n    name: \"Maria Garcia\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"3rd Year\",\n    section: \"CS-3B\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU006_2025\"\n  },\n  {\n    id: \"STU007\",\n    name: \"David Brown\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"4th Year\",\n    section: \"IT-4A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU007_2025\"\n  },\n  {\n    id: \"STU008\",\n    name: \"Lisa Anderson\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"1st Year\",\n    section: \"CS-1A\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU008_2025\"\n  },\n  {\n    id: \"STU009\",\n    name: \"Robert Taylor\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"3rd Year\",\n    section: \"IT-3B\",\n    grade: \"11\",\n    status: \"Inactive\",\n    photo: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU009_2025\"\n  },\n  {\n    id: \"STU010\",\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"2nd Year\",\n    section: \"CS-2A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU010_2025\"\n  }\n]\n\n// Mock subjects\nexport const mockSubjects: Subject[] = [\n  {\n    id: \"SUBJ001\",\n    name: \"Programming Fundamentals\",\n    code: \"IT101\",\n    instructor: \"Prof. Martinez\",\n    schedule: [\n      { day: \"Monday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Wednesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Friday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ002\",\n    name: \"Database Management\",\n    code: \"IT201\",\n    instructor: \"Prof. Rodriguez\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"10:00\", endTime: \"12:00\" },\n      { day: \"Thursday\", startTime: \"10:00\", endTime: \"12:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ003\",\n    name: \"Web Development\",\n    code: \"IT301\",\n    instructor: \"Prof. Santos\",\n    schedule: [\n      { day: \"Monday\", startTime: \"13:00\", endTime: \"15:00\" },\n      { day: \"Wednesday\", startTime: \"13:00\", endTime: \"15:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ004\",\n    name: \"Data Structures\",\n    code: \"CS201\",\n    instructor: \"Prof. Reyes\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Thursday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ005\",\n    name: \"Software Engineering\",\n    code: \"CS301\",\n    instructor: \"Prof. Cruz\",\n    schedule: [\n      { day: \"Monday\", startTime: \"15:00\", endTime: \"17:00\" },\n      { day: \"Friday\", startTime: \"15:00\", endTime: \"17:00\" }\n    ]\n  }\n]\n\n// Mock time periods\nexport const mockTimePeriods: TimePeriod[] = [\n  {\n    id: \"PERIOD001\",\n    name: \"1st Period\",\n    startTime: \"08:00\",\n    endTime: \"10:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD002\",\n    name: \"2nd Period\",\n    startTime: \"10:00\",\n    endTime: \"12:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD003\",\n    name: \"3rd Period\",\n    startTime: \"13:00\",\n    endTime: \"15:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD004\",\n    name: \"4th Period\",\n    startTime: \"15:00\",\n    endTime: \"17:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD005\",\n    name: \"Evening Class\",\n    startTime: \"18:00\",\n    endTime: \"20:00\",\n    type: \"evening\"\n  }\n]\n\n// Mock attendance records\nexport const mockAttendanceRecords: AttendanceRecord[] = [\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"John Doe\",\n    course: \"Information Technology\",\n    checkIn: \"8:30 AM\",\n    checkOut: \"5:00 PM\",\n    date: \"2025-01-02\",\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(\"2025-01-02T08:30:00\")\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Jane Smith\",\n    course: \"Computer Science\",\n    checkIn: \"8:25 AM\",\n    checkOut: \"4:55 PM\",\n    date: \"2025-01-02\",\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(\"2025-01-02T08:25:00\")\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Mike Johnson\",\n    course: \"Information Technology\",\n    checkIn: \"8:45 AM\",\n    checkOut: \"5:10 PM\",\n    date: \"2025-01-02\",\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(\"2025-01-02T08:45:00\")\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Sarah Wilson\",\n    course: \"Computer Science\",\n    date: \"2025-01-02\",\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Programming Fundamentals\",\n    period: \"1st Period\",\n    timestamp: new Date(\"2025-01-02T08:00:00\")\n  }\n]\n\n// Helper functions\nexport function findStudentById(id: string): Student | undefined {\n  return mockStudents.find(student => student.id === id)\n}\n\nexport function findStudentByQRCode(qrCode: string): Student | undefined {\n  return mockStudents.find(student => student.qrCode === qrCode)\n}\n\nexport function findSubjectById(id: string): Subject | undefined {\n  return mockSubjects.find(subject => subject.id === id)\n}\n\nexport function findPeriodById(id: string): TimePeriod | undefined {\n  return mockTimePeriods.find(period => period.id === id)\n}\n\nexport function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {\n  return mockAttendanceRecords.filter(record => record.studentId === studentId)\n}\n\nexport function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {\n  const today = new Date().toISOString().split('T')[0]\n  return mockAttendanceRecords.find(record => \n    record.studentId === studentId && record.date === today\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGO,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;YACzD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;CACD;AAGM,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,wBAA4C;IACvD;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,MAAc;IAChD,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACzD;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AACtD;AAEO,SAAS,4BAA4B,SAAiB;IAC3D,OAAO,sBAAsB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;AACrE;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAChC,OAAO,SAAS,KAAK,aAAa,OAAO,IAAI,KAAK;AAEtD", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/api/scanner/attendance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\nimport { findStudentById, findSubjectById, findPeriodById } from \"@/lib/data/mock-data\"\nimport { AttendanceResponse, AttendanceRecord, AttendanceAction } from \"@/lib/types/scanner\"\n\n// In-memory storage for demo purposes\n// In production, this would be stored in a database\nlet attendanceRecords: AttendanceRecord[] = []\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { \n      studentId, \n      action, \n      subject, \n      period, \n      reason,\n      offline = false,\n      originalTimestamp \n    } = body\n\n    if (!studentId || !action) {\n      return NextResponse.json({\n        success: false,\n        error: \"studentId and action are required\"\n      } as AttendanceResponse, { status: 400 })\n    }\n\n    // Validate student exists\n    const student = findStudentById(studentId)\n    if (!student) {\n      return NextResponse.json({\n        success: false,\n        error: \"Student not found\"\n      } as AttendanceResponse, { status: 404 })\n    }\n\n    // Validate subject if provided\n    if (subject) {\n      const subjectData = findSubjectById(subject)\n      if (!subjectData) {\n        return NextResponse.json({\n          success: false,\n          error: \"Subject not found\"\n        } as AttendanceResponse, { status: 404 })\n      }\n    }\n\n    // Validate period if provided\n    if (period) {\n      const periodData = findPeriodById(period)\n      if (!periodData) {\n        return NextResponse.json({\n          success: false,\n          error: \"Period not found\"\n        } as AttendanceResponse, { status: 404 })\n      }\n    }\n\n    // Create attendance record\n    const timestamp = originalTimestamp ? new Date(originalTimestamp) : new Date()\n    const attendanceRecord: AttendanceRecord = {\n      id: `ATT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      studentId: student.id,\n      studentName: student.name,\n      course: student.course,\n      date: timestamp.toISOString().split('T')[0],\n      status: mapActionToStatus(action),\n      type: determineType(action, subject),\n      timestamp,\n      subject,\n      period\n    }\n\n    // Set check-in/check-out times for gate actions\n    if (action === 'check-in') {\n      attendanceRecord.checkIn = timestamp.toLocaleTimeString('en-US', {\n        hour: 'numeric',\n        minute: '2-digit',\n        hour12: true\n      })\n    } else if (action === 'check-out') {\n      attendanceRecord.checkOut = timestamp.toLocaleTimeString('en-US', {\n        hour: 'numeric',\n        minute: '2-digit',\n        hour12: true\n      })\n    }\n\n    // Store the record\n    attendanceRecords.push(attendanceRecord)\n\n    // Log for debugging\n    console.log(`Attendance recorded: ${student.name} - ${action}${offline ? ' (offline)' : ''}`)\n\n    return NextResponse.json({\n      success: true,\n      data: attendanceRecord,\n      message: `Attendance recorded successfully${offline ? ' (synced from offline)' : ''}`\n    } as AttendanceResponse)\n\n  } catch (error) {\n    console.error(\"Attendance recording error:\", error)\n    return NextResponse.json({\n      success: false,\n      error: \"Internal server error\"\n    } as AttendanceResponse, { status: 500 })\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const studentId = searchParams.get('studentId')\n    const date = searchParams.get('date')\n\n    let filteredRecords = attendanceRecords\n\n    if (studentId) {\n      filteredRecords = filteredRecords.filter(record => record.studentId === studentId)\n    }\n\n    if (date) {\n      filteredRecords = filteredRecords.filter(record => record.date === date)\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: filteredRecords,\n      message: \"Attendance records retrieved successfully\"\n    })\n\n  } catch (error) {\n    console.error(\"Attendance retrieval error:\", error)\n    return NextResponse.json({\n      success: false,\n      error: \"Internal server error\"\n    }, { status: 500 })\n  }\n}\n\n// Helper functions\nfunction mapActionToStatus(action: AttendanceAction): 'Present' | 'Late' | 'Absent' {\n  switch (action) {\n    case 'present':\n    case 'check-in':\n    case 'check-out':\n      return 'Present'\n    case 'late':\n      return 'Late'\n    case 'absent':\n      return 'Absent'\n    default:\n      return 'Present'\n  }\n}\n\nfunction determineType(action: AttendanceAction, subject?: string): 'gate' | 'subject' {\n  if (action === 'check-in' || action === 'check-out') {\n    return 'gate'\n  }\n  return subject ? 'subject' : 'gate'\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGA,sCAAsC;AACtC,oDAAoD;AACpD,IAAI,oBAAwC,EAAE;AAEvC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,EACT,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,UAAU,KAAK,EACf,iBAAiB,EAClB,GAAG;QAEJ,IAAI,CAAC,aAAa,CAAC,QAAQ;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAyB;gBAAE,QAAQ;YAAI;QACzC;QAEA,0BAA0B;QAC1B,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE;QAChC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAyB;gBAAE,QAAQ;YAAI;QACzC;QAEA,+BAA+B;QAC/B,IAAI,SAAS;YACX,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE;YACpC,IAAI,CAAC,aAAa;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAyB;oBAAE,QAAQ;gBAAI;YACzC;QACF;QAEA,8BAA8B;QAC9B,IAAI,QAAQ;YACV,MAAM,aAAa,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YAClC,IAAI,CAAC,YAAY;gBACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAyB;oBAAE,QAAQ;gBAAI;YACzC;QACF;QAEA,2BAA2B;QAC3B,MAAM,YAAY,oBAAoB,IAAI,KAAK,qBAAqB,IAAI;QACxE,MAAM,mBAAqC;YACzC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE,WAAW,QAAQ,EAAE;YACrB,aAAa,QAAQ,IAAI;YACzB,QAAQ,QAAQ,MAAM;YACtB,MAAM,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3C,QAAQ,kBAAkB;YAC1B,MAAM,cAAc,QAAQ;YAC5B;YACA;YACA;QACF;QAEA,gDAAgD;QAChD,IAAI,WAAW,YAAY;YACzB,iBAAiB,OAAO,GAAG,UAAU,kBAAkB,CAAC,SAAS;gBAC/D,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF,OAAO,IAAI,WAAW,aAAa;YACjC,iBAAiB,QAAQ,GAAG,UAAU,kBAAkB,CAAC,SAAS;gBAChE,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF;QAEA,mBAAmB;QACnB,kBAAkB,IAAI,CAAC;QAEvB,oBAAoB;QACpB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,SAAS,UAAU,eAAe,IAAI;QAE5F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS,CAAC,gCAAgC,EAAE,UAAU,2BAA2B,IAAI;QACvF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAyB;YAAE,QAAQ;QAAI;IACzC;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,IAAI,kBAAkB;QAEtB,IAAI,WAAW;YACb,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;QAC1E;QAEA,IAAI,MAAM;YACR,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;QACrE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEA,mBAAmB;AACnB,SAAS,kBAAkB,MAAwB;IACjD,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,cAAc,MAAwB,EAAE,OAAgB;IAC/D,IAAI,WAAW,cAAc,WAAW,aAAa;QACnD,OAAO;IACT;IACA,OAAO,UAAU,YAAY;AAC/B", "debugId": null}}]}