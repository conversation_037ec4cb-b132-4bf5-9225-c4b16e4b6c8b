{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/types/student.ts"], "sourcesContent": ["// Student Management Types\n\nexport interface Guardian {\n  name: string\n  phone: string\n  email?: string\n  relationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'\n  address?: string\n}\n\nexport interface EmergencyContact {\n  name: string\n  phone: string\n  relationship: string\n  address?: string\n}\n\nexport interface Address {\n  street: string\n  barangay: string\n  city: string\n  province: string\n  zipCode: string\n  country?: string\n}\n\nexport interface AttendanceStats {\n  totalDays: number\n  presentDays: number\n  lateDays: number\n  absentDays: number\n  attendanceRate: number\n  lastAttendance?: string\n}\n\nexport interface Student {\n  id: string // DepEd ID format (e.g., 123456789012)\n  firstName: string\n  middleName?: string\n  lastName: string\n  email: string\n  course: string\n  year: string\n  section?: string\n  grade: '7' | '8' | '9' | '10' | '11' | '12'\n  status: 'Active' | 'Inactive' | 'Transferred' | 'Graduated'\n  photo?: string\n  qrCode?: string\n  dateOfBirth?: string\n  gender?: 'Male' | 'Female'\n  guardian: Guardian\n  emergencyContacts: EmergencyContact[]\n  address: Address\n  enrollmentDate: string\n  lastUpdated: string\n  attendanceStats?: AttendanceStats\n}\n\n// Computed properties helper\nexport const getFullName = (student: Student): string => {\n  const parts = [student.firstName, student.middleName, student.lastName].filter(Boolean)\n  return parts.join(' ')\n}\n\n// Form types for student registration/editing\nexport interface StudentFormData {\n  // Basic Information\n  id: string\n  firstName: string\n  middleName?: string\n  lastName: string\n  email: string\n  dateOfBirth?: string\n  gender?: 'Male' | 'Female'\n  \n  // Academic Information\n  course: string\n  year: string\n  section?: string\n  grade: '7' | '8' | '9' | '10' | '11' | '12'\n  \n  // Guardian Information\n  guardianName: string\n  guardianPhone: string\n  guardianEmail?: string\n  guardianRelationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'\n  guardianAddress?: string\n  \n  // Emergency Contacts\n  emergencyContacts: EmergencyContact[]\n  \n  // Address Information\n  street: string\n  barangay: string\n  city: string\n  province: string\n  zipCode: string\n  country?: string\n  \n  // Photo\n  photo?: File | string\n}\n\n// Filter and search types\nexport interface StudentFilters {\n  search?: string\n  grade?: string[]\n  section?: string[]\n  status?: string[]\n  course?: string[]\n  year?: string[]\n}\n\nexport interface StudentSortConfig {\n  field: keyof Student | 'name'\n  direction: 'asc' | 'desc'\n}\n\n// Bulk operations\nexport interface BulkOperation {\n  type: 'export' | 'generateQR' | 'updateStatus' | 'delete'\n  studentIds: string[]\n  options?: Record<string, any>\n}\n\nexport interface CSVImportResult {\n  success: boolean\n  imported: number\n  failed: number\n  errors: Array<{\n    row: number\n    field: string\n    message: string\n  }>\n}\n\n// QR Code types\nexport interface QRCodeData {\n  studentId: string\n  name: string\n  grade: string\n  section?: string\n  validUntil?: string\n}\n\nexport interface QRCodeBatch {\n  id: string\n  createdAt: string\n  studentIds: string[]\n  format: 'individual' | 'sheet'\n  status: 'generating' | 'ready' | 'failed'\n}\n\n// Pagination\nexport interface PaginationConfig {\n  page: number\n  pageSize: number\n  total: number\n}\n\n// API Response types\nexport interface StudentListResponse {\n  students: Student[]\n  pagination: PaginationConfig\n  filters: StudentFilters\n}\n\nexport interface StudentResponse {\n  student: Student\n  success: boolean\n  message?: string\n}\n\n// Validation schemas (for use with zod)\nexport const GRADE_LEVELS = ['7', '8', '9', '10', '11', '12'] as const\nexport const STUDENT_STATUSES = ['Active', 'Inactive', 'Transferred', 'Graduated'] as const\nexport const GUARDIAN_RELATIONSHIPS = ['Father', 'Mother', 'Guardian', 'Grandparent', 'Sibling', 'Other'] as const\nexport const GENDERS = ['Male', 'Female'] as const\n\n// Default values\nexport const DEFAULT_STUDENT: Partial<Student> = {\n  status: 'Active',\n  emergencyContacts: [],\n  country: 'Philippines'\n}\n\nexport const DEFAULT_PAGINATION: PaginationConfig = {\n  page: 1,\n  pageSize: 20,\n  total: 0\n}\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;AA2DpB,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;QAAC,QAAQ,SAAS;QAAE,QAAQ,UAAU;QAAE,QAAQ,QAAQ;KAAC,CAAC,MAAM,CAAC;IAC/E,OAAO,MAAM,IAAI,CAAC;AACpB;AAgHO,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAM;IAAM;CAAK;AACtD,MAAM,mBAAmB;IAAC;IAAU;IAAY;IAAe;CAAY;AAC3E,MAAM,yBAAyB;IAAC;IAAU;IAAU;IAAY;IAAe;IAAW;CAAQ;AAClG,MAAM,UAAU;IAAC;IAAQ;CAAS;AAGlC,MAAM,kBAAoC;IAC/C,QAAQ;IACR,mBAAmB,EAAE;IACrB,SAAS;AACX;AAEO,MAAM,qBAAuC;IAClD,MAAM;IACN,UAAU;IACV,OAAO;AACT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/qr-code.ts"], "sourcesContent": ["import { Student, QRCodeData, getFullName } from \"@/lib/types/student\"\n\n// QR Code generation utilities\nexport class QRCodeGenerator {\n  private static instance: QRCodeGenerator\n  \n  static getInstance(): QRCodeGenerator {\n    if (!QRCodeGenerator.instance) {\n      QRCodeGenerator.instance = new QRCodeGenerator()\n    }\n    return QRCodeGenerator.instance\n  }\n\n  // Generate QR code data for a student\n  generateQRData(student: Student): QRCodeData {\n    return {\n      studentId: student.id,\n      name: getFullName(student),\n      grade: student.grade,\n      section: student.section,\n      validUntil: this.getValidUntilDate()\n    }\n  }\n\n  // Generate QR code string (what gets encoded in the QR code)\n  generateQRString(student: Student): string {\n    const data = this.generateQRData(student)\n    return JSON.stringify({\n      id: data.studentId,\n      name: data.name,\n      grade: data.grade,\n      section: data.section,\n      school: \"Tanauan School of Arts and Trade\",\n      type: \"student_id\",\n      validUntil: data.validUntil,\n      generated: new Date().toISOString()\n    })\n  }\n\n  // Generate a unique QR code identifier\n  generateQRCodeId(student: Student): string {\n    const timestamp = Date.now()\n    const year = new Date().getFullYear()\n    return `QR_${student.id}_${year}_${timestamp.toString().slice(-6)}`\n  }\n\n  // Get validity date (1 year from now)\n  private getValidUntilDate(): string {\n    const date = new Date()\n    date.setFullYear(date.getFullYear() + 1)\n    return date.toISOString().split('T')[0]\n  }\n\n  // Validate QR code data\n  validateQRData(qrString: string): { valid: boolean; data?: any; error?: string } {\n    try {\n      const data = JSON.parse(qrString)\n      \n      if (!data.id || !data.name || !data.type) {\n        return { valid: false, error: 'Missing required fields' }\n      }\n\n      if (data.type !== 'student_id') {\n        return { valid: false, error: 'Invalid QR code type' }\n      }\n\n      if (data.validUntil && new Date(data.validUntil) < new Date()) {\n        return { valid: false, error: 'QR code has expired' }\n      }\n\n      return { valid: true, data }\n    } catch (error) {\n      return { valid: false, error: 'Invalid QR code format' }\n    }\n  }\n}\n\n// QR Code printing utilities\nexport interface QRPrintOptions {\n  size: 'small' | 'medium' | 'large'\n  includeStudentInfo: boolean\n  includeSchoolLogo: boolean\n  paperSize: 'A4' | 'Letter'\n  codesPerSheet: number\n}\n\nexport class QRCodePrinter {\n  private static instance: QRCodePrinter\n  \n  static getInstance(): QRCodePrinter {\n    if (!QRCodePrinter.instance) {\n      QRCodePrinter.instance = new QRCodePrinter()\n    }\n    return QRCodePrinter.instance\n  }\n\n  // Get QR code size in pixels\n  getQRSize(size: 'small' | 'medium' | 'large'): number {\n    switch (size) {\n      case 'small': return 96  // 1 inch at 96 DPI\n      case 'medium': return 144 // 1.5 inches at 96 DPI\n      case 'large': return 192  // 2 inches at 96 DPI\n      default: return 144\n    }\n  }\n\n  // Generate print layout for multiple QR codes\n  generatePrintLayout(students: Student[], options: QRPrintOptions): PrintLayout {\n    const qrGenerator = QRCodeGenerator.getInstance()\n    const qrSize = this.getQRSize(options.size)\n    \n    const qrCodes = students.map(student => ({\n      student,\n      qrData: qrGenerator.generateQRString(student),\n      qrId: qrGenerator.generateQRCodeId(student)\n    }))\n\n    const sheets = this.layoutQRCodes(qrCodes, options)\n    \n    return {\n      sheets,\n      totalCodes: qrCodes.length,\n      totalSheets: sheets.length,\n      options\n    }\n  }\n\n  // Layout QR codes on sheets\n  private layoutQRCodes(qrCodes: any[], options: QRPrintOptions): PrintSheet[] {\n    const sheets: PrintSheet[] = []\n    const codesPerSheet = options.codesPerSheet\n    \n    for (let i = 0; i < qrCodes.length; i += codesPerSheet) {\n      const sheetCodes = qrCodes.slice(i, i + codesPerSheet)\n      sheets.push({\n        id: `sheet_${Math.floor(i / codesPerSheet) + 1}`,\n        codes: sheetCodes,\n        paperSize: options.paperSize\n      })\n    }\n    \n    return sheets\n  }\n\n  // Generate CSS for print styles\n  generatePrintCSS(options: QRPrintOptions): string {\n    const qrSize = this.getQRSize(options.size)\n    const margin = 10 // 10px margin\n    \n    return `\n      @media print {\n        @page {\n          size: ${options.paperSize};\n          margin: 0.5in;\n        }\n        \n        .qr-sheet {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(${qrSize + margin * 2}px, 1fr));\n          gap: ${margin}px;\n          page-break-after: always;\n        }\n        \n        .qr-code-item {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          padding: ${margin}px;\n          border: 1px solid #ddd;\n          border-radius: 4px;\n        }\n        \n        .qr-code-image {\n          width: ${qrSize}px;\n          height: ${qrSize}px;\n          margin-bottom: 8px;\n        }\n        \n        .student-info {\n          font-size: 10px;\n          line-height: 1.2;\n        }\n        \n        .student-name {\n          font-weight: bold;\n          margin-bottom: 2px;\n        }\n        \n        .student-details {\n          color: #666;\n        }\n      }\n    `\n  }\n}\n\n// Types for print layout\nexport interface PrintLayout {\n  sheets: PrintSheet[]\n  totalCodes: number\n  totalSheets: number\n  options: QRPrintOptions\n}\n\nexport interface PrintSheet {\n  id: string\n  codes: Array<{\n    student: Student\n    qrData: string\n    qrId: string\n  }>\n  paperSize: 'A4' | 'Letter'\n}\n\n// QR Code management utilities\nexport class QRCodeManager {\n  private static instance: QRCodeManager\n  \n  static getInstance(): QRCodeManager {\n    if (!QRCodeManager.instance) {\n      QRCodeManager.instance = new QRCodeManager()\n    }\n    return QRCodeManager.instance\n  }\n\n  // Generate QR code for a student\n  async generateForStudent(student: Student): Promise<string> {\n    const generator = QRCodeGenerator.getInstance()\n    const qrString = generator.generateQRString(student)\n    const qrId = generator.generateQRCodeId(student)\n    \n    // In a real implementation, you would:\n    // 1. Generate the actual QR code image using a library like qrcode\n    // 2. Save it to storage (file system, cloud storage, etc.)\n    // 3. Update the student record with the QR code ID\n    \n    // For now, we'll simulate this\n    await new Promise(resolve => setTimeout(resolve, 100))\n    \n    return qrId\n  }\n\n  // Generate QR codes for multiple students\n  async generateBatch(students: Student[], onProgress?: (progress: number) => void): Promise<string[]> {\n    const qrIds: string[] = []\n    \n    for (let i = 0; i < students.length; i++) {\n      const qrId = await this.generateForStudent(students[i])\n      qrIds.push(qrId)\n      \n      if (onProgress) {\n        onProgress(((i + 1) / students.length) * 100)\n      }\n    }\n    \n    return qrIds\n  }\n\n  // Regenerate QR code for a student\n  async regenerateForStudent(student: Student): Promise<string> {\n    // Invalidate old QR code and generate new one\n    return this.generateForStudent(student)\n  }\n\n  // Validate and decode QR code\n  validateAndDecode(qrString: string): { valid: boolean; studentId?: string; error?: string } {\n    const generator = QRCodeGenerator.getInstance()\n    const result = generator.validateQRData(qrString)\n    \n    if (result.valid && result.data) {\n      return {\n        valid: true,\n        studentId: result.data.id\n      }\n    }\n    \n    return {\n      valid: false,\n      error: result.error\n    }\n  }\n}\n\n// Export singleton instances\nexport const qrGenerator = QRCodeGenerator.getInstance()\nexport const qrPrinter = QRCodePrinter.getInstance()\nexport const qrManager = QRCodeManager.getInstance()\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM;IACX,OAAe,SAAyB;IAExC,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,gBAAgB,QAAQ,GAAG,IAAI;QACjC;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA,sCAAsC;IACtC,eAAe,OAAgB,EAAc;QAC3C,OAAO;YACL,WAAW,QAAQ,EAAE;YACrB,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;YAClB,OAAO,QAAQ,KAAK;YACpB,SAAS,QAAQ,OAAO;YACxB,YAAY,IAAI,CAAC,iBAAiB;QACpC;IACF;IAEA,6DAA6D;IAC7D,iBAAiB,OAAgB,EAAU;QACzC,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC;QACjC,OAAO,KAAK,SAAS,CAAC;YACpB,IAAI,KAAK,SAAS;YAClB,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,OAAO;YACrB,QAAQ;YACR,MAAM;YACN,YAAY,KAAK,UAAU;YAC3B,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,uCAAuC;IACvC,iBAAiB,OAAgB,EAAU;QACzC,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,OAAO,IAAI,OAAO,WAAW;QACnC,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,UAAU,QAAQ,GAAG,KAAK,CAAC,CAAC,IAAI;IACrE;IAEA,sCAAsC;IAC9B,oBAA4B;QAClC,MAAM,OAAO,IAAI;QACjB,KAAK,WAAW,CAAC,KAAK,WAAW,KAAK;QACtC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC;IAEA,wBAAwB;IACxB,eAAe,QAAgB,EAAkD;QAC/E,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YAExB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;gBACxC,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAA0B;YAC1D;YAEA,IAAI,KAAK,IAAI,KAAK,cAAc;gBAC9B,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAAuB;YACvD;YAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;gBAC7D,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAAsB;YACtD;YAEA,OAAO;gBAAE,OAAO;gBAAM;YAAK;QAC7B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAyB;QACzD;IACF;AACF;AAWO,MAAM;IACX,OAAe,SAAuB;IAEtC,OAAO,cAA6B;QAClC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAC3B,cAAc,QAAQ,GAAG,IAAI;QAC/B;QACA,OAAO,cAAc,QAAQ;IAC/B;IAEA,6BAA6B;IAC7B,UAAU,IAAkC,EAAU;QACpD,OAAQ;YACN,KAAK;gBAAS,OAAO,GAAI,mBAAmB;;YAC5C,KAAK;gBAAU,OAAO,IAAI,uBAAuB;;YACjD,KAAK;gBAAS,OAAO,IAAK,qBAAqB;;YAC/C;gBAAS,OAAO;QAClB;IACF;IAEA,8CAA8C;IAC9C,oBAAoB,QAAmB,EAAE,OAAuB,EAAe;QAC7E,MAAM,cAAc,gBAAgB,WAAW;QAC/C,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI;QAE1C,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBACvC;gBACA,QAAQ,YAAY,gBAAgB,CAAC;gBACrC,MAAM,YAAY,gBAAgB,CAAC;YACrC,CAAC;QAED,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,SAAS;QAE3C,OAAO;YACL;YACA,YAAY,QAAQ,MAAM;YAC1B,aAAa,OAAO,MAAM;YAC1B;QACF;IACF;IAEA,4BAA4B;IACpB,cAAc,OAAc,EAAE,OAAuB,EAAgB;QAC3E,MAAM,SAAuB,EAAE;QAC/B,MAAM,gBAAgB,QAAQ,aAAa;QAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,cAAe;YACtD,MAAM,aAAa,QAAQ,KAAK,CAAC,GAAG,IAAI;YACxC,OAAO,IAAI,CAAC;gBACV,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,IAAI,iBAAiB,GAAG;gBAChD,OAAO;gBACP,WAAW,QAAQ,SAAS;YAC9B;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,iBAAiB,OAAuB,EAAU;QAChD,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI;QAC1C,MAAM,SAAS,GAAG,cAAc;;QAEhC,OAAO,CAAC;;;gBAGI,EAAE,QAAQ,SAAS,CAAC;;;;;;yDAMqB,EAAE,SAAS,SAAS,EAAE;eAChE,EAAE,OAAO;;;;;;;;;mBASL,EAAE,OAAO;;;;;;iBAMX,EAAE,OAAO;kBACR,EAAE,OAAO;;;;;;;;;;;;;;;;;;IAkBvB,CAAC;IACH;AACF;AAqBO,MAAM;IACX,OAAe,SAAuB;IAEtC,OAAO,cAA6B;QAClC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAC3B,cAAc,QAAQ,GAAG,IAAI;QAC/B;QACA,OAAO,cAAc,QAAQ;IAC/B;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,OAAgB,EAAmB;QAC1D,MAAM,YAAY,gBAAgB,WAAW;QAC7C,MAAM,WAAW,UAAU,gBAAgB,CAAC;QAC5C,MAAM,OAAO,UAAU,gBAAgB,CAAC;QAExC,uCAAuC;QACvC,mEAAmE;QACnE,2DAA2D;QAC3D,mDAAmD;QAEnD,+BAA+B;QAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,cAAc,QAAmB,EAAE,UAAuC,EAAqB;QACnG,MAAM,QAAkB,EAAE;QAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,MAAM,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACtD,MAAM,IAAI,CAAC;YAEX,IAAI,YAAY;gBACd,WAAW,AAAC,CAAC,IAAI,CAAC,IAAI,SAAS,MAAM,GAAI;YAC3C;QACF;QAEA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,OAAgB,EAAmB;QAC5D,8CAA8C;QAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC;IAEA,8BAA8B;IAC9B,kBAAkB,QAAgB,EAA0D;QAC1F,MAAM,YAAY,gBAAgB,WAAW;QAC7C,MAAM,SAAS,UAAU,cAAc,CAAC;QAExC,IAAI,OAAO,KAAK,IAAI,OAAO,IAAI,EAAE;YAC/B,OAAO;gBACL,OAAO;gBACP,WAAW,OAAO,IAAI,CAAC,EAAE;YAC3B;QACF;QAEA,OAAO;YACL,OAAO;YACP,OAAO,OAAO,KAAK;QACrB;IACF;AACF;AAGO,MAAM,cAAc,gBAAgB,WAAW;AAC/C,MAAM,YAAY,cAAc,WAAW;AAC3C,MAAM,YAAY,cAAc,WAAW", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/validations/student.ts"], "sourcesContent": ["import { z } from \"zod\"\nimport { GRADE_LEVELS, GUARDIAN_RELATIONSHIPS, GENDERS } from \"@/lib/types/student\"\n\n// Emergency contact schema\nconst emergencyContactSchema = z.object({\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\n  phone: z.string().min(10, \"Phone number must be at least 10 digits\").regex(/^[\\+]?[0-9\\s\\-\\(\\)]+$/, \"Invalid phone number format\"),\n  relationship: z.string().min(1, \"Relationship is required\"),\n  address: z.string().optional()\n})\n\n// Address schema\nconst addressSchema = z.object({\n  street: z.string().min(5, \"Street address must be at least 5 characters\"),\n  barangay: z.string().min(2, \"Barangay is required\"),\n  city: z.string().min(2, \"City is required\"),\n  province: z.string().min(2, \"Province is required\"),\n  zipCode: z.string().min(4, \"ZIP code must be at least 4 characters\").max(10, \"ZIP code must be at most 10 characters\"),\n  country: z.string().default(\"Philippines\")\n})\n\n// Guardian schema\nconst guardianSchema = z.object({\n  name: z.string().min(2, \"Guardian name must be at least 2 characters\"),\n  phone: z.string().min(10, \"Phone number must be at least 10 digits\").regex(/^[\\+]?[0-9\\s\\-\\(\\)]+$/, \"Invalid phone number format\"),\n  email: z.string().email(\"Invalid email format\").optional().or(z.literal(\"\")),\n  relationship: z.enum(GUARDIAN_RELATIONSHIPS, {\n    errorMap: () => ({ message: \"Please select a valid relationship\" })\n  }),\n  address: z.string().optional()\n})\n\n// Main student registration schema\nexport const studentRegistrationSchema = z.object({\n  // Basic Information\n  id: z.string()\n    .min(12, \"DepEd ID must be 12 digits\")\n    .max(12, \"DepEd ID must be 12 digits\")\n    .regex(/^\\d{12}$/, \"DepEd ID must contain only numbers\"),\n  firstName: z.string().min(2, \"First name must be at least 2 characters\").max(50, \"First name must be at most 50 characters\"),\n  middleName: z.string().max(50, \"Middle name must be at most 50 characters\").optional(),\n  lastName: z.string().min(2, \"Last name must be at least 2 characters\").max(50, \"Last name must be at most 50 characters\"),\n  email: z.string().email(\"Invalid email format\"),\n  dateOfBirth: z.string().optional(),\n  gender: z.enum(GENDERS).optional(),\n  \n  // Academic Information\n  course: z.string().min(2, \"Course is required\"),\n  year: z.string().min(1, \"Year level is required\"),\n  section: z.string().optional(),\n  grade: z.enum(GRADE_LEVELS, {\n    errorMap: () => ({ message: \"Please select a valid grade level\" })\n  }),\n  \n  // Guardian Information\n  guardianName: z.string().min(2, \"Guardian name must be at least 2 characters\"),\n  guardianPhone: z.string().min(10, \"Phone number must be at least 10 digits\").regex(/^[\\+]?[0-9\\s\\-\\(\\)]+$/, \"Invalid phone number format\"),\n  guardianEmail: z.string().email(\"Invalid email format\").optional().or(z.literal(\"\")),\n  guardianRelationship: z.enum(GUARDIAN_RELATIONSHIPS, {\n    errorMap: () => ({ message: \"Please select a valid relationship\" })\n  }),\n  guardianAddress: z.string().optional(),\n  \n  // Emergency Contacts\n  emergencyContacts: z.array(emergencyContactSchema).min(1, \"At least one emergency contact is required\").max(3, \"Maximum 3 emergency contacts allowed\"),\n  \n  // Address Information\n  street: z.string().min(5, \"Street address must be at least 5 characters\"),\n  barangay: z.string().min(2, \"Barangay is required\"),\n  city: z.string().min(2, \"City is required\"),\n  province: z.string().min(2, \"Province is required\"),\n  zipCode: z.string().min(4, \"ZIP code must be at least 4 characters\").max(10, \"ZIP code must be at most 10 characters\"),\n  country: z.string().default(\"Philippines\"),\n  \n  // Photo (optional for now)\n  photo: z.any().optional()\n})\n\n// Student update schema (allows partial updates)\nexport const studentUpdateSchema = studentRegistrationSchema.partial().extend({\n  id: z.string().min(12, \"DepEd ID must be 12 digits\").max(12, \"DepEd ID must be 12 digits\").regex(/^\\d{12}$/, \"DepEd ID must contain only numbers\")\n})\n\n// Quick registration schema (minimal required fields)\nexport const quickStudentSchema = z.object({\n  id: z.string().min(12, \"DepEd ID must be 12 digits\").max(12, \"DepEd ID must be 12 digits\").regex(/^\\d{12}$/, \"DepEd ID must contain only numbers\"),\n  firstName: z.string().min(2, \"First name is required\"),\n  lastName: z.string().min(2, \"Last name is required\"),\n  email: z.string().email(\"Invalid email format\"),\n  grade: z.enum(GRADE_LEVELS),\n  course: z.string().min(2, \"Course is required\"),\n  year: z.string().min(1, \"Year level is required\"),\n  guardianName: z.string().min(2, \"Guardian name is required\"),\n  guardianPhone: z.string().min(10, \"Guardian phone is required\").regex(/^[\\+]?[0-9\\s\\-\\(\\)]+$/, \"Invalid phone number format\"),\n  guardianRelationship: z.enum(GUARDIAN_RELATIONSHIPS)\n})\n\n// Bulk import schema\nexport const bulkImportSchema = z.array(studentRegistrationSchema)\n\n// Export types\nexport type StudentRegistrationFormData = z.infer<typeof studentRegistrationSchema>\nexport type StudentUpdateFormData = z.infer<typeof studentUpdateSchema>\nexport type QuickStudentFormData = z.infer<typeof quickStudentSchema>\nexport type BulkImportData = z.infer<typeof bulkImportSchema>\n\n// Validation helpers\nexport const validateDepEdId = (id: string): boolean => {\n  return /^\\d{12}$/.test(id)\n}\n\nexport const validatePhoneNumber = (phone: string): boolean => {\n  return /^[\\+]?[0-9\\s\\-\\(\\)]+$/.test(phone) && phone.replace(/\\D/g, '').length >= 10\n}\n\nexport const validateEmail = (email: string): boolean => {\n  return z.string().email().safeParse(email).success\n}\n\n// Default form values\nexport const getDefaultFormValues = (): Partial<StudentRegistrationFormData> => ({\n  country: \"Philippines\",\n  province: \"Batangas\",\n  city: \"Tanauan City\",\n  emergencyContacts: [\n    {\n      name: \"\",\n      phone: \"\",\n      relationship: \"\",\n      address: \"\"\n    }\n  ]\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,2CAA2C,KAAK,CAAC,yBAAyB;IACpG,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0CAA0C,GAAG,CAAC,IAAI;IAC7E,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAC9B;AAEA,kBAAkB;AAClB,MAAM,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,2CAA2C,KAAK,CAAC,yBAAyB;IACpG,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,wBAAwB,QAAQ,GAAG,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACxE,cAAc,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,yBAAsB,EAAE;QAC3C,UAAU,IAAM,CAAC;gBAAE,SAAS;YAAqC,CAAC;IACpE;IACA,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAGO,MAAM,4BAA4B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,oBAAoB;IACpB,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GACT,GAAG,CAAC,IAAI,8BACR,GAAG,CAAC,IAAI,8BACR,KAAK,CAAC,YAAY;IACrB,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,4CAA4C,GAAG,CAAC,IAAI;IACjF,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,6CAA6C,QAAQ;IACpF,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,2CAA2C,GAAG,CAAC,IAAI;IAC/E,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,QAAQ,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,UAAO,EAAE,QAAQ;IAEhC,uBAAuB;IACvB,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,eAAY,EAAE;QAC1B,UAAU,IAAM,CAAC;gBAAE,SAAS;YAAoC,CAAC;IACnE;IAEA,uBAAuB;IACvB,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,2CAA2C,KAAK,CAAC,yBAAyB;IAC5G,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,wBAAwB,QAAQ,GAAG,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChF,sBAAsB,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,yBAAsB,EAAE;QACnD,UAAU,IAAM,CAAC;gBAAE,SAAS;YAAqC,CAAC;IACpE;IACA,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEpC,qBAAqB;IACrB,mBAAmB,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wBAAwB,GAAG,CAAC,GAAG,8CAA8C,GAAG,CAAC,GAAG;IAE/G,sBAAsB;IACtB,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0CAA0C,GAAG,CAAC,IAAI;IAC7E,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAE5B,2BAA2B;IAC3B,OAAO,6KAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AACzB;AAGO,MAAM,sBAAsB,0BAA0B,OAAO,GAAG,MAAM,CAAC;IAC5E,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,8BAA8B,GAAG,CAAC,IAAI,8BAA8B,KAAK,CAAC,YAAY;AAC/G;AAGO,MAAM,qBAAqB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,8BAA8B,GAAG,CAAC,IAAI,8BAA8B,KAAK,CAAC,YAAY;IAC7G,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,eAAY;IAC1B,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,8BAA8B,KAAK,CAAC,yBAAyB;IAC/F,sBAAsB,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uHAAA,CAAA,yBAAsB;AACrD;AAGO,MAAM,mBAAmB,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC;AASjC,MAAM,kBAAkB,CAAC;IAC9B,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,wBAAwB,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACnF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,SAAS,CAAC,OAAO,OAAO;AACpD;AAGO,MAAM,uBAAuB,IAA4C,CAAC;QAC/E,SAAS;QACT,UAAU;QACV,MAAM;QACN,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,SAAS;YACX;SACD;IACH,CAAC", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/photo-management.ts"], "sourcesContent": ["// Photo management utilities for student photos\n\nexport interface PhotoValidationResult {\n  valid: boolean\n  error?: string\n  warnings?: string[]\n}\n\nexport interface PhotoCompressionOptions {\n  maxWidth: number\n  maxHeight: number\n  quality: number\n  format: 'jpeg' | 'png' | 'webp'\n}\n\nexport interface PhotoMetadata {\n  originalName: string\n  size: number\n  type: string\n  dimensions: { width: number; height: number }\n  lastModified: number\n}\n\nexport class PhotoManager {\n  private static instance: PhotoManager\n  \n  static getInstance(): PhotoManager {\n    if (!PhotoManager.instance) {\n      PhotoManager.instance = new PhotoManager()\n    }\n    return PhotoManager.instance\n  }\n\n  // Default compression options\n  private defaultOptions: PhotoCompressionOptions = {\n    maxWidth: 800,\n    maxHeight: 800,\n    quality: 0.8,\n    format: 'jpeg'\n  }\n\n  // Validate photo file\n  validatePhoto(file: File): PhotoValidationResult {\n    const result: PhotoValidationResult = { valid: true, warnings: [] }\n\n    // Check file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.'\n      }\n    }\n\n    // Check file size (max 10MB)\n    const maxSize = 10 * 1024 * 1024 // 10MB\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: 'File size too large. Please upload an image smaller than 10MB.'\n      }\n    }\n\n    // Add warnings for large files\n    if (file.size > 5 * 1024 * 1024) { // 5MB\n      result.warnings?.push('Large file size detected. Consider compressing the image.')\n    }\n\n    // Check file name\n    if (file.name.length > 100) {\n      result.warnings?.push('File name is very long. It will be shortened during upload.')\n    }\n\n    return result\n  }\n\n  // Get image dimensions\n  async getImageDimensions(file: File): Promise<{ width: number; height: number }> {\n    return new Promise((resolve, reject) => {\n      const img = new Image()\n      const url = URL.createObjectURL(file)\n      \n      img.onload = () => {\n        URL.revokeObjectURL(url)\n        resolve({ width: img.width, height: img.height })\n      }\n      \n      img.onerror = () => {\n        URL.revokeObjectURL(url)\n        reject(new Error('Failed to load image'))\n      }\n      \n      img.src = url\n    })\n  }\n\n  // Compress image\n  async compressImage(\n    file: File, \n    options: Partial<PhotoCompressionOptions> = {}\n  ): Promise<{ file: File; metadata: PhotoMetadata }> {\n    const opts = { ...this.defaultOptions, ...options }\n    \n    return new Promise(async (resolve, reject) => {\n      try {\n        const canvas = document.createElement('canvas')\n        const ctx = canvas.getContext('2d')\n        if (!ctx) {\n          reject(new Error('Canvas context not available'))\n          return\n        }\n\n        const img = new Image()\n        const url = URL.createObjectURL(file)\n        \n        img.onload = () => {\n          URL.revokeObjectURL(url)\n          \n          // Calculate new dimensions\n          let { width, height } = img\n          const aspectRatio = width / height\n          \n          if (width > opts.maxWidth) {\n            width = opts.maxWidth\n            height = width / aspectRatio\n          }\n          \n          if (height > opts.maxHeight) {\n            height = opts.maxHeight\n            width = height * aspectRatio\n          }\n          \n          // Set canvas dimensions\n          canvas.width = width\n          canvas.height = height\n          \n          // Draw and compress image\n          ctx.drawImage(img, 0, 0, width, height)\n          \n          canvas.toBlob(\n            (blob) => {\n              if (!blob) {\n                reject(new Error('Failed to compress image'))\n                return\n              }\n              \n              // Create new file\n              const compressedFile = new File(\n                [blob], \n                this.generateFileName(file.name, opts.format),\n                { type: `image/${opts.format}` }\n              )\n              \n              const metadata: PhotoMetadata = {\n                originalName: file.name,\n                size: compressedFile.size,\n                type: compressedFile.type,\n                dimensions: { width, height },\n                lastModified: Date.now()\n              }\n              \n              resolve({ file: compressedFile, metadata })\n            },\n            `image/${opts.format}`,\n            opts.quality\n          )\n        }\n        \n        img.onerror = () => {\n          URL.revokeObjectURL(url)\n          reject(new Error('Failed to load image for compression'))\n        }\n        \n        img.src = url\n      } catch (error) {\n        reject(error)\n      }\n    })\n  }\n\n  // Generate unique filename\n  private generateFileName(originalName: string, format: string): string {\n    const timestamp = Date.now()\n    const random = Math.random().toString(36).substring(2, 8)\n    const baseName = originalName.split('.')[0].substring(0, 20)\n    return `${baseName}_${timestamp}_${random}.${format}`\n  }\n\n  // Create photo preview URL\n  createPreviewUrl(file: File): string {\n    return URL.createObjectURL(file)\n  }\n\n  // Revoke preview URL\n  revokePreviewUrl(url: string): void {\n    URL.revokeObjectURL(url)\n  }\n\n  // Process photo for upload\n  async processPhotoForUpload(\n    file: File,\n    options: Partial<PhotoCompressionOptions> = {}\n  ): Promise<{\n    processedFile: File\n    metadata: PhotoMetadata\n    previewUrl: string\n    validationResult: PhotoValidationResult\n  }> {\n    // Validate photo\n    const validationResult = this.validatePhoto(file)\n    if (!validationResult.valid) {\n      throw new Error(validationResult.error)\n    }\n\n    // Get original dimensions\n    const originalDimensions = await this.getImageDimensions(file)\n    \n    // Compress if needed\n    let processedFile = file\n    let metadata: PhotoMetadata = {\n      originalName: file.name,\n      size: file.size,\n      type: file.type,\n      dimensions: originalDimensions,\n      lastModified: file.lastModified\n    }\n\n    // Compress if image is large or if compression is requested\n    const shouldCompress = \n      file.size > 1024 * 1024 || // > 1MB\n      originalDimensions.width > 1200 || \n      originalDimensions.height > 1200 ||\n      Object.keys(options).length > 0\n\n    if (shouldCompress) {\n      const compressed = await this.compressImage(file, options)\n      processedFile = compressed.file\n      metadata = compressed.metadata\n    }\n\n    // Create preview URL\n    const previewUrl = this.createPreviewUrl(processedFile)\n\n    return {\n      processedFile,\n      metadata,\n      previewUrl,\n      validationResult\n    }\n  }\n\n  // Upload photo (mock implementation)\n  async uploadPhoto(file: File, studentId: string): Promise<string> {\n    // In a real implementation, this would upload to a storage service\n    // For now, we'll simulate the upload and return a mock URL\n    \n    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate upload delay\n    \n    const timestamp = Date.now()\n    const mockUrl = `https://storage.qrsams.edu.ph/students/${studentId}/photo_${timestamp}.jpg`\n    \n    return mockUrl\n  }\n\n  // Delete photo (mock implementation)\n  async deletePhoto(photoUrl: string): Promise<void> {\n    // In a real implementation, this would delete from storage service\n    await new Promise(resolve => setTimeout(resolve, 500))\n    console.log('Photo deleted:', photoUrl)\n  }\n\n  // Get photo info from URL\n  getPhotoInfo(url: string): { studentId?: string; timestamp?: number; filename?: string } {\n    try {\n      const urlParts = url.split('/')\n      const filename = urlParts[urlParts.length - 1]\n      const studentId = urlParts[urlParts.length - 2]\n      \n      const timestampMatch = filename.match(/_(\\d+)\\./)\n      const timestamp = timestampMatch ? parseInt(timestampMatch[1]) : undefined\n      \n      return { studentId, timestamp, filename }\n    } catch (error) {\n      return {}\n    }\n  }\n\n  // Batch process photos\n  async batchProcessPhotos(\n    files: File[],\n    options: Partial<PhotoCompressionOptions> = {}\n  ): Promise<Array<{\n    originalFile: File\n    processedFile: File\n    metadata: PhotoMetadata\n    previewUrl: string\n    validationResult: PhotoValidationResult\n    error?: string\n  }>> {\n    const results = []\n    \n    for (const file of files) {\n      try {\n        const result = await this.processPhotoForUpload(file, options)\n        results.push({\n          originalFile: file,\n          ...result\n        })\n      } catch (error) {\n        results.push({\n          originalFile: file,\n          processedFile: file,\n          metadata: {\n            originalName: file.name,\n            size: file.size,\n            type: file.type,\n            dimensions: { width: 0, height: 0 },\n            lastModified: file.lastModified\n          },\n          previewUrl: '',\n          validationResult: { valid: false, error: error instanceof Error ? error.message : 'Unknown error' },\n          error: error instanceof Error ? error.message : 'Unknown error'\n        })\n      }\n    }\n    \n    return results\n  }\n}\n\n// Export singleton instance\nexport const photoManager = PhotoManager.getInstance()\n\n// Utility functions\nexport const validatePhotoFile = (file: File): PhotoValidationResult => {\n  return photoManager.validatePhoto(file)\n}\n\nexport const compressPhoto = async (\n  file: File, \n  options?: Partial<PhotoCompressionOptions>\n): Promise<{ file: File; metadata: PhotoMetadata }> => {\n  return photoManager.compressImage(file, options)\n}\n\nexport const processPhoto = async (\n  file: File,\n  options?: Partial<PhotoCompressionOptions>\n) => {\n  return photoManager.processPhotoForUpload(file, options)\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;AAuBzC,MAAM;IACX,OAAe,SAAsB;IAErC,OAAO,cAA4B;QACjC,IAAI,CAAC,aAAa,QAAQ,EAAE;YAC1B,aAAa,QAAQ,GAAG,IAAI;QAC9B;QACA,OAAO,aAAa,QAAQ;IAC9B;IAEA,8BAA8B;IACtB,iBAA0C;QAChD,UAAU;QACV,WAAW;QACX,SAAS;QACT,QAAQ;IACV,EAAC;IAED,sBAAsB;IACtB,cAAc,IAAU,EAAyB;QAC/C,MAAM,SAAgC;YAAE,OAAO;YAAM,UAAU,EAAE;QAAC;QAElE,kBAAkB;QAClB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B,MAAM,UAAU,KAAK,OAAO,KAAK,OAAO;;QACxC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,OAAO,QAAQ,EAAE,KAAK;QACxB;QAEA,kBAAkB;QAClB,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,OAAO,QAAQ,EAAE,KAAK;QACxB;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,IAAU,EAA8C;QAC/E,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,eAAe,CAAC;gBACpB,QAAQ;oBAAE,OAAO,IAAI,KAAK;oBAAE,QAAQ,IAAI,MAAM;gBAAC;YACjD;YAEA,IAAI,OAAO,GAAG;gBACZ,IAAI,eAAe,CAAC;gBACpB,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,iBAAiB;IACjB,MAAM,cACJ,IAAU,EACV,UAA4C,CAAC,CAAC,EACI;QAClD,MAAM,OAAO;YAAE,GAAG,IAAI,CAAC,cAAc;YAAE,GAAG,OAAO;QAAC;QAElD,OAAO,IAAI,QAAQ,OAAO,SAAS;YACjC,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAC9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI,eAAe,CAAC;gBAEhC,IAAI,MAAM,GAAG;oBACX,IAAI,eAAe,CAAC;oBAEpB,2BAA2B;oBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;oBACxB,MAAM,cAAc,QAAQ;oBAE5B,IAAI,QAAQ,KAAK,QAAQ,EAAE;wBACzB,QAAQ,KAAK,QAAQ;wBACrB,SAAS,QAAQ;oBACnB;oBAEA,IAAI,SAAS,KAAK,SAAS,EAAE;wBAC3B,SAAS,KAAK,SAAS;wBACvB,QAAQ,SAAS;oBACnB;oBAEA,wBAAwB;oBACxB,OAAO,KAAK,GAAG;oBACf,OAAO,MAAM,GAAG;oBAEhB,0BAA0B;oBAC1B,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;oBAEhC,OAAO,MAAM,CACX,CAAC;wBACC,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,kBAAkB;wBAClB,MAAM,iBAAiB,IAAI,KACzB;4BAAC;yBAAK,EACN,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE,KAAK,MAAM,GAC5C;4BAAE,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE;wBAAC;wBAGjC,MAAM,WAA0B;4BAC9B,cAAc,KAAK,IAAI;4BACvB,MAAM,eAAe,IAAI;4BACzB,MAAM,eAAe,IAAI;4BACzB,YAAY;gCAAE;gCAAO;4BAAO;4BAC5B,cAAc,KAAK,GAAG;wBACxB;wBAEA,QAAQ;4BAAE,MAAM;4BAAgB;wBAAS;oBAC3C,GACA,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE,EACtB,KAAK,OAAO;gBAEhB;gBAEA,IAAI,OAAO,GAAG;oBACZ,IAAI,eAAe,CAAC;oBACpB,OAAO,IAAI,MAAM;gBACnB;gBAEA,IAAI,GAAG,GAAG;YACZ,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;IACF;IAEA,2BAA2B;IACnB,iBAAiB,YAAoB,EAAE,MAAc,EAAU;QACrE,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QACvD,MAAM,WAAW,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;QACzD,OAAO,GAAG,SAAS,CAAC,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;IACvD;IAEA,2BAA2B;IAC3B,iBAAiB,IAAU,EAAU;QACnC,OAAO,IAAI,eAAe,CAAC;IAC7B;IAEA,qBAAqB;IACrB,iBAAiB,GAAW,EAAQ;QAClC,IAAI,eAAe,CAAC;IACtB;IAEA,2BAA2B;IAC3B,MAAM,sBACJ,IAAU,EACV,UAA4C,CAAC,CAAC,EAM7C;QACD,iBAAiB;QACjB,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,iBAAiB,KAAK,EAAE;YAC3B,MAAM,IAAI,MAAM,iBAAiB,KAAK;QACxC;QAEA,0BAA0B;QAC1B,MAAM,qBAAqB,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAEzD,qBAAqB;QACrB,IAAI,gBAAgB;QACpB,IAAI,WAA0B;YAC5B,cAAc,KAAK,IAAI;YACvB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,YAAY;YACZ,cAAc,KAAK,YAAY;QACjC;QAEA,4DAA4D;QAC5D,MAAM,iBACJ,KAAK,IAAI,GAAG,OAAO,QAAQ,QAAQ;QACnC,mBAAmB,KAAK,GAAG,QAC3B,mBAAmB,MAAM,GAAG,QAC5B,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;QAEhC,IAAI,gBAAgB;YAClB,MAAM,aAAa,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM;YAClD,gBAAgB,WAAW,IAAI;YAC/B,WAAW,WAAW,QAAQ;QAChC;QAEA,qBAAqB;QACrB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAEzC,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,YAAY,IAAU,EAAE,SAAiB,EAAmB;QAChE,mEAAmE;QACnE,2DAA2D;QAE3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAO,wBAAwB;QAEhF,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAU,CAAC,uCAAuC,EAAE,UAAU,OAAO,EAAE,UAAU,IAAI,CAAC;QAE5F,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,YAAY,QAAgB,EAAiB;QACjD,mEAAmE;QACnE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,0BAA0B;IAC1B,aAAa,GAAW,EAAiE;QACvF,IAAI;YACF,MAAM,WAAW,IAAI,KAAK,CAAC;YAC3B,MAAM,WAAW,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YAC9C,MAAM,YAAY,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YAE/C,MAAM,iBAAiB,SAAS,KAAK,CAAC;YACtC,MAAM,YAAY,iBAAiB,SAAS,cAAc,CAAC,EAAE,IAAI;YAEjE,OAAO;gBAAE;gBAAW;gBAAW;YAAS;QAC1C,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IAEA,uBAAuB;IACvB,MAAM,mBACJ,KAAa,EACb,UAA4C,CAAC,CAAC,EAQ5C;QACF,MAAM,UAAU,EAAE;QAElB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM;gBACtD,QAAQ,IAAI,CAAC;oBACX,cAAc;oBACd,GAAG,MAAM;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC;oBACX,cAAc;oBACd,eAAe;oBACf,UAAU;wBACR,cAAc,KAAK,IAAI;wBACvB,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,YAAY;4BAAE,OAAO;4BAAG,QAAQ;wBAAE;wBAClC,cAAc,KAAK,YAAY;oBACjC;oBACA,YAAY;oBACZ,kBAAkB;wBAAE,OAAO;wBAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAAgB;oBAClG,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;QAEA,OAAO;IACT;AACF;AAGO,MAAM,eAAe,aAAa,WAAW;AAG7C,MAAM,oBAAoB,CAAC;IAChC,OAAO,aAAa,aAAa,CAAC;AACpC;AAEO,MAAM,gBAAgB,OAC3B,MACA;IAEA,OAAO,aAAa,aAAa,CAAC,MAAM;AAC1C;AAEO,MAAM,eAAe,OAC1B,MACA;IAEA,OAAO,aAAa,qBAAqB,CAAC,MAAM;AAClD", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/students-mock-data.ts"], "sourcesContent": ["import { Student, getFullName } from \"@/lib/types/student\"\n\n// Enhanced mock student data\nexport const mockStudentsData: Student[] = [\n  {\n    id: \"123456789001\",\n    firstName: \"<PERSON>\",\n    middleName: \"<PERSON>\",\n    lastName: \"<PERSON><PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"3rd Year\",\n    section: \"IT-3A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_123456789001_2025\",\n    dateOfBirth: \"2007-05-15\",\n    gender: \"Male\",\n    guardian: {\n      name: \"<PERSON>\",\n      phone: \"+63 ************\",\n      email: \"<EMAIL>\",\n      relationship: \"Father\",\n      address: \"123 Main St, Tanauan City\"\n    },\n    emergencyContacts: [\n      {\n        name: \"<PERSON>\",\n        phone: \"+63 ************\",\n        relationship: \"<PERSON>\",\n        address: \"123 Main St, Tanauan City\"\n      },\n      {\n        name: \"<PERSON>\",\n        phone: \"+63 ************\",\n        relationship: \"Uncle\"\n      }\n    ],\n    address: {\n      street: \"123 Main Street\",\n      barangay: \"Poblacion\",\n      city: \"Tanauan City\",\n      province: \"Batangas\",\n      zipCode: \"4232\",\n      country: \"Philippines\"\n    },\n    enrollmentDate: \"2023-08-15\",\n    lastUpdated: \"2025-01-15\",\n    attendanceStats: {\n      totalDays: 120,\n      presentDays: 110,\n      lateDays: 8,\n      absentDays: 2,\n      attendanceRate: 91.7,\n      lastAttendance: \"2025-01-15\"\n    }\n  },\n  {\n    id: \"123456789002\",\n    firstName: \"Jane\",\n    middleName: \"Marie\",\n    lastName: \"Smith\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"2nd Year\",\n    section: \"CS-2B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_123456789002_2025\",\n    dateOfBirth: \"2008-03-22\",\n    gender: \"Female\",\n    guardian: {\n      name: \"Patricia Smith\",\n      phone: \"+63 ************\",\n      email: \"<EMAIL>\",\n      relationship: \"Mother\",\n      address: \"456 Oak Ave, Tanauan City\"\n    },\n    emergencyContacts: [\n      {\n        name: \"David Smith\",\n        phone: \"+63 ************\",\n        relationship: \"Father\",\n        address: \"456 Oak Ave, Tanauan City\"\n      }\n    ],\n    address: {\n      street: \"456 Oak Avenue\",\n      barangay: \"San Jose\",\n      city: \"Tanauan City\",\n      province: \"Batangas\",\n      zipCode: \"4232\",\n      country: \"Philippines\"\n    },\n    enrollmentDate: \"2023-08-15\",\n    lastUpdated: \"2025-01-14\",\n    attendanceStats: {\n      totalDays: 120,\n      presentDays: 115,\n      lateDays: 3,\n      absentDays: 2,\n      attendanceRate: 95.8,\n      lastAttendance: \"2025-01-14\"\n    }\n  },\n  {\n    id: \"123456789003\",\n    firstName: \"Mike\",\n    lastName: \"Johnson\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"1st Year\",\n    section: \"IT-1C\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_123456789003_2025\",\n    dateOfBirth: \"2009-11-08\",\n    gender: \"Male\",\n    guardian: {\n      name: \"Linda Johnson\",\n      phone: \"+63 ************\",\n      email: \"<EMAIL>\",\n      relationship: \"Mother\",\n      address: \"789 Pine St, Tanauan City\"\n    },\n    emergencyContacts: [\n      {\n        name: \"Mark Johnson\",\n        phone: \"+63 ************\",\n        relationship: \"Father\"\n      },\n      {\n        name: \"Susan Johnson\",\n        phone: \"+63 ************\",\n        relationship: \"Grandmother\",\n        address: \"321 Elm St, Tanauan City\"\n      }\n    ],\n    address: {\n      street: \"789 Pine Street\",\n      barangay: \"Natatas\",\n      city: \"Tanauan City\",\n      province: \"Batangas\",\n      zipCode: \"4232\",\n      country: \"Philippines\"\n    },\n    enrollmentDate: \"2024-08-15\",\n    lastUpdated: \"2025-01-13\",\n    attendanceStats: {\n      totalDays: 120,\n      presentDays: 105,\n      lateDays: 12,\n      absentDays: 3,\n      attendanceRate: 87.5,\n      lastAttendance: \"2025-01-13\"\n    }\n  },\n  {\n    id: \"123456789004\",\n    firstName: \"Sarah\",\n    middleName: \"Grace\",\n    lastName: \"Wilson\",\n    email: \"<EMAIL>\",\n    course: \"Computer Science\",\n    year: \"4th Year\",\n    section: \"CS-4A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_123456789004_2025\",\n    dateOfBirth: \"2006-09-12\",\n    gender: \"Female\",\n    guardian: {\n      name: \"Thomas Wilson\",\n      phone: \"+63 ************\",\n      email: \"<EMAIL>\",\n      relationship: \"Father\",\n      address: \"654 Maple Dr, Tanauan City\"\n    },\n    emergencyContacts: [\n      {\n        name: \"Helen Wilson\",\n        phone: \"+63 ************\",\n        relationship: \"Mother\",\n        address: \"654 Maple Dr, Tanauan City\"\n      }\n    ],\n    address: {\n      street: \"654 Maple Drive\",\n      barangay: \"Sambat\",\n      city: \"Tanauan City\",\n      province: \"Batangas\",\n      zipCode: \"4232\",\n      country: \"Philippines\"\n    },\n    enrollmentDate: \"2021-08-15\",\n    lastUpdated: \"2025-01-12\",\n    attendanceStats: {\n      totalDays: 120,\n      presentDays: 118,\n      lateDays: 1,\n      absentDays: 1,\n      attendanceRate: 98.3,\n      lastAttendance: \"2025-01-12\"\n    }\n  },\n  {\n    id: \"123456789005\",\n    firstName: \"Alex\",\n    lastName: \"Rodriguez\",\n    email: \"<EMAIL>\",\n    course: \"Information Technology\",\n    year: \"2nd Year\",\n    section: \"IT-2A\",\n    grade: \"10\",\n    status: \"Inactive\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_123456789005_2025\",\n    dateOfBirth: \"2008-07-25\",\n    gender: \"Male\",\n    guardian: {\n      name: \"Maria Rodriguez\",\n      phone: \"+63 ************\",\n      email: \"<EMAIL>\",\n      relationship: \"Mother\",\n      address: \"987 Cedar Ln, Tanauan City\"\n    },\n    emergencyContacts: [\n      {\n        name: \"Carlos Rodriguez\",\n        phone: \"+63 ************\",\n        relationship: \"Father\"\n      }\n    ],\n    address: {\n      street: \"987 Cedar Lane\",\n      barangay: \"Ulango\",\n      city: \"Tanauan City\",\n      province: \"Batangas\",\n      zipCode: \"4232\",\n      country: \"Philippines\"\n    },\n    enrollmentDate: \"2023-08-15\",\n    lastUpdated: \"2024-12-15\",\n    attendanceStats: {\n      totalDays: 120,\n      presentDays: 85,\n      lateDays: 15,\n      absentDays: 20,\n      attendanceRate: 70.8,\n      lastAttendance: \"2024-12-10\"\n    }\n  }\n]\n\n// Helper functions\nexport const findStudentById = (id: string): Student | undefined => {\n  return mockStudentsData.find(student => student.id === id)\n}\n\nexport const findStudentByQRCode = (qrCode: string): Student | undefined => {\n  return mockStudentsData.find(student => student.qrCode === qrCode)\n}\n\nexport const getStudentsByGrade = (grade: string): Student[] => {\n  return mockStudentsData.filter(student => student.grade === grade)\n}\n\nexport const getStudentsBySection = (section: string): Student[] => {\n  return mockStudentsData.filter(student => student.section === section)\n}\n\nexport const getStudentsByStatus = (status: string): Student[] => {\n  return mockStudentsData.filter(student => student.status === status)\n}\n\n// Get unique values for filters\nexport const getUniqueGrades = (): string[] => {\n  return [...new Set(mockStudentsData.map(s => s.grade))].sort()\n}\n\nexport const getUniqueSections = (): string[] => {\n  return [...new Set(mockStudentsData.map(s => s.section).filter(Boolean))].sort()\n}\n\nexport const getUniqueCourses = (): string[] => {\n  return [...new Set(mockStudentsData.map(s => s.course))].sort()\n}\n\nexport const getUniqueYears = (): string[] => {\n  return [...new Set(mockStudentsData.map(s => s.year))].sort()\n}\n\n// Search function\nexport const searchStudents = (query: string): Student[] => {\n  if (!query.trim()) return mockStudentsData\n  \n  const searchTerm = query.toLowerCase()\n  return mockStudentsData.filter(student => {\n    const fullName = getFullName(student).toLowerCase()\n    return (\n      fullName.includes(searchTerm) ||\n      student.id.toLowerCase().includes(searchTerm) ||\n      student.email.toLowerCase().includes(searchTerm) ||\n      student.course.toLowerCase().includes(searchTerm) ||\n      student.section?.toLowerCase().includes(searchTerm) ||\n      student.guardian.name.toLowerCase().includes(searchTerm)\n    )\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGO,MAAM,mBAA8B;IACzC;QACE,IAAI;QACJ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;YACR,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,SAAS;QACX;QACA,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,SAAS;YACX;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;SACD;QACD,SAAS;YACP,QAAQ;YACR,UAAU;YACV,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;QAChB,aAAa;QACb,iBAAiB;YACf,WAAW;YACX,aAAa;YACb,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA;QACE,IAAI;QACJ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;YACR,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,SAAS;QACX;QACA,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,SAAS;YACX;SACD;QACD,SAAS;YACP,QAAQ;YACR,UAAU;YACV,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;QAChB,aAAa;QACb,iBAAiB;YACf,WAAW;YACX,aAAa;YACb,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA;QACE,IAAI;QACJ,WAAW;QACX,UAAU;QACV,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;YACR,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,SAAS;QACX;QACA,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,SAAS;YACX;SACD;QACD,SAAS;YACP,QAAQ;YACR,UAAU;YACV,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;QAChB,aAAa;QACb,iBAAiB;YACf,WAAW;YACX,aAAa;YACb,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA;QACE,IAAI;QACJ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;YACR,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,SAAS;QACX;QACA,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,SAAS;YACX;SACD;QACD,SAAS;YACP,QAAQ;YACR,UAAU;YACV,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;QAChB,aAAa;QACb,iBAAiB;YACf,WAAW;YACX,aAAa;YACb,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA;QACE,IAAI;QACJ,WAAW;QACX,UAAU;QACV,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;YACR,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,SAAS;QACX;QACA,mBAAmB;YACjB;gBACE,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;SACD;QACD,SAAS;YACP,QAAQ;YACR,UAAU;YACV,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;QAChB,aAAa;QACb,iBAAiB;YACf,WAAW;YACX,aAAa;YACb,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,kBAAkB,CAAC;IAC9B,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACzD;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AAC7D;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;AAC9D;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AAChE;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AAC/D;AAGO,MAAM,kBAAkB;IAC7B,OAAO;WAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;KAAG,CAAC,IAAI;AAC9D;AAEO,MAAM,oBAAoB;IAC/B,OAAO;WAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC;KAAU,CAAC,IAAI;AAChF;AAEO,MAAM,mBAAmB;IAC9B,OAAO;WAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;KAAG,CAAC,IAAI;AAC/D;AAEO,MAAM,iBAAiB;IAC5B,OAAO;WAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;KAAG,CAAC,IAAI;AAC7D;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;IAE1B,MAAM,aAAa,MAAM,WAAW;IACpC,OAAO,iBAAiB,MAAM,CAAC,CAAA;QAC7B,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,WAAW;QACjD,OACE,SAAS,QAAQ,CAAC,eAClB,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACrC,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,eACtC,QAAQ,OAAO,EAAE,cAAc,SAAS,eACxC,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEjD;AACF", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/students/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useMemo } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Plus, Users, TrendingUp, UserCheck, UserX, Upload, Download, QrCode } from \"lucide-react\"\nimport { Student, StudentFilters, StudentSortConfig, PaginationConfig, getFullName } from \"@/lib/types/student\"\nimport { StudentFiltersComponent } from \"@/components/students/student-filters\"\nimport { StudentsTable } from \"@/components/students/students-table\"\nimport { BulkActions } from \"@/components/students/bulk-actions\"\nimport { Pagination } from \"@/components/students/pagination\"\nimport { StudentRegistrationDialog } from \"@/components/students/student-registration-dialog\"\nimport { CSVImportDialog } from \"@/components/students/csv-import-dialog\"\nimport { QRBatchGenerator } from \"@/components/students/qr-batch-generator\"\nimport {\n  mockStudentsData,\n  searchStudents,\n  getUniqueGrades,\n  getUniqueSections,\n  getUniqueCourses,\n  getUniqueYears\n} from \"@/lib/data/students-mock-data\"\nimport { toast } from \"sonner\"\n\nexport default function StudentsPage() {\n  const [students, setStudents] = useState<Student[]>(mockStudentsData)\n  const [selectedStudents, setSelectedStudents] = useState<string[]>([])\n  const [filters, setFilters] = useState<StudentFilters>({})\n  const [sortConfig, setSortConfig] = useState<StudentSortConfig>({ field: 'name', direction: 'asc' })\n  const [pagination, setPagination] = useState<PaginationConfig>({ page: 1, pageSize: 20, total: 0 })\n  const [showAddDialog, setShowAddDialog] = useState(false)\n\n  // Filter and sort students\n  const filteredAndSortedStudents = useMemo(() => {\n    let filtered = students\n\n    // Apply search filter\n    if (filters.search) {\n      filtered = searchStudents(filters.search)\n    }\n\n    // Apply other filters\n    if (filters.grade?.length) {\n      filtered = filtered.filter(s => filters.grade!.includes(s.grade))\n    }\n    if (filters.section?.length) {\n      filtered = filtered.filter(s => s.section && filters.section!.includes(s.section))\n    }\n    if (filters.status?.length) {\n      filtered = filtered.filter(s => filters.status!.includes(s.status))\n    }\n    if (filters.course?.length) {\n      filtered = filtered.filter(s => filters.course!.includes(s.course))\n    }\n    if (filters.year?.length) {\n      filtered = filtered.filter(s => filters.year!.includes(s.year))\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      let aValue: any\n      let bValue: any\n\n      if (sortConfig.field === 'name') {\n        aValue = getFullName(a).toLowerCase()\n        bValue = getFullName(b).toLowerCase()\n      } else {\n        aValue = a[sortConfig.field as keyof Student]\n        bValue = b[sortConfig.field as keyof Student]\n      }\n\n      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1\n      return 0\n    })\n\n    return sorted\n  }, [students, filters, sortConfig])\n\n  // Paginate students\n  const paginatedStudents = useMemo(() => {\n    const startIndex = (pagination.page - 1) * pagination.pageSize\n    const endIndex = startIndex + pagination.pageSize\n    return filteredAndSortedStudents.slice(startIndex, endIndex)\n  }, [filteredAndSortedStudents, pagination.page, pagination.pageSize])\n\n  // Update pagination total when filtered students change\n  useEffect(() => {\n    setPagination(prev => ({\n      ...prev,\n      total: filteredAndSortedStudents.length,\n      page: 1 // Reset to first page when filters change\n    }))\n  }, [filteredAndSortedStudents.length])\n\n  // Statistics\n  const stats = useMemo(() => {\n    const total = students.length\n    const active = students.filter(s => s.status === 'Active').length\n    const inactive = students.filter(s => s.status === 'Inactive').length\n    const avgAttendance = students\n      .filter(s => s.attendanceStats)\n      .reduce((sum, s) => sum + (s.attendanceStats?.attendanceRate || 0), 0) /\n      students.filter(s => s.attendanceStats).length || 0\n\n    return { total, active, inactive, avgAttendance }\n  }, [students])\n\n  const handleStudentCreated = (student: Student) => {\n    setStudents(prev => [...prev, student])\n    toast.success(\"Student registered successfully!\")\n  }\n\n  const handleStudentUpdated = (updatedStudent: Student) => {\n    setStudents(prev => prev.map(s => s.id === updatedStudent.id ? updatedStudent : s))\n    toast.success(\"Student updated successfully!\")\n  }\n\n  const handleStudentDeleted = (studentId: string) => {\n    setStudents(prev => prev.filter(s => s.id !== studentId))\n    setSelectedStudents(prev => prev.filter(id => id !== studentId))\n    toast.success(\"Student deleted successfully!\")\n  }\n\n  const handleBulkStudentsUpdated = (updatedStudents: Student[]) => {\n    setStudents(updatedStudents)\n    setSelectedStudents([])\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col gap-4\">\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\">\n          <div>\n            <h1 className=\"text-2xl sm:text-3xl font-bold tracking-tight\">Students</h1>\n            <p className=\"text-muted-foreground\">\n              Manage student records and information\n            </p>\n          </div>\n\n          {/* Primary Action - Always Visible */}\n          <StudentRegistrationDialog\n            open={showAddDialog}\n            onOpenChange={setShowAddDialog}\n            onStudentCreated={handleStudentCreated}\n            trigger={\n              <Button className=\"w-full sm:w-auto\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Student\n              </Button>\n            }\n          />\n        </div>\n\n        {/* Secondary Actions - Mobile Responsive */}\n        <div className=\"flex flex-col sm:flex-row gap-2\">\n          <div className=\"flex flex-1 gap-2\">\n            <CSVImportDialog\n              onStudentsImported={(importedStudents) => {\n                setStudents(prev => [...prev, ...importedStudents])\n                toast.success(`Imported ${importedStudents.length} students`)\n              }}\n              trigger={\n                <Button variant=\"outline\" size=\"sm\" className=\"flex-1 sm:flex-none\">\n                  <Upload className=\"mr-2 h-4 w-4\" />\n                  <span className=\"hidden sm:inline\">Import CSV</span>\n                  <span className=\"sm:hidden\">Import</span>\n                </Button>\n              }\n            />\n\n            <QRBatchGenerator\n              students={students}\n              trigger={\n                <Button variant=\"outline\" size=\"sm\" className=\"flex-1 sm:flex-none\">\n                  <QrCode className=\"mr-2 h-4 w-4\" />\n                  <span className=\"hidden sm:inline\">Generate QR</span>\n                  <span className=\"sm:hidden\">QR</span>\n                </Button>\n              }\n            />\n\n            <Button variant=\"outline\" size=\"sm\" className=\"flex-1 sm:flex-none\">\n              <Download className=\"mr-2 h-4 w-4\" />\n              <span className=\"hidden sm:inline\">Export All</span>\n              <span className=\"sm:hidden\">Export</span>\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Total Students</CardTitle>\n            <Users className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold\">{stats.total}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              <span className=\"hidden sm:inline\">Registered in system</span>\n              <span className=\"sm:hidden\">Registered</span>\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Active Students</CardTitle>\n            <UserCheck className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-green-600\">{stats.active}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              <span className=\"hidden sm:inline\">Currently enrolled</span>\n              <span className=\"sm:hidden\">Enrolled</span>\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Inactive Students</CardTitle>\n            <UserX className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-yellow-600\">{stats.inactive}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              <span className=\"hidden sm:inline\">Not currently active</span>\n              <span className=\"sm:hidden\">Inactive</span>\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Avg. Attendance</CardTitle>\n            <TrendingUp className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold\">{stats.avgAttendance.toFixed(1)}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              <span className=\"hidden sm:inline\">Overall attendance rate</span>\n              <span className=\"sm:hidden\">Attendance</span>\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <StudentFiltersComponent\n        filters={filters}\n        onFiltersChange={setFilters}\n        availableGrades={getUniqueGrades()}\n        availableSections={getUniqueSections()}\n        availableCourses={getUniqueCourses()}\n        availableYears={getUniqueYears()}\n      />\n\n      {/* Bulk Actions */}\n      {selectedStudents.length > 0 && (\n        <BulkActions\n          selectedStudents={selectedStudents}\n          students={students}\n          onClearSelection={() => setSelectedStudents([])}\n          onStudentsUpdated={handleBulkStudentsUpdated}\n        />\n      )}\n\n      {/* Students Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <span>Student Directory</span>\n            <Badge variant=\"secondary\">\n              {filteredAndSortedStudents.length} student{filteredAndSortedStudents.length !== 1 ? 's' : ''}\n            </Badge>\n          </CardTitle>\n          <CardDescription>\n            {filters.search || Object.keys(filters).length > 1 ?\n              `Filtered results from ${students.length} total students` :\n              \"Complete list of registered students\"\n            }\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"p-0\">\n          <StudentsTable\n            students={paginatedStudents}\n            selectedStudents={selectedStudents}\n            onSelectionChange={setSelectedStudents}\n            sortConfig={sortConfig}\n            onSortChange={setSortConfig}\n            onStudentUpdated={handleStudentUpdated}\n            onStudentDeleted={handleStudentDeleted}\n          />\n\n          {filteredAndSortedStudents.length > pagination.pageSize && (\n            <>\n              <Separator />\n              <div className=\"p-4\">\n                <Pagination\n                  pagination={pagination}\n                  onPaginationChange={setPagination}\n                />\n              </div>\n            </>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAxBA;;;;;;;;;;;;;;;;;;AA0Be,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,uIAAA,CAAA,mBAAgB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAAE,OAAO;QAAQ,WAAW;IAAM;IAClG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAAE,MAAM;QAAG,UAAU;QAAI,OAAO;IAAE;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2BAA2B;IAC3B,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxC,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,QAAQ,MAAM,EAAE;YAClB,WAAW,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;QAC1C;QAEA,sBAAsB;QACtB,IAAI,QAAQ,KAAK,EAAE,QAAQ;YACzB,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,QAAQ,KAAK,CAAE,QAAQ,CAAC,EAAE,KAAK;QACjE;QACA,IAAI,QAAQ,OAAO,EAAE,QAAQ;YAC3B,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,QAAQ,OAAO,CAAE,QAAQ,CAAC,EAAE,OAAO;QAClF;QACA,IAAI,QAAQ,MAAM,EAAE,QAAQ;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,QAAQ,MAAM,CAAE,QAAQ,CAAC,EAAE,MAAM;QACnE;QACA,IAAI,QAAQ,MAAM,EAAE,QAAQ;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,QAAQ,MAAM,CAAE,QAAQ,CAAC,EAAE,MAAM;QACnE;QACA,IAAI,QAAQ,IAAI,EAAE,QAAQ;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,QAAQ,IAAI,CAAE,QAAQ,CAAC,EAAE,IAAI;QAC/D;QAEA,gBAAgB;QAChB,MAAM,SAAS;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YACpC,IAAI;YACJ,IAAI;YAEJ,IAAI,WAAW,KAAK,KAAK,QAAQ;gBAC/B,SAAS,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,GAAG,WAAW;gBACnC,SAAS,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,GAAG,WAAW;YACrC,OAAO;gBACL,SAAS,CAAC,CAAC,WAAW,KAAK,CAAkB;gBAC7C,SAAS,CAAC,CAAC,WAAW,KAAK,CAAkB;YAC/C;YAEA,IAAI,SAAS,QAAQ,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;YAClE,IAAI,SAAS,QAAQ,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;YAClE,OAAO;QACT;QAEA,OAAO;IACT,GAAG;QAAC;QAAU;QAAS;KAAW;IAElC,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,QAAQ;QAC9D,MAAM,WAAW,aAAa,WAAW,QAAQ;QACjD,OAAO,0BAA0B,KAAK,CAAC,YAAY;IACrD,GAAG;QAAC;QAA2B,WAAW,IAAI;QAAE,WAAW,QAAQ;KAAC;IAEpE,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,OAAO,0BAA0B,MAAM;gBACvC,MAAM,EAAE,0CAA0C;YACpD,CAAC;IACH,GAAG;QAAC,0BAA0B,MAAM;KAAC;IAErC,aAAa;IACb,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,QAAQ,SAAS,MAAM;QAC7B,MAAM,SAAS,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACjE,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QACrE,MAAM,gBAAgB,SACnB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAC7B,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,eAAe,EAAE,kBAAkB,CAAC,GAAG,KACpE,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM,IAAI;QAEpD,OAAO;YAAE;YAAO;YAAQ;YAAU;QAAc;IAClD,GAAG;QAAC;KAAS;IAEb,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;QACtC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;QAChF,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;QACrD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,4BAA4B,CAAC;QACjC,YAAY;QACZ,oBAAoB,EAAE;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC,4JAAA,CAAA,4BAAyB;gCACxB,MAAM;gCACN,cAAc;gCACd,kBAAkB;gCAClB,uBACE,8OAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAQzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kJAAA,CAAA,kBAAe;oCACd,oBAAoB,CAAC;wCACnB,YAAY,CAAA,OAAQ;mDAAI;mDAAS;6CAAiB;wCAClD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC;oCAC9D;oCACA,uBACE,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;8CAKlC,8OAAC,mJAAA,CAAA,mBAAgB;oCACf,UAAU;oCACV,uBACE,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;8CAKlC,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAiC;;;;;;kDACtD,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAiC,MAAM,KAAK;;;;;;kDAC3D,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAKlC,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAiC;;;;;;kDACtD,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAgD,MAAM,MAAM;;;;;;kDAC3E,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAKlC,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAiC;;;;;;kDACtD,8OAAC,wMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAiD,MAAM,QAAQ;;;;;;kDAC9E,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAKlC,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAiC;;;;;;kDACtD,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAiC,MAAM,aAAa,CAAC,OAAO,CAAC;4CAAG;;;;;;;kDAC/E,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,6IAAA,CAAA,0BAAuB;gBACtB,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;gBAC/B,mBAAmB,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD;gBACnC,kBAAkB,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;gBACjC,gBAAgB,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;;;;;;YAI9B,iBAAiB,MAAM,GAAG,mBACzB,8OAAC,0IAAA,CAAA,cAAW;gBACV,kBAAkB;gBAClB,UAAU;gBACV,kBAAkB,IAAM,oBAAoB,EAAE;gBAC9C,mBAAmB;;;;;;0BAKvB,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CACZ,0BAA0B,MAAM;4CAAC;4CAAS,0BAA0B,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;0CAG9F,8OAAC,yHAAA,CAAA,kBAAe;0CACb,QAAQ,MAAM,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IAC/C,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,eAAe,CAAC,GACzD;;;;;;;;;;;;kCAIN,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,4IAAA,CAAA,gBAAa;gCACZ,UAAU;gCACV,kBAAkB;gCAClB,mBAAmB;gCACnB,YAAY;gCACZ,cAAc;gCACd,kBAAkB;gCAClB,kBAAkB;;;;;;4BAGnB,0BAA0B,MAAM,GAAG,WAAW,QAAQ,kBACrD;;kDACE,8OAAC,8HAAA,CAAA,YAAS;;;;;kDACV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qIAAA,CAAA,aAAU;4CACT,YAAY;4CACZ,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC", "debugId": null}}]}