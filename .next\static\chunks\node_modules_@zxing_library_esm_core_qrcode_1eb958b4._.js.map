{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ArgumentException from '../../ArgumentException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ErrorCorrectionLevelValues;\n(function (ErrorCorrectionLevelValues) {\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"L\"] = 0] = \"L\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"M\"] = 1] = \"M\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"Q\"] = 2] = \"Q\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"H\"] = 3] = \"H\";\n})(ErrorCorrectionLevelValues || (ErrorCorrectionLevelValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.5.1. This enum encapsulates the four error correction levels\n * defined by the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar ErrorCorrectionLevel = /** @class */ (function () {\n    function ErrorCorrectionLevel(value, stringValue, bits /*int*/) {\n        this.value = value;\n        this.stringValue = stringValue;\n        this.bits = bits;\n        ErrorCorrectionLevel.FOR_BITS.set(bits, this);\n        ErrorCorrectionLevel.FOR_VALUE.set(value, this);\n    }\n    ErrorCorrectionLevel.prototype.getValue = function () {\n        return this.value;\n    };\n    ErrorCorrectionLevel.prototype.getBits = function () {\n        return this.bits;\n    };\n    ErrorCorrectionLevel.fromString = function (s) {\n        switch (s) {\n            case 'L': return ErrorCorrectionLevel.L;\n            case 'M': return ErrorCorrectionLevel.M;\n            case 'Q': return ErrorCorrectionLevel.Q;\n            case 'H': return ErrorCorrectionLevel.H;\n            default: throw new ArgumentException(s + 'not available');\n        }\n    };\n    ErrorCorrectionLevel.prototype.toString = function () {\n        return this.stringValue;\n    };\n    ErrorCorrectionLevel.prototype.equals = function (o) {\n        if (!(o instanceof ErrorCorrectionLevel)) {\n            return false;\n        }\n        var other = o;\n        return this.value === other.value;\n    };\n    /**\n     * @param bits int containing the two bits encoding a QR Code's error correction level\n     * @return ErrorCorrectionLevel representing the encoded error correction level\n     */\n    ErrorCorrectionLevel.forBits = function (bits /*int*/) {\n        if (bits < 0 || bits >= ErrorCorrectionLevel.FOR_BITS.size) {\n            throw new IllegalArgumentException();\n        }\n        return ErrorCorrectionLevel.FOR_BITS.get(bits);\n    };\n    ErrorCorrectionLevel.FOR_BITS = new Map();\n    ErrorCorrectionLevel.FOR_VALUE = new Map();\n    /** L = ~7% correction */\n    ErrorCorrectionLevel.L = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.L, 'L', 0x01);\n    /** M = ~15% correction */\n    ErrorCorrectionLevel.M = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.M, 'M', 0x00);\n    /** Q = ~25% correction */\n    ErrorCorrectionLevel.Q = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.Q, 'Q', 0x03);\n    /** H = ~30% correction */\n    ErrorCorrectionLevel.H = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.H, 'H', 0x02);\n    return ErrorCorrectionLevel;\n}());\nexport default ErrorCorrectionLevel;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,6CAA6C;;;;AAC7C;AACA;;;AACO,IAAI;AACX,CAAC,SAAU,0BAA0B;IACjC,0BAA0B,CAAC,0BAA0B,CAAC,IAAI,GAAG,EAAE,GAAG;IAClE,0BAA0B,CAAC,0BAA0B,CAAC,IAAI,GAAG,EAAE,GAAG;IAClE,0BAA0B,CAAC,0BAA0B,CAAC,IAAI,GAAG,EAAE,GAAG;IAClE,0BAA0B,CAAC,0BAA0B,CAAC,IAAI,GAAG,EAAE,GAAG;AACtE,CAAC,EAAE,8BAA8B,CAAC,6BAA6B,CAAC,CAAC;AACjE;;;;;CAKC,GACD,IAAI,uBAAsC;IACtC,SAAS,qBAAqB,KAAK,EAAE,WAAW,EAAE,KAAK,KAAK,GAAN;QAClD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,qBAAqB,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI;QAC5C,qBAAqB,SAAS,CAAC,GAAG,CAAC,OAAO,IAAI;IAClD;IACA,qBAAqB,SAAS,CAAC,QAAQ,GAAG;QACtC,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,qBAAqB,SAAS,CAAC,OAAO,GAAG;QACrC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,qBAAqB,UAAU,GAAG,SAAU,CAAC;QACzC,OAAQ;YACJ,KAAK;gBAAK,OAAO,qBAAqB,CAAC;YACvC,KAAK;gBAAK,OAAO,qBAAqB,CAAC;YACvC,KAAK;gBAAK,OAAO,qBAAqB,CAAC;YACvC,KAAK;gBAAK,OAAO,qBAAqB,CAAC;YACvC;gBAAS,MAAM,IAAI,yKAAA,CAAA,UAAiB,CAAC,IAAI;QAC7C;IACJ;IACA,qBAAqB,SAAS,CAAC,QAAQ,GAAG;QACtC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,qBAAqB,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QAC/C,IAAI,CAAC,CAAC,aAAa,oBAAoB,GAAG;YACtC,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK;IACrC;IACA;;;KAGC,GACD,qBAAqB,OAAO,GAAG,SAAU,KAAK,KAAK,GAAN;QACzC,IAAI,OAAO,KAAK,QAAQ,qBAAqB,QAAQ,CAAC,IAAI,EAAE;YACxD,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,OAAO,qBAAqB,QAAQ,CAAC,GAAG,CAAC;IAC7C;IACA,qBAAqB,QAAQ,GAAG,IAAI;IACpC,qBAAqB,SAAS,GAAG,IAAI;IACrC,uBAAuB,GACvB,qBAAqB,CAAC,GAAG,IAAI,qBAAqB,2BAA2B,CAAC,EAAE,KAAK;IACrF,wBAAwB,GACxB,qBAAqB,CAAC,GAAG,IAAI,qBAAqB,2BAA2B,CAAC,EAAE,KAAK;IACrF,wBAAwB,GACxB,qBAAqB,CAAC,GAAG,IAAI,qBAAqB,2BAA2B,CAAC,EAAE,KAAK;IACrF,wBAAwB,GACxB,qBAAqB,CAAC,GAAG,IAAI,qBAAqB,2BAA2B,CAAC,EAAE,KAAK;IACrF,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/FormatInformation.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ErrorCorrectionLevel from './ErrorCorrectionLevel';\nimport Integer from '../../util/Integer';\n/**\n * <p>Encapsulates a QR Code's format information, including the data mask used and\n * error correction level.</p>\n *\n * <AUTHOR> Owen\n * @see DataMask\n * @see ErrorCorrectionLevel\n */\nvar FormatInformation = /** @class */ (function () {\n    function FormatInformation(formatInfo /*int*/) {\n        // Bits 3,4\n        this.errorCorrectionLevel = ErrorCorrectionLevel.forBits((formatInfo >> 3) & 0x03);\n        // Bottom 3 bits\n        this.dataMask = /*(byte) */ (formatInfo & 0x07);\n    }\n    FormatInformation.numBitsDiffering = function (a /*int*/, b /*int*/) {\n        return Integer.bitCount(a ^ b);\n    };\n    /**\n     * @param maskedFormatInfo1 format info indicator, with mask still applied\n     * @param maskedFormatInfo2 second copy of same info; both are checked at the same time\n     *  to establish best match\n     * @return information about the format it specifies, or {@code null}\n     *  if doesn't seem to match any known pattern\n     */\n    FormatInformation.decodeFormatInformation = function (maskedFormatInfo1 /*int*/, maskedFormatInfo2 /*int*/) {\n        var formatInfo = FormatInformation.doDecodeFormatInformation(maskedFormatInfo1, maskedFormatInfo2);\n        if (formatInfo !== null) {\n            return formatInfo;\n        }\n        // Should return null, but, some QR codes apparently\n        // do not mask this info. Try again by actually masking the pattern\n        // first\n        return FormatInformation.doDecodeFormatInformation(maskedFormatInfo1 ^ FormatInformation.FORMAT_INFO_MASK_QR, maskedFormatInfo2 ^ FormatInformation.FORMAT_INFO_MASK_QR);\n    };\n    FormatInformation.doDecodeFormatInformation = function (maskedFormatInfo1 /*int*/, maskedFormatInfo2 /*int*/) {\n        var e_1, _a;\n        // Find the int in FORMAT_INFO_DECODE_LOOKUP with fewest bits differing\n        var bestDifference = Number.MAX_SAFE_INTEGER;\n        var bestFormatInfo = 0;\n        try {\n            for (var _b = __values(FormatInformation.FORMAT_INFO_DECODE_LOOKUP), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var decodeInfo = _c.value;\n                var targetInfo = decodeInfo[0];\n                if (targetInfo === maskedFormatInfo1 || targetInfo === maskedFormatInfo2) {\n                    // Found an exact match\n                    return new FormatInformation(decodeInfo[1]);\n                }\n                var bitsDifference = FormatInformation.numBitsDiffering(maskedFormatInfo1, targetInfo);\n                if (bitsDifference < bestDifference) {\n                    bestFormatInfo = decodeInfo[1];\n                    bestDifference = bitsDifference;\n                }\n                if (maskedFormatInfo1 !== maskedFormatInfo2) {\n                    // also try the other option\n                    bitsDifference = FormatInformation.numBitsDiffering(maskedFormatInfo2, targetInfo);\n                    if (bitsDifference < bestDifference) {\n                        bestFormatInfo = decodeInfo[1];\n                        bestDifference = bitsDifference;\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits\n        // differing means we found a match\n        if (bestDifference <= 3) {\n            return new FormatInformation(bestFormatInfo);\n        }\n        return null;\n    };\n    FormatInformation.prototype.getErrorCorrectionLevel = function () {\n        return this.errorCorrectionLevel;\n    };\n    FormatInformation.prototype.getDataMask = function () {\n        return this.dataMask;\n    };\n    /*@Override*/\n    FormatInformation.prototype.hashCode = function () {\n        return (this.errorCorrectionLevel.getBits() << 3) | this.dataMask;\n    };\n    /*@Override*/\n    FormatInformation.prototype.equals = function (o) {\n        if (!(o instanceof FormatInformation)) {\n            return false;\n        }\n        var other = o;\n        return this.errorCorrectionLevel === other.errorCorrectionLevel &&\n            this.dataMask === other.dataMask;\n    };\n    FormatInformation.FORMAT_INFO_MASK_QR = 0x5412;\n    /**\n     * See ISO 18004:2006, Annex C, Table C.1\n     */\n    FormatInformation.FORMAT_INFO_DECODE_LOOKUP = [\n        Int32Array.from([0x5412, 0x00]),\n        Int32Array.from([0x5125, 0x01]),\n        Int32Array.from([0x5E7C, 0x02]),\n        Int32Array.from([0x5B4B, 0x03]),\n        Int32Array.from([0x45F9, 0x04]),\n        Int32Array.from([0x40CE, 0x05]),\n        Int32Array.from([0x4F97, 0x06]),\n        Int32Array.from([0x4AA0, 0x07]),\n        Int32Array.from([0x77C4, 0x08]),\n        Int32Array.from([0x72F3, 0x09]),\n        Int32Array.from([0x7DAA, 0x0A]),\n        Int32Array.from([0x789D, 0x0B]),\n        Int32Array.from([0x662F, 0x0C]),\n        Int32Array.from([0x6318, 0x0D]),\n        Int32Array.from([0x6C41, 0x0E]),\n        Int32Array.from([0x6976, 0x0F]),\n        Int32Array.from([0x1689, 0x10]),\n        Int32Array.from([0x13BE, 0x11]),\n        Int32Array.from([0x1CE7, 0x12]),\n        Int32Array.from([0x19D0, 0x13]),\n        Int32Array.from([0x0762, 0x14]),\n        Int32Array.from([0x0255, 0x15]),\n        Int32Array.from([0x0D0C, 0x16]),\n        Int32Array.from([0x083B, 0x17]),\n        Int32Array.from([0x355F, 0x18]),\n        Int32Array.from([0x3068, 0x19]),\n        Int32Array.from([0x3F31, 0x1A]),\n        Int32Array.from([0x3A06, 0x1B]),\n        Int32Array.from([0x24B4, 0x1C]),\n        Int32Array.from([0x2183, 0x1D]),\n        Int32Array.from([0x2EDA, 0x1E]),\n        Int32Array.from([0x2BED, 0x1F]),\n    ];\n    return FormatInformation;\n}());\nexport default FormatInformation;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,6CAA6C,GAC7C;AACA;AAbA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAIA;;;;;;;CAOC,GACD,IAAI,oBAAmC;IACnC,SAAS,kBAAkB,WAAW,KAAK,GAAN;QACjC,WAAW;QACX,IAAI,CAAC,oBAAoB,GAAG,iMAAA,CAAA,UAAoB,CAAC,OAAO,CAAC,AAAC,cAAc,IAAK;QAC7E,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAgB,aAAa;IAC9C;IACA,kBAAkB,gBAAgB,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACvD,OAAO,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI;IAChC;IACA;;;;;;KAMC,GACD,kBAAkB,uBAAuB,GAAG,SAAU,kBAAkB,KAAK,GAAN,EAAU,kBAAkB,KAAK,GAAN;QAC9F,IAAI,aAAa,kBAAkB,yBAAyB,CAAC,mBAAmB;QAChF,IAAI,eAAe,MAAM;YACrB,OAAO;QACX;QACA,oDAAoD;QACpD,mEAAmE;QACnE,QAAQ;QACR,OAAO,kBAAkB,yBAAyB,CAAC,oBAAoB,kBAAkB,mBAAmB,EAAE,oBAAoB,kBAAkB,mBAAmB;IAC3K;IACA,kBAAkB,yBAAyB,GAAG,SAAU,kBAAkB,KAAK,GAAN,EAAU,kBAAkB,KAAK,GAAN;QAChG,IAAI,KAAK;QACT,uEAAuE;QACvE,IAAI,iBAAiB,OAAO,gBAAgB;QAC5C,IAAI,iBAAiB;QACrB,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,kBAAkB,yBAAyB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC3G,IAAI,aAAa,GAAG,KAAK;gBACzB,IAAI,aAAa,UAAU,CAAC,EAAE;gBAC9B,IAAI,eAAe,qBAAqB,eAAe,mBAAmB;oBACtE,uBAAuB;oBACvB,OAAO,IAAI,kBAAkB,UAAU,CAAC,EAAE;gBAC9C;gBACA,IAAI,iBAAiB,kBAAkB,gBAAgB,CAAC,mBAAmB;gBAC3E,IAAI,iBAAiB,gBAAgB;oBACjC,iBAAiB,UAAU,CAAC,EAAE;oBAC9B,iBAAiB;gBACrB;gBACA,IAAI,sBAAsB,mBAAmB;oBACzC,4BAA4B;oBAC5B,iBAAiB,kBAAkB,gBAAgB,CAAC,mBAAmB;oBACvE,IAAI,iBAAiB,gBAAgB;wBACjC,iBAAiB,UAAU,CAAC,EAAE;wBAC9B,iBAAiB;oBACrB;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,8EAA8E;QAC9E,mCAAmC;QACnC,IAAI,kBAAkB,GAAG;YACrB,OAAO,IAAI,kBAAkB;QACjC;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,uBAAuB,GAAG;QAClD,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,kBAAkB,SAAS,CAAC,WAAW,GAAG;QACtC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,WAAW,GACX,kBAAkB,SAAS,CAAC,QAAQ,GAAG;QACnC,OAAO,AAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,MAAM,IAAK,IAAI,CAAC,QAAQ;IACrE;IACA,WAAW,GACX,kBAAkB,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QAC5C,IAAI,CAAC,CAAC,aAAa,iBAAiB,GAAG;YACnC,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,oBAAoB,KAAK,MAAM,oBAAoB,IAC3D,IAAI,CAAC,QAAQ,KAAK,MAAM,QAAQ;IACxC;IACA,kBAAkB,mBAAmB,GAAG;IACxC;;KAEC,GACD,kBAAkB,yBAAyB,GAAG;QAC1C,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAQ;SAAK;KACjC;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/ECBlocks.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */ (function () {\n    function ECBlocks(ecCodewordsPerBlock /*int*/) {\n        var ecBlocks = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            ecBlocks[_i - 1] = arguments[_i];\n        }\n        this.ecCodewordsPerBlock = ecCodewordsPerBlock;\n        this.ecBlocks = ecBlocks;\n    }\n    ECBlocks.prototype.getECCodewordsPerBlock = function () {\n        return this.ecCodewordsPerBlock;\n    };\n    ECBlocks.prototype.getNumBlocks = function () {\n        var e_1, _a;\n        var total = 0;\n        var ecBlocks = this.ecBlocks;\n        try {\n            for (var ecBlocks_1 = __values(ecBlocks), ecBlocks_1_1 = ecBlocks_1.next(); !ecBlocks_1_1.done; ecBlocks_1_1 = ecBlocks_1.next()) {\n                var ecBlock = ecBlocks_1_1.value;\n                total += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlocks_1_1 && !ecBlocks_1_1.done && (_a = ecBlocks_1.return)) _a.call(ecBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return total;\n    };\n    ECBlocks.prototype.getTotalECCodewords = function () {\n        return this.ecCodewordsPerBlock * this.getNumBlocks();\n    };\n    ECBlocks.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    return ECBlocks;\n}());\nexport default ECBlocks;\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;AACA;;;;;CAKC,GACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,oBAAoB,KAAK,GAAN;QACjC,IAAI,WAAW,EAAE;QACjB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,QAAQ,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QACpC;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,SAAS,SAAS,CAAC,sBAAsB,GAAG;QACxC,OAAO,IAAI,CAAC,mBAAmB;IACnC;IACA,SAAS,SAAS,CAAC,YAAY,GAAG;QAC9B,IAAI,KAAK;QACT,IAAI,QAAQ;QACZ,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI;YACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;gBAC9H,IAAI,UAAU,aAAa,KAAK;gBAChC,SAAS,QAAQ,QAAQ;YAC7B;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;YAChF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,SAAS,SAAS,CAAC,mBAAmB,GAAG;QACrC,OAAO,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY;IACvD;IACA,SAAS,SAAS,CAAC,WAAW,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/ECB.js"], "sourcesContent": ["/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the QR code version's format.</p>\n */\nvar ECB = /** @class */ (function () {\n    function ECB(count /*int*/, dataCodewords /*int*/) {\n        this.count = count;\n        this.dataCodewords = dataCodewords;\n    }\n    ECB.prototype.getCount = function () {\n        return this.count;\n    };\n    ECB.prototype.getDataCodewords = function () {\n        return this.dataCodewords;\n    };\n    return ECB;\n}());\nexport default ECB;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,IAAI,MAAqB;IACrB,SAAS,IAAI,MAAM,KAAK,GAAN,EAAU,cAAc,KAAK,GAAN;QACrC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,IAAI,SAAS,CAAC,QAAQ,GAAG;QACrB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,IAAI,SAAS,CAAC,gBAAgB,GAAG;QAC7B,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/Version.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitMatrix from '../../common/BitMatrix';\nimport FormatInformation from './FormatInformation';\nimport ECBlocks from './ECBlocks';\nimport ECB from './ECB';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * See ISO 18004:2006 Annex D\n *\n * <AUTHOR> Owen\n */\nvar Version = /** @class */ (function () {\n    function Version(versionNumber /*int*/, alignmentPatternCenters) {\n        var e_1, _a;\n        var ecBlocks = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            ecBlocks[_i - 2] = arguments[_i];\n        }\n        this.versionNumber = versionNumber;\n        this.alignmentPatternCenters = alignmentPatternCenters;\n        this.ecBlocks = ecBlocks;\n        var total = 0;\n        var ecCodewords = ecBlocks[0].getECCodewordsPerBlock();\n        var ecbArray = ecBlocks[0].getECBlocks();\n        try {\n            for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n                var ecBlock = ecbArray_1_1.value;\n                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.totalCodewords = total;\n    }\n    Version.prototype.getVersionNumber = function () {\n        return this.versionNumber;\n    };\n    Version.prototype.getAlignmentPatternCenters = function () {\n        return this.alignmentPatternCenters;\n    };\n    Version.prototype.getTotalCodewords = function () {\n        return this.totalCodewords;\n    };\n    Version.prototype.getDimensionForVersion = function () {\n        return 17 + 4 * this.versionNumber;\n    };\n    Version.prototype.getECBlocksForLevel = function (ecLevel) {\n        return this.ecBlocks[ecLevel.getValue()];\n        // TYPESCRIPTPORT: original was using ordinal, and using the order of levels as defined in ErrorCorrectionLevel enum (LMQH)\n        // I will use the direct value from ErrorCorrectionLevelValues enum which in typescript goes to a number\n    };\n    /**\n     * <p>Deduces version information purely from QR Code dimensions.</p>\n     *\n     * @param dimension dimension in modules\n     * @return Version for a QR Code of that dimension\n     * @throws FormatException if dimension is not 1 mod 4\n     */\n    Version.getProvisionalVersionForDimension = function (dimension /*int*/) {\n        if (dimension % 4 !== 1) {\n            throw new FormatException();\n        }\n        try {\n            return this.getVersionForNumber((dimension - 17) / 4);\n        }\n        catch (ignored /*: IllegalArgumentException*/) {\n            throw new FormatException();\n        }\n    };\n    Version.getVersionForNumber = function (versionNumber /*int*/) {\n        if (versionNumber < 1 || versionNumber > 40) {\n            throw new IllegalArgumentException();\n        }\n        return Version.VERSIONS[versionNumber - 1];\n    };\n    Version.decodeVersionInformation = function (versionBits /*int*/) {\n        var bestDifference = Number.MAX_SAFE_INTEGER;\n        var bestVersion = 0;\n        for (var i = 0; i < Version.VERSION_DECODE_INFO.length; i++) {\n            var targetVersion = Version.VERSION_DECODE_INFO[i];\n            // Do the version info bits match exactly? done.\n            if (targetVersion === versionBits) {\n                return Version.getVersionForNumber(i + 7);\n            }\n            // Otherwise see if this is the closest to a real version info bit string\n            // we have seen so far\n            var bitsDifference = FormatInformation.numBitsDiffering(versionBits, targetVersion);\n            if (bitsDifference < bestDifference) {\n                bestVersion = i + 7;\n                bestDifference = bitsDifference;\n            }\n        }\n        // We can tolerate up to 3 bits of error since no two version info codewords will\n        // differ in less than 8 bits.\n        if (bestDifference <= 3) {\n            return Version.getVersionForNumber(bestVersion);\n        }\n        // If we didn't find a close enough match, fail\n        return null;\n    };\n    /**\n     * See ISO 18004:2006 Annex E\n     */\n    Version.prototype.buildFunctionPattern = function () {\n        var dimension = this.getDimensionForVersion();\n        var bitMatrix = new BitMatrix(dimension);\n        // Top left finder pattern + separator + format\n        bitMatrix.setRegion(0, 0, 9, 9);\n        // Top right finder pattern + separator + format\n        bitMatrix.setRegion(dimension - 8, 0, 8, 9);\n        // Bottom left finder pattern + separator + format\n        bitMatrix.setRegion(0, dimension - 8, 9, 8);\n        // Alignment patterns\n        var max = this.alignmentPatternCenters.length;\n        for (var x = 0; x < max; x++) {\n            var i = this.alignmentPatternCenters[x] - 2;\n            for (var y = 0; y < max; y++) {\n                if ((x === 0 && (y === 0 || y === max - 1)) || (x === max - 1 && y === 0)) {\n                    // No alignment patterns near the three finder patterns\n                    continue;\n                }\n                bitMatrix.setRegion(this.alignmentPatternCenters[y] - 2, i, 5, 5);\n            }\n        }\n        // Vertical timing pattern\n        bitMatrix.setRegion(6, 9, 1, dimension - 17);\n        // Horizontal timing pattern\n        bitMatrix.setRegion(9, 6, dimension - 17, 1);\n        if (this.versionNumber > 6) {\n            // Version info, top right\n            bitMatrix.setRegion(dimension - 11, 0, 3, 6);\n            // Version info, bottom left\n            bitMatrix.setRegion(0, dimension - 11, 6, 3);\n        }\n        return bitMatrix;\n    };\n    /*@Override*/\n    Version.prototype.toString = function () {\n        return '' + this.versionNumber;\n    };\n    /**\n       * See ISO 18004:2006 Annex D.\n       * Element i represents the raw version bits that specify version i + 7\n       */\n    Version.VERSION_DECODE_INFO = Int32Array.from([\n        0x07C94, 0x085BC, 0x09A99, 0x0A4D3, 0x0BBF6,\n        0x0C762, 0x0D847, 0x0E60D, 0x0F928, 0x10B78,\n        0x1145D, 0x12A17, 0x13532, 0x149A6, 0x15683,\n        0x168C9, 0x177EC, 0x18EC4, 0x191E1, 0x1AFAB,\n        0x1B08E, 0x1CC1A, 0x1D33F, 0x1ED75, 0x1F250,\n        0x209D5, 0x216F0, 0x228BA, 0x2379F, 0x24B0B,\n        0x2542E, 0x26A64, 0x27541, 0x28C69\n    ]);\n    /**\n       * See ISO 18004:2006 6.5.1 Table 9\n       */\n    Version.VERSIONS = [\n        new Version(1, new Int32Array(0), new ECBlocks(7, new ECB(1, 19)), new ECBlocks(10, new ECB(1, 16)), new ECBlocks(13, new ECB(1, 13)), new ECBlocks(17, new ECB(1, 9))),\n        new Version(2, Int32Array.from([6, 18]), new ECBlocks(10, new ECB(1, 34)), new ECBlocks(16, new ECB(1, 28)), new ECBlocks(22, new ECB(1, 22)), new ECBlocks(28, new ECB(1, 16))),\n        new Version(3, Int32Array.from([6, 22]), new ECBlocks(15, new ECB(1, 55)), new ECBlocks(26, new ECB(1, 44)), new ECBlocks(18, new ECB(2, 17)), new ECBlocks(22, new ECB(2, 13))),\n        new Version(4, Int32Array.from([6, 26]), new ECBlocks(20, new ECB(1, 80)), new ECBlocks(18, new ECB(2, 32)), new ECBlocks(26, new ECB(2, 24)), new ECBlocks(16, new ECB(4, 9))),\n        new Version(5, Int32Array.from([6, 30]), new ECBlocks(26, new ECB(1, 108)), new ECBlocks(24, new ECB(2, 43)), new ECBlocks(18, new ECB(2, 15), new ECB(2, 16)), new ECBlocks(22, new ECB(2, 11), new ECB(2, 12))),\n        new Version(6, Int32Array.from([6, 34]), new ECBlocks(18, new ECB(2, 68)), new ECBlocks(16, new ECB(4, 27)), new ECBlocks(24, new ECB(4, 19)), new ECBlocks(28, new ECB(4, 15))),\n        new Version(7, Int32Array.from([6, 22, 38]), new ECBlocks(20, new ECB(2, 78)), new ECBlocks(18, new ECB(4, 31)), new ECBlocks(18, new ECB(2, 14), new ECB(4, 15)), new ECBlocks(26, new ECB(4, 13), new ECB(1, 14))),\n        new Version(8, Int32Array.from([6, 24, 42]), new ECBlocks(24, new ECB(2, 97)), new ECBlocks(22, new ECB(2, 38), new ECB(2, 39)), new ECBlocks(22, new ECB(4, 18), new ECB(2, 19)), new ECBlocks(26, new ECB(4, 14), new ECB(2, 15))),\n        new Version(9, Int32Array.from([6, 26, 46]), new ECBlocks(30, new ECB(2, 116)), new ECBlocks(22, new ECB(3, 36), new ECB(2, 37)), new ECBlocks(20, new ECB(4, 16), new ECB(4, 17)), new ECBlocks(24, new ECB(4, 12), new ECB(4, 13))),\n        new Version(10, Int32Array.from([6, 28, 50]), new ECBlocks(18, new ECB(2, 68), new ECB(2, 69)), new ECBlocks(26, new ECB(4, 43), new ECB(1, 44)), new ECBlocks(24, new ECB(6, 19), new ECB(2, 20)), new ECBlocks(28, new ECB(6, 15), new ECB(2, 16))),\n        new Version(11, Int32Array.from([6, 30, 54]), new ECBlocks(20, new ECB(4, 81)), new ECBlocks(30, new ECB(1, 50), new ECB(4, 51)), new ECBlocks(28, new ECB(4, 22), new ECB(4, 23)), new ECBlocks(24, new ECB(3, 12), new ECB(8, 13))),\n        new Version(12, Int32Array.from([6, 32, 58]), new ECBlocks(24, new ECB(2, 92), new ECB(2, 93)), new ECBlocks(22, new ECB(6, 36), new ECB(2, 37)), new ECBlocks(26, new ECB(4, 20), new ECB(6, 21)), new ECBlocks(28, new ECB(7, 14), new ECB(4, 15))),\n        new Version(13, Int32Array.from([6, 34, 62]), new ECBlocks(26, new ECB(4, 107)), new ECBlocks(22, new ECB(8, 37), new ECB(1, 38)), new ECBlocks(24, new ECB(8, 20), new ECB(4, 21)), new ECBlocks(22, new ECB(12, 11), new ECB(4, 12))),\n        new Version(14, Int32Array.from([6, 26, 46, 66]), new ECBlocks(30, new ECB(3, 115), new ECB(1, 116)), new ECBlocks(24, new ECB(4, 40), new ECB(5, 41)), new ECBlocks(20, new ECB(11, 16), new ECB(5, 17)), new ECBlocks(24, new ECB(11, 12), new ECB(5, 13))),\n        new Version(15, Int32Array.from([6, 26, 48, 70]), new ECBlocks(22, new ECB(5, 87), new ECB(1, 88)), new ECBlocks(24, new ECB(5, 41), new ECB(5, 42)), new ECBlocks(30, new ECB(5, 24), new ECB(7, 25)), new ECBlocks(24, new ECB(11, 12), new ECB(7, 13))),\n        new Version(16, Int32Array.from([6, 26, 50, 74]), new ECBlocks(24, new ECB(5, 98), new ECB(1, 99)), new ECBlocks(28, new ECB(7, 45), new ECB(3, 46)), new ECBlocks(24, new ECB(15, 19), new ECB(2, 20)), new ECBlocks(30, new ECB(3, 15), new ECB(13, 16))),\n        new Version(17, Int32Array.from([6, 30, 54, 78]), new ECBlocks(28, new ECB(1, 107), new ECB(5, 108)), new ECBlocks(28, new ECB(10, 46), new ECB(1, 47)), new ECBlocks(28, new ECB(1, 22), new ECB(15, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(17, 15))),\n        new Version(18, Int32Array.from([6, 30, 56, 82]), new ECBlocks(30, new ECB(5, 120), new ECB(1, 121)), new ECBlocks(26, new ECB(9, 43), new ECB(4, 44)), new ECBlocks(28, new ECB(17, 22), new ECB(1, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(19, 15))),\n        new Version(19, Int32Array.from([6, 30, 58, 86]), new ECBlocks(28, new ECB(3, 113), new ECB(4, 114)), new ECBlocks(26, new ECB(3, 44), new ECB(11, 45)), new ECBlocks(26, new ECB(17, 21), new ECB(4, 22)), new ECBlocks(26, new ECB(9, 13), new ECB(16, 14))),\n        new Version(20, Int32Array.from([6, 34, 62, 90]), new ECBlocks(28, new ECB(3, 107), new ECB(5, 108)), new ECBlocks(26, new ECB(3, 41), new ECB(13, 42)), new ECBlocks(30, new ECB(15, 24), new ECB(5, 25)), new ECBlocks(28, new ECB(15, 15), new ECB(10, 16))),\n        new Version(21, Int32Array.from([6, 28, 50, 72, 94]), new ECBlocks(28, new ECB(4, 116), new ECB(4, 117)), new ECBlocks(26, new ECB(17, 42)), new ECBlocks(28, new ECB(17, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(19, 16), new ECB(6, 17))),\n        new Version(22, Int32Array.from([6, 26, 50, 74, 98]), new ECBlocks(28, new ECB(2, 111), new ECB(7, 112)), new ECBlocks(28, new ECB(17, 46)), new ECBlocks(30, new ECB(7, 24), new ECB(16, 25)), new ECBlocks(24, new ECB(34, 13))),\n        new Version(23, Int32Array.from([6, 30, 54, 78, 102]), new ECBlocks(30, new ECB(4, 121), new ECB(5, 122)), new ECBlocks(28, new ECB(4, 47), new ECB(14, 48)), new ECBlocks(30, new ECB(11, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(16, 15), new ECB(14, 16))),\n        new Version(24, Int32Array.from([6, 28, 54, 80, 106]), new ECBlocks(30, new ECB(6, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(6, 45), new ECB(14, 46)), new ECBlocks(30, new ECB(11, 24), new ECB(16, 25)), new ECBlocks(30, new ECB(30, 16), new ECB(2, 17))),\n        new Version(25, Int32Array.from([6, 32, 58, 84, 110]), new ECBlocks(26, new ECB(8, 106), new ECB(4, 107)), new ECBlocks(28, new ECB(8, 47), new ECB(13, 48)), new ECBlocks(30, new ECB(7, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(13, 16))),\n        new Version(26, Int32Array.from([6, 30, 58, 86, 114]), new ECBlocks(28, new ECB(10, 114), new ECB(2, 115)), new ECBlocks(28, new ECB(19, 46), new ECB(4, 47)), new ECBlocks(28, new ECB(28, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(33, 16), new ECB(4, 17))),\n        new Version(27, Int32Array.from([6, 34, 62, 90, 118]), new ECBlocks(30, new ECB(8, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(22, 45), new ECB(3, 46)), new ECBlocks(30, new ECB(8, 23), new ECB(26, 24)), new ECBlocks(30, new ECB(12, 15), new ECB(28, 16))),\n        new Version(28, Int32Array.from([6, 26, 50, 74, 98, 122]), new ECBlocks(30, new ECB(3, 117), new ECB(10, 118)), new ECBlocks(28, new ECB(3, 45), new ECB(23, 46)), new ECBlocks(30, new ECB(4, 24), new ECB(31, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(31, 16))),\n        new Version(29, Int32Array.from([6, 30, 54, 78, 102, 126]), new ECBlocks(30, new ECB(7, 116), new ECB(7, 117)), new ECBlocks(28, new ECB(21, 45), new ECB(7, 46)), new ECBlocks(30, new ECB(1, 23), new ECB(37, 24)), new ECBlocks(30, new ECB(19, 15), new ECB(26, 16))),\n        new Version(30, Int32Array.from([6, 26, 52, 78, 104, 130]), new ECBlocks(30, new ECB(5, 115), new ECB(10, 116)), new ECBlocks(28, new ECB(19, 47), new ECB(10, 48)), new ECBlocks(30, new ECB(15, 24), new ECB(25, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(25, 16))),\n        new Version(31, Int32Array.from([6, 30, 56, 82, 108, 134]), new ECBlocks(30, new ECB(13, 115), new ECB(3, 116)), new ECBlocks(28, new ECB(2, 46), new ECB(29, 47)), new ECBlocks(30, new ECB(42, 24), new ECB(1, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(28, 16))),\n        new Version(32, Int32Array.from([6, 34, 60, 86, 112, 138]), new ECBlocks(30, new ECB(17, 115)), new ECBlocks(28, new ECB(10, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(10, 24), new ECB(35, 25)), new ECBlocks(30, new ECB(19, 15), new ECB(35, 16))),\n        new Version(33, Int32Array.from([6, 30, 58, 86, 114, 142]), new ECBlocks(30, new ECB(17, 115), new ECB(1, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(21, 47)), new ECBlocks(30, new ECB(29, 24), new ECB(19, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(46, 16))),\n        new Version(34, Int32Array.from([6, 34, 62, 90, 118, 146]), new ECBlocks(30, new ECB(13, 115), new ECB(6, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(44, 24), new ECB(7, 25)), new ECBlocks(30, new ECB(59, 16), new ECB(1, 17))),\n        new Version(35, Int32Array.from([6, 30, 54, 78, 102, 126, 150]), new ECBlocks(30, new ECB(12, 121), new ECB(7, 122)), new ECBlocks(28, new ECB(12, 47), new ECB(26, 48)), new ECBlocks(30, new ECB(39, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(41, 16))),\n        new Version(36, Int32Array.from([6, 24, 50, 76, 102, 128, 154]), new ECBlocks(30, new ECB(6, 121), new ECB(14, 122)), new ECBlocks(28, new ECB(6, 47), new ECB(34, 48)), new ECBlocks(30, new ECB(46, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(2, 15), new ECB(64, 16))),\n        new Version(37, Int32Array.from([6, 28, 54, 80, 106, 132, 158]), new ECBlocks(30, new ECB(17, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(29, 46), new ECB(14, 47)), new ECBlocks(30, new ECB(49, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(24, 15), new ECB(46, 16))),\n        new Version(38, Int32Array.from([6, 32, 58, 84, 110, 136, 162]), new ECBlocks(30, new ECB(4, 122), new ECB(18, 123)), new ECBlocks(28, new ECB(13, 46), new ECB(32, 47)), new ECBlocks(30, new ECB(48, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(42, 15), new ECB(32, 16))),\n        new Version(39, Int32Array.from([6, 26, 54, 82, 110, 138, 166]), new ECBlocks(30, new ECB(20, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(40, 47), new ECB(7, 48)), new ECBlocks(30, new ECB(43, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(10, 15), new ECB(67, 16))),\n        new Version(40, Int32Array.from([6, 30, 58, 86, 114, 142, 170]), new ECBlocks(30, new ECB(19, 118), new ECB(6, 119)), new ECBlocks(28, new ECB(18, 47), new ECB(31, 48)), new ECBlocks(30, new ECB(34, 24), new ECB(34, 25)), new ECBlocks(30, new ECB(20, 15), new ECB(61, 16)))\n    ];\n    return Version;\n}());\nexport default Version;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,6CAA6C,GAC7C;AACA;AACA;AACA;AACA;AACA;AAjBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAQA;;;;CAIC,GACD,IAAI,UAAyB;IACzB,SAAS,QAAQ,cAAc,KAAK,GAAN,EAAU,uBAAuB;QAC3D,IAAI,KAAK;QACT,IAAI,WAAW,EAAE;QACjB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,QAAQ,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QACpC;QACA,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,uBAAuB,GAAG;QAC/B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,QAAQ;QACZ,IAAI,cAAc,QAAQ,CAAC,EAAE,CAAC,sBAAsB;QACpD,IAAI,WAAW,QAAQ,CAAC,EAAE,CAAC,WAAW;QACtC,IAAI;YACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;gBAC9H,IAAI,UAAU,aAAa,KAAK;gBAChC,SAAS,QAAQ,QAAQ,KAAK,CAAC,QAAQ,gBAAgB,KAAK,WAAW;YAC3E;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;YAChF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,QAAQ,SAAS,CAAC,gBAAgB,GAAG;QACjC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,QAAQ,SAAS,CAAC,0BAA0B,GAAG;QAC3C,OAAO,IAAI,CAAC,uBAAuB;IACvC;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG;QAClC,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,QAAQ,SAAS,CAAC,sBAAsB,GAAG;QACvC,OAAO,KAAK,IAAI,IAAI,CAAC,aAAa;IACtC;IACA,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAU,OAAO;QACrD,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG;IACxC,2HAA2H;IAC3H,wGAAwG;IAC5G;IACA;;;;;;KAMC,GACD,QAAQ,iCAAiC,GAAG,SAAU,UAAU,KAAK,GAAN;QAC3D,IAAI,YAAY,MAAM,GAAG;YACrB,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI;YACA,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,YAAY,EAAE,IAAI;QACvD,EACA,OAAO,QAAQ,4BAA4B,KAAI;YAC3C,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;IACJ;IACA,QAAQ,mBAAmB,GAAG,SAAU,cAAc,KAAK,GAAN;QACjD,IAAI,gBAAgB,KAAK,gBAAgB,IAAI;YACzC,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,OAAO,QAAQ,QAAQ,CAAC,gBAAgB,EAAE;IAC9C;IACA,QAAQ,wBAAwB,GAAG,SAAU,YAAY,KAAK,GAAN;QACpD,IAAI,iBAAiB,OAAO,gBAAgB;QAC5C,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,mBAAmB,CAAC,MAAM,EAAE,IAAK;YACzD,IAAI,gBAAgB,QAAQ,mBAAmB,CAAC,EAAE;YAClD,gDAAgD;YAChD,IAAI,kBAAkB,aAAa;gBAC/B,OAAO,QAAQ,mBAAmB,CAAC,IAAI;YAC3C;YACA,yEAAyE;YACzE,sBAAsB;YACtB,IAAI,iBAAiB,8LAAA,CAAA,UAAiB,CAAC,gBAAgB,CAAC,aAAa;YACrE,IAAI,iBAAiB,gBAAgB;gBACjC,cAAc,IAAI;gBAClB,iBAAiB;YACrB;QACJ;QACA,iFAAiF;QACjF,8BAA8B;QAC9B,IAAI,kBAAkB,GAAG;YACrB,OAAO,QAAQ,mBAAmB,CAAC;QACvC;QACA,+CAA+C;QAC/C,OAAO;IACX;IACA;;KAEC,GACD,QAAQ,SAAS,CAAC,oBAAoB,GAAG;QACrC,IAAI,YAAY,IAAI,CAAC,sBAAsB;QAC3C,IAAI,YAAY,IAAI,2KAAA,CAAA,UAAS,CAAC;QAC9B,+CAA+C;QAC/C,UAAU,SAAS,CAAC,GAAG,GAAG,GAAG;QAC7B,gDAAgD;QAChD,UAAU,SAAS,CAAC,YAAY,GAAG,GAAG,GAAG;QACzC,kDAAkD;QAClD,UAAU,SAAS,CAAC,GAAG,YAAY,GAAG,GAAG;QACzC,qBAAqB;QACrB,IAAI,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC,EAAE,GAAG;YAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC1B,IAAI,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,MAAM,CAAC,KAAO,MAAM,MAAM,KAAK,MAAM,GAAI;oBAEvE;gBACJ;gBACA,UAAU,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;YACnE;QACJ;QACA,0BAA0B;QAC1B,UAAU,SAAS,CAAC,GAAG,GAAG,GAAG,YAAY;QACzC,4BAA4B;QAC5B,UAAU,SAAS,CAAC,GAAG,GAAG,YAAY,IAAI;QAC1C,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;YACxB,0BAA0B;YAC1B,UAAU,SAAS,CAAC,YAAY,IAAI,GAAG,GAAG;YAC1C,4BAA4B;YAC5B,UAAU,SAAS,CAAC,GAAG,YAAY,IAAI,GAAG;QAC9C;QACA,OAAO;IACX;IACA,WAAW,GACX,QAAQ,SAAS,CAAC,QAAQ,GAAG;QACzB,OAAO,KAAK,IAAI,CAAC,aAAa;IAClC;IACA;;;OAGG,GACH,QAAQ,mBAAmB,GAAG,WAAW,IAAI,CAAC;QAC1C;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;QAAS;QACpC;QAAS;QAAS;QAAS;KAC9B;IACD;;OAEG,GACH,QAAQ,QAAQ,GAAG;QACf,IAAI,QAAQ,GAAG,IAAI,WAAW,IAAI,IAAI,qLAAA,CAAA,UAAQ,CAAC,GAAG,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QACnK,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC3K,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC3K,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC3K,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC5M,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC3K,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC/M,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC/N,IAAI,QAAQ,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAChO,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAChP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAChO,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAChP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAClO,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QACxP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QACrP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACtP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACzP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACxP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACzP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC1P,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC7O,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAG,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC7N,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAChQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC/P,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC/P,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QAC/P,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC/P,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAI;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACpQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACpQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACvQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACrQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACtP,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QACvQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG;QACrQ,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC5Q,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC1Q,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC5Q,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC5Q,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;QAC3Q,IAAI,QAAQ,IAAI,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI,GAAG,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,gLAAA,CAAA,UAAG,CAAC,GAAG,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,MAAM,IAAI,qLAAA,CAAA,UAAQ,CAAC,IAAI,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI,KAAK,IAAI,gLAAA,CAAA,UAAG,CAAC,IAAI;KAC/Q;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/DataMask.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var DataMaskValues;\n(function (DataMaskValues) {\n    DataMaskValues[DataMaskValues[\"DATA_MASK_000\"] = 0] = \"DATA_MASK_000\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_001\"] = 1] = \"DATA_MASK_001\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_010\"] = 2] = \"DATA_MASK_010\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_011\"] = 3] = \"DATA_MASK_011\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_100\"] = 4] = \"DATA_MASK_100\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_101\"] = 5] = \"DATA_MASK_101\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_110\"] = 6] = \"DATA_MASK_110\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_111\"] = 7] = \"DATA_MASK_111\";\n})(DataMaskValues || (DataMaskValues = {}));\n/**\n * <p>Encapsulates data masks for the data bits in a QR code, per ISO 18004:2006 6.8. Implementations\n * of this class can un-mask a raw BitMatrix. For simplicity, they will unmask the entire BitMatrix,\n * including areas used for finder patterns, timing patterns, etc. These areas should be unused\n * after the point they are unmasked anyway.</p>\n *\n * <p>Note that the diagram in section 6.8.1 is misleading since it indicates that i is column position\n * and j is row position. In fact, as the text says, i is row position and j is column position.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataMask = /** @class */ (function () {\n    // See ISO 18004:2006 6.8.1\n    function DataMask(value, isMasked) {\n        this.value = value;\n        this.isMasked = isMasked;\n    }\n    // End of enum constants.\n    /**\n     * <p>Implementations of this method reverse the data masking process applied to a QR Code and\n     * make its bits ready to read.</p>\n     *\n     * @param bits representation of QR Code bits\n     * @param dimension dimension of QR Code, represented by bits, being unmasked\n     */\n    DataMask.prototype.unmaskBitMatrix = function (bits, dimension /*int*/) {\n        for (var i = 0; i < dimension; i++) {\n            for (var j = 0; j < dimension; j++) {\n                if (this.isMasked(i, j)) {\n                    bits.flip(j, i);\n                }\n            }\n        }\n    };\n    DataMask.values = new Map([\n        /**\n         * 000: mask bits for which (x + y) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_000, new DataMask(DataMaskValues.DATA_MASK_000, function (i /*int*/, j /*int*/) { return ((i + j) & 0x01) === 0; })],\n        /**\n         * 001: mask bits for which x mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_001, new DataMask(DataMaskValues.DATA_MASK_001, function (i /*int*/, j /*int*/) { return (i & 0x01) === 0; })],\n        /**\n         * 010: mask bits for which y mod 3 == 0\n         */\n        [DataMaskValues.DATA_MASK_010, new DataMask(DataMaskValues.DATA_MASK_010, function (i /*int*/, j /*int*/) { return j % 3 === 0; })],\n        /**\n         * 011: mask bits for which (x + y) mod 3 == 0\n         */\n        [DataMaskValues.DATA_MASK_011, new DataMask(DataMaskValues.DATA_MASK_011, function (i /*int*/, j /*int*/) { return (i + j) % 3 === 0; })],\n        /**\n         * 100: mask bits for which (x/2 + y/3) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_100, new DataMask(DataMaskValues.DATA_MASK_100, function (i /*int*/, j /*int*/) { return ((Math.floor(i / 2) + Math.floor(j / 3)) & 0x01) === 0; })],\n        /**\n         * 101: mask bits for which xy mod 2 + xy mod 3 == 0\n         * equivalently, such that xy mod 6 == 0\n         */\n        [DataMaskValues.DATA_MASK_101, new DataMask(DataMaskValues.DATA_MASK_101, function (i /*int*/, j /*int*/) { return (i * j) % 6 === 0; })],\n        /**\n         * 110: mask bits for which (xy mod 2 + xy mod 3) mod 2 == 0\n         * equivalently, such that xy mod 6 < 3\n         */\n        [DataMaskValues.DATA_MASK_110, new DataMask(DataMaskValues.DATA_MASK_110, function (i /*int*/, j /*int*/) { return ((i * j) % 6) < 3; })],\n        /**\n         * 111: mask bits for which ((x+y)mod 2 + xy mod 3) mod 2 == 0\n         * equivalently, such that (x + y + xy mod 3) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_111, new DataMask(DataMaskValues.DATA_MASK_111, function (i /*int*/, j /*int*/) { return ((i + j + ((i * j) % 3)) & 0x01) === 0; })],\n    ]);\n    return DataMask;\n}());\nexport default DataMask;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AACM,IAAI;AACX,CAAC,SAAU,cAAc;IACrB,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtD,cAAc,CAAC,cAAc,CAAC,gBAAgB,GAAG,EAAE,GAAG;AAC1D,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AACzC;;;;;;;;;;CAUC,GACD,IAAI,WAA0B;IAC1B,2BAA2B;IAC3B,SAAS,SAAS,KAAK,EAAE,QAAQ;QAC7B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,yBAAyB;IACzB;;;;;;KAMC,GACD,SAAS,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI,EAAE,UAAU,KAAK,GAAN;QAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAChC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;oBACrB,KAAK,IAAI,CAAC,GAAG;gBACjB;YACJ;QACJ;IACJ;IACA,SAAS,MAAM,GAAG,IAAI,IAAI;QACtB;;SAEC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,CAAC,AAAC,IAAI,IAAK,IAAI,MAAM;YAAG;SAAG;QAC9I;;SAEC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,CAAC,IAAI,IAAI,MAAM;YAAG;SAAG;QACxI;;SAEC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,IAAI,MAAM;YAAG;SAAG;QACnI;;SAEC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;YAAG;SAAG;QACzI;;SAEC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,CAAC,AAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,KAAM,IAAI,MAAM;YAAG;SAAG;QAC9K;;;SAGC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,AAAC,IAAI,IAAK,MAAM;YAAG;SAAG;QACzI;;;SAGC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,AAAE,IAAI,IAAK,IAAK;YAAG;SAAG;QACzI;;;SAGC,GACD;YAAC,eAAe,aAAa;YAAE,IAAI,SAAS,eAAe,aAAa,EAAE,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;gBAAY,OAAO,CAAC,AAAC,IAAI,IAAK,AAAC,IAAI,IAAK,IAAM,IAAI,MAAM;YAAG;SAAG;KACjK;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/BitMatrixParser.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Version from './Version';\nimport FormatInformation from './FormatInformation';\nimport DataMask from './DataMask';\nimport FormatException from '../../FormatException';\n/**\n * <AUTHOR>\n */\nvar BitMatrixParser = /** @class */ (function () {\n    /**\n     * @param bitMatrix {@link BitMatrix} to parse\n     * @throws FormatException if dimension is not >= 21 and 1 mod 4\n     */\n    function BitMatrixParser(bitMatrix) {\n        var dimension = bitMatrix.getHeight();\n        if (dimension < 21 || (dimension & 0x03) !== 1) {\n            throw new FormatException();\n        }\n        this.bitMatrix = bitMatrix;\n    }\n    /**\n     * <p>Reads format information from one of its two locations within the QR Code.</p>\n     *\n     * @return {@link FormatInformation} encapsulating the QR Code's format info\n     * @throws FormatException if both format information locations cannot be parsed as\n     * the valid encoding of format information\n     */\n    BitMatrixParser.prototype.readFormatInformation = function () {\n        if (this.parsedFormatInfo !== null && this.parsedFormatInfo !== undefined) {\n            return this.parsedFormatInfo;\n        }\n        // Read top-left format info bits\n        var formatInfoBits1 = 0;\n        for (var i = 0; i < 6; i++) {\n            formatInfoBits1 = this.copyBit(i, 8, formatInfoBits1);\n        }\n        // .. and skip a bit in the timing pattern ...\n        formatInfoBits1 = this.copyBit(7, 8, formatInfoBits1);\n        formatInfoBits1 = this.copyBit(8, 8, formatInfoBits1);\n        formatInfoBits1 = this.copyBit(8, 7, formatInfoBits1);\n        // .. and skip a bit in the timing pattern ...\n        for (var j = 5; j >= 0; j--) {\n            formatInfoBits1 = this.copyBit(8, j, formatInfoBits1);\n        }\n        // Read the top-right/bottom-left pattern too\n        var dimension = this.bitMatrix.getHeight();\n        var formatInfoBits2 = 0;\n        var jMin = dimension - 7;\n        for (var j = dimension - 1; j >= jMin; j--) {\n            formatInfoBits2 = this.copyBit(8, j, formatInfoBits2);\n        }\n        for (var i = dimension - 8; i < dimension; i++) {\n            formatInfoBits2 = this.copyBit(i, 8, formatInfoBits2);\n        }\n        this.parsedFormatInfo = FormatInformation.decodeFormatInformation(formatInfoBits1, formatInfoBits2);\n        if (this.parsedFormatInfo !== null) {\n            return this.parsedFormatInfo;\n        }\n        throw new FormatException();\n    };\n    /**\n     * <p>Reads version information from one of its two locations within the QR Code.</p>\n     *\n     * @return {@link Version} encapsulating the QR Code's version\n     * @throws FormatException if both version information locations cannot be parsed as\n     * the valid encoding of version information\n     */\n    BitMatrixParser.prototype.readVersion = function () {\n        if (this.parsedVersion !== null && this.parsedVersion !== undefined) {\n            return this.parsedVersion;\n        }\n        var dimension = this.bitMatrix.getHeight();\n        var provisionalVersion = Math.floor((dimension - 17) / 4);\n        if (provisionalVersion <= 6) {\n            return Version.getVersionForNumber(provisionalVersion);\n        }\n        // Read top-right version info: 3 wide by 6 tall\n        var versionBits = 0;\n        var ijMin = dimension - 11;\n        for (var j = 5; j >= 0; j--) {\n            for (var i = dimension - 9; i >= ijMin; i--) {\n                versionBits = this.copyBit(i, j, versionBits);\n            }\n        }\n        var theParsedVersion = Version.decodeVersionInformation(versionBits);\n        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n            this.parsedVersion = theParsedVersion;\n            return theParsedVersion;\n        }\n        // Hmm, failed. Try bottom left: 6 wide by 3 tall\n        versionBits = 0;\n        for (var i = 5; i >= 0; i--) {\n            for (var j = dimension - 9; j >= ijMin; j--) {\n                versionBits = this.copyBit(i, j, versionBits);\n            }\n        }\n        theParsedVersion = Version.decodeVersionInformation(versionBits);\n        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n            this.parsedVersion = theParsedVersion;\n            return theParsedVersion;\n        }\n        throw new FormatException();\n    };\n    BitMatrixParser.prototype.copyBit = function (i /*int*/, j /*int*/, versionBits /*int*/) {\n        var bit = this.isMirror ? this.bitMatrix.get(j, i) : this.bitMatrix.get(i, j);\n        return bit ? (versionBits << 1) | 0x1 : versionBits << 1;\n    };\n    /**\n     * <p>Reads the bits in the {@link BitMatrix} representing the finder pattern in the\n     * correct order in order to reconstruct the codewords bytes contained within the\n     * QR Code.</p>\n     *\n     * @return bytes encoded within the QR Code\n     * @throws FormatException if the exact number of bytes expected is not read\n     */\n    BitMatrixParser.prototype.readCodewords = function () {\n        var formatInfo = this.readFormatInformation();\n        var version = this.readVersion();\n        // Get the data mask for the format used in this QR Code. This will exclude\n        // some bits from reading as we wind through the bit matrix.\n        var dataMask = DataMask.values.get(formatInfo.getDataMask());\n        var dimension = this.bitMatrix.getHeight();\n        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n        var functionPattern = version.buildFunctionPattern();\n        var readingUp = true;\n        var result = new Uint8Array(version.getTotalCodewords());\n        var resultOffset = 0;\n        var currentByte = 0;\n        var bitsRead = 0;\n        // Read columns in pairs, from right to left\n        for (var j = dimension - 1; j > 0; j -= 2) {\n            if (j === 6) {\n                // Skip whole column with vertical alignment pattern\n                // saves time and makes the other code proceed more cleanly\n                j--;\n            }\n            // Read alternatingly from bottom to top then top to bottom\n            for (var count = 0; count < dimension; count++) {\n                var i = readingUp ? dimension - 1 - count : count;\n                for (var col = 0; col < 2; col++) {\n                    // Ignore bits covered by the function pattern\n                    if (!functionPattern.get(j - col, i)) {\n                        // Read a bit\n                        bitsRead++;\n                        currentByte <<= 1;\n                        if (this.bitMatrix.get(j - col, i)) {\n                            currentByte |= 1;\n                        }\n                        // If we've made a whole byte, save it off\n                        if (bitsRead === 8) {\n                            result[resultOffset++] = /*(byte) */ currentByte;\n                            bitsRead = 0;\n                            currentByte = 0;\n                        }\n                    }\n                }\n            }\n            readingUp = !readingUp; // readingUp ^= true; // readingUp = !readingUp; // switch directions\n        }\n        if (resultOffset !== version.getTotalCodewords()) {\n            throw new FormatException();\n        }\n        return result;\n    };\n    /**\n     * Revert the mask removal done while reading the code words. The bit matrix should revert to its original state.\n     */\n    BitMatrixParser.prototype.remask = function () {\n        if (this.parsedFormatInfo === null) {\n            return; // We have no format information, and have no data mask\n        }\n        var dataMask = DataMask.values.get(this.parsedFormatInfo.getDataMask());\n        var dimension = this.bitMatrix.getHeight();\n        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n    };\n    /**\n     * Prepare the parser for a mirrored operation.\n     * This flag has effect only on the {@link #readFormatInformation()} and the\n     * {@link #readVersion()}. Before proceeding with {@link #readCodewords()} the\n     * {@link #mirror()} method should be called.\n     *\n     * @param mirror Whether to read version and format information mirrored.\n     */\n    BitMatrixParser.prototype.setMirror = function (isMirror) {\n        this.parsedVersion = null;\n        this.parsedFormatInfo = null;\n        this.isMirror = isMirror;\n    };\n    /** Mirror the bit matrix in order to attempt a second reading. */\n    BitMatrixParser.prototype.mirror = function () {\n        var bitMatrix = this.bitMatrix;\n        for (var x = 0, width = bitMatrix.getWidth(); x < width; x++) {\n            for (var y = x + 1, height = bitMatrix.getHeight(); y < height; y++) {\n                if (bitMatrix.get(x, y) !== bitMatrix.get(y, x)) {\n                    bitMatrix.flip(y, x);\n                    bitMatrix.flip(x, y);\n                }\n            }\n        }\n    };\n    return BitMatrixParser;\n}());\nexport default BitMatrixParser;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;AACA;;;;;AACA;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;KAGC,GACD,SAAS,gBAAgB,SAAS;QAC9B,IAAI,YAAY,UAAU,SAAS;QACnC,IAAI,YAAY,MAAM,CAAC,YAAY,IAAI,MAAM,GAAG;YAC5C,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;KAMC,GACD,gBAAgB,SAAS,CAAC,qBAAqB,GAAG;QAC9C,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,IAAI,CAAC,gBAAgB,KAAK,WAAW;YACvE,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,iCAAiC;QACjC,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACzC;QACA,8CAA8C;QAC9C,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACrC,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACrC,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACrC,8CAA8C;QAC9C,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YACzB,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACzC;QACA,6CAA6C;QAC7C,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;QACxC,IAAI,kBAAkB;QACtB,IAAI,OAAO,YAAY;QACvB,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,MAAM,IAAK;YACxC,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACzC;QACA,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW,IAAK;YAC5C,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACzC;QACA,IAAI,CAAC,gBAAgB,GAAG,8LAAA,CAAA,UAAiB,CAAC,uBAAuB,CAAC,iBAAiB;QACnF,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM;YAChC,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,MAAM,IAAI,uKAAA,CAAA,UAAe;IAC7B;IACA;;;;;;KAMC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG;QACpC,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,CAAC,aAAa,KAAK,WAAW;YACjE,OAAO,IAAI,CAAC,aAAa;QAC7B;QACA,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;QACxC,IAAI,qBAAqB,KAAK,KAAK,CAAC,CAAC,YAAY,EAAE,IAAI;QACvD,IAAI,sBAAsB,GAAG;YACzB,OAAO,oLAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;QACvC;QACA,gDAAgD;QAChD,IAAI,cAAc;QAClB,IAAI,QAAQ,YAAY;QACxB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YACzB,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,OAAO,IAAK;gBACzC,cAAc,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;YACrC;QACJ;QACA,IAAI,mBAAmB,oLAAA,CAAA,UAAO,CAAC,wBAAwB,CAAC;QACxD,IAAI,qBAAqB,QAAQ,iBAAiB,sBAAsB,OAAO,WAAW;YACtF,IAAI,CAAC,aAAa,GAAG;YACrB,OAAO;QACX;QACA,iDAAiD;QACjD,cAAc;QACd,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YACzB,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,OAAO,IAAK;gBACzC,cAAc,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;YACrC;QACJ;QACA,mBAAmB,oLAAA,CAAA,UAAO,CAAC,wBAAwB,CAAC;QACpD,IAAI,qBAAqB,QAAQ,iBAAiB,sBAAsB,OAAO,WAAW;YACtF,IAAI,CAAC,aAAa,GAAG;YACrB,OAAO;QACX;QACA,MAAM,IAAI,uKAAA,CAAA,UAAe;IAC7B;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,YAAY,KAAK,GAAN;QAC3E,IAAI,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG;QAC3E,OAAO,MAAM,AAAC,eAAe,IAAK,MAAM,eAAe;IAC3D;IACA;;;;;;;KAOC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG;QACtC,IAAI,aAAa,IAAI,CAAC,qBAAqB;QAC3C,IAAI,UAAU,IAAI,CAAC,WAAW;QAC9B,2EAA2E;QAC3E,4DAA4D;QAC5D,IAAI,WAAW,qLAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,WAAW;QACzD,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;QACxC,SAAS,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;QACzC,IAAI,kBAAkB,QAAQ,oBAAoB;QAClD,IAAI,YAAY;QAChB,IAAI,SAAS,IAAI,WAAW,QAAQ,iBAAiB;QACrD,IAAI,eAAe;QACnB,IAAI,cAAc;QAClB,IAAI,WAAW;QACf,4CAA4C;QAC5C,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,GAAG,KAAK,EAAG;YACvC,IAAI,MAAM,GAAG;gBACT,oDAAoD;gBACpD,2DAA2D;gBAC3D;YACJ;YACA,2DAA2D;YAC3D,IAAK,IAAI,QAAQ,GAAG,QAAQ,WAAW,QAAS;gBAC5C,IAAI,IAAI,YAAY,YAAY,IAAI,QAAQ;gBAC5C,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;oBAC9B,8CAA8C;oBAC9C,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,KAAK,IAAI;wBAClC,aAAa;wBACb;wBACA,gBAAgB;wBAChB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI;4BAChC,eAAe;wBACnB;wBACA,0CAA0C;wBAC1C,IAAI,aAAa,GAAG;4BAChB,MAAM,CAAC,eAAe,GAAG,SAAS,GAAG;4BACrC,WAAW;4BACX,cAAc;wBAClB;oBACJ;gBACJ;YACJ;YACA,YAAY,CAAC,WAAW,qEAAqE;QACjG;QACA,IAAI,iBAAiB,QAAQ,iBAAiB,IAAI;YAC9C,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,OAAO;IACX;IACA;;KAEC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG;QAC/B,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM;YAChC,QAAQ,uDAAuD;QACnE;QACA,IAAI,WAAW,qLAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW;QACpE,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;QACxC,SAAS,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;IAC7C;IACA;;;;;;;KAOC,GACD,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAU,QAAQ;QACpD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,gEAAgE,GAChE,gBAAgB,SAAS,CAAC,MAAM,GAAG;QAC/B,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAK,IAAI,IAAI,GAAG,QAAQ,UAAU,QAAQ,IAAI,IAAI,OAAO,IAAK;YAC1D,IAAK,IAAI,IAAI,IAAI,GAAG,SAAS,UAAU,SAAS,IAAI,IAAI,QAAQ,IAAK;gBACjE,IAAI,UAAU,GAAG,CAAC,GAAG,OAAO,UAAU,GAAG,CAAC,GAAG,IAAI;oBAC7C,UAAU,IAAI,CAAC,GAAG;oBAClB,UAAU,IAAI,CAAC,GAAG;gBACtB;YACJ;QACJ;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/DataBlock.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Encapsulates a block of data within a QR Code. QR Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataBlock = /** @class */ (function () {\n    function DataBlock(numDataCodewords /*int*/, codewords) {\n        this.numDataCodewords = numDataCodewords;\n        this.codewords = codewords;\n    }\n    /**\n     * <p>When QR Codes use multiple data blocks, they are actually interleaved.\n     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n     * method will separate the data into original blocks.</p>\n     *\n     * @param rawCodewords bytes as read directly from the QR Code\n     * @param version version of the QR Code\n     * @param ecLevel error-correction level of the QR Code\n     * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n     *         QR Code\n     */\n    DataBlock.getDataBlocks = function (rawCodewords, version, ecLevel) {\n        var e_1, _a, e_2, _b;\n        if (rawCodewords.length !== version.getTotalCodewords()) {\n            throw new IllegalArgumentException();\n        }\n        // Figure out the number and size of data blocks used by this version and\n        // error correction level\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        // First count the total number of data blocks\n        var totalBlocks = 0;\n        var ecBlockArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n                var ecBlock = ecBlockArray_1_1.value;\n                totalBlocks += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // Now establish DataBlocks of the appropriate size and number of data codewords\n        var result = new Array(totalBlocks);\n        var numResultBlocks = 0;\n        try {\n            for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n                var ecBlock = ecBlockArray_2_1.value;\n                for (var i = 0; i < ecBlock.getCount(); i++) {\n                    var numDataCodewords = ecBlock.getDataCodewords();\n                    var numBlockCodewords = ecBlocks.getECCodewordsPerBlock() + numDataCodewords;\n                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // All blocks have the same amount of data, except that the last n\n        // (where n may be 0) have 1 more byte. Figure out where these start.\n        var shorterBlocksTotalCodewords = result[0].codewords.length;\n        var longerBlocksStartAt = result.length - 1;\n        // TYPESCRIPTPORT: check length is correct here\n        while (longerBlocksStartAt >= 0) {\n            var numCodewords = result[longerBlocksStartAt].codewords.length;\n            if (numCodewords === shorterBlocksTotalCodewords) {\n                break;\n            }\n            longerBlocksStartAt--;\n        }\n        longerBlocksStartAt++;\n        var shorterBlocksNumDataCodewords = shorterBlocksTotalCodewords - ecBlocks.getECCodewordsPerBlock();\n        // The last elements of result may be 1 element longer\n        // first fill out as many elements as all of them have\n        var rawCodewordsOffset = 0;\n        for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        // Fill out the last data block in the longer ones\n        for (var j = longerBlocksStartAt; j < numResultBlocks; j++) {\n            result[j].codewords[shorterBlocksNumDataCodewords] = rawCodewords[rawCodewordsOffset++];\n        }\n        // Now add in error correction blocks\n        var max = result[0].codewords.length;\n        for (var i = shorterBlocksNumDataCodewords; i < max; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                var iOffset = j < longerBlocksStartAt ? i : i + 1;\n                result[j].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        return result;\n    };\n    DataBlock.prototype.getNumDataCodewords = function () {\n        return this.numDataCodewords;\n    };\n    DataBlock.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    return DataBlock;\n}());\nexport default DataBlock;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;AAEA;;;;;;CAMC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,iBAAiB,KAAK,GAAN,EAAU,SAAS;QAClD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;;;;KAUC,GACD,UAAU,aAAa,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,OAAO;QAC9D,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,aAAa,MAAM,KAAK,QAAQ,iBAAiB,IAAI;YACrD,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,yEAAyE;QACzE,yBAAyB;QACzB,IAAI,WAAW,QAAQ,mBAAmB,CAAC;QAC3C,8CAA8C;QAC9C,IAAI,cAAc;QAClB,IAAI,eAAe,SAAS,WAAW;QACvC,IAAI;YACA,IAAK,IAAI,iBAAiB,SAAS,eAAe,mBAAmB,eAAe,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,mBAAmB,eAAe,IAAI,GAAI;gBAC1J,IAAI,UAAU,iBAAiB,KAAK;gBACpC,eAAe,QAAQ,QAAQ;YACnC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,oBAAoB,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC;YAC5F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,gFAAgF;QAChF,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAK,IAAI,iBAAiB,SAAS,eAAe,mBAAmB,eAAe,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,mBAAmB,eAAe,IAAI,GAAI;gBAC1J,IAAI,UAAU,iBAAiB,KAAK;gBACpC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,IAAK;oBACzC,IAAI,mBAAmB,QAAQ,gBAAgB;oBAC/C,IAAI,oBAAoB,SAAS,sBAAsB,KAAK;oBAC5D,MAAM,CAAC,kBAAkB,GAAG,IAAI,UAAU,kBAAkB,IAAI,WAAW;gBAC/E;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,oBAAoB,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC;YAC5F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,kEAAkE;QAClE,qEAAqE;QACrE,IAAI,8BAA8B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QAC5D,IAAI,sBAAsB,OAAO,MAAM,GAAG;QAC1C,+CAA+C;QAC/C,MAAO,uBAAuB,EAAG;YAC7B,IAAI,eAAe,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,MAAM;YAC/D,IAAI,iBAAiB,6BAA6B;gBAC9C;YACJ;YACA;QACJ;QACA;QACA,IAAI,gCAAgC,8BAA8B,SAAS,sBAAsB;QACjG,sDAAsD;QACtD,sDAAsD;QACtD,IAAI,qBAAqB;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,+BAA+B,IAAK;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;gBACtC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,GAAG,YAAY,CAAC,qBAAqB;YAC/D;QACJ;QACA,kDAAkD;QAClD,IAAK,IAAI,IAAI,qBAAqB,IAAI,iBAAiB,IAAK;YACxD,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,8BAA8B,GAAG,YAAY,CAAC,qBAAqB;QAC3F;QACA,qCAAqC;QACrC,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QACpC,IAAK,IAAI,IAAI,+BAA+B,IAAI,KAAK,IAAK;YACtD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;gBACtC,IAAI,UAAU,IAAI,sBAAsB,IAAI,IAAI;gBAChD,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC,qBAAqB;YACrE;QACJ;QACA,OAAO;IACX;IACA,UAAU,SAAS,CAAC,mBAAmB,GAAG;QACtC,OAAO,IAAI,CAAC,gBAAgB;IAChC;IACA,UAAU,SAAS,CAAC,YAAY,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/Mode.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ModeValues;\n(function (ModeValues) {\n    ModeValues[ModeValues[\"TERMINATOR\"] = 0] = \"TERMINATOR\";\n    ModeValues[ModeValues[\"NUMERIC\"] = 1] = \"NUMERIC\";\n    ModeValues[ModeValues[\"ALPHANUMERIC\"] = 2] = \"ALPHANUMERIC\";\n    ModeValues[ModeValues[\"STRUCTURED_APPEND\"] = 3] = \"STRUCTURED_APPEND\";\n    ModeValues[ModeValues[\"BYTE\"] = 4] = \"BYTE\";\n    ModeValues[ModeValues[\"ECI\"] = 5] = \"ECI\";\n    ModeValues[ModeValues[\"KANJI\"] = 6] = \"KANJI\";\n    ModeValues[ModeValues[\"FNC1_FIRST_POSITION\"] = 7] = \"FNC1_FIRST_POSITION\";\n    ModeValues[ModeValues[\"FNC1_SECOND_POSITION\"] = 8] = \"FNC1_SECOND_POSITION\";\n    /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n    ModeValues[ModeValues[\"HANZI\"] = 9] = \"HANZI\";\n})(ModeValues || (ModeValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.4.1, Tables 2 and 3. This enum encapsulates the various modes in which\n * data can be encoded to bits in the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar Mode = /** @class */ (function () {\n    function Mode(value, stringValue, characterCountBitsForVersions, bits /*int*/) {\n        this.value = value;\n        this.stringValue = stringValue;\n        this.characterCountBitsForVersions = characterCountBitsForVersions;\n        this.bits = bits;\n        Mode.FOR_BITS.set(bits, this);\n        Mode.FOR_VALUE.set(value, this);\n    }\n    /**\n     * @param bits four bits encoding a QR Code data mode\n     * @return Mode encoded by these bits\n     * @throws IllegalArgumentException if bits do not correspond to a known mode\n     */\n    Mode.forBits = function (bits /*int*/) {\n        var mode = Mode.FOR_BITS.get(bits);\n        if (undefined === mode) {\n            throw new IllegalArgumentException();\n        }\n        return mode;\n    };\n    /**\n     * @param version version in question\n     * @return number of bits used, in this QR Code symbol {@link Version}, to encode the\n     *         count of characters that will follow encoded in this Mode\n     */\n    Mode.prototype.getCharacterCountBits = function (version) {\n        var versionNumber = version.getVersionNumber();\n        var offset;\n        if (versionNumber <= 9) {\n            offset = 0;\n        }\n        else if (versionNumber <= 26) {\n            offset = 1;\n        }\n        else {\n            offset = 2;\n        }\n        return this.characterCountBitsForVersions[offset];\n    };\n    Mode.prototype.getValue = function () {\n        return this.value;\n    };\n    Mode.prototype.getBits = function () {\n        return this.bits;\n    };\n    Mode.prototype.equals = function (o) {\n        if (!(o instanceof Mode)) {\n            return false;\n        }\n        var other = o;\n        return this.value === other.value;\n    };\n    Mode.prototype.toString = function () {\n        return this.stringValue;\n    };\n    Mode.FOR_BITS = new Map();\n    Mode.FOR_VALUE = new Map();\n    Mode.TERMINATOR = new Mode(ModeValues.TERMINATOR, 'TERMINATOR', Int32Array.from([0, 0, 0]), 0x00); // Not really a mode...\n    Mode.NUMERIC = new Mode(ModeValues.NUMERIC, 'NUMERIC', Int32Array.from([10, 12, 14]), 0x01);\n    Mode.ALPHANUMERIC = new Mode(ModeValues.ALPHANUMERIC, 'ALPHANUMERIC', Int32Array.from([9, 11, 13]), 0x02);\n    Mode.STRUCTURED_APPEND = new Mode(ModeValues.STRUCTURED_APPEND, 'STRUCTURED_APPEND', Int32Array.from([0, 0, 0]), 0x03); // Not supported\n    Mode.BYTE = new Mode(ModeValues.BYTE, 'BYTE', Int32Array.from([8, 16, 16]), 0x04);\n    Mode.ECI = new Mode(ModeValues.ECI, 'ECI', Int32Array.from([0, 0, 0]), 0x07); // character counts don't apply\n    Mode.KANJI = new Mode(ModeValues.KANJI, 'KANJI', Int32Array.from([8, 10, 12]), 0x08);\n    Mode.FNC1_FIRST_POSITION = new Mode(ModeValues.FNC1_FIRST_POSITION, 'FNC1_FIRST_POSITION', Int32Array.from([0, 0, 0]), 0x05);\n    Mode.FNC1_SECOND_POSITION = new Mode(ModeValues.FNC1_SECOND_POSITION, 'FNC1_SECOND_POSITION', Int32Array.from([0, 0, 0]), 0x09);\n    /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n    Mode.HANZI = new Mode(ModeValues.HANZI, 'HANZI', Int32Array.from([8, 10, 12]), 0x0D);\n    return Mode;\n}());\nexport default Mode;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AACD;;AACO,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,eAAe,GAAG,EAAE,GAAG;IAC7C,UAAU,CAAC,UAAU,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,GAAG;IACrC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG;IACpC,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,sBAAsB,GAAG,EAAE,GAAG;IACpD,UAAU,CAAC,UAAU,CAAC,uBAAuB,GAAG,EAAE,GAAG;IACrD,wEAAwE,GACxE,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;AAC1C,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AACjC;;;;;CAKC,GACD,IAAI,OAAsB;IACtB,SAAS,KAAK,KAAK,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,KAAK,GAAN;QACjE,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,6BAA6B,GAAG;QACrC,IAAI,CAAC,IAAI,GAAG;QACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI;QAC5B,KAAK,SAAS,CAAC,GAAG,CAAC,OAAO,IAAI;IAClC;IACA;;;;KAIC,GACD,KAAK,OAAO,GAAG,SAAU,KAAK,KAAK,GAAN;QACzB,IAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC;QAC7B,IAAI,cAAc,MAAM;YACpB,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,KAAK,SAAS,CAAC,qBAAqB,GAAG,SAAU,OAAO;QACpD,IAAI,gBAAgB,QAAQ,gBAAgB;QAC5C,IAAI;QACJ,IAAI,iBAAiB,GAAG;YACpB,SAAS;QACb,OACK,IAAI,iBAAiB,IAAI;YAC1B,SAAS;QACb,OACK;YACD,SAAS;QACb;QACA,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO;IACrD;IACA,KAAK,SAAS,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,KAAK,SAAS,CAAC,OAAO,GAAG;QACrB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,KAAK,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QAC/B,IAAI,CAAC,CAAC,aAAa,IAAI,GAAG;YACtB,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK;IACrC;IACA,KAAK,SAAS,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,KAAK,QAAQ,GAAG,IAAI;IACpB,KAAK,SAAS,GAAG,IAAI;IACrB,KAAK,UAAU,GAAG,IAAI,KAAK,WAAW,UAAU,EAAE,cAAc,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE,GAAG,OAAO,uBAAuB;IAC1H,KAAK,OAAO,GAAG,IAAI,KAAK,WAAW,OAAO,EAAE,WAAW,WAAW,IAAI,CAAC;QAAC;QAAI;QAAI;KAAG,GAAG;IACtF,KAAK,YAAY,GAAG,IAAI,KAAK,WAAW,YAAY,EAAE,gBAAgB,WAAW,IAAI,CAAC;QAAC;QAAG;QAAI;KAAG,GAAG;IACpG,KAAK,iBAAiB,GAAG,IAAI,KAAK,WAAW,iBAAiB,EAAE,qBAAqB,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE,GAAG,OAAO,gBAAgB;IACxI,KAAK,IAAI,GAAG,IAAI,KAAK,WAAW,IAAI,EAAE,QAAQ,WAAW,IAAI,CAAC;QAAC;QAAG;QAAI;KAAG,GAAG;IAC5E,KAAK,GAAG,GAAG,IAAI,KAAK,WAAW,GAAG,EAAE,OAAO,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE,GAAG,OAAO,+BAA+B;IAC7G,KAAK,KAAK,GAAG,IAAI,KAAK,WAAW,KAAK,EAAE,SAAS,WAAW,IAAI,CAAC;QAAC;QAAG;QAAI;KAAG,GAAG;IAC/E,KAAK,mBAAmB,GAAG,IAAI,KAAK,WAAW,mBAAmB,EAAE,uBAAuB,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE,GAAG;IACvH,KAAK,oBAAoB,GAAG,IAAI,KAAK,WAAW,oBAAoB,EAAE,wBAAwB,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE,GAAG;IAC1H,wEAAwE,GACxE,KAAK,KAAK,GAAG,IAAI,KAAK,WAAW,KAAK,EAAE,SAAS,WAAW,IAAI,CAAC;QAAC;QAAG;QAAI;KAAG,GAAG;IAC/E,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitSource from '../../common/BitSource';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport DecoderResult from '../../common/DecoderResult';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport Mode from './Mode';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>QR Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one QR Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 18004:2006, 6.4.3 - 6.4.7</p>\n *\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    DecodedBitStreamParser.decode = function (bytes, version, ecLevel, hints) {\n        var bits = new BitSource(bytes);\n        var result = new StringBuilder();\n        var byteSegments = new Array(); // 1\n        // TYPESCRIPTPORT: I do not use constructor with size 1 as in original Java means capacity and the array length is checked below\n        var symbolSequence = -1;\n        var parityData = -1;\n        try {\n            var currentCharacterSetECI = null;\n            var fc1InEffect = false;\n            var mode = void 0;\n            do {\n                // While still another segment to read...\n                if (bits.available() < 4) {\n                    // OK, assume we're done. Really, a TERMINATOR mode should have been recorded here\n                    mode = Mode.TERMINATOR;\n                }\n                else {\n                    var modeBits = bits.readBits(4);\n                    mode = Mode.forBits(modeBits); // mode is encoded by 4 bits\n                }\n                switch (mode) {\n                    case Mode.TERMINATOR:\n                        break;\n                    case Mode.FNC1_FIRST_POSITION:\n                    case Mode.FNC1_SECOND_POSITION:\n                        // We do little with FNC1 except alter the parsed result a bit according to the spec\n                        fc1InEffect = true;\n                        break;\n                    case Mode.STRUCTURED_APPEND:\n                        if (bits.available() < 16) {\n                            throw new FormatException();\n                        }\n                        // sequence number and parity is added later to the result metadata\n                        // Read next 8 bits (symbol sequence #) and 8 bits (data: parity), then continue\n                        symbolSequence = bits.readBits(8);\n                        parityData = bits.readBits(8);\n                        break;\n                    case Mode.ECI:\n                        // Count doesn't apply to ECI\n                        var value = DecodedBitStreamParser.parseECIValue(bits);\n                        currentCharacterSetECI = CharacterSetECI.getCharacterSetECIByValue(value);\n                        if (currentCharacterSetECI === null) {\n                            throw new FormatException();\n                        }\n                        break;\n                    case Mode.HANZI:\n                        // First handle Hanzi mode which does not start with character count\n                        // Chinese mode contains a sub set indicator right after mode indicator\n                        var subset = bits.readBits(4);\n                        var countHanzi = bits.readBits(mode.getCharacterCountBits(version));\n                        if (subset === DecodedBitStreamParser.GB2312_SUBSET) {\n                            DecodedBitStreamParser.decodeHanziSegment(bits, result, countHanzi);\n                        }\n                        break;\n                    default:\n                        // \"Normal\" QR code modes:\n                        // How many characters will follow, encoded in this mode?\n                        var count = bits.readBits(mode.getCharacterCountBits(version));\n                        switch (mode) {\n                            case Mode.NUMERIC:\n                                DecodedBitStreamParser.decodeNumericSegment(bits, result, count);\n                                break;\n                            case Mode.ALPHANUMERIC:\n                                DecodedBitStreamParser.decodeAlphanumericSegment(bits, result, count, fc1InEffect);\n                                break;\n                            case Mode.BYTE:\n                                DecodedBitStreamParser.decodeByteSegment(bits, result, count, currentCharacterSetECI, byteSegments, hints);\n                                break;\n                            case Mode.KANJI:\n                                DecodedBitStreamParser.decodeKanjiSegment(bits, result, count);\n                                break;\n                            default:\n                                throw new FormatException();\n                        }\n                        break;\n                }\n            } while (mode !== Mode.TERMINATOR);\n        }\n        catch (iae /*: IllegalArgumentException*/) {\n            // from readBits() calls\n            throw new FormatException();\n        }\n        return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, ecLevel === null ? null : ecLevel.toString(), symbolSequence, parityData);\n    };\n    /**\n     * See specification GBT 18284-2000\n     */\n    DecodedBitStreamParser.decodeHanziSegment = function (bits, result, count /*int*/) {\n        // Don't crash trying to read more bits than we have available.\n        if (count * 13 > bits.available()) {\n            throw new FormatException();\n        }\n        // Each character will require 2 bytes. Read the characters as 2-byte pairs\n        // and decode as GB2312 afterwards\n        var buffer = new Uint8Array(2 * count);\n        var offset = 0;\n        while (count > 0) {\n            // Each 13 bits encodes a 2-byte character\n            var twoBytes = bits.readBits(13);\n            var assembledTwoBytes = (((twoBytes / 0x060) << 8) & 0xFFFFFFFF) | (twoBytes % 0x060);\n            if (assembledTwoBytes < 0x003BF) {\n                // In the 0xA1A1 to 0xAAFE range\n                assembledTwoBytes += 0x0A1A1;\n            }\n            else {\n                // In the 0xB0A1 to 0xFAFE range\n                assembledTwoBytes += 0x0A6A1;\n            }\n            buffer[offset] = /*(byte) */ ((assembledTwoBytes >> 8) & 0xFF);\n            buffer[offset + 1] = /*(byte) */ (assembledTwoBytes & 0xFF);\n            offset += 2;\n            count--;\n        }\n        try {\n            result.append(StringEncoding.decode(buffer, StringUtils.GB2312));\n            // TYPESCRIPTPORT: TODO: implement GB2312 decode. StringView from MDN could be a starting point\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n    };\n    DecodedBitStreamParser.decodeKanjiSegment = function (bits, result, count /*int*/) {\n        // Don't crash trying to read more bits than we have available.\n        if (count * 13 > bits.available()) {\n            throw new FormatException();\n        }\n        // Each character will require 2 bytes. Read the characters as 2-byte pairs\n        // and decode as Shift_JIS afterwards\n        var buffer = new Uint8Array(2 * count);\n        var offset = 0;\n        while (count > 0) {\n            // Each 13 bits encodes a 2-byte character\n            var twoBytes = bits.readBits(13);\n            var assembledTwoBytes = (((twoBytes / 0x0C0) << 8) & 0xFFFFFFFF) | (twoBytes % 0x0C0);\n            if (assembledTwoBytes < 0x01F00) {\n                // In the 0x8140 to 0x9FFC range\n                assembledTwoBytes += 0x08140;\n            }\n            else {\n                // In the 0xE040 to 0xEBBF range\n                assembledTwoBytes += 0x0C140;\n            }\n            buffer[offset] = /*(byte) */ (assembledTwoBytes >> 8);\n            buffer[offset + 1] = /*(byte) */ assembledTwoBytes;\n            offset += 2;\n            count--;\n        }\n        // Shift_JIS may not be supported in some environments:\n        try {\n            result.append(StringEncoding.decode(buffer, StringUtils.SHIFT_JIS));\n            // TYPESCRIPTPORT: TODO: implement SHIFT_JIS decode. StringView from MDN could be a starting point\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n    };\n    DecodedBitStreamParser.decodeByteSegment = function (bits, result, count /*int*/, currentCharacterSetECI, byteSegments, hints) {\n        // Don't crash trying to read more bits than we have available.\n        if (8 * count > bits.available()) {\n            throw new FormatException();\n        }\n        var readBytes = new Uint8Array(count);\n        for (var i = 0; i < count; i++) {\n            readBytes[i] = /*(byte) */ bits.readBits(8);\n        }\n        var encoding;\n        if (currentCharacterSetECI === null) {\n            // The spec isn't clear on this mode; see\n            // section 6.4.5: t does not say which encoding to assuming\n            // upon decoding. I have seen ISO-8859-1 used as well as\n            // Shift_JIS -- without anything like an ECI designator to\n            // give a hint.\n            encoding = StringUtils.guessEncoding(readBytes, hints);\n        }\n        else {\n            encoding = currentCharacterSetECI.getName();\n        }\n        try {\n            result.append(StringEncoding.decode(readBytes, encoding));\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n        byteSegments.push(readBytes);\n    };\n    DecodedBitStreamParser.toAlphaNumericChar = function (value /*int*/) {\n        if (value >= DecodedBitStreamParser.ALPHANUMERIC_CHARS.length) {\n            throw new FormatException();\n        }\n        return DecodedBitStreamParser.ALPHANUMERIC_CHARS[value];\n    };\n    DecodedBitStreamParser.decodeAlphanumericSegment = function (bits, result, count /*int*/, fc1InEffect) {\n        // Read two characters at a time\n        var start = result.length();\n        while (count > 1) {\n            if (bits.available() < 11) {\n                throw new FormatException();\n            }\n            var nextTwoCharsBits = bits.readBits(11);\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(nextTwoCharsBits / 45)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(nextTwoCharsBits % 45));\n            count -= 2;\n        }\n        if (count === 1) {\n            // special case: one character left\n            if (bits.available() < 6) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(bits.readBits(6)));\n        }\n        // See section 6.4.8.1, 6.4.8.2\n        if (fc1InEffect) {\n            // We need to massage the result a bit if in an FNC1 mode:\n            for (var i = start; i < result.length(); i++) {\n                if (result.charAt(i) === '%') {\n                    if (i < result.length() - 1 && result.charAt(i + 1) === '%') {\n                        // %% is rendered as %\n                        result.deleteCharAt(i + 1);\n                    }\n                    else {\n                        // In alpha mode, % should be converted to FNC1 separator 0x1D\n                        result.setCharAt(i, String.fromCharCode(0x1D));\n                    }\n                }\n            }\n        }\n    };\n    DecodedBitStreamParser.decodeNumericSegment = function (bits, result, count /*int*/) {\n        // Read three digits at a time\n        while (count >= 3) {\n            // Each 10 bits encodes three digits\n            if (bits.available() < 10) {\n                throw new FormatException();\n            }\n            var threeDigitsBits = bits.readBits(10);\n            if (threeDigitsBits >= 1000) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 100)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 10) % 10));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(threeDigitsBits % 10));\n            count -= 3;\n        }\n        if (count === 2) {\n            // Two digits left over to read, encoded in 7 bits\n            if (bits.available() < 7) {\n                throw new FormatException();\n            }\n            var twoDigitsBits = bits.readBits(7);\n            if (twoDigitsBits >= 100) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(twoDigitsBits / 10)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(twoDigitsBits % 10));\n        }\n        else if (count === 1) {\n            // One digit left over to read\n            if (bits.available() < 4) {\n                throw new FormatException();\n            }\n            var digitBits = bits.readBits(4);\n            if (digitBits >= 10) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(digitBits));\n        }\n    };\n    DecodedBitStreamParser.parseECIValue = function (bits) {\n        var firstByte = bits.readBits(8);\n        if ((firstByte & 0x80) === 0) {\n            // just one byte\n            return firstByte & 0x7F;\n        }\n        if ((firstByte & 0xC0) === 0x80) {\n            // two bytes\n            var secondByte = bits.readBits(8);\n            return (((firstByte & 0x3F) << 8) & 0xFFFFFFFF) | secondByte;\n        }\n        if ((firstByte & 0xE0) === 0xC0) {\n            // three bytes\n            var secondThirdBytes = bits.readBits(16);\n            return (((firstByte & 0x1F) << 16) & 0xFFFFFFFF) | secondThirdBytes;\n        }\n        throw new FormatException();\n    };\n    /**\n     * See ISO 18004:2006, 6.4.4 Table 5\n     */\n    DecodedBitStreamParser.ALPHANUMERIC_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';\n    DecodedBitStreamParser.GB2312_SUBSET = 1;\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n// function Uint8ArrayToString(a: Uint8Array): string {\n//     const CHUNK_SZ = 0x8000;\n//     const c = new StringBuilder();\n//     for (let i = 0, length = a.length; i < length; i += CHUNK_SZ) {\n//         c.append(String.fromCharCode.apply(null, a.subarray(i, i + CHUNK_SZ)));\n//     }\n//     return c.toString();\n// }\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,6CAA6C;;;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,8CAA8C,GAC9C,6BAA6B,GAC7B,8BAA8B,GAC9B,wBAAwB,GACxB,uBAAuB,GACvB;;;;;;;CAOC,GACD,IAAI,yBAAwC;IACxC,SAAS,0BACT;IACA,uBAAuB,MAAM,GAAG,SAAU,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;QACpE,IAAI,OAAO,IAAI,2KAAA,CAAA,UAAS,CAAC;QACzB,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;QAC9B,IAAI,eAAe,IAAI,SAAS,IAAI;QACpC,gIAAgI;QAChI,IAAI,iBAAiB,CAAC;QACtB,IAAI,aAAa,CAAC;QAClB,IAAI;YACA,IAAI,yBAAyB;YAC7B,IAAI,cAAc;YAClB,IAAI,OAAO,KAAK;YAChB,GAAG;gBACC,yCAAyC;gBACzC,IAAI,KAAK,SAAS,KAAK,GAAG;oBACtB,kFAAkF;oBAClF,OAAO,iLAAA,CAAA,UAAI,CAAC,UAAU;gBAC1B,OACK;oBACD,IAAI,WAAW,KAAK,QAAQ,CAAC;oBAC7B,OAAO,iLAAA,CAAA,UAAI,CAAC,OAAO,CAAC,WAAW,4BAA4B;gBAC/D;gBACA,OAAQ;oBACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,UAAU;wBAChB;oBACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,mBAAmB;oBAC7B,KAAK,iLAAA,CAAA,UAAI,CAAC,oBAAoB;wBAC1B,oFAAoF;wBACpF,cAAc;wBACd;oBACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,iBAAiB;wBACvB,IAAI,KAAK,SAAS,KAAK,IAAI;4BACvB,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA,mEAAmE;wBACnE,gFAAgF;wBAChF,iBAAiB,KAAK,QAAQ,CAAC;wBAC/B,aAAa,KAAK,QAAQ,CAAC;wBAC3B;oBACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,GAAG;wBACT,6BAA6B;wBAC7B,IAAI,QAAQ,uBAAuB,aAAa,CAAC;wBACjD,yBAAyB,iLAAA,CAAA,UAAe,CAAC,yBAAyB,CAAC;wBACnE,IAAI,2BAA2B,MAAM;4BACjC,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,KAAK;wBACX,oEAAoE;wBACpE,uEAAuE;wBACvE,IAAI,SAAS,KAAK,QAAQ,CAAC;wBAC3B,IAAI,aAAa,KAAK,QAAQ,CAAC,KAAK,qBAAqB,CAAC;wBAC1D,IAAI,WAAW,uBAAuB,aAAa,EAAE;4BACjD,uBAAuB,kBAAkB,CAAC,MAAM,QAAQ;wBAC5D;wBACA;oBACJ;wBACI,0BAA0B;wBAC1B,yDAAyD;wBACzD,IAAI,QAAQ,KAAK,QAAQ,CAAC,KAAK,qBAAqB,CAAC;wBACrD,OAAQ;4BACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,OAAO;gCACb,uBAAuB,oBAAoB,CAAC,MAAM,QAAQ;gCAC1D;4BACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,YAAY;gCAClB,uBAAuB,yBAAyB,CAAC,MAAM,QAAQ,OAAO;gCACtE;4BACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,IAAI;gCACV,uBAAuB,iBAAiB,CAAC,MAAM,QAAQ,OAAO,wBAAwB,cAAc;gCACpG;4BACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,KAAK;gCACX,uBAAuB,kBAAkB,CAAC,MAAM,QAAQ;gCACxD;4BACJ;gCACI,MAAM,IAAI,uKAAA,CAAA,UAAe;wBACjC;wBACA;gBACR;YACJ,QAAS,SAAS,iLAAA,CAAA,UAAI,CAAC,UAAU,CAAE;QACvC,EACA,OAAO,IAAI,4BAA4B,KAAI;YACvC,wBAAwB;YACxB,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,OAAO,IAAI,+KAAA,CAAA,UAAa,CAAC,OAAO,OAAO,QAAQ,IAAI,aAAa,MAAM,KAAK,IAAI,OAAO,cAAc,YAAY,OAAO,OAAO,QAAQ,QAAQ,IAAI,gBAAgB;IACtK;IACA;;KAEC,GACD,uBAAuB,kBAAkB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN;QACrE,+DAA+D;QAC/D,IAAI,QAAQ,KAAK,KAAK,SAAS,IAAI;YAC/B,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,2EAA2E;QAC3E,kCAAkC;QAClC,IAAI,SAAS,IAAI,WAAW,IAAI;QAChC,IAAI,SAAS;QACb,MAAO,QAAQ,EAAG;YACd,0CAA0C;YAC1C,IAAI,WAAW,KAAK,QAAQ,CAAC;YAC7B,IAAI,oBAAoB,AAAG,WAAW,SAAU,IAAK,aAAe,WAAW;YAC/E,IAAI,oBAAoB,SAAS;gBAC7B,gCAAgC;gBAChC,qBAAqB;YACzB,OACK;gBACD,gCAAgC;gBAChC,qBAAqB;YACzB;YACA,MAAM,CAAC,OAAO,GAAgB,AAAC,qBAAqB,IAAK;YACzD,MAAM,CAAC,SAAS,EAAE,GAAgB,oBAAoB;YACtD,UAAU;YACV;QACJ;QACA,IAAI;YACA,OAAO,MAAM,CAAC,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,QAAQ,6KAAA,CAAA,UAAW,CAAC,MAAM;QAC9D,+FAA+F;QACnG,EACA,OAAO,QAAQ,gCAAgC,KAAI;YAC/C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;IACJ;IACA,uBAAuB,kBAAkB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN;QACrE,+DAA+D;QAC/D,IAAI,QAAQ,KAAK,KAAK,SAAS,IAAI;YAC/B,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,2EAA2E;QAC3E,qCAAqC;QACrC,IAAI,SAAS,IAAI,WAAW,IAAI;QAChC,IAAI,SAAS;QACb,MAAO,QAAQ,EAAG;YACd,0CAA0C;YAC1C,IAAI,WAAW,KAAK,QAAQ,CAAC;YAC7B,IAAI,oBAAoB,AAAG,WAAW,SAAU,IAAK,aAAe,WAAW;YAC/E,IAAI,oBAAoB,SAAS;gBAC7B,gCAAgC;gBAChC,qBAAqB;YACzB,OACK;gBACD,gCAAgC;gBAChC,qBAAqB;YACzB;YACA,MAAM,CAAC,OAAO,GAAgB,qBAAqB;YACnD,MAAM,CAAC,SAAS,EAAE,GAAG,SAAS,GAAG;YACjC,UAAU;YACV;QACJ;QACA,uDAAuD;QACvD,IAAI;YACA,OAAO,MAAM,CAAC,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,QAAQ,6KAAA,CAAA,UAAW,CAAC,SAAS;QACjE,kGAAkG;QACtG,EACA,OAAO,QAAQ,gCAAgC,KAAI;YAC/C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;IACJ;IACA,uBAAuB,iBAAiB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN,EAAU,sBAAsB,EAAE,YAAY,EAAE,KAAK;QACzH,+DAA+D;QAC/D,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI;YAC9B,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI,YAAY,IAAI,WAAW;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,KAAK,QAAQ,CAAC;QAC7C;QACA,IAAI;QACJ,IAAI,2BAA2B,MAAM;YACjC,yCAAyC;YACzC,2DAA2D;YAC3D,wDAAwD;YACxD,0DAA0D;YAC1D,eAAe;YACf,WAAW,6KAAA,CAAA,UAAW,CAAC,aAAa,CAAC,WAAW;QACpD,OACK;YACD,WAAW,uBAAuB,OAAO;QAC7C;QACA,IAAI;YACA,OAAO,MAAM,CAAC,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,WAAW;QACnD,EACA,OAAO,QAAQ,gCAAgC,KAAI;YAC/C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,aAAa,IAAI,CAAC;IACtB;IACA,uBAAuB,kBAAkB,GAAG,SAAU,MAAM,KAAK,GAAN;QACvD,IAAI,SAAS,uBAAuB,kBAAkB,CAAC,MAAM,EAAE;YAC3D,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,OAAO,uBAAuB,kBAAkB,CAAC,MAAM;IAC3D;IACA,uBAAuB,yBAAyB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN,EAAU,WAAW;QACjG,gCAAgC;QAChC,IAAI,QAAQ,OAAO,MAAM;QACzB,MAAO,QAAQ,EAAG;YACd,IAAI,KAAK,SAAS,KAAK,IAAI;gBACvB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,IAAI,mBAAmB,KAAK,QAAQ,CAAC;YACrC,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,KAAK,KAAK,CAAC,mBAAmB;YACtF,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,mBAAmB;YAC3E,SAAS;QACb;QACA,IAAI,UAAU,GAAG;YACb,mCAAmC;YACnC,IAAI,KAAK,SAAS,KAAK,GAAG;gBACtB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,KAAK,QAAQ,CAAC;QAC1E;QACA,+BAA+B;QAC/B,IAAI,aAAa;YACb,0DAA0D;YAC1D,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,IAAI,IAAK;gBAC1C,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK;oBAC1B,IAAI,IAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK;wBACzD,sBAAsB;wBACtB,OAAO,YAAY,CAAC,IAAI;oBAC5B,OACK;wBACD,8DAA8D;wBAC9D,OAAO,SAAS,CAAC,GAAG,OAAO,YAAY,CAAC;oBAC5C;gBACJ;YACJ;QACJ;IACJ;IACA,uBAAuB,oBAAoB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN;QACvE,8BAA8B;QAC9B,MAAO,SAAS,EAAG;YACf,oCAAoC;YACpC,IAAI,KAAK,SAAS,KAAK,IAAI;gBACvB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,IAAI,kBAAkB,KAAK,QAAQ,CAAC;YACpC,IAAI,mBAAmB,MAAM;gBACzB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,KAAK,KAAK,CAAC,kBAAkB;YACrF,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,KAAK,KAAK,CAAC,kBAAkB,MAAM;YAC3F,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,kBAAkB;YAC1E,SAAS;QACb;QACA,IAAI,UAAU,GAAG;YACb,kDAAkD;YAClD,IAAI,KAAK,SAAS,KAAK,GAAG;gBACtB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,IAAI,gBAAgB,KAAK,QAAQ,CAAC;YAClC,IAAI,iBAAiB,KAAK;gBACtB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,KAAK,KAAK,CAAC,gBAAgB;YACnF,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC,gBAAgB;QAC5E,OACK,IAAI,UAAU,GAAG;YAClB,8BAA8B;YAC9B,IAAI,KAAK,SAAS,KAAK,GAAG;gBACtB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,IAAI,YAAY,KAAK,QAAQ,CAAC;YAC9B,IAAI,aAAa,IAAI;gBACjB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,MAAM,CAAC,uBAAuB,kBAAkB,CAAC;QAC5D;IACJ;IACA,uBAAuB,aAAa,GAAG,SAAU,IAAI;QACjD,IAAI,YAAY,KAAK,QAAQ,CAAC;QAC9B,IAAI,CAAC,YAAY,IAAI,MAAM,GAAG;YAC1B,gBAAgB;YAChB,OAAO,YAAY;QACvB;QACA,IAAI,CAAC,YAAY,IAAI,MAAM,MAAM;YAC7B,YAAY;YACZ,IAAI,aAAa,KAAK,QAAQ,CAAC;YAC/B,OAAO,AAAE,CAAC,YAAY,IAAI,KAAK,IAAK,aAAc;QACtD;QACA,IAAI,CAAC,YAAY,IAAI,MAAM,MAAM;YAC7B,cAAc;YACd,IAAI,mBAAmB,KAAK,QAAQ,CAAC;YACrC,OAAO,AAAE,CAAC,YAAY,IAAI,KAAK,KAAM,aAAc;QACvD;QACA,MAAM,IAAI,uKAAA,CAAA,UAAe;IAC7B;IACA;;KAEC,GACD,uBAAuB,kBAAkB,GAAG;IAC5C,uBAAuB,aAAa,GAAG;IACvC,OAAO;AACX;uCACe;CACf,uDAAuD;CACvD,+BAA+B;CAC/B,qCAAqC;CACrC,sEAAsE;CACtE,kFAAkF;CAClF,QAAQ;CACR,2BAA2B;CAC3B,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/QRCodeDecoderMetaData.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Meta-data container for QR Code decoding. Instances of this class may be used to convey information back to the\n * decoding caller. Callers are expected to process this.\n *\n * @see com.google.zxing.common.DecoderResult#getOther()\n */\nvar QRCodeDecoderMetaData = /** @class */ (function () {\n    function QRCodeDecoderMetaData(mirrored) {\n        this.mirrored = mirrored;\n    }\n    /**\n     * @return true if the QR Code was mirrored.\n     */\n    QRCodeDecoderMetaData.prototype.isMirrored = function () {\n        return this.mirrored;\n    };\n    /**\n     * Apply the result points' order correction due to mirroring.\n     *\n     * @param points Array of points to apply mirror correction to.\n     */\n    QRCodeDecoderMetaData.prototype.applyMirroredCorrection = function (points) {\n        if (!this.mirrored || points === null || points.length < 3) {\n            return;\n        }\n        var bottomLeft = points[0];\n        points[0] = points[2];\n        points[2] = bottomLeft;\n        // No need to 'fix' top-left and alignment pattern.\n    };\n    return QRCodeDecoderMetaData;\n}());\nexport default QRCodeDecoderMetaData;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD;;;;;CAKC;;;AACD,IAAI,wBAAuC;IACvC,SAAS,sBAAsB,QAAQ;QACnC,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;KAEC,GACD,sBAAsB,SAAS,CAAC,UAAU,GAAG;QACzC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;;;;KAIC,GACD,sBAAsB,SAAS,CAAC,uBAAuB,GAAG,SAAU,MAAM;QACtE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,QAAQ,OAAO,MAAM,GAAG,GAAG;YACxD;QACJ;QACA,IAAI,aAAa,MAAM,CAAC,EAAE;QAC1B,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;QACrB,MAAM,CAAC,EAAE,GAAG;IACZ,mDAAmD;IACvD;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/decoder/Decoder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ChecksumException from '../../ChecksumException';\nimport BitMatrix from '../../common/BitMatrix';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\nimport QRCodeDecoderMetaData from './QRCodeDecoderMetaData';\n/*import java.util.Map;*/\n/**\n * <p>The main class which implements QR Code decoding -- as opposed to locating and extracting\n * the QR Code from an image.</p>\n *\n * <AUTHOR> Owen\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n        this.rsDecoder = new ReedSolomonDecoder(GenericGF.QR_CODE_FIELD_256);\n    }\n    // public decode(image: boolean[][]): DecoderResult /*throws ChecksumException, FormatException*/ {\n    //   return decode(image, null)\n    // }\n    /**\n     * <p>Convenience method that can decode a QR Code represented as a 2D array of booleans.\n     * \"true\" is taken to mean a black module.</p>\n     *\n     * @param image booleans representing white/black QR Code modules\n     * @param hints decoding hints that should be used to influence decoding\n     * @return text and bytes encoded within the QR Code\n     * @throws FormatException if the QR Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decodeBooleanArray = function (image, hints) {\n        return this.decodeBitMatrix(BitMatrix.parseFromBooleanArray(image), hints);\n    };\n    // public decodeBitMatrix(bits: BitMatrix): DecoderResult /*throws ChecksumException, FormatException*/ {\n    //   return decode(bits, null)\n    // }\n    /**\n     * <p>Decodes a QR Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken to mean a black module.</p>\n     *\n     * @param bits booleans representing white/black QR Code modules\n     * @param hints decoding hints that should be used to influence decoding\n     * @return text and bytes encoded within the QR Code\n     * @throws FormatException if the QR Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decodeBitMatrix = function (bits, hints) {\n        // Construct a parser and read version, error-correction level\n        var parser = new BitMatrixParser(bits);\n        var ex = null;\n        try {\n            return this.decodeBitMatrixParser(parser, hints);\n        }\n        catch (e /*: FormatException, ChecksumException*/) {\n            ex = e;\n        }\n        try {\n            // Revert the bit matrix\n            parser.remask();\n            // Will be attempting a mirrored reading of the version and format info.\n            parser.setMirror(true);\n            // Preemptively read the version.\n            parser.readVersion();\n            // Preemptively read the format information.\n            parser.readFormatInformation();\n            /*\n             * Since we're here, this means we have successfully detected some kind\n             * of version and format information when mirrored. This is a good sign,\n             * that the QR code may be mirrored, and we should try once more with a\n             * mirrored content.\n             */\n            // Prepare for a mirrored reading.\n            parser.mirror();\n            var result = this.decodeBitMatrixParser(parser, hints);\n            // Success! Notify the caller that the code was mirrored.\n            result.setOther(new QRCodeDecoderMetaData(true));\n            return result;\n        }\n        catch (e /*FormatException | ChecksumException*/) {\n            // Throw the exception from the original reading\n            if (ex !== null) {\n                throw ex;\n            }\n            throw e;\n        }\n    };\n    Decoder.prototype.decodeBitMatrixParser = function (parser, hints) {\n        var e_1, _a, e_2, _b;\n        var version = parser.readVersion();\n        var ecLevel = parser.readFormatInformation().getErrorCorrectionLevel();\n        // Read codewords\n        var codewords = parser.readCodewords();\n        // Separate into data blocks\n        var dataBlocks = DataBlock.getDataBlocks(codewords, version, ecLevel);\n        // Count total number of data bytes\n        var totalBytes = 0;\n        try {\n            for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n                var dataBlock = dataBlocks_1_1.value;\n                totalBytes += dataBlock.getNumDataCodewords();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var resultBytes = new Uint8Array(totalBytes);\n        var resultOffset = 0;\n        try {\n            // Error-correct and copy data blocks together into a stream of bytes\n            for (var dataBlocks_2 = __values(dataBlocks), dataBlocks_2_1 = dataBlocks_2.next(); !dataBlocks_2_1.done; dataBlocks_2_1 = dataBlocks_2.next()) {\n                var dataBlock = dataBlocks_2_1.value;\n                var codewordBytes = dataBlock.getCodewords();\n                var numDataCodewords = dataBlock.getNumDataCodewords();\n                this.correctErrors(codewordBytes, numDataCodewords);\n                for (var i = 0; i < numDataCodewords; i++) {\n                    resultBytes[resultOffset++] = codewordBytes[i];\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (dataBlocks_2_1 && !dataBlocks_2_1.done && (_b = dataBlocks_2.return)) _b.call(dataBlocks_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // Decode the contents of that stream of bytes\n        return DecodedBitStreamParser.decode(resultBytes, version, ecLevel, hints);\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place using Reed-Solomon error correction.</p>\n     *\n     * @param codewordBytes data and error correction codewords\n     * @param numDataCodewords number of codewords that are data bytes\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords /*int*/) {\n        // const numCodewords = codewordBytes.length;\n        // First read into an array of ints\n        var codewordsInts = new Int32Array(codewordBytes);\n        // TYPESCRIPTPORT: not realy necessary to transform to ints? could redesign everything to work with unsigned bytes?\n        // const codewordsInts = new Int32Array(numCodewords)\n        // for (let i = 0; i < numCodewords; i++) {\n        //   codewordsInts[i] = codewordBytes[i] & 0xFF\n        // }\n        try {\n            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n        }\n        catch (ignored /*: ReedSolomonException*/) {\n            throw new ChecksumException();\n        }\n        // Copy back into array of bytes -- only need to worry about the bytes that were data\n        // We don't care about errors in the error-correction codewords\n        for (var i = 0; i < numDataCodewords; i++) {\n            codewordBytes[i] = /*(byte) */ codewordsInts[i];\n        }\n    };\n    return Decoder;\n}());\nexport default Decoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,6CAA6C,GAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;AAUA,uBAAuB,GACvB;;;;;CAKC,GACD,IAAI,UAAyB;IACzB,SAAS;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,mMAAA,CAAA,UAAkB,CAAC,0LAAA,CAAA,UAAS,CAAC,iBAAiB;IACvE;IACA,mGAAmG;IACnG,+BAA+B;IAC/B,IAAI;IACJ;;;;;;;;;KASC,GACD,QAAQ,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK,EAAE,KAAK;QACzD,OAAO,IAAI,CAAC,eAAe,CAAC,2KAAA,CAAA,UAAS,CAAC,qBAAqB,CAAC,QAAQ;IACxE;IACA,yGAAyG;IACzG,8BAA8B;IAC9B,IAAI;IACJ;;;;;;;;KAQC,GACD,QAAQ,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI,EAAE,KAAK;QACrD,8DAA8D;QAC9D,IAAI,SAAS,IAAI,4LAAA,CAAA,UAAe,CAAC;QACjC,IAAI,KAAK;QACT,IAAI;YACA,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ;QAC9C,EACA,OAAO,EAAE,sCAAsC,KAAI;YAC/C,KAAK;QACT;QACA,IAAI;YACA,wBAAwB;YACxB,OAAO,MAAM;YACb,wEAAwE;YACxE,OAAO,SAAS,CAAC;YACjB,iCAAiC;YACjC,OAAO,WAAW;YAClB,4CAA4C;YAC5C,OAAO,qBAAqB;YAC5B;;;;;aAKC,GACD,kCAAkC;YAClC,OAAO,MAAM;YACb,IAAI,SAAS,IAAI,CAAC,qBAAqB,CAAC,QAAQ;YAChD,yDAAyD;YACzD,OAAO,QAAQ,CAAC,IAAI,kMAAA,CAAA,UAAqB,CAAC;YAC1C,OAAO;QACX,EACA,OAAO,EAAE,qCAAqC,KAAI;YAC9C,gDAAgD;YAChD,IAAI,OAAO,MAAM;gBACb,MAAM;YACV;YACA,MAAM;QACV;IACJ;IACA,QAAQ,SAAS,CAAC,qBAAqB,GAAG,SAAU,MAAM,EAAE,KAAK;QAC7D,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,UAAU,OAAO,WAAW;QAChC,IAAI,UAAU,OAAO,qBAAqB,GAAG,uBAAuB;QACpE,iBAAiB;QACjB,IAAI,YAAY,OAAO,aAAa;QACpC,4BAA4B;QAC5B,IAAI,aAAa,sLAAA,CAAA,UAAS,CAAC,aAAa,CAAC,WAAW,SAAS;QAC7D,mCAAmC;QACnC,IAAI,aAAa;QACjB,IAAI;YACA,IAAK,IAAI,eAAe,SAAS,aAAa,iBAAiB,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,EAAE,iBAAiB,aAAa,IAAI,GAAI;gBAC5I,IAAI,YAAY,eAAe,KAAK;gBACpC,cAAc,UAAU,mBAAmB;YAC/C;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,kBAAkB,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,aAAa,MAAM,GAAG,GAAG,IAAI,CAAC;YACtF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,cAAc,IAAI,WAAW;QACjC,IAAI,eAAe;QACnB,IAAI;YACA,qEAAqE;YACrE,IAAK,IAAI,eAAe,SAAS,aAAa,iBAAiB,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,EAAE,iBAAiB,aAAa,IAAI,GAAI;gBAC5I,IAAI,YAAY,eAAe,KAAK;gBACpC,IAAI,gBAAgB,UAAU,YAAY;gBAC1C,IAAI,mBAAmB,UAAU,mBAAmB;gBACpD,IAAI,CAAC,aAAa,CAAC,eAAe;gBAClC,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;oBACvC,WAAW,CAAC,eAAe,GAAG,aAAa,CAAC,EAAE;gBAClD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,kBAAkB,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,aAAa,MAAM,GAAG,GAAG,IAAI,CAAC;YACtF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,8CAA8C;QAC9C,OAAO,mMAAA,CAAA,UAAsB,CAAC,MAAM,CAAC,aAAa,SAAS,SAAS;IACxE;IACA;;;;;;;KAOC,GACD,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAU,aAAa,EAAE,iBAAiB,KAAK,GAAN;QACvE,6CAA6C;QAC7C,mCAAmC;QACnC,IAAI,gBAAgB,IAAI,WAAW;QACnC,mHAAmH;QACnH,qDAAqD;QACrD,2CAA2C;QAC3C,+CAA+C;QAC/C,IAAI;QACJ,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,cAAc,MAAM,GAAG;QAChE,EACA,OAAO,QAAQ,wBAAwB,KAAI;YACvC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,qFAAqF;QACrF,+DAA+D;QAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;YACvC,aAAa,CAAC,EAAE,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE;QACnD;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/AlignmentPattern.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates an alignment pattern, which are the smaller square patterns found in\n * all but the simplest QR Codes.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPattern = /** @class */ (function (_super) {\n    __extends(AlignmentPattern, _super);\n    function AlignmentPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/) {\n        var _this = _super.call(this, posX, posY) || this;\n        _this.estimatedModuleSize = estimatedModuleSize;\n        return _this;\n    }\n    /**\n     * <p>Determines if this alignment pattern \"about equals\" an alignment pattern at the stated\n     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n     */\n    AlignmentPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n        }\n        return false;\n    };\n    /**\n     * Combines this object's current estimate of a finder pattern position and module size\n     * with a new estimate. It returns a new {@code FinderPattern} containing an average of the two.\n     */\n    AlignmentPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n        var combinedX = (this.getX() + j) / 2.0;\n        var combinedY = (this.getY() + i) / 2.0;\n        var combinedModuleSize = (this.estimatedModuleSize + newModuleSize) / 2.0;\n        return new AlignmentPattern(combinedX, combinedY, combinedModuleSize);\n    };\n    return AlignmentPattern;\n}(ResultPoint));\nexport default AlignmentPattern;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,8CAA8C,GAC9C;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAGA;;;;;CAKC,GACD,IAAI,mBAAkC,SAAU,MAAM;IAClD,UAAU,kBAAkB;IAC5B,SAAS,iBAAiB,KAAK,OAAO,GAAR,EAAY,KAAK,OAAO,GAAR,EAAY,oBAAoB,OAAO,GAAR;QACzE,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,SAAS,IAAI;QACjD,MAAM,mBAAmB,GAAG;QAC5B,OAAO;IACX;IACA;;;KAGC,GACD,iBAAiB,SAAS,CAAC,WAAW,GAAG,SAAU,WAAW,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR;QACnF,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,cAAc,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,YAAY;YACpF,IAAI,iBAAiB,KAAK,GAAG,CAAC,aAAa,IAAI,CAAC,mBAAmB;YACnE,OAAO,kBAAkB,OAAO,kBAAkB,IAAI,CAAC,mBAAmB;QAC9E;QACA,OAAO;IACX;IACA;;;KAGC,GACD,iBAAiB,SAAS,CAAC,eAAe,GAAG,SAAU,EAAE,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR,EAAY,cAAc,OAAO,GAAR;QAC1F,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI;QACpC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI;QACpC,IAAI,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,GAAG,aAAa,IAAI;QACtE,OAAO,IAAI,iBAAiB,WAAW,WAAW;IACtD;IACA,OAAO;AACX,EAAE,mKAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/AlignmentPatternFinder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AlignmentPattern from './AlignmentPattern';\nimport NotFoundException from '../../NotFoundException';\n/*import java.util.ArrayList;*/\n/*import java.util.List;*/\n/**\n * <p>This class attempts to find alignment patterns in a QR Code. Alignment patterns look like finder\n * patterns but are smaller and appear at regular intervals throughout the image.</p>\n *\n * <p>At the moment this only looks for the bottom-right alignment pattern.</p>\n *\n * <p>This is mostly a simplified copy of {@link FinderPatternFinder}. It is copied,\n * pasted and stripped down here for maximum performance but does unfortunately duplicate\n * some code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPatternFinder = /** @class */ (function () {\n    /**\n     * <p>Creates a finder that will look in a portion of the whole image.</p>\n     *\n     * @param image image to search\n     * @param startX left column from which to start searching\n     * @param startY top row from which to start searching\n     * @param width width of region to search\n     * @param height height of region to search\n     * @param moduleSize estimated module size so far\n     */\n    function AlignmentPatternFinder(image, startX /*int*/, startY /*int*/, width /*int*/, height /*int*/, moduleSize /*float*/, resultPointCallback) {\n        this.image = image;\n        this.startX = startX;\n        this.startY = startY;\n        this.width = width;\n        this.height = height;\n        this.moduleSize = moduleSize;\n        this.resultPointCallback = resultPointCallback;\n        this.possibleCenters = []; // new Array<any>(5))\n        // TYPESCRIPTPORT: array initialization without size as the length is checked below\n        this.crossCheckStateCount = new Int32Array(3);\n    }\n    /**\n     * <p>This method attempts to find the bottom-right alignment pattern in the image. It is a bit messy since\n     * it's pretty performance-critical and so is written to be fast foremost.</p>\n     *\n     * @return {@link AlignmentPattern} if found\n     * @throws NotFoundException if not found\n     */\n    AlignmentPatternFinder.prototype.find = function () {\n        var startX = this.startX;\n        var height = this.height;\n        var width = this.width;\n        var maxJ = startX + width;\n        var middleI = this.startY + (height / 2);\n        // We are looking for black/white/black modules in 1:1:1 ratio\n        // this tracks the number of black/white/black modules seen so far\n        var stateCount = new Int32Array(3);\n        var image = this.image;\n        for (var iGen = 0; iGen < height; iGen++) {\n            // Search from middle outwards\n            var i = middleI + ((iGen & 0x01) === 0 ? Math.floor((iGen + 1) / 2) : -Math.floor((iGen + 1) / 2));\n            stateCount[0] = 0;\n            stateCount[1] = 0;\n            stateCount[2] = 0;\n            var j = startX;\n            // Burn off leading white pixels before anything else; if we start in the middle of\n            // a white run, it doesn't make sense to count its length, since we don't know if the\n            // white run continued to the left of the start point\n            while (j < maxJ && !image.get(j, i)) {\n                j++;\n            }\n            var currentState = 0;\n            while (j < maxJ) {\n                if (image.get(j, i)) {\n                    // Black pixel\n                    if (currentState === 1) { // Counting black pixels\n                        stateCount[1]++;\n                    }\n                    else { // Counting white pixels\n                        if (currentState === 2) { // A winner?\n                            if (this.foundPatternCross(stateCount)) { // Yes\n                                var confirmed = this.handlePossibleCenter(stateCount, i, j);\n                                if (confirmed !== null) {\n                                    return confirmed;\n                                }\n                            }\n                            stateCount[0] = stateCount[2];\n                            stateCount[1] = 1;\n                            stateCount[2] = 0;\n                            currentState = 1;\n                        }\n                        else {\n                            stateCount[++currentState]++;\n                        }\n                    }\n                }\n                else { // White pixel\n                    if (currentState === 1) { // Counting black pixels\n                        currentState++;\n                    }\n                    stateCount[currentState]++;\n                }\n                j++;\n            }\n            if (this.foundPatternCross(stateCount)) {\n                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);\n                if (confirmed !== null) {\n                    return confirmed;\n                }\n            }\n        }\n        // Hmm, nothing we saw was observed and confirmed twice. If we had\n        // any guess at all, return it.\n        if (this.possibleCenters.length !== 0) {\n            return this.possibleCenters[0];\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Given a count of black/white/black pixels just seen and an end position,\n     * figures the location of the center of this black/white/black run.\n     */\n    AlignmentPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n        return (end - stateCount[2]) - stateCount[1] / 2.0;\n    };\n    /**\n     * @param stateCount count of black/white/black pixels just read\n     * @return true iff the proportions of the counts is close enough to the 1/1/1 ratios\n     *         used by alignment patterns to be considered a match\n     */\n    AlignmentPatternFinder.prototype.foundPatternCross = function (stateCount) {\n        var moduleSize = this.moduleSize;\n        var maxVariance = moduleSize / 2.0;\n        for (var i = 0; i < 3; i++) {\n            if (Math.abs(moduleSize - stateCount[i]) >= maxVariance) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * <p>After a horizontal scan finds a potential alignment pattern, this method\n     * \"cross-checks\" by scanning down vertically through the center of the possible\n     * alignment pattern to see if the same proportion is detected.</p>\n     *\n     * @param startI row where an alignment pattern was detected\n     * @param centerJ center of the section that appears to cross an alignment pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     * observed in any reading state, based on the results of the horizontal scan\n     * @return vertical center of alignment pattern, or {@link Float#NaN} if not found\n     */\n    AlignmentPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxI = image.getHeight();\n        var stateCount = this.crossCheckStateCount;\n        stateCount[0] = 0;\n        stateCount[1] = 0;\n        stateCount[2] = 0;\n        // Start counting up from center\n        var i = startI;\n        while (i >= 0 && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i--;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (i < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i >= 0 && !image.get(centerJ, i) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        // Now also count down from center\n        i = startI + 1;\n        while (i < maxI && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i++;\n        }\n        if (i === maxI || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i < maxI && !image.get(centerJ, i) && stateCount[2] <= maxCount) {\n            stateCount[2]++;\n            i++;\n        }\n        if (stateCount[2] > maxCount) {\n            return NaN;\n        }\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n            return NaN;\n        }\n        return this.foundPatternCross(stateCount) ? AlignmentPatternFinder.centerFromEnd(stateCount, i) : NaN;\n    };\n    /**\n     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n     * cross check with a vertical scan, and if successful, will see if this pattern had been\n     * found on a previous horizontal scan. If so, we consider it confirmed and conclude we have\n     * found the alignment pattern.</p>\n     *\n     * @param stateCount reading state module counts from horizontal scan\n     * @param i row where alignment pattern may be found\n     * @param j end of possible alignment pattern in row\n     * @return {@link AlignmentPattern} if we have found the same pattern twice, or null if not\n     */\n    AlignmentPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/) {\n        var e_1, _a;\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n        var centerJ = AlignmentPatternFinder.centerFromEnd(stateCount, j);\n        var centerI = this.crossCheckVertical(i, /*(int) */ centerJ, 2 * stateCount[1], stateCountTotal);\n        if (!isNaN(centerI)) {\n            var estimatedModuleSize = (stateCount[0] + stateCount[1] + stateCount[2]) / 3.0;\n            try {\n                for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var center = _c.value;\n                    // Look for about the same center and module size:\n                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n                        return center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Hadn't found this before; save it\n            var point = new AlignmentPattern(centerJ, centerI, estimatedModuleSize);\n            this.possibleCenters.push(point);\n            if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n                this.resultPointCallback.foundPossibleResultPoint(point);\n            }\n        }\n        return null;\n    };\n    return AlignmentPatternFinder;\n}());\nexport default AlignmentPatternFinder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD;AACA;AAZA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAGA,6BAA6B,GAC7B,wBAAwB,GACxB;;;;;;;;;;;;;CAaC,GACD,IAAI,yBAAwC;IACxC;;;;;;;;;KASC,GACD,SAAS,uBAAuB,KAAK,EAAE,OAAO,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,WAAW,OAAO,GAAR,EAAY,mBAAmB;QAC3I,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG,EAAE,EAAE,qBAAqB;QAChD,mFAAmF;QACnF,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW;IAC/C;IACA;;;;;;KAMC,GACD,uBAAuB,SAAS,CAAC,IAAI,GAAG;QACpC,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,SAAS;QACpB,IAAI,UAAU,IAAI,CAAC,MAAM,GAAI,SAAS;QACtC,8DAA8D;QAC9D,kEAAkE;QAClE,IAAI,aAAa,IAAI,WAAW;QAChC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,OAAO,GAAG,OAAO,QAAQ,OAAQ;YACtC,8BAA8B;YAC9B,IAAI,IAAI,UAAU,CAAC,CAAC,OAAO,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;YACjG,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,IAAI,IAAI;YACR,mFAAmF;YACnF,qFAAqF;YACrF,qDAAqD;YACrD,MAAO,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,GAAI;gBACjC;YACJ;YACA,IAAI,eAAe;YACnB,MAAO,IAAI,KAAM;gBACb,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI;oBACjB,cAAc;oBACd,IAAI,iBAAiB,GAAG;wBACpB,UAAU,CAAC,EAAE;oBACjB,OACK;wBACD,IAAI,iBAAiB,GAAG;4BACpB,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa;gCACpC,IAAI,YAAY,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG;gCACzD,IAAI,cAAc,MAAM;oCACpB,OAAO;gCACX;4BACJ;4BACA,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;4BAC7B,UAAU,CAAC,EAAE,GAAG;4BAChB,UAAU,CAAC,EAAE,GAAG;4BAChB,eAAe;wBACnB,OACK;4BACD,UAAU,CAAC,EAAE,aAAa;wBAC9B;oBACJ;gBACJ,OACK;oBACD,IAAI,iBAAiB,GAAG;wBACpB;oBACJ;oBACA,UAAU,CAAC,aAAa;gBAC5B;gBACA;YACJ;YACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa;gBACpC,IAAI,YAAY,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG;gBACzD,IAAI,cAAc,MAAM;oBACpB,OAAO;gBACX;YACJ;QACJ;QACA,kEAAkE;QAClE,+BAA+B;QAC/B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,GAAG;YACnC,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE;QAClC;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA;;;KAGC,GACD,uBAAuB,aAAa,GAAG,SAAU,UAAU,EAAE,IAAI,KAAK,GAAN;QAC5D,OAAO,AAAC,MAAM,UAAU,CAAC,EAAE,GAAI,UAAU,CAAC,EAAE,GAAG;IACnD;IACA;;;;KAIC,GACD,uBAAuB,SAAS,CAAC,iBAAiB,GAAG,SAAU,UAAU;QACrE,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,cAAc,aAAa;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,KAAK,GAAG,CAAC,aAAa,UAAU,CAAC,EAAE,KAAK,aAAa;gBACrD,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;KAUC,GACD,uBAAuB,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,SAAS,KAAK,GAAN,EAAU,wBAAwB,KAAK,GAAN;QACtI,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,MAAM,SAAS;QAC1B,IAAI,aAAa,IAAI,CAAC,oBAAoB;QAC1C,UAAU,CAAC,EAAE,GAAG;QAChB,UAAU,CAAC,EAAE,GAAG;QAChB,UAAU,CAAC,EAAE,GAAG;QAChB,gCAAgC;QAChC,IAAI,IAAI;QACR,MAAO,KAAK,KAAK,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YACjE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,iEAAiE;QACjE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,UAAU;YACnC,OAAO;QACX;QACA,MAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YAClE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,GAAG,UAAU;YAC1B,OAAO;QACX;QACA,kCAAkC;QAClC,IAAI,SAAS;QACb,MAAO,IAAI,QAAQ,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YACnE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,MAAM,QAAQ,UAAU,CAAC,EAAE,GAAG,UAAU;YACxC,OAAO;QACX;QACA,MAAO,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YACpE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,GAAG,UAAU;YAC1B,OAAO;QACX;QACA,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,IAAI,IAAI,KAAK,GAAG,CAAC,kBAAkB,4BAA4B,IAAI,yBAAyB;YACxF,OAAO;QACX;QACA,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,uBAAuB,aAAa,CAAC,YAAY,KAAK;IACtG;IACA;;;;;;;;;;KAUC,GACD,uBAAuB,SAAS,CAAC,oBAAoB,GAAG,SAAU,UAAU,EAAE,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACtF,IAAI,KAAK;QACT,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,IAAI,UAAU,uBAAuB,aAAa,CAAC,YAAY;QAC/D,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,QAAQ,GAAG,SAAS,IAAI,UAAU,CAAC,EAAE,EAAE;QAChF,IAAI,CAAC,MAAM,UAAU;YACjB,IAAI,sBAAsB,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI;YAC5E,IAAI;gBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;oBACpF,IAAI,SAAS,GAAG,KAAK;oBACrB,kDAAkD;oBAClD,IAAI,OAAO,WAAW,CAAC,qBAAqB,SAAS,UAAU;wBAC3D,OAAO,OAAO,eAAe,CAAC,SAAS,SAAS;oBACpD;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;gBACpD,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,oCAAoC;YACpC,IAAI,QAAQ,IAAI,8LAAA,CAAA,UAAgB,CAAC,SAAS,SAAS;YACnD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1B,IAAI,IAAI,CAAC,mBAAmB,KAAK,QAAQ,IAAI,CAAC,mBAAmB,KAAK,WAAW;gBAC7E,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;YACtD;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/FinderPattern.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates a finder pattern, which are the three square patterns found in\n * the corners of QR Codes. It also encapsulates a count of similar finder patterns,\n * as a convenience to the finder's bookkeeping.</p>\n *\n * <AUTHOR> Owen\n */\nvar FinderPattern = /** @class */ (function (_super) {\n    __extends(FinderPattern, _super);\n    // FinderPattern(posX: number/*float*/, posY: number/*float*/, estimatedModuleSize: number/*float*/) {\n    //   this(posX, posY, estimatedModuleSize, 1)\n    // }\n    function FinderPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/, count /*int*/) {\n        var _this = _super.call(this, posX, posY) || this;\n        _this.estimatedModuleSize = estimatedModuleSize;\n        _this.count = count;\n        if (undefined === count) {\n            _this.count = 1;\n        }\n        return _this;\n    }\n    FinderPattern.prototype.getEstimatedModuleSize = function () {\n        return this.estimatedModuleSize;\n    };\n    FinderPattern.prototype.getCount = function () {\n        return this.count;\n    };\n    /*\n    void incrementCount() {\n      this.count++\n    }\n     */\n    /**\n     * <p>Determines if this finder pattern \"about equals\" a finder pattern at the stated\n     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n     */\n    FinderPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n        }\n        return false;\n    };\n    /**\n     * Combines this object's current estimate of a finder pattern position and module size\n     * with a new estimate. It returns a new {@code FinderPattern} containing a weighted average\n     * based on count.\n     */\n    FinderPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n        var combinedCount = this.count + 1;\n        var combinedX = (this.count * this.getX() + j) / combinedCount;\n        var combinedY = (this.count * this.getY() + i) / combinedCount;\n        var combinedModuleSize = (this.count * this.estimatedModuleSize + newModuleSize) / combinedCount;\n        return new FinderPattern(combinedX, combinedY, combinedModuleSize, combinedCount);\n    };\n    return FinderPattern;\n}(ResultPoint));\nexport default FinderPattern;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,8CAA8C,GAC9C;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAGA;;;;;;CAMC,GACD,IAAI,gBAA+B,SAAU,MAAM;IAC/C,UAAU,eAAe;IACzB,sGAAsG;IACtG,6CAA6C;IAC7C,IAAI;IACJ,SAAS,cAAc,KAAK,OAAO,GAAR,EAAY,KAAK,OAAO,GAAR,EAAY,oBAAoB,OAAO,GAAR,EAAY,MAAM,KAAK,GAAN;QACvF,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,SAAS,IAAI;QACjD,MAAM,mBAAmB,GAAG;QAC5B,MAAM,KAAK,GAAG;QACd,IAAI,cAAc,OAAO;YACrB,MAAM,KAAK,GAAG;QAClB;QACA,OAAO;IACX;IACA,cAAc,SAAS,CAAC,sBAAsB,GAAG;QAC7C,OAAO,IAAI,CAAC,mBAAmB;IACnC;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;;;KAIC,GACD;;;KAGC,GACD,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,WAAW,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR;QAChF,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,cAAc,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,YAAY;YACpF,IAAI,iBAAiB,KAAK,GAAG,CAAC,aAAa,IAAI,CAAC,mBAAmB;YACnE,OAAO,kBAAkB,OAAO,kBAAkB,IAAI,CAAC,mBAAmB;QAC9E;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,EAAE,OAAO,GAAR,EAAY,EAAE,OAAO,GAAR,EAAY,cAAc,OAAO,GAAR;QACvF,IAAI,gBAAgB,IAAI,CAAC,KAAK,GAAG;QACjC,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI;QACjD,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI;QACjD,IAAI,qBAAqB,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,GAAG,aAAa,IAAI;QACnF,OAAO,IAAI,cAAc,WAAW,WAAW,oBAAoB;IACvE;IACA,OAAO;AACX,EAAE,mKAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/FinderPatternInfo.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates information about finder patterns in an image, including the location of\n * the three finder patterns, and their estimated module size.</p>\n *\n * <AUTHOR>\n */\nvar FinderPatternInfo = /** @class */ (function () {\n    function FinderPatternInfo(patternCenters) {\n        this.bottomLeft = patternCenters[0];\n        this.topLeft = patternCenters[1];\n        this.topRight = patternCenters[2];\n    }\n    FinderPatternInfo.prototype.getBottomLeft = function () {\n        return this.bottomLeft;\n    };\n    FinderPatternInfo.prototype.getTopLeft = function () {\n        return this.topLeft;\n    };\n    FinderPatternInfo.prototype.getTopRight = function () {\n        return this.topRight;\n    };\n    return FinderPatternInfo;\n}());\nexport default FinderPatternInfo;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD;;;;;CAKC;;;AACD,IAAI,oBAAmC;IACnC,SAAS,kBAAkB,cAAc;QACrC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE;QACnC,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,EAAE;QAChC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,EAAE;IACrC;IACA,kBAAkB,SAAS,CAAC,aAAa,GAAG;QACxC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,kBAAkB,SAAS,CAAC,UAAU,GAAG;QACrC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,kBAAkB,SAAS,CAAC,WAAW,GAAG;QACtC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/FinderPatternFinder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.detector {*/\nimport DecodeHintType from '../../DecodeHintType';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport FinderPatternInfo from './FinderPatternInfo';\nimport NotFoundException from '../../NotFoundException';\n/*import java.io.Serializable;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collections;*/\n/*import java.util.Comparator;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>This class attempts to find finder patterns in a QR Code. Finder patterns are the square\n * markers at three corners of a QR Code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.\n *\n * <AUTHOR> Owen\n */\nvar FinderPatternFinder = /** @class */ (function () {\n    /**\n     * <p>Creates a finder that will search the image for three finder patterns.</p>\n     *\n     * @param image image to search\n     */\n    // public constructor(image: BitMatrix) {\n    //   this(image, null)\n    // }\n    function FinderPatternFinder(image, resultPointCallback) {\n        this.image = image;\n        this.resultPointCallback = resultPointCallback;\n        this.possibleCenters = [];\n        this.crossCheckStateCount = new Int32Array(5);\n        this.resultPointCallback = resultPointCallback;\n    }\n    FinderPatternFinder.prototype.getImage = function () {\n        return this.image;\n    };\n    FinderPatternFinder.prototype.getPossibleCenters = function () {\n        return this.possibleCenters;\n    };\n    FinderPatternFinder.prototype.find = function (hints) {\n        var tryHarder = (hints !== null && hints !== undefined) && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n        var pureBarcode = (hints !== null && hints !== undefined) && undefined !== hints.get(DecodeHintType.PURE_BARCODE);\n        var image = this.image;\n        var maxI = image.getHeight();\n        var maxJ = image.getWidth();\n        // We are looking for black/white/black/white/black modules in\n        // 1:1:3:1:1 ratio; this tracks the number of such modules seen so far\n        // Let's assume that the maximum version QR Code we support takes up 1/4 the height of the\n        // image, and then account for the center being 3 modules in size. This gives the smallest\n        // number of pixels the center could be, so skip this often. When trying harder, look for all\n        // QR versions regardless of how dense they are.\n        var iSkip = Math.floor((3 * maxI) / (4 * FinderPatternFinder.MAX_MODULES));\n        if (iSkip < FinderPatternFinder.MIN_SKIP || tryHarder) {\n            iSkip = FinderPatternFinder.MIN_SKIP;\n        }\n        var done = false;\n        var stateCount = new Int32Array(5);\n        for (var i = iSkip - 1; i < maxI && !done; i += iSkip) {\n            // Get a row of black/white values\n            stateCount[0] = 0;\n            stateCount[1] = 0;\n            stateCount[2] = 0;\n            stateCount[3] = 0;\n            stateCount[4] = 0;\n            var currentState = 0;\n            for (var j = 0; j < maxJ; j++) {\n                if (image.get(j, i)) {\n                    // Black pixel\n                    if ((currentState & 1) === 1) { // Counting white pixels\n                        currentState++;\n                    }\n                    stateCount[currentState]++;\n                }\n                else { // White pixel\n                    if ((currentState & 1) === 0) { // Counting black pixels\n                        if (currentState === 4) { // A winner?\n                            if (FinderPatternFinder.foundPatternCross(stateCount)) { // Yes\n                                var confirmed = this.handlePossibleCenter(stateCount, i, j, pureBarcode);\n                                if (confirmed === true) {\n                                    // Start examining every other line. Checking each line turned out to be too\n                                    // expensive and didn't improve performance.\n                                    iSkip = 2;\n                                    if (this.hasSkipped === true) {\n                                        done = this.haveMultiplyConfirmedCenters();\n                                    }\n                                    else {\n                                        var rowSkip = this.findRowSkip();\n                                        if (rowSkip > stateCount[2]) {\n                                            // Skip rows between row of lower confirmed center\n                                            // and top of presumed third confirmed center\n                                            // but back up a bit to get a full chance of detecting\n                                            // it, entire width of center of finder pattern\n                                            // Skip by rowSkip, but back off by stateCount[2] (size of last center\n                                            // of pattern we saw) to be conservative, and also back off by iSkip which\n                                            // is about to be re-added\n                                            i += rowSkip - stateCount[2] - iSkip;\n                                            j = maxJ - 1;\n                                        }\n                                    }\n                                }\n                                else {\n                                    stateCount[0] = stateCount[2];\n                                    stateCount[1] = stateCount[3];\n                                    stateCount[2] = stateCount[4];\n                                    stateCount[3] = 1;\n                                    stateCount[4] = 0;\n                                    currentState = 3;\n                                    continue;\n                                }\n                                // Clear state to start looking again\n                                currentState = 0;\n                                stateCount[0] = 0;\n                                stateCount[1] = 0;\n                                stateCount[2] = 0;\n                                stateCount[3] = 0;\n                                stateCount[4] = 0;\n                            }\n                            else { // No, shift counts back by two\n                                stateCount[0] = stateCount[2];\n                                stateCount[1] = stateCount[3];\n                                stateCount[2] = stateCount[4];\n                                stateCount[3] = 1;\n                                stateCount[4] = 0;\n                                currentState = 3;\n                            }\n                        }\n                        else {\n                            stateCount[++currentState]++;\n                        }\n                    }\n                    else { // Counting white pixels\n                        stateCount[currentState]++;\n                    }\n                }\n            }\n            if (FinderPatternFinder.foundPatternCross(stateCount)) {\n                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ, pureBarcode);\n                if (confirmed === true) {\n                    iSkip = stateCount[0];\n                    if (this.hasSkipped) {\n                        // Found a third one\n                        done = this.haveMultiplyConfirmedCenters();\n                    }\n                }\n            }\n        }\n        var patternInfo = this.selectBestPatterns();\n        ResultPoint.orderBestPatterns(patternInfo);\n        return new FinderPatternInfo(patternInfo);\n    };\n    /**\n     * Given a count of black/white/black/white/black pixels just seen and an end position,\n     * figures the location of the center of this run.\n     */\n    FinderPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n        return (end - stateCount[4] - stateCount[3]) - stateCount[2] / 2.0;\n    };\n    /**\n     * @param stateCount count of black/white/black/white/black pixels just read\n     * @return true iff the proportions of the counts is close enough to the 1/1/3/1/1 ratios\n     *         used by finder patterns to be considered a match\n     */\n    FinderPatternFinder.foundPatternCross = function (stateCount) {\n        var totalModuleSize = 0;\n        for (var i = 0; i < 5; i++) {\n            var count = stateCount[i];\n            if (count === 0) {\n                return false;\n            }\n            totalModuleSize += count;\n        }\n        if (totalModuleSize < 7) {\n            return false;\n        }\n        var moduleSize = totalModuleSize / 7.0;\n        var maxVariance = moduleSize / 2.0;\n        // Allow less than 50% variance from 1-1-3-1-1 proportions\n        return Math.abs(moduleSize - stateCount[0]) < maxVariance &&\n            Math.abs(moduleSize - stateCount[1]) < maxVariance &&\n            Math.abs(3.0 * moduleSize - stateCount[2]) < 3 * maxVariance &&\n            Math.abs(moduleSize - stateCount[3]) < maxVariance &&\n            Math.abs(moduleSize - stateCount[4]) < maxVariance;\n    };\n    FinderPatternFinder.prototype.getCrossCheckStateCount = function () {\n        var crossCheckStateCount = this.crossCheckStateCount;\n        crossCheckStateCount[0] = 0;\n        crossCheckStateCount[1] = 0;\n        crossCheckStateCount[2] = 0;\n        crossCheckStateCount[3] = 0;\n        crossCheckStateCount[4] = 0;\n        return crossCheckStateCount;\n    };\n    /**\n     * After a vertical and horizontal scan finds a potential finder pattern, this method\n     * \"cross-cross-cross-checks\" by scanning down diagonally through the center of the possible\n     * finder pattern to see if the same proportion is detected.\n     *\n     * @param startI row where a finder pattern was detected\n     * @param centerJ center of the section that appears to cross a finder pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     *  observed in any reading state, based on the results of the horizontal scan\n     * @param originalStateCountTotal The original state count total.\n     * @return true if proportions are withing expected limits\n     */\n    FinderPatternFinder.prototype.crossCheckDiagonal = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var stateCount = this.getCrossCheckStateCount();\n        // Start counting up, left from center finding black center mass\n        var i = 0;\n        var image = this.image;\n        while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i)) {\n            stateCount[2]++;\n            i++;\n        }\n        if (startI < i || centerJ < i) {\n            return false;\n        }\n        // Continue up, left finding white space\n        while (startI >= i && centerJ >= i && !image.get(centerJ - i, startI - i) &&\n            stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i++;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (startI < i || centerJ < i || stateCount[1] > maxCount) {\n            return false;\n        }\n        // Continue up, left finding black border\n        while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i) &&\n            stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i++;\n        }\n        if (stateCount[0] > maxCount) {\n            return false;\n        }\n        var maxI = image.getHeight();\n        var maxJ = image.getWidth();\n        // Now also count down, right from center\n        i = 1;\n        while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i)) {\n            stateCount[2]++;\n            i++;\n        }\n        // Ran off the edge?\n        if (startI + i >= maxI || centerJ + i >= maxJ) {\n            return false;\n        }\n        while (startI + i < maxI && centerJ + i < maxJ && !image.get(centerJ + i, startI + i) &&\n            stateCount[3] < maxCount) {\n            stateCount[3]++;\n            i++;\n        }\n        if (startI + i >= maxI || centerJ + i >= maxJ || stateCount[3] >= maxCount) {\n            return false;\n        }\n        while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i) &&\n            stateCount[4] < maxCount) {\n            stateCount[4]++;\n            i++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return false;\n        }\n        // If we found a finder-pattern-like section, but its size is more than 100% different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n        return Math.abs(stateCountTotal - originalStateCountTotal) < 2 * originalStateCountTotal &&\n            FinderPatternFinder.foundPatternCross(stateCount);\n    };\n    /**\n     * <p>After a horizontal scan finds a potential finder pattern, this method\n     * \"cross-checks\" by scanning down vertically through the center of the possible\n     * finder pattern to see if the same proportion is detected.</p>\n     *\n     * @param startI row where a finder pattern was detected\n     * @param centerJ center of the section that appears to cross a finder pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     * observed in any reading state, based on the results of the horizontal scan\n     * @return vertical center of finder pattern, or {@link Float#NaN} if not found\n     */\n    FinderPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxI = image.getHeight();\n        var stateCount = this.getCrossCheckStateCount();\n        // Start counting up from center\n        var i = startI;\n        while (i >= 0 && image.get(centerJ, i)) {\n            stateCount[2]++;\n            i--;\n        }\n        if (i < 0) {\n            return NaN;\n        }\n        while (i >= 0 && !image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i--;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (i < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i >= 0 && image.get(centerJ, i) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        // Now also count down from center\n        i = startI + 1;\n        while (i < maxI && image.get(centerJ, i)) {\n            stateCount[2]++;\n            i++;\n        }\n        if (i === maxI) {\n            return NaN;\n        }\n        while (i < maxI && !image.get(centerJ, i) && stateCount[3] < maxCount) {\n            stateCount[3]++;\n            i++;\n        }\n        if (i === maxI || stateCount[3] >= maxCount) {\n            return NaN;\n        }\n        while (i < maxI && image.get(centerJ, i) && stateCount[4] < maxCount) {\n            stateCount[4]++;\n            i++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return NaN;\n        }\n        // If we found a finder-pattern-like section, but its size is more than 40% different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n            return NaN;\n        }\n        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, i) : NaN;\n    };\n    /**\n     * <p>Like {@link #crossCheckVertical(int, int, int, int)}, and in fact is basically identical,\n     * except it reads horizontally instead of vertically. This is used to cross-cross\n     * check a vertical cross check and locate the real center of the alignment pattern.</p>\n     */\n    FinderPatternFinder.prototype.crossCheckHorizontal = function (startJ /*int*/, centerI /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxJ = image.getWidth();\n        var stateCount = this.getCrossCheckStateCount();\n        var j = startJ;\n        while (j >= 0 && image.get(j, centerI)) {\n            stateCount[2]++;\n            j--;\n        }\n        if (j < 0) {\n            return NaN;\n        }\n        while (j >= 0 && !image.get(j, centerI) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            j--;\n        }\n        if (j < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (j >= 0 && image.get(j, centerI) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            j--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        j = startJ + 1;\n        while (j < maxJ && image.get(j, centerI)) {\n            stateCount[2]++;\n            j++;\n        }\n        if (j === maxJ) {\n            return NaN;\n        }\n        while (j < maxJ && !image.get(j, centerI) && stateCount[3] < maxCount) {\n            stateCount[3]++;\n            j++;\n        }\n        if (j === maxJ || stateCount[3] >= maxCount) {\n            return NaN;\n        }\n        while (j < maxJ && image.get(j, centerI) && stateCount[4] < maxCount) {\n            stateCount[4]++;\n            j++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return NaN;\n        }\n        // If we found a finder-pattern-like section, but its size is significantly different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= originalStateCountTotal) {\n            return NaN;\n        }\n        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, j) : NaN;\n    };\n    /**\n     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n     * cross check with a vertical scan, and if successful, will, ah, cross-cross-check\n     * with another horizontal scan. This is needed primarily to locate the real horizontal\n     * center of the pattern in cases of extreme skew.\n     * And then we cross-cross-cross check with another diagonal scan.</p>\n     *\n     * <p>If that succeeds the finder pattern location is added to a list that tracks\n     * the number of times each location has been nearly-matched as a finder pattern.\n     * Each additional find is more evidence that the location is in fact a finder\n     * pattern center\n     *\n     * @param stateCount reading state module counts from horizontal scan\n     * @param i row where finder pattern may be found\n     * @param j end of possible finder pattern in row\n     * @param pureBarcode true if in \"pure barcode\" mode\n     * @return true if a finder pattern candidate was found this time\n     */\n    FinderPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/, pureBarcode) {\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        var centerJ = FinderPatternFinder.centerFromEnd(stateCount, j);\n        var centerI = this.crossCheckVertical(i, /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal);\n        if (!isNaN(centerI)) {\n            // Re-cross check\n            centerJ = this.crossCheckHorizontal(/*(int) */ Math.floor(centerJ), /*(int) */ Math.floor(centerI), stateCount[2], stateCountTotal);\n            if (!isNaN(centerJ) &&\n                (!pureBarcode || this.crossCheckDiagonal(/*(int) */ Math.floor(centerI), /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal))) {\n                var estimatedModuleSize = stateCountTotal / 7.0;\n                var found = false;\n                var possibleCenters = this.possibleCenters;\n                for (var index = 0, length_1 = possibleCenters.length; index < length_1; index++) {\n                    var center = possibleCenters[index];\n                    // Look for about the same center and module size:\n                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n                        possibleCenters[index] = center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    var point = new FinderPattern(centerJ, centerI, estimatedModuleSize);\n                    possibleCenters.push(point);\n                    if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n                        this.resultPointCallback.foundPossibleResultPoint(point);\n                    }\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    /**\n     * @return number of rows we could safely skip during scanning, based on the first\n     *         two finder patterns that have been located. In some cases their position will\n     *         allow us to infer that the third pattern must lie below a certain point farther\n     *         down in the image.\n     */\n    FinderPatternFinder.prototype.findRowSkip = function () {\n        var e_1, _a;\n        var max = this.possibleCenters.length;\n        if (max <= 1) {\n            return 0;\n        }\n        var firstConfirmedCenter = null;\n        try {\n            for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var center = _c.value;\n                if (center.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n                    if (firstConfirmedCenter == null) {\n                        firstConfirmedCenter = center;\n                    }\n                    else {\n                        // We have two confirmed centers\n                        // How far down can we skip before resuming looking for the next\n                        // pattern? In the worst case, only the difference between the\n                        // difference in the x / y coordinates of the two centers.\n                        // This is the case where you find top left last.\n                        this.hasSkipped = true;\n                        return /*(int) */ Math.floor((Math.abs(firstConfirmedCenter.getX() - center.getX()) -\n                            Math.abs(firstConfirmedCenter.getY() - center.getY())) / 2);\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return 0;\n    };\n    /**\n     * @return true iff we have found at least 3 finder patterns that have been detected\n     *         at least {@link #CENTER_QUORUM} times each, and, the estimated module size of the\n     *         candidates is \"pretty similar\"\n     */\n    FinderPatternFinder.prototype.haveMultiplyConfirmedCenters = function () {\n        var e_2, _a, e_3, _b;\n        var confirmedCount = 0;\n        var totalModuleSize = 0.0;\n        var max = this.possibleCenters.length;\n        try {\n            for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var pattern = _d.value;\n                if (pattern.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n                    confirmedCount++;\n                    totalModuleSize += pattern.getEstimatedModuleSize();\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        if (confirmedCount < 3) {\n            return false;\n        }\n        // OK, we have at least 3 confirmed centers, but, it's possible that one is a \"false positive\"\n        // and that we need to keep looking. We detect this by asking if the estimated module sizes\n        // vary too much. We arbitrarily say that when the total deviation from average exceeds\n        // 5% of the total module size estimates, it's too much.\n        var average = totalModuleSize / max;\n        var totalDeviation = 0.0;\n        try {\n            for (var _e = __values(this.possibleCenters), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var pattern = _f.value;\n                totalDeviation += Math.abs(pattern.getEstimatedModuleSize() - average);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return totalDeviation <= 0.05 * totalModuleSize;\n    };\n    /**\n     * @return the 3 best {@link FinderPattern}s from our list of candidates. The \"best\" are\n     *         those that have been detected at least {@link #CENTER_QUORUM} times, and whose module\n     *         size differs from the average among those patterns the least\n     * @throws NotFoundException if 3 such finder patterns do not exist\n     */\n    FinderPatternFinder.prototype.selectBestPatterns = function () {\n        var e_4, _a, e_5, _b;\n        var startSize = this.possibleCenters.length;\n        if (startSize < 3) {\n            // Couldn't find enough finder patterns\n            throw new NotFoundException();\n        }\n        var possibleCenters = this.possibleCenters;\n        var average;\n        // Filter outlier possibilities whose module size is too different\n        if (startSize > 3) {\n            // But we can only afford to do so if we have at least 4 possibilities to choose from\n            var totalModuleSize = 0.0;\n            var square = 0.0;\n            try {\n                for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var center = _d.value;\n                    var size = center.getEstimatedModuleSize();\n                    totalModuleSize += size;\n                    square += size * size;\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n            average = totalModuleSize / startSize;\n            var stdDev = Math.sqrt(square / startSize - average * average);\n            possibleCenters.sort(\n            /**\n             * <p>Orders by furthest from average</p>\n             */\n            // FurthestFromAverageComparator implements Comparator<FinderPattern>\n            function (center1, center2) {\n                var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n                var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n                return dA < dB ? -1 : dA > dB ? 1 : 0;\n            });\n            var limit = Math.max(0.2 * average, stdDev);\n            for (var i = 0; i < possibleCenters.length && possibleCenters.length > 3; i++) {\n                var pattern = possibleCenters[i];\n                if (Math.abs(pattern.getEstimatedModuleSize() - average) > limit) {\n                    possibleCenters.splice(i, 1);\n                    i--;\n                }\n            }\n        }\n        if (possibleCenters.length > 3) {\n            // Throw away all but those first size candidate points we found.\n            var totalModuleSize = 0.0;\n            try {\n                for (var possibleCenters_1 = __values(possibleCenters), possibleCenters_1_1 = possibleCenters_1.next(); !possibleCenters_1_1.done; possibleCenters_1_1 = possibleCenters_1.next()) {\n                    var possibleCenter = possibleCenters_1_1.value;\n                    totalModuleSize += possibleCenter.getEstimatedModuleSize();\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (possibleCenters_1_1 && !possibleCenters_1_1.done && (_b = possibleCenters_1.return)) _b.call(possibleCenters_1);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            average = totalModuleSize / possibleCenters.length;\n            possibleCenters.sort(\n            /**\n             * <p>Orders by {@link FinderPattern#getCount()}, descending.</p>\n             */\n            // CenterComparator implements Comparator<FinderPattern>\n            function (center1, center2) {\n                if (center2.getCount() === center1.getCount()) {\n                    var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n                    var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n                    return dA < dB ? 1 : dA > dB ? -1 : 0;\n                }\n                else {\n                    return center2.getCount() - center1.getCount();\n                }\n            });\n            possibleCenters.splice(3); // this is not realy necessary as we only return first 3 anyway\n        }\n        return [\n            possibleCenters[0],\n            possibleCenters[1],\n            possibleCenters[2]\n        ];\n    };\n    FinderPatternFinder.CENTER_QUORUM = 2;\n    FinderPatternFinder.MIN_SKIP = 3; // 1 pixel/module times 3 modules/center\n    FinderPatternFinder.MAX_MODULES = 57; // support up to version 10 for mobile clients\n    return FinderPatternFinder;\n}());\nexport default FinderPatternFinder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,8CAA8C,GAC9C;AACA;AACA;AACA;AACA;AAhBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;AAOA,8BAA8B,GAC9B,6BAA6B,GAC7B,+BAA+B,GAC/B,8BAA8B,GAC9B,wBAAwB,GACxB,uBAAuB,GACvB;;;;;;;CAOC,GACD,IAAI,sBAAqC;IACrC;;;;KAIC,GACD,yCAAyC;IACzC,sBAAsB;IACtB,IAAI;IACJ,SAAS,oBAAoB,KAAK,EAAE,mBAAmB;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW;QAC3C,IAAI,CAAC,mBAAmB,GAAG;IAC/B;IACA,oBAAoB,SAAS,CAAC,QAAQ,GAAG;QACrC,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,oBAAoB,SAAS,CAAC,kBAAkB,GAAG;QAC/C,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,oBAAoB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK;QAChD,IAAI,YAAY,AAAC,UAAU,QAAQ,UAAU,aAAc,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU;QAC5G,IAAI,cAAc,AAAC,UAAU,QAAQ,UAAU,aAAc,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,YAAY;QAChH,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,MAAM,SAAS;QAC1B,IAAI,OAAO,MAAM,QAAQ;QACzB,8DAA8D;QAC9D,sEAAsE;QACtE,0FAA0F;QAC1F,0FAA0F;QAC1F,6FAA6F;QAC7F,gDAAgD;QAChD,IAAI,QAAQ,KAAK,KAAK,CAAC,AAAC,IAAI,OAAQ,CAAC,IAAI,oBAAoB,WAAW;QACxE,IAAI,QAAQ,oBAAoB,QAAQ,IAAI,WAAW;YACnD,QAAQ,oBAAoB,QAAQ;QACxC;QACA,IAAI,OAAO;QACX,IAAI,aAAa,IAAI,WAAW;QAChC,IAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAO;YACnD,kCAAkC;YAClC,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,UAAU,CAAC,EAAE,GAAG;YAChB,IAAI,eAAe;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC3B,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI;oBACjB,cAAc;oBACd,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG;wBAC1B;oBACJ;oBACA,UAAU,CAAC,aAAa;gBAC5B,OACK;oBACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG;wBAC1B,IAAI,iBAAiB,GAAG;4BACpB,IAAI,oBAAoB,iBAAiB,CAAC,aAAa;gCACnD,IAAI,YAAY,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG,GAAG;gCAC5D,IAAI,cAAc,MAAM;oCACpB,4EAA4E;oCAC5E,4CAA4C;oCAC5C,QAAQ;oCACR,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM;wCAC1B,OAAO,IAAI,CAAC,4BAA4B;oCAC5C,OACK;wCACD,IAAI,UAAU,IAAI,CAAC,WAAW;wCAC9B,IAAI,UAAU,UAAU,CAAC,EAAE,EAAE;4CACzB,kDAAkD;4CAClD,6CAA6C;4CAC7C,sDAAsD;4CACtD,+CAA+C;4CAC/C,sEAAsE;4CACtE,0EAA0E;4CAC1E,0BAA0B;4CAC1B,KAAK,UAAU,UAAU,CAAC,EAAE,GAAG;4CAC/B,IAAI,OAAO;wCACf;oCACJ;gCACJ,OACK;oCACD,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;oCAC7B,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;oCAC7B,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;oCAC7B,UAAU,CAAC,EAAE,GAAG;oCAChB,UAAU,CAAC,EAAE,GAAG;oCAChB,eAAe;oCACf;gCACJ;gCACA,qCAAqC;gCACrC,eAAe;gCACf,UAAU,CAAC,EAAE,GAAG;gCAChB,UAAU,CAAC,EAAE,GAAG;gCAChB,UAAU,CAAC,EAAE,GAAG;gCAChB,UAAU,CAAC,EAAE,GAAG;gCAChB,UAAU,CAAC,EAAE,GAAG;4BACpB,OACK;gCACD,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;gCAC7B,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;gCAC7B,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;gCAC7B,UAAU,CAAC,EAAE,GAAG;gCAChB,UAAU,CAAC,EAAE,GAAG;gCAChB,eAAe;4BACnB;wBACJ,OACK;4BACD,UAAU,CAAC,EAAE,aAAa;wBAC9B;oBACJ,OACK;wBACD,UAAU,CAAC,aAAa;oBAC5B;gBACJ;YACJ;YACA,IAAI,oBAAoB,iBAAiB,CAAC,aAAa;gBACnD,IAAI,YAAY,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG,MAAM;gBAC/D,IAAI,cAAc,MAAM;oBACpB,QAAQ,UAAU,CAAC,EAAE;oBACrB,IAAI,IAAI,CAAC,UAAU,EAAE;wBACjB,oBAAoB;wBACpB,OAAO,IAAI,CAAC,4BAA4B;oBAC5C;gBACJ;YACJ;QACJ;QACA,IAAI,cAAc,IAAI,CAAC,kBAAkB;QACzC,mKAAA,CAAA,UAAW,CAAC,iBAAiB,CAAC;QAC9B,OAAO,IAAI,+LAAA,CAAA,UAAiB,CAAC;IACjC;IACA;;;KAGC,GACD,oBAAoB,aAAa,GAAG,SAAU,UAAU,EAAE,IAAI,KAAK,GAAN;QACzD,OAAO,AAAC,MAAM,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAI,UAAU,CAAC,EAAE,GAAG;IACnE;IACA;;;;KAIC,GACD,oBAAoB,iBAAiB,GAAG,SAAU,UAAU;QACxD,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,QAAQ,UAAU,CAAC,EAAE;YACzB,IAAI,UAAU,GAAG;gBACb,OAAO;YACX;YACA,mBAAmB;QACvB;QACA,IAAI,kBAAkB,GAAG;YACrB,OAAO;QACX;QACA,IAAI,aAAa,kBAAkB;QACnC,IAAI,cAAc,aAAa;QAC/B,0DAA0D;QAC1D,OAAO,KAAK,GAAG,CAAC,aAAa,UAAU,CAAC,EAAE,IAAI,eAC1C,KAAK,GAAG,CAAC,aAAa,UAAU,CAAC,EAAE,IAAI,eACvC,KAAK,GAAG,CAAC,MAAM,aAAa,UAAU,CAAC,EAAE,IAAI,IAAI,eACjD,KAAK,GAAG,CAAC,aAAa,UAAU,CAAC,EAAE,IAAI,eACvC,KAAK,GAAG,CAAC,aAAa,UAAU,CAAC,EAAE,IAAI;IAC/C;IACA,oBAAoB,SAAS,CAAC,uBAAuB,GAAG;QACpD,IAAI,uBAAuB,IAAI,CAAC,oBAAoB;QACpD,oBAAoB,CAAC,EAAE,GAAG;QAC1B,oBAAoB,CAAC,EAAE,GAAG;QAC1B,oBAAoB,CAAC,EAAE,GAAG;QAC1B,oBAAoB,CAAC,EAAE,GAAG;QAC1B,oBAAoB,CAAC,EAAE,GAAG;QAC1B,OAAO;IACX;IACA;;;;;;;;;;;KAWC,GACD,oBAAoB,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,SAAS,KAAK,GAAN,EAAU,wBAAwB,KAAK,GAAN;QACnI,IAAI,aAAa,IAAI,CAAC,uBAAuB;QAC7C,gEAAgE;QAChE,IAAI,IAAI;QACR,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,MAAO,UAAU,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,GAAI;YACtE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,SAAS,KAAK,UAAU,GAAG;YAC3B,OAAO;QACX;QACA,wCAAwC;QACxC,MAAO,UAAU,KAAK,WAAW,KAAK,CAAC,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,MACnE,UAAU,CAAC,EAAE,IAAI,SAAU;YAC3B,UAAU,CAAC,EAAE;YACb;QACJ;QACA,iEAAiE;QACjE,IAAI,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,GAAG,UAAU;YACvD,OAAO;QACX;QACA,yCAAyC;QACzC,MAAO,UAAU,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,MAClE,UAAU,CAAC,EAAE,IAAI,SAAU;YAC3B,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,GAAG,UAAU;YAC1B,OAAO;QACX;QACA,IAAI,OAAO,MAAM,SAAS;QAC1B,IAAI,OAAO,MAAM,QAAQ;QACzB,yCAAyC;QACzC,IAAI;QACJ,MAAO,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,GAAI;YAClF,UAAU,CAAC,EAAE;YACb;QACJ;QACA,oBAAoB;QACpB,IAAI,SAAS,KAAK,QAAQ,UAAU,KAAK,MAAM;YAC3C,OAAO;QACX;QACA,MAAO,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,MAC/E,UAAU,CAAC,EAAE,GAAG,SAAU;YAC1B,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,SAAS,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,CAAC,EAAE,IAAI,UAAU;YACxE,OAAO;QACX;QACA,MAAO,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,MAAM,GAAG,CAAC,UAAU,GAAG,SAAS,MAC9E,UAAU,CAAC,EAAE,GAAG,SAAU;YAC1B,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU;YAC3B,OAAO;QACX;QACA,2FAA2F;QAC3F,6CAA6C;QAC7C,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnG,OAAO,KAAK,GAAG,CAAC,kBAAkB,2BAA2B,IAAI,2BAC7D,oBAAoB,iBAAiB,CAAC;IAC9C;IACA;;;;;;;;;;KAUC,GACD,oBAAoB,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,SAAS,KAAK,GAAN,EAAU,wBAAwB,KAAK,GAAN;QACnI,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,MAAM,SAAS;QAC1B,IAAI,aAAa,IAAI,CAAC,uBAAuB;QAC7C,gCAAgC;QAChC,IAAI,IAAI;QACR,MAAO,KAAK,KAAK,MAAM,GAAG,CAAC,SAAS,GAAI;YACpC,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,IAAI,GAAG;YACP,OAAO;QACX;QACA,MAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YAClE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,iEAAiE;QACjE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,UAAU;YACnC,OAAO;QACX;QACA,MAAO,KAAK,KAAK,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI,SAAU;YACjE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,GAAG,UAAU;YAC1B,OAAO;QACX;QACA,kCAAkC;QAClC,IAAI,SAAS;QACb,MAAO,IAAI,QAAQ,MAAM,GAAG,CAAC,SAAS,GAAI;YACtC,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,MAAM,MAAM;YACZ,OAAO;QACX;QACA,MAAO,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,GAAG,SAAU;YACnE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,MAAM,QAAQ,UAAU,CAAC,EAAE,IAAI,UAAU;YACzC,OAAO;QACX;QACA,MAAO,IAAI,QAAQ,MAAM,GAAG,CAAC,SAAS,MAAM,UAAU,CAAC,EAAE,GAAG,SAAU;YAClE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU;YAC3B,OAAO;QACX;QACA,0FAA0F;QAC1F,6CAA6C;QAC7C,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAC/E,UAAU,CAAC,EAAE;QACjB,IAAI,IAAI,KAAK,GAAG,CAAC,kBAAkB,4BAA4B,IAAI,yBAAyB;YACxF,OAAO;QACX;QACA,OAAO,oBAAoB,iBAAiB,CAAC,cAAc,oBAAoB,aAAa,CAAC,YAAY,KAAK;IAClH;IACA;;;;KAIC,GACD,oBAAoB,SAAS,CAAC,oBAAoB,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,SAAS,KAAK,GAAN,EAAU,wBAAwB,KAAK,GAAN;QACrI,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,MAAM,QAAQ;QACzB,IAAI,aAAa,IAAI,CAAC,uBAAuB;QAC7C,IAAI,IAAI;QACR,MAAO,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG,SAAU;YACpC,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,IAAI,GAAG;YACP,OAAO;QACX;QACA,MAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,IAAI,SAAU;YAClE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,UAAU;YACnC,OAAO;QACX;QACA,MAAO,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,IAAI,SAAU;YACjE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,GAAG,UAAU;YAC1B,OAAO;QACX;QACA,IAAI,SAAS;QACb,MAAO,IAAI,QAAQ,MAAM,GAAG,CAAC,GAAG,SAAU;YACtC,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,MAAM,MAAM;YACZ,OAAO;QACX;QACA,MAAO,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,GAAG,SAAU;YACnE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,MAAM,QAAQ,UAAU,CAAC,EAAE,IAAI,UAAU;YACzC,OAAO;QACX;QACA,MAAO,IAAI,QAAQ,MAAM,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,GAAG,SAAU;YAClE,UAAU,CAAC,EAAE;YACb;QACJ;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU;YAC3B,OAAO;QACX;QACA,0FAA0F;QAC1F,6CAA6C;QAC7C,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAC/E,UAAU,CAAC,EAAE;QACjB,IAAI,IAAI,KAAK,GAAG,CAAC,kBAAkB,4BAA4B,yBAAyB;YACpF,OAAO;QACX;QACA,OAAO,oBAAoB,iBAAiB,CAAC,cAAc,oBAAoB,aAAa,CAAC,YAAY,KAAK;IAClH;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,oBAAoB,SAAS,CAAC,oBAAoB,GAAG,SAAU,UAAU,EAAE,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,WAAW;QACxG,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAC/E,UAAU,CAAC,EAAE;QACjB,IAAI,UAAU,oBAAoB,aAAa,CAAC,YAAY;QAC5D,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,QAAQ,GAAG,KAAK,KAAK,CAAC,UAAU,UAAU,CAAC,EAAE,EAAE;QACxF,IAAI,CAAC,MAAM,UAAU;YACjB,iBAAiB;YACjB,UAAU,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,UAAU,QAAQ,GAAG,KAAK,KAAK,CAAC,UAAU,UAAU,CAAC,EAAE,EAAE;YACnH,IAAI,CAAC,MAAM,YACP,CAAC,CAAC,eAAe,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,UAAU,QAAQ,GAAG,KAAK,KAAK,CAAC,UAAU,UAAU,CAAC,EAAE,EAAE,gBAAgB,GAAG;gBAC3I,IAAI,sBAAsB,kBAAkB;gBAC5C,IAAI,QAAQ;gBACZ,IAAI,kBAAkB,IAAI,CAAC,eAAe;gBAC1C,IAAK,IAAI,QAAQ,GAAG,WAAW,gBAAgB,MAAM,EAAE,QAAQ,UAAU,QAAS;oBAC9E,IAAI,SAAS,eAAe,CAAC,MAAM;oBACnC,kDAAkD;oBAClD,IAAI,OAAO,WAAW,CAAC,qBAAqB,SAAS,UAAU;wBAC3D,eAAe,CAAC,MAAM,GAAG,OAAO,eAAe,CAAC,SAAS,SAAS;wBAClE,QAAQ;wBACR;oBACJ;gBACJ;gBACA,IAAI,CAAC,OAAO;oBACR,IAAI,QAAQ,IAAI,2LAAA,CAAA,UAAa,CAAC,SAAS,SAAS;oBAChD,gBAAgB,IAAI,CAAC;oBACrB,IAAI,IAAI,CAAC,mBAAmB,KAAK,QAAQ,IAAI,CAAC,mBAAmB,KAAK,WAAW;wBAC7E,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;oBACtD;gBACJ;gBACA,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,oBAAoB,SAAS,CAAC,WAAW,GAAG;QACxC,IAAI,KAAK;QACT,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM;QACrC,IAAI,OAAO,GAAG;YACV,OAAO;QACX;QACA,IAAI,uBAAuB;QAC3B,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACpF,IAAI,SAAS,GAAG,KAAK;gBACrB,IAAI,OAAO,QAAQ,MAAM,oBAAoB,aAAa,EAAE;oBACxD,IAAI,wBAAwB,MAAM;wBAC9B,uBAAuB;oBAC3B,OACK;wBACD,gCAAgC;wBAChC,gEAAgE;wBAChE,8DAA8D;wBAC9D,0DAA0D;wBAC1D,iDAAiD;wBACjD,IAAI,CAAC,UAAU,GAAG;wBAClB,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,qBAAqB,IAAI,KAAK,OAAO,IAAI,MAC5E,KAAK,GAAG,CAAC,qBAAqB,IAAI,KAAK,OAAO,IAAI,GAAG,IAAI;oBACjE;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,oBAAoB,SAAS,CAAC,4BAA4B,GAAG;QACzD,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,iBAAiB;QACrB,IAAI,kBAAkB;QACtB,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM;QACrC,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACpF,IAAI,UAAU,GAAG,KAAK;gBACtB,IAAI,QAAQ,QAAQ,MAAM,oBAAoB,aAAa,EAAE;oBACzD;oBACA,mBAAmB,QAAQ,sBAAsB;gBACrD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,iBAAiB,GAAG;YACpB,OAAO;QACX;QACA,8FAA8F;QAC9F,2FAA2F;QAC3F,uFAAuF;QACvF,wDAAwD;QACxD,IAAI,UAAU,kBAAkB;QAChC,IAAI,iBAAiB;QACrB,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACpF,IAAI,UAAU,GAAG,KAAK;gBACtB,kBAAkB,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK;YAClE;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,kBAAkB,OAAO;IACpC;IACA;;;;;KAKC,GACD,oBAAoB,SAAS,CAAC,kBAAkB,GAAG;QAC/C,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM;QAC3C,IAAI,YAAY,GAAG;YACf,uCAAuC;YACvC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,kBAAkB,IAAI,CAAC,eAAe;QAC1C,IAAI;QACJ,kEAAkE;QAClE,IAAI,YAAY,GAAG;YACf,qFAAqF;YACrF,IAAI,kBAAkB;YACtB,IAAI,SAAS;YACb,IAAI;gBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;oBACpF,IAAI,SAAS,GAAG,KAAK;oBACrB,IAAI,OAAO,OAAO,sBAAsB;oBACxC,mBAAmB;oBACnB,UAAU,OAAO;gBACrB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;gBACpD,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,UAAU,kBAAkB;YAC5B,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,YAAY,UAAU;YACtD,gBAAgB,IAAI,CACpB;;aAEC,GACD,qEAAqE;YACrE,SAAU,OAAO,EAAE,OAAO;gBACtB,IAAI,KAAK,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK;gBACrD,IAAI,KAAK,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK;gBACrD,OAAO,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI;YACxC;YACA,IAAI,QAAQ,KAAK,GAAG,CAAC,MAAM,SAAS;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM,GAAG,GAAG,IAAK;gBAC3E,IAAI,UAAU,eAAe,CAAC,EAAE;gBAChC,IAAI,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK,WAAW,OAAO;oBAC9D,gBAAgB,MAAM,CAAC,GAAG;oBAC1B;gBACJ;YACJ;QACJ;QACA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC5B,iEAAiE;YACjE,IAAI,kBAAkB;YACtB,IAAI;gBACA,IAAK,IAAI,oBAAoB,SAAS,kBAAkB,sBAAsB,kBAAkB,IAAI,IAAI,CAAC,oBAAoB,IAAI,EAAE,sBAAsB,kBAAkB,IAAI,GAAI;oBAC/K,IAAI,iBAAiB,oBAAoB,KAAK;oBAC9C,mBAAmB,eAAe,sBAAsB;gBAC5D;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,uBAAuB,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,kBAAkB,MAAM,GAAG,GAAG,IAAI,CAAC;gBACrG,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,UAAU,kBAAkB,gBAAgB,MAAM;YAClD,gBAAgB,IAAI,CACpB;;aAEC,GACD,wDAAwD;YACxD,SAAU,OAAO,EAAE,OAAO;gBACtB,IAAI,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,IAAI;oBAC3C,IAAI,KAAK,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK;oBACrD,IAAI,KAAK,KAAK,GAAG,CAAC,QAAQ,sBAAsB,KAAK;oBACrD,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI;gBACxC,OACK;oBACD,OAAO,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;gBAChD;YACJ;YACA,gBAAgB,MAAM,CAAC,IAAI,+DAA+D;QAC9F;QACA,OAAO;YACH,eAAe,CAAC,EAAE;YAClB,eAAe,CAAC,EAAE;YAClB,eAAe,CAAC,EAAE;SACrB;IACL;IACA,oBAAoB,aAAa,GAAG;IACpC,oBAAoB,QAAQ,GAAG,GAAG,wCAAwC;IAC1E,oBAAoB,WAAW,GAAG,IAAI,8CAA8C;IACpF,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/detector/Detector.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport MathUtils from '../../common/detector/MathUtils';\nimport DetectorResult from '../../common/DetectorResult';\n// import GridSampler from '../../common/GridSampler';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport PerspectiveTransform from '../../common/PerspectiveTransform';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\nimport Version from '../decoder/Version';\nimport AlignmentPatternFinder from './AlignmentPatternFinder';\nimport FinderPatternFinder from './FinderPatternFinder';\n/*import java.util.Map;*/\n/**\n * <p>Encapsulates logic that can detect a QR Code in an image, even if the QR Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Owen\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.image = image;\n    }\n    Detector.prototype.getImage = function () {\n        return this.image;\n    };\n    Detector.prototype.getResultPointCallback = function () {\n        return this.resultPointCallback;\n    };\n    /**\n     * <p>Detects a QR Code in an image.</p>\n     *\n     * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n     * @throws NotFoundException if QR Code cannot be found\n     * @throws FormatException if a QR Code cannot be decoded\n     */\n    // public detect(): DetectorResult /*throws NotFoundException, FormatException*/ {\n    //   return detect(null)\n    // }\n    /**\n     * <p>Detects a QR Code in an image.</p>\n     *\n     * @param hints optional hints to detector\n     * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n     * @throws NotFoundException if QR Code cannot be found\n     * @throws FormatException if a QR Code cannot be decoded\n     */\n    Detector.prototype.detect = function (hints) {\n        this.resultPointCallback = (hints === null || hints === undefined) ? null :\n            /*(ResultPointCallback) */ hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n        var finder = new FinderPatternFinder(this.image, this.resultPointCallback);\n        var info = finder.find(hints);\n        return this.processFinderPatternInfo(info);\n    };\n    Detector.prototype.processFinderPatternInfo = function (info) {\n        var topLeft = info.getTopLeft();\n        var topRight = info.getTopRight();\n        var bottomLeft = info.getBottomLeft();\n        var moduleSize = this.calculateModuleSize(topLeft, topRight, bottomLeft);\n        if (moduleSize < 1.0) {\n            throw new NotFoundException('No pattern found in proccess finder.');\n        }\n        var dimension = Detector.computeDimension(topLeft, topRight, bottomLeft, moduleSize);\n        var provisionalVersion = Version.getProvisionalVersionForDimension(dimension);\n        var modulesBetweenFPCenters = provisionalVersion.getDimensionForVersion() - 7;\n        var alignmentPattern = null;\n        // Anything above version 1 has an alignment pattern\n        if (provisionalVersion.getAlignmentPatternCenters().length > 0) {\n            // Guess where a \"bottom right\" finder pattern would have been\n            var bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();\n            var bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();\n            // Estimate that alignment pattern is closer by 3 modules\n            // from \"bottom right\" to known top left location\n            var correctionToTopLeft = 1.0 - 3.0 / modulesBetweenFPCenters;\n            var estAlignmentX = /*(int) */ Math.floor(topLeft.getX() + correctionToTopLeft * (bottomRightX - topLeft.getX()));\n            var estAlignmentY = /*(int) */ Math.floor(topLeft.getY() + correctionToTopLeft * (bottomRightY - topLeft.getY()));\n            // Kind of arbitrary -- expand search radius before giving up\n            for (var i = 4; i <= 16; i <<= 1) {\n                try {\n                    alignmentPattern = this.findAlignmentInRegion(moduleSize, estAlignmentX, estAlignmentY, i);\n                    break;\n                }\n                catch (re /*NotFoundException*/) {\n                    if (!(re instanceof NotFoundException)) {\n                        throw re;\n                    }\n                    // try next round\n                }\n            }\n            // If we didn't find alignment pattern... well try anyway without it\n        }\n        var transform = Detector.createTransform(topLeft, topRight, bottomLeft, alignmentPattern, dimension);\n        var bits = Detector.sampleGrid(this.image, transform, dimension);\n        var points;\n        if (alignmentPattern === null) {\n            points = [bottomLeft, topLeft, topRight];\n        }\n        else {\n            points = [bottomLeft, topLeft, topRight, alignmentPattern];\n        }\n        return new DetectorResult(bits, points);\n    };\n    Detector.createTransform = function (topLeft, topRight, bottomLeft, alignmentPattern, dimension /*int*/) {\n        var dimMinusThree = dimension - 3.5;\n        var bottomRightX; /*float*/\n        var bottomRightY; /*float*/\n        var sourceBottomRightX; /*float*/\n        var sourceBottomRightY; /*float*/\n        if (alignmentPattern !== null) {\n            bottomRightX = alignmentPattern.getX();\n            bottomRightY = alignmentPattern.getY();\n            sourceBottomRightX = dimMinusThree - 3.0;\n            sourceBottomRightY = sourceBottomRightX;\n        }\n        else {\n            // Don't have an alignment pattern, just make up the bottom-right point\n            bottomRightX = (topRight.getX() - topLeft.getX()) + bottomLeft.getX();\n            bottomRightY = (topRight.getY() - topLeft.getY()) + bottomLeft.getY();\n            sourceBottomRightX = dimMinusThree;\n            sourceBottomRightY = dimMinusThree;\n        }\n        return PerspectiveTransform.quadrilateralToQuadrilateral(3.5, 3.5, dimMinusThree, 3.5, sourceBottomRightX, sourceBottomRightY, 3.5, dimMinusThree, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRightX, bottomRightY, bottomLeft.getX(), bottomLeft.getY());\n    };\n    Detector.sampleGrid = function (image, transform, dimension /*int*/) {\n        var sampler = GridSamplerInstance.getInstance();\n        return sampler.sampleGridWithTransform(image, dimension, dimension, transform);\n    };\n    /**\n     * <p>Computes the dimension (number of modules on a size) of the QR Code based on the position\n     * of the finder patterns and estimated module size.</p>\n     */\n    Detector.computeDimension = function (topLeft, topRight, bottomLeft, moduleSize /*float*/) {\n        var tltrCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, topRight) / moduleSize);\n        var tlblCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, bottomLeft) / moduleSize);\n        var dimension = Math.floor((tltrCentersDimension + tlblCentersDimension) / 2) + 7;\n        switch (dimension & 0x03) { // mod 4\n            case 0:\n                dimension++;\n                break;\n            // 1? do nothing\n            case 2:\n                dimension--;\n                break;\n            case 3:\n                throw new NotFoundException('Dimensions could be not found.');\n        }\n        return dimension;\n    };\n    /**\n     * <p>Computes an average estimated module size based on estimated derived from the positions\n     * of the three finder patterns.</p>\n     *\n     * @param topLeft detected top-left finder pattern center\n     * @param topRight detected top-right finder pattern center\n     * @param bottomLeft detected bottom-left finder pattern center\n     * @return estimated module size\n     */\n    Detector.prototype.calculateModuleSize = function (topLeft, topRight, bottomLeft) {\n        // Take the average\n        return (this.calculateModuleSizeOneWay(topLeft, topRight) +\n            this.calculateModuleSizeOneWay(topLeft, bottomLeft)) / 2.0;\n    };\n    /**\n     * <p>Estimates module size based on two finder patterns -- it uses\n     * {@link #sizeOfBlackWhiteBlackRunBothWays(int, int, int, int)} to figure the\n     * width of each, measuring along the axis between their centers.</p>\n     */\n    Detector.prototype.calculateModuleSizeOneWay = function (pattern, otherPattern) {\n        var moduleSizeEst1 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(pattern.getX()), \n        /*(int) */ Math.floor(pattern.getY()), \n        /*(int) */ Math.floor(otherPattern.getX()), \n        /*(int) */ Math.floor(otherPattern.getY()));\n        var moduleSizeEst2 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(otherPattern.getX()), \n        /*(int) */ Math.floor(otherPattern.getY()), \n        /*(int) */ Math.floor(pattern.getX()), \n        /*(int) */ Math.floor(pattern.getY()));\n        if (isNaN(moduleSizeEst1)) {\n            return moduleSizeEst2 / 7.0;\n        }\n        if (isNaN(moduleSizeEst2)) {\n            return moduleSizeEst1 / 7.0;\n        }\n        // Average them, and divide by 7 since we've counted the width of 3 black modules,\n        // and 1 white and 1 black module on either side. Ergo, divide sum by 14.\n        return (moduleSizeEst1 + moduleSizeEst2) / 14.0;\n    };\n    /**\n     * See {@link #sizeOfBlackWhiteBlackRun(int, int, int, int)}; computes the total width of\n     * a finder pattern by looking for a black-white-black run from the center in the direction\n     * of another point (another finder pattern center), and in the opposite direction too.\n     */\n    Detector.prototype.sizeOfBlackWhiteBlackRunBothWays = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n        var result = this.sizeOfBlackWhiteBlackRun(fromX, fromY, toX, toY);\n        // Now count other way -- don't run off image though of course\n        var scale = 1.0;\n        var otherToX = fromX - (toX - fromX);\n        if (otherToX < 0) {\n            scale = fromX / /*(float) */ (fromX - otherToX);\n            otherToX = 0;\n        }\n        else if (otherToX >= this.image.getWidth()) {\n            scale = (this.image.getWidth() - 1 - fromX) / /*(float) */ (otherToX - fromX);\n            otherToX = this.image.getWidth() - 1;\n        }\n        var otherToY = /*(int) */ Math.floor(fromY - (toY - fromY) * scale);\n        scale = 1.0;\n        if (otherToY < 0) {\n            scale = fromY / /*(float) */ (fromY - otherToY);\n            otherToY = 0;\n        }\n        else if (otherToY >= this.image.getHeight()) {\n            scale = (this.image.getHeight() - 1 - fromY) / /*(float) */ (otherToY - fromY);\n            otherToY = this.image.getHeight() - 1;\n        }\n        otherToX = /*(int) */ Math.floor(fromX + (otherToX - fromX) * scale);\n        result += this.sizeOfBlackWhiteBlackRun(fromX, fromY, otherToX, otherToY);\n        // Middle pixel is double-counted this way; subtract 1\n        return result - 1.0;\n    };\n    /**\n     * <p>This method traces a line from a point in the image, in the direction towards another point.\n     * It begins in a black region, and keeps going until it finds white, then black, then white again.\n     * It reports the distance from the start to this point.</p>\n     *\n     * <p>This is used when figuring out how wide a finder pattern is, when the finder pattern\n     * may be skewed or rotated.</p>\n     */\n    Detector.prototype.sizeOfBlackWhiteBlackRun = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n        // Mild variant of Bresenham's algorithm\n        // see http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\n        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n        if (steep) {\n            var temp = fromX;\n            fromX = fromY;\n            fromY = temp;\n            temp = toX;\n            toX = toY;\n            toY = temp;\n        }\n        var dx = Math.abs(toX - fromX);\n        var dy = Math.abs(toY - fromY);\n        var error = -dx / 2;\n        var xstep = fromX < toX ? 1 : -1;\n        var ystep = fromY < toY ? 1 : -1;\n        // In black pixels, looking for white, first or second time.\n        var state = 0;\n        // Loop up until x == toX, but not beyond\n        var xLimit = toX + xstep;\n        for (var x = fromX, y = fromY; x !== xLimit; x += xstep) {\n            var realX = steep ? y : x;\n            var realY = steep ? x : y;\n            // Does current pixel mean we have moved white to black or vice versa?\n            // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n            // color, advance to next state or end if we are in state 2 already\n            if ((state === 1) === this.image.get(realX, realY)) {\n                if (state === 2) {\n                    return MathUtils.distance(x, y, fromX, fromY);\n                }\n                state++;\n            }\n            error += dy;\n            if (error > 0) {\n                if (y === toY) {\n                    break;\n                }\n                y += ystep;\n                error -= dx;\n            }\n        }\n        // Found black-white-black; give the benefit of the doubt that the next pixel outside the image\n        // is \"white\" so this last point at (toX+xStep,toY) is the right ending. This is really a\n        // small approximation; (toX+xStep,toY+yStep) might be really correct. Ignore this.\n        if (state === 2) {\n            return MathUtils.distance(toX + xstep, toY, fromX, fromY);\n        }\n        // else we didn't find even black-white-black; no estimate is really possible\n        return NaN;\n    };\n    /**\n     * <p>Attempts to locate an alignment pattern in a limited region of the image, which is\n     * guessed to contain it. This method uses {@link AlignmentPattern}.</p>\n     *\n     * @param overallEstModuleSize estimated module size so far\n     * @param estAlignmentX x coordinate of center of area probably containing alignment pattern\n     * @param estAlignmentY y coordinate of above\n     * @param allowanceFactor number of pixels in all directions to search from the center\n     * @return {@link AlignmentPattern} if found, or null otherwise\n     * @throws NotFoundException if an unexpected error occurs during detection\n     */\n    Detector.prototype.findAlignmentInRegion = function (overallEstModuleSize /*float*/, estAlignmentX /*int*/, estAlignmentY /*int*/, allowanceFactor /*float*/) {\n        // Look for an alignment pattern (3 modules in size) around where it\n        // should be\n        var allowance = /*(int) */ Math.floor(allowanceFactor * overallEstModuleSize);\n        var alignmentAreaLeftX = Math.max(0, estAlignmentX - allowance);\n        var alignmentAreaRightX = Math.min(this.image.getWidth() - 1, estAlignmentX + allowance);\n        if (alignmentAreaRightX - alignmentAreaLeftX < overallEstModuleSize * 3) {\n            throw new NotFoundException('Alignment top exceeds estimated module size.');\n        }\n        var alignmentAreaTopY = Math.max(0, estAlignmentY - allowance);\n        var alignmentAreaBottomY = Math.min(this.image.getHeight() - 1, estAlignmentY + allowance);\n        if (alignmentAreaBottomY - alignmentAreaTopY < overallEstModuleSize * 3) {\n            throw new NotFoundException('Alignment bottom exceeds estimated module size.');\n        }\n        var alignmentFinder = new AlignmentPatternFinder(this.image, alignmentAreaLeftX, alignmentAreaTopY, alignmentAreaRightX - alignmentAreaLeftX, alignmentAreaBottomY - alignmentAreaTopY, overallEstModuleSize, this.resultPointCallback);\n        return alignmentFinder.find();\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,uBAAuB,GACvB;;;;;CAKC,GACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,KAAK;QACnB,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,SAAS,SAAS,CAAC,sBAAsB,GAAG;QACxC,OAAO,IAAI,CAAC,mBAAmB;IACnC;IACA;;;;;;KAMC,GACD,kFAAkF;IAClF,wBAAwB;IACxB,IAAI;IACJ;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QACvC,IAAI,CAAC,mBAAmB,GAAG,AAAC,UAAU,QAAQ,UAAU,YAAa,OACjE,wBAAwB,GAAG,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B;QAClF,IAAI,SAAS,IAAI,iMAAA,CAAA,UAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB;QACzE,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,SAAS,SAAS,CAAC,wBAAwB,GAAG,SAAU,IAAI;QACxD,IAAI,UAAU,KAAK,UAAU;QAC7B,IAAI,WAAW,KAAK,WAAW;QAC/B,IAAI,aAAa,KAAK,aAAa;QACnC,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC,SAAS,UAAU;QAC7D,IAAI,aAAa,KAAK;YAClB,MAAM,IAAI,yKAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,YAAY,SAAS,gBAAgB,CAAC,SAAS,UAAU,YAAY;QACzE,IAAI,qBAAqB,oLAAA,CAAA,UAAO,CAAC,iCAAiC,CAAC;QACnE,IAAI,0BAA0B,mBAAmB,sBAAsB,KAAK;QAC5E,IAAI,mBAAmB;QACvB,oDAAoD;QACpD,IAAI,mBAAmB,0BAA0B,GAAG,MAAM,GAAG,GAAG;YAC5D,8DAA8D;YAC9D,IAAI,eAAe,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI;YACrE,IAAI,eAAe,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI;YACrE,yDAAyD;YACzD,iDAAiD;YACjD,IAAI,sBAAsB,MAAM,MAAM;YACtC,IAAI,gBAAgB,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,sBAAsB,CAAC,eAAe,QAAQ,IAAI,EAAE;YAC/G,IAAI,gBAAgB,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,sBAAsB,CAAC,eAAe,QAAQ,IAAI,EAAE;YAC/G,6DAA6D;YAC7D,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,MAAM,EAAG;gBAC9B,IAAI;oBACA,mBAAmB,IAAI,CAAC,qBAAqB,CAAC,YAAY,eAAe,eAAe;oBACxF;gBACJ,EACA,OAAO,GAAG,mBAAmB,KAAI;oBAC7B,IAAI,CAAC,CAAC,cAAc,yKAAA,CAAA,UAAiB,GAAG;wBACpC,MAAM;oBACV;gBACA,iBAAiB;gBACrB;YACJ;QACA,oEAAoE;QACxE;QACA,IAAI,YAAY,SAAS,eAAe,CAAC,SAAS,UAAU,YAAY,kBAAkB;QAC1F,IAAI,OAAO,SAAS,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW;QACtD,IAAI;QACJ,IAAI,qBAAqB,MAAM;YAC3B,SAAS;gBAAC;gBAAY;gBAAS;aAAS;QAC5C,OACK;YACD,SAAS;gBAAC;gBAAY;gBAAS;gBAAU;aAAiB;QAC9D;QACA,OAAO,IAAI,gLAAA,CAAA,UAAc,CAAC,MAAM;IACpC;IACA,SAAS,eAAe,GAAG,SAAU,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,KAAK,GAAN;QAC3F,IAAI,gBAAgB,YAAY;QAChC,IAAI,cAAc,OAAO;QACzB,IAAI,cAAc,OAAO;QACzB,IAAI,oBAAoB,OAAO;QAC/B,IAAI,oBAAoB,OAAO;QAC/B,IAAI,qBAAqB,MAAM;YAC3B,eAAe,iBAAiB,IAAI;YACpC,eAAe,iBAAiB,IAAI;YACpC,qBAAqB,gBAAgB;YACrC,qBAAqB;QACzB,OACK;YACD,uEAAuE;YACvE,eAAe,AAAC,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAM,WAAW,IAAI;YACnE,eAAe,AAAC,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAM,WAAW,IAAI;YACnE,qBAAqB;YACrB,qBAAqB;QACzB;QACA,OAAO,sLAAA,CAAA,UAAoB,CAAC,4BAA4B,CAAC,KAAK,KAAK,eAAe,KAAK,oBAAoB,oBAAoB,KAAK,eAAe,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,IAAI,cAAc,cAAc,WAAW,IAAI,IAAI,WAAW,IAAI;IACvR;IACA,SAAS,UAAU,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,UAAU,KAAK,GAAN;QACvD,IAAI,UAAU,qLAAA,CAAA,UAAmB,CAAC,WAAW;QAC7C,OAAO,QAAQ,uBAAuB,CAAC,OAAO,WAAW,WAAW;IACxE;IACA;;;KAGC,GACD,SAAS,gBAAgB,GAAG,SAAU,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,OAAO,GAAR;QAC3E,IAAI,uBAAuB,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,mKAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,SAAS,YAAY;QACrF,IAAI,uBAAuB,uLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,mKAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,SAAS,cAAc;QACvF,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,uBAAuB,oBAAoB,IAAI,KAAK;QAChF,OAAQ,YAAY;YAChB,KAAK;gBACD;gBACA;YACJ,gBAAgB;YAChB,KAAK;gBACD;gBACA;YACJ,KAAK;gBACD,MAAM,IAAI,yKAAA,CAAA,UAAiB,CAAC;QACpC;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,SAAS,SAAS,CAAC,mBAAmB,GAAG,SAAU,OAAO,EAAE,QAAQ,EAAE,UAAU;QAC5E,mBAAmB;QACnB,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,YAC5C,IAAI,CAAC,yBAAyB,CAAC,SAAS,WAAW,IAAI;IAC/D;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,yBAAyB,GAAG,SAAU,OAAO,EAAE,YAAY;QAC1E,IAAI,iBAAiB,IAAI,CAAC,gCAAgC,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,KAC7F,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,KAClC,QAAQ,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,KACvC,QAAQ,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI;QACvC,IAAI,iBAAiB,IAAI,CAAC,gCAAgC,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,KAClG,QAAQ,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,KACvC,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,KAClC,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI;QAClC,IAAI,MAAM,iBAAiB;YACvB,OAAO,iBAAiB;QAC5B;QACA,IAAI,MAAM,iBAAiB;YACvB,OAAO,iBAAiB;QAC5B;QACA,kFAAkF;QAClF,yEAAyE;QACzE,OAAO,CAAC,iBAAiB,cAAc,IAAI;IAC/C;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,gCAAgC,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN;QAC1G,IAAI,SAAS,IAAI,CAAC,wBAAwB,CAAC,OAAO,OAAO,KAAK;QAC9D,8DAA8D;QAC9D,IAAI,QAAQ;QACZ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK;QACnC,IAAI,WAAW,GAAG;YACd,QAAQ,QAAQ,UAAU,GAAG,CAAC,QAAQ,QAAQ;YAC9C,WAAW;QACf,OACK,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACxC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,KAAK,IAAI,UAAU,GAAG,CAAC,WAAW,KAAK;YAC5E,WAAW,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK;QACvC;QACA,IAAI,WAAW,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI;QAC7D,QAAQ;QACR,IAAI,WAAW,GAAG;YACd,QAAQ,QAAQ,UAAU,GAAG,CAAC,QAAQ,QAAQ;YAC9C,WAAW;QACf,OACK,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI;YACzC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,KAAK,IAAI,UAAU,GAAG,CAAC,WAAW,KAAK;YAC7E,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK;QACxC;QACA,WAAW,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI;QAC9D,UAAU,IAAI,CAAC,wBAAwB,CAAC,OAAO,OAAO,UAAU;QAChE,sDAAsD;QACtD,OAAO,SAAS;IACpB;IACA;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,wBAAwB,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN;QAClG,wCAAwC;QACxC,8DAA8D;QAC9D,IAAI,QAAQ,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM;QACnD,IAAI,OAAO;YACP,IAAI,OAAO;YACX,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,MAAM;YACN,MAAM;QACV;QACA,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;QACxB,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;QACxB,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;QAC/B,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;QAC/B,4DAA4D;QAC5D,IAAI,QAAQ;QACZ,yCAAyC;QACzC,IAAI,SAAS,MAAM;QACnB,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,QAAQ,KAAK,MAAO;YACrD,IAAI,QAAQ,QAAQ,IAAI;YACxB,IAAI,QAAQ,QAAQ,IAAI;YACxB,sEAAsE;YACtE,4EAA4E;YAC5E,mEAAmE;YACnE,IAAI,AAAC,UAAU,MAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,QAAQ;gBAChD,IAAI,UAAU,GAAG;oBACb,OAAO,uLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO;gBAC3C;gBACA;YACJ;YACA,SAAS;YACT,IAAI,QAAQ,GAAG;gBACX,IAAI,MAAM,KAAK;oBACX;gBACJ;gBACA,KAAK;gBACL,SAAS;YACb;QACJ;QACA,+FAA+F;QAC/F,yFAAyF;QACzF,mFAAmF;QACnF,IAAI,UAAU,GAAG;YACb,OAAO,uLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,MAAM,OAAO,KAAK,OAAO;QACvD;QACA,6EAA6E;QAC7E,OAAO;IACX;IACA;;;;;;;;;;KAUC,GACD,SAAS,SAAS,CAAC,qBAAqB,GAAG,SAAU,qBAAqB,OAAO,GAAR,EAAY,cAAc,KAAK,GAAN,EAAU,cAAc,KAAK,GAAN,EAAU,gBAAgB,OAAO,GAAR;QAC9I,oEAAoE;QACpE,YAAY;QACZ,IAAI,YAAY,QAAQ,GAAG,KAAK,KAAK,CAAC,kBAAkB;QACxD,IAAI,qBAAqB,KAAK,GAAG,CAAC,GAAG,gBAAgB;QACrD,IAAI,sBAAsB,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,GAAG,gBAAgB;QAC9E,IAAI,sBAAsB,qBAAqB,uBAAuB,GAAG;YACrE,MAAM,IAAI,yKAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,oBAAoB,KAAK,GAAG,CAAC,GAAG,gBAAgB;QACpD,IAAI,uBAAuB,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,GAAG,gBAAgB;QAChF,IAAI,uBAAuB,oBAAoB,uBAAuB,GAAG;YACrE,MAAM,IAAI,yKAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,kBAAkB,IAAI,oMAAA,CAAA,UAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,oBAAoB,mBAAmB,sBAAsB,oBAAoB,uBAAuB,mBAAmB,sBAAsB,IAAI,CAAC,mBAAmB;QACtO,OAAO,gBAAgB,IAAI;IAC/B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/QRCodeReader.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\n// import DetectorResult from '../common/DetectorResult';\nimport Decoder from './decoder/Decoder';\nimport QRCodeDecoderMetaData from './decoder/QRCodeDecoderMetaData';\nimport Detector from './detector/Detector';\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * This implementation can detect and decode QR Codes in an image.\n *\n * <AUTHOR> Owen\n */\nvar QRCodeReader = /** @class */ (function () {\n    function QRCodeReader() {\n        this.decoder = new Decoder();\n    }\n    QRCodeReader.prototype.getDecoder = function () {\n        return this.decoder;\n    };\n    /**\n     * Locates and decodes a QR code in an image.\n     *\n     * @return a representing: string the content encoded by the QR code\n     * @throws NotFoundException if a QR code cannot be found\n     * @throws FormatException if a QR code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    /*@Override*/\n    // public decode(image: BinaryBitmap): Result /*throws NotFoundException, ChecksumException, FormatException */ {\n    //   return this.decode(image, null)\n    // }\n    /*@Override*/\n    QRCodeReader.prototype.decode = function (image, hints) {\n        var decoderResult;\n        var points;\n        if (hints !== undefined && hints !== null && undefined !== hints.get(DecodeHintType.PURE_BARCODE)) {\n            var bits = QRCodeReader.extractPureBits(image.getBlackMatrix());\n            decoderResult = this.decoder.decodeBitMatrix(bits, hints);\n            points = QRCodeReader.NO_POINTS;\n        }\n        else {\n            var detectorResult = new Detector(image.getBlackMatrix()).detect(hints);\n            decoderResult = this.decoder.decodeBitMatrix(detectorResult.getBits(), hints);\n            points = detectorResult.getPoints();\n        }\n        // If the code was mirrored: swap the bottom-left and the top-right points.\n        if (decoderResult.getOther() instanceof QRCodeDecoderMetaData) {\n            decoderResult.getOther().applyMirroredCorrection(points);\n        }\n        var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), undefined, points, BarcodeFormat.QR_CODE, undefined);\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments !== null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel !== null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        if (decoderResult.hasStructuredAppend()) {\n            result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_SEQUENCE, decoderResult.getStructuredAppendSequenceNumber());\n            result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_PARITY, decoderResult.getStructuredAppendParity());\n        }\n        return result;\n    };\n    /*@Override*/\n    QRCodeReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n     * which contains only an unrotated, unskewed, image of a code, with some white border\n     * around it. This is a specialized method that works exceptionally fast in this special\n     * case.\n     *\n     * @see com.google.zxing.datamatrix.DataMatrixReader#extractPureBits(BitMatrix)\n     */\n    QRCodeReader.extractPureBits = function (image) {\n        var leftTopBlack = image.getTopLeftOnBit();\n        var rightBottomBlack = image.getBottomRightOnBit();\n        if (leftTopBlack === null || rightBottomBlack === null) {\n            throw new NotFoundException();\n        }\n        var moduleSize = this.moduleSize(leftTopBlack, image);\n        var top = leftTopBlack[1];\n        var bottom = rightBottomBlack[1];\n        var left = leftTopBlack[0];\n        var right = rightBottomBlack[0];\n        // Sanity check!\n        if (left >= right || top >= bottom) {\n            throw new NotFoundException();\n        }\n        if (bottom - top !== right - left) {\n            // Special case, where bottom-right module wasn't black so we found something else in the last row\n            // Assume it's a square, so use height as the width\n            right = left + (bottom - top);\n            if (right >= image.getWidth()) {\n                // Abort if that would not make sense -- off image\n                throw new NotFoundException();\n            }\n        }\n        var matrixWidth = Math.round((right - left + 1) / moduleSize);\n        var matrixHeight = Math.round((bottom - top + 1) / moduleSize);\n        if (matrixWidth <= 0 || matrixHeight <= 0) {\n            throw new NotFoundException();\n        }\n        if (matrixHeight !== matrixWidth) {\n            // Only possibly decode square regions\n            throw new NotFoundException();\n        }\n        // Push in the \"border\" by half the module width so that we start\n        // sampling in the middle of the module. Just in case the image is a\n        // little off, this will help recover.\n        var nudge = /*(int) */ Math.floor(moduleSize / 2.0);\n        top += nudge;\n        left += nudge;\n        // But careful that this does not sample off the edge\n        // \"right\" is the farthest-right valid pixel location -- right+1 is not necessarily\n        // This is positive by how much the inner x loop below would be too large\n        var nudgedTooFarRight = left + /*(int) */ Math.floor((matrixWidth - 1) * moduleSize) - right;\n        if (nudgedTooFarRight > 0) {\n            if (nudgedTooFarRight > nudge) {\n                // Neither way fits; abort\n                throw new NotFoundException();\n            }\n            left -= nudgedTooFarRight;\n        }\n        // See logic above\n        var nudgedTooFarDown = top + /*(int) */ Math.floor((matrixHeight - 1) * moduleSize) - bottom;\n        if (nudgedTooFarDown > 0) {\n            if (nudgedTooFarDown > nudge) {\n                // Neither way fits; abort\n                throw new NotFoundException();\n            }\n            top -= nudgedTooFarDown;\n        }\n        // Now just read off the bits\n        var bits = new BitMatrix(matrixWidth, matrixHeight);\n        for (var y = 0; y < matrixHeight; y++) {\n            var iOffset = top + /*(int) */ Math.floor(y * moduleSize);\n            for (var x = 0; x < matrixWidth; x++) {\n                if (image.get(left + /*(int) */ Math.floor(x * moduleSize), iOffset)) {\n                    bits.set(x, y);\n                }\n            }\n        }\n        return bits;\n    };\n    QRCodeReader.moduleSize = function (leftTopBlack, image) {\n        var height = image.getHeight();\n        var width = image.getWidth();\n        var x = leftTopBlack[0];\n        var y = leftTopBlack[1];\n        var inBlack = true;\n        var transitions = 0;\n        while (x < width && y < height) {\n            if (inBlack !== image.get(x, y)) {\n                if (++transitions === 5) {\n                    break;\n                }\n                inBlack = !inBlack;\n            }\n            x++;\n            y++;\n        }\n        if (x === width || y === height) {\n            throw new NotFoundException();\n        }\n        return (x - leftTopBlack[0]) / 7.0;\n    };\n    QRCodeReader.NO_POINTS = new Array();\n    return QRCodeReader;\n}());\nexport default QRCodeReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC;;;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD;AACA;AACA;;;;;;;;;;AACA,wBAAwB,GACxB,uBAAuB,GACvB;;;;CAIC,GACD,IAAI,eAA8B;IAC9B,SAAS;QACL,IAAI,CAAC,OAAO,GAAG,IAAI,oLAAA,CAAA,UAAO;IAC9B;IACA,aAAa,SAAS,CAAC,UAAU,GAAG;QAChC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;;;;KAOC,GACD,WAAW,GACX,iHAAiH;IACjH,oCAAoC;IACpC,IAAI;IACJ,WAAW,GACX,aAAa,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QAClD,IAAI;QACJ,IAAI;QACJ,IAAI,UAAU,aAAa,UAAU,QAAQ,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,YAAY,GAAG;YAC/F,IAAI,OAAO,aAAa,eAAe,CAAC,MAAM,cAAc;YAC5D,gBAAgB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM;YACnD,SAAS,aAAa,SAAS;QACnC,OACK;YACD,IAAI,iBAAiB,IAAI,sLAAA,CAAA,UAAQ,CAAC,MAAM,cAAc,IAAI,MAAM,CAAC;YACjE,gBAAgB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,OAAO,IAAI;YACvE,SAAS,eAAe,SAAS;QACrC;QACA,2EAA2E;QAC3E,IAAI,cAAc,QAAQ,cAAc,kMAAA,CAAA,UAAqB,EAAE;YAC3D,cAAc,QAAQ,GAAG,uBAAuB,CAAC;QACrD;QACA,IAAI,SAAS,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,OAAO,IAAI,cAAc,WAAW,IAAI,WAAW,QAAQ,qKAAA,CAAA,UAAa,CAAC,OAAO,EAAE;QACxH,IAAI,eAAe,cAAc,eAAe;QAChD,IAAI,iBAAiB,MAAM;YACvB,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,aAAa,EAAE;QACzD;QACA,IAAI,UAAU,cAAc,UAAU;QACtC,IAAI,YAAY,MAAM;YAClB,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,sBAAsB,EAAE;QAClE;QACA,IAAI,cAAc,mBAAmB,IAAI;YACrC,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,0BAA0B,EAAE,cAAc,iCAAiC;YACjH,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,wBAAwB,EAAE,cAAc,yBAAyB;QAC3G;QACA,OAAO;IACX;IACA,WAAW,GACX,aAAa,SAAS,CAAC,KAAK,GAAG;IAC3B,aAAa;IACjB;IACA;;;;;;;KAOC,GACD,aAAa,eAAe,GAAG,SAAU,KAAK;QAC1C,IAAI,eAAe,MAAM,eAAe;QACxC,IAAI,mBAAmB,MAAM,mBAAmB;QAChD,IAAI,iBAAiB,QAAQ,qBAAqB,MAAM;YACpD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC,cAAc;QAC/C,IAAI,MAAM,YAAY,CAAC,EAAE;QACzB,IAAI,SAAS,gBAAgB,CAAC,EAAE;QAChC,IAAI,OAAO,YAAY,CAAC,EAAE;QAC1B,IAAI,QAAQ,gBAAgB,CAAC,EAAE;QAC/B,gBAAgB;QAChB,IAAI,QAAQ,SAAS,OAAO,QAAQ;YAChC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,SAAS,QAAQ,QAAQ,MAAM;YAC/B,kGAAkG;YAClG,mDAAmD;YACnD,QAAQ,OAAO,CAAC,SAAS,GAAG;YAC5B,IAAI,SAAS,MAAM,QAAQ,IAAI;gBAC3B,kDAAkD;gBAClD,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;QACJ;QACA,IAAI,cAAc,KAAK,KAAK,CAAC,CAAC,QAAQ,OAAO,CAAC,IAAI;QAClD,IAAI,eAAe,KAAK,KAAK,CAAC,CAAC,SAAS,MAAM,CAAC,IAAI;QACnD,IAAI,eAAe,KAAK,gBAAgB,GAAG;YACvC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,iBAAiB,aAAa;YAC9B,sCAAsC;YACtC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,iEAAiE;QACjE,oEAAoE;QACpE,sCAAsC;QACtC,IAAI,QAAQ,QAAQ,GAAG,KAAK,KAAK,CAAC,aAAa;QAC/C,OAAO;QACP,QAAQ;QACR,qDAAqD;QACrD,mFAAmF;QACnF,yEAAyE;QACzE,IAAI,oBAAoB,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,cAAc,CAAC,IAAI,cAAc;QACvF,IAAI,oBAAoB,GAAG;YACvB,IAAI,oBAAoB,OAAO;gBAC3B,0BAA0B;gBAC1B,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,QAAQ;QACZ;QACA,kBAAkB;QAClB,IAAI,mBAAmB,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,eAAe,CAAC,IAAI,cAAc;QACtF,IAAI,mBAAmB,GAAG;YACtB,IAAI,mBAAmB,OAAO;gBAC1B,0BAA0B;gBAC1B,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,OAAO;QACX;QACA,6BAA6B;QAC7B,IAAI,OAAO,IAAI,2KAAA,CAAA,UAAS,CAAC,aAAa;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,IAAI,UAAU,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gBAClC,IAAI,MAAM,GAAG,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI,aAAa,UAAU;oBAClE,KAAK,GAAG,CAAC,GAAG;gBAChB;YACJ;QACJ;QACA,OAAO;IACX;IACA,aAAa,UAAU,GAAG,SAAU,YAAY,EAAE,KAAK;QACnD,IAAI,SAAS,MAAM,SAAS;QAC5B,IAAI,QAAQ,MAAM,QAAQ;QAC1B,IAAI,IAAI,YAAY,CAAC,EAAE;QACvB,IAAI,IAAI,YAAY,CAAC,EAAE;QACvB,IAAI,UAAU;QACd,IAAI,cAAc;QAClB,MAAO,IAAI,SAAS,IAAI,OAAQ;YAC5B,IAAI,YAAY,MAAM,GAAG,CAAC,GAAG,IAAI;gBAC7B,IAAI,EAAE,gBAAgB,GAAG;oBACrB;gBACJ;gBACA,UAAU,CAAC;YACf;YACA;YACA;QACJ;QACA,IAAI,MAAM,SAAS,MAAM,QAAQ;YAC7B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,OAAO,CAAC,IAAI,YAAY,CAAC,EAAE,IAAI;IACnC;IACA,aAAa,SAAS,GAAG,IAAI;IAC7B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/MaskUtil.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR>\n * <AUTHOR>\n * <AUTHOR>\n */\nvar MaskUtil = /** @class */ (function () {\n    function MaskUtil() {\n        // do nothing\n    }\n    /**\n     * Apply mask penalty rule 1 and return the penalty. Find repetitive cells with the same color and\n     * give penalty to them. Example: 00000 or 11111.\n     */\n    MaskUtil.applyMaskPenaltyRule1 = function (matrix) {\n        return MaskUtil.applyMaskPenaltyRule1Internal(matrix, true) + MaskUtil.applyMaskPenaltyRule1Internal(matrix, false);\n    };\n    /**\n     * Apply mask penalty rule 2 and return the penalty. Find 2x2 blocks with the same color and give\n     * penalty to them. This is actually equivalent to the spec's rule, which is to find MxN blocks and give a\n     * penalty proportional to (M-1)x(N-1), because this is the number of 2x2 blocks inside such a block.\n     */\n    MaskUtil.applyMaskPenaltyRule2 = function (matrix) {\n        var penalty = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height - 1; y++) {\n            var arrayY = array[y];\n            for (var x = 0; x < width - 1; x++) {\n                var value = arrayY[x];\n                if (value === arrayY[x + 1] && value === array[y + 1][x] && value === array[y + 1][x + 1]) {\n                    penalty++;\n                }\n            }\n        }\n        return MaskUtil.N2 * penalty;\n    };\n    /**\n     * Apply mask penalty rule 3 and return the penalty. Find consecutive runs of 1:1:3:1:1:4\n     * starting with black, or 4:1:1:3:1:1 starting with white, and give penalty to them.  If we\n     * find patterns like 000010111010000, we give penalty once.\n     */\n    MaskUtil.applyMaskPenaltyRule3 = function (matrix) {\n        var numPenalties = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height; y++) {\n            for (var x = 0; x < width; x++) {\n                var arrayY = array[y]; // We can at least optimize this access\n                if (x + 6 < width &&\n                    arrayY[x] === 1 &&\n                    arrayY[x + 1] === 0 &&\n                    arrayY[x + 2] === 1 &&\n                    arrayY[x + 3] === 1 &&\n                    arrayY[x + 4] === 1 &&\n                    arrayY[x + 5] === 0 &&\n                    arrayY[x + 6] === 1 &&\n                    (MaskUtil.isWhiteHorizontal(arrayY, x - 4, x) || MaskUtil.isWhiteHorizontal(arrayY, x + 7, x + 11))) {\n                    numPenalties++;\n                }\n                if (y + 6 < height &&\n                    array[y][x] === 1 &&\n                    array[y + 1][x] === 0 &&\n                    array[y + 2][x] === 1 &&\n                    array[y + 3][x] === 1 &&\n                    array[y + 4][x] === 1 &&\n                    array[y + 5][x] === 0 &&\n                    array[y + 6][x] === 1 &&\n                    (MaskUtil.isWhiteVertical(array, x, y - 4, y) || MaskUtil.isWhiteVertical(array, x, y + 7, y + 11))) {\n                    numPenalties++;\n                }\n            }\n        }\n        return numPenalties * MaskUtil.N3;\n    };\n    MaskUtil.isWhiteHorizontal = function (rowArray, from /*int*/, to /*int*/) {\n        from = Math.max(from, 0);\n        to = Math.min(to, rowArray.length);\n        for (var i = from; i < to; i++) {\n            if (rowArray[i] === 1) {\n                return false;\n            }\n        }\n        return true;\n    };\n    MaskUtil.isWhiteVertical = function (array, col /*int*/, from /*int*/, to /*int*/) {\n        from = Math.max(from, 0);\n        to = Math.min(to, array.length);\n        for (var i = from; i < to; i++) {\n            if (array[i][col] === 1) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * Apply mask penalty rule 4 and return the penalty. Calculate the ratio of dark cells and give\n     * penalty if the ratio is far from 50%. It gives 10 penalty for 5% distance.\n     */\n    MaskUtil.applyMaskPenaltyRule4 = function (matrix) {\n        var numDarkCells = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height; y++) {\n            var arrayY = array[y];\n            for (var x = 0; x < width; x++) {\n                if (arrayY[x] === 1) {\n                    numDarkCells++;\n                }\n            }\n        }\n        var numTotalCells = matrix.getHeight() * matrix.getWidth();\n        var fivePercentVariances = Math.floor(Math.abs(numDarkCells * 2 - numTotalCells) * 10 / numTotalCells);\n        return fivePercentVariances * MaskUtil.N4;\n    };\n    /**\n     * Return the mask bit for \"getMaskPattern\" at \"x\" and \"y\". See 8.8 of JISX0510:2004 for mask\n     * pattern conditions.\n     */\n    MaskUtil.getDataMaskBit = function (maskPattern /*int*/, x /*int*/, y /*int*/) {\n        var intermediate; /*int*/\n        var temp; /*int*/\n        switch (maskPattern) {\n            case 0:\n                intermediate = (y + x) & 0x1;\n                break;\n            case 1:\n                intermediate = y & 0x1;\n                break;\n            case 2:\n                intermediate = x % 3;\n                break;\n            case 3:\n                intermediate = (y + x) % 3;\n                break;\n            case 4:\n                intermediate = (Math.floor(y / 2) + Math.floor(x / 3)) & 0x1;\n                break;\n            case 5:\n                temp = y * x;\n                intermediate = (temp & 0x1) + (temp % 3);\n                break;\n            case 6:\n                temp = y * x;\n                intermediate = ((temp & 0x1) + (temp % 3)) & 0x1;\n                break;\n            case 7:\n                temp = y * x;\n                intermediate = ((temp % 3) + ((y + x) & 0x1)) & 0x1;\n                break;\n            default:\n                throw new IllegalArgumentException('Invalid mask pattern: ' + maskPattern);\n        }\n        return intermediate === 0;\n    };\n    /**\n     * Helper function for applyMaskPenaltyRule1. We need this for doing this calculation in both\n     * vertical and horizontal orders respectively.\n     */\n    MaskUtil.applyMaskPenaltyRule1Internal = function (matrix, isHorizontal) {\n        var penalty = 0;\n        var iLimit = isHorizontal ? matrix.getHeight() : matrix.getWidth();\n        var jLimit = isHorizontal ? matrix.getWidth() : matrix.getHeight();\n        var array = matrix.getArray();\n        for (var i = 0; i < iLimit; i++) {\n            var numSameBitCells = 0;\n            var prevBit = -1;\n            for (var j = 0; j < jLimit; j++) {\n                var bit = isHorizontal ? array[i][j] : array[j][i];\n                if (bit === prevBit) {\n                    numSameBitCells++;\n                }\n                else {\n                    if (numSameBitCells >= 5) {\n                        penalty += MaskUtil.N1 + (numSameBitCells - 5);\n                    }\n                    numSameBitCells = 1; // Include the cell itself.\n                    prevBit = bit;\n                }\n            }\n            if (numSameBitCells >= 5) {\n                penalty += MaskUtil.N1 + (numSameBitCells - 5);\n            }\n        }\n        return penalty;\n    };\n    // Penalty weights from section 6.8.2.1\n    MaskUtil.N1 = 3;\n    MaskUtil.N2 = 3;\n    MaskUtil.N3 = 40;\n    MaskUtil.N4 = 10;\n    return MaskUtil;\n}());\nexport default MaskUtil;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA;;;;CAIC,GACD,IAAI,WAA0B;IAC1B,SAAS;IACL,aAAa;IACjB;IACA;;;KAGC,GACD,SAAS,qBAAqB,GAAG,SAAU,MAAM;QAC7C,OAAO,SAAS,6BAA6B,CAAC,QAAQ,QAAQ,SAAS,6BAA6B,CAAC,QAAQ;IACjH;IACA;;;;KAIC,GACD,SAAS,qBAAqB,GAAG,SAAU,MAAM;QAC7C,IAAI,UAAU;QACd,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;YACjC,IAAI,SAAS,KAAK,CAAC,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;gBAChC,IAAI,QAAQ,MAAM,CAAC,EAAE;gBACrB,IAAI,UAAU,MAAM,CAAC,IAAI,EAAE,IAAI,UAAU,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,UAAU,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;oBACvF;gBACJ;YACJ;QACJ;QACA,OAAO,SAAS,EAAE,GAAG;IACzB;IACA;;;;KAIC,GACD,SAAS,qBAAqB,GAAG,SAAU,MAAM;QAC7C,IAAI,eAAe;QACnB,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,uCAAuC;gBAC9D,IAAI,IAAI,IAAI,SACR,MAAM,CAAC,EAAE,KAAK,KACd,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,MAAM,CAAC,IAAI,EAAE,KAAK,KAClB,CAAC,SAAS,iBAAiB,CAAC,QAAQ,IAAI,GAAG,MAAM,SAAS,iBAAiB,CAAC,QAAQ,IAAI,GAAG,IAAI,GAAG,GAAG;oBACrG;gBACJ;gBACA,IAAI,IAAI,IAAI,UACR,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAChB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KACpB,CAAC,SAAS,eAAe,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM,SAAS,eAAe,CAAC,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG;oBACrG;gBACJ;YACJ;QACJ;QACA,OAAO,eAAe,SAAS,EAAE;IACrC;IACA,SAAS,iBAAiB,GAAG,SAAU,QAAQ,EAAE,KAAK,KAAK,GAAN,EAAU,GAAG,KAAK,GAAN;QAC7D,OAAO,KAAK,GAAG,CAAC,MAAM;QACtB,KAAK,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;QACjC,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,IAAK;YAC5B,IAAI,QAAQ,CAAC,EAAE,KAAK,GAAG;gBACnB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,SAAS,eAAe,GAAG,SAAU,KAAK,EAAE,IAAI,KAAK,GAAN,EAAU,KAAK,KAAK,GAAN,EAAU,GAAG,KAAK,GAAN;QACrE,OAAO,KAAK,GAAG,CAAC,MAAM;QACtB,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM;QAC9B,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,IAAK;YAC5B,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG;gBACrB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,SAAS,qBAAqB,GAAG,SAAU,MAAM;QAC7C,IAAI,eAAe;QACnB,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,SAAS,KAAK,CAAC,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,MAAM,CAAC,EAAE,KAAK,GAAG;oBACjB;gBACJ;YACJ;QACJ;QACA,IAAI,gBAAgB,OAAO,SAAS,KAAK,OAAO,QAAQ;QACxD,IAAI,uBAAuB,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,eAAe,IAAI,iBAAiB,KAAK;QACxF,OAAO,uBAAuB,SAAS,EAAE;IAC7C;IACA;;;KAGC,GACD,SAAS,cAAc,GAAG,SAAU,YAAY,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACjE,IAAI,cAAc,KAAK;QACvB,IAAI,MAAM,KAAK;QACf,OAAQ;YACJ,KAAK;gBACD,eAAe,AAAC,IAAI,IAAK;gBACzB;YACJ,KAAK;gBACD,eAAe,IAAI;gBACnB;YACJ,KAAK;gBACD,eAAe,IAAI;gBACnB;YACJ,KAAK;gBACD,eAAe,CAAC,IAAI,CAAC,IAAI;gBACzB;YACJ,KAAK;gBACD,eAAe,AAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,KAAM;gBACzD;YACJ,KAAK;gBACD,OAAO,IAAI;gBACX,eAAe,CAAC,OAAO,GAAG,IAAK,OAAO;gBACtC;YACJ,KAAK;gBACD,OAAO,IAAI;gBACX,eAAe,AAAC,CAAC,OAAO,GAAG,IAAK,OAAO,IAAM;gBAC7C;YACJ,KAAK;gBACD,OAAO,IAAI;gBACX,eAAe,AAAE,OAAO,IAAK,CAAC,AAAC,IAAI,IAAK,GAAG,IAAK;gBAChD;YACJ;gBACI,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,2BAA2B;QACtE;QACA,OAAO,iBAAiB;IAC5B;IACA;;;KAGC,GACD,SAAS,6BAA6B,GAAG,SAAU,MAAM,EAAE,YAAY;QACnE,IAAI,UAAU;QACd,IAAI,SAAS,eAAe,OAAO,SAAS,KAAK,OAAO,QAAQ;QAChE,IAAI,SAAS,eAAe,OAAO,QAAQ,KAAK,OAAO,SAAS;QAChE,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,kBAAkB;YACtB,IAAI,UAAU,CAAC;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;gBAC7B,IAAI,MAAM,eAAe,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE;gBAClD,IAAI,QAAQ,SAAS;oBACjB;gBACJ,OACK;oBACD,IAAI,mBAAmB,GAAG;wBACtB,WAAW,SAAS,EAAE,GAAG,CAAC,kBAAkB,CAAC;oBACjD;oBACA,kBAAkB,GAAG,2BAA2B;oBAChD,UAAU;gBACd;YACJ;YACA,IAAI,mBAAmB,GAAG;gBACtB,WAAW,SAAS,EAAE,GAAG,CAAC,kBAAkB,CAAC;YACjD;QACJ;QACA,OAAO;IACX;IACA,uCAAuC;IACvC,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/ByteMatrix.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\n/*import java.util.Arrays;*/\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * JAVAPORT: The original code was a 2D array of ints, but since it only ever gets assigned\n * -1, 0, and 1, I'm going to use less memory and go with bytes.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar ByteMatrix = /** @class */ (function () {\n    function ByteMatrix(width /*int*/, height /*int*/) {\n        this.width = width;\n        this.height = height;\n        var bytes = new Array(height); // [height][width]\n        for (var i = 0; i !== height; i++) {\n            bytes[i] = new Uint8Array(width);\n        }\n        this.bytes = bytes;\n    }\n    ByteMatrix.prototype.getHeight = function () {\n        return this.height;\n    };\n    ByteMatrix.prototype.getWidth = function () {\n        return this.width;\n    };\n    ByteMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n        return this.bytes[y][x];\n    };\n    /**\n     * @return an internal representation as bytes, in row-major order. array[y][x] represents point (x,y)\n     */\n    ByteMatrix.prototype.getArray = function () {\n        return this.bytes;\n    };\n    // TYPESCRIPTPORT: preffer to let two methods instead of override to avoid type comparison inside\n    ByteMatrix.prototype.setNumber = function (x /*int*/, y /*int*/, value /*byte|int*/) {\n        this.bytes[y][x] = value;\n    };\n    // public set(x: number /*int*/, y: number /*int*/, value: number /*int*/): void {\n    //   bytes[y][x] = (byte) value\n    // }\n    ByteMatrix.prototype.setBoolean = function (x /*int*/, y /*int*/, value) {\n        this.bytes[y][x] = /*(byte) */ (value ? 1 : 0);\n    };\n    ByteMatrix.prototype.clear = function (value /*byte*/) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.bytes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var aByte = _c.value;\n                Arrays.fill(aByte, value);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    ByteMatrix.prototype.equals = function (o) {\n        if (!(o instanceof ByteMatrix)) {\n            return false;\n        }\n        var other = o;\n        if (this.width !== other.width) {\n            return false;\n        }\n        if (this.height !== other.height) {\n            return false;\n        }\n        for (var y = 0, height = this.height; y < height; ++y) {\n            var bytesY = this.bytes[y];\n            var otherBytesY = other.bytes[y];\n            for (var x = 0, width = this.width; x < width; ++x) {\n                if (bytesY[x] !== otherBytesY[x]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    /*@Override*/\n    ByteMatrix.prototype.toString = function () {\n        var result = new StringBuilder(); // (2 * width * height + 2)\n        for (var y = 0, height = this.height; y < height; ++y) {\n            var bytesY = this.bytes[y];\n            for (var x = 0, width = this.width; x < width; ++x) {\n                switch (bytesY[x]) {\n                    case 0:\n                        result.append(' 0');\n                        break;\n                    case 1:\n                        result.append(' 1');\n                        break;\n                    default:\n                        result.append('  ');\n                        break;\n                }\n            }\n            result.append('\\n');\n        }\n        return result.toString();\n    };\n    return ByteMatrix;\n}());\nexport default ByteMatrix;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,6CAA6C,GAC7C,0BAA0B,GAC1B;AACA;AAdA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAKA;;;;;CAKC,GACD,IAAI,aAA4B;IAC5B,SAAS,WAAW,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACrC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,QAAQ,IAAI,MAAM,SAAS,kBAAkB;QACjD,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAK;YAC/B,KAAK,CAAC,EAAE,GAAG,IAAI,WAAW;QAC9B;QACA,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,WAAW,SAAS,CAAC,SAAS,GAAG;QAC7B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,WAAW,SAAS,CAAC,QAAQ,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;IAC3B;IACA;;KAEC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,iGAAiG;IACjG,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,MAAM,UAAU,GAAX;QAClE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG;IACvB;IACA,kFAAkF;IAClF,+BAA+B;IAC/B,IAAI;IACJ,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,KAAK;QACnE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAgB,QAAQ,IAAI;IAChD;IACA,WAAW,SAAS,CAAC,KAAK,GAAG,SAAU,MAAM,MAAM,GAAP;QACxC,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC1E,IAAI,QAAQ,GAAG,KAAK;gBACpB,sKAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAAO;YACvB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACrC,IAAI,CAAC,CAAC,aAAa,UAAU,GAAG;YAC5B,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,EAAE;YAC5B,OAAO;QACX;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,MAAM,EAAE;YAC9B,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,EAAE,EAAG;YACnD,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,cAAc,MAAM,KAAK,CAAC,EAAE;YAChC,IAAK,IAAI,IAAI,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,OAAO,EAAE,EAAG;gBAChD,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,EAAE;oBAC9B,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,WAAW,GACX,WAAW,SAAS,CAAC,QAAQ,GAAG;QAC5B,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa,IAAI,2BAA2B;QAC7D,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,EAAE,EAAG;YACnD,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAK,IAAI,IAAI,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,OAAO,EAAE,EAAG;gBAChD,OAAQ,MAAM,CAAC,EAAE;oBACb,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ;wBACI,OAAO,MAAM,CAAC;wBACd;gBACR;YACJ;YACA,OAAO,MAAM,CAAC;QAClB;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/QRCode.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar QRCode = /** @class */ (function () {\n    function QRCode() {\n        this.maskPattern = -1;\n    }\n    QRCode.prototype.getMode = function () {\n        return this.mode;\n    };\n    QRCode.prototype.getECLevel = function () {\n        return this.ecLevel;\n    };\n    QRCode.prototype.getVersion = function () {\n        return this.version;\n    };\n    QRCode.prototype.getMaskPattern = function () {\n        return this.maskPattern;\n    };\n    QRCode.prototype.getMatrix = function () {\n        return this.matrix;\n    };\n    /*@Override*/\n    QRCode.prototype.toString = function () {\n        var result = new StringBuilder(); // (200)\n        result.append('<<\\n');\n        result.append(' mode: ');\n        result.append(this.mode ? this.mode.toString() : 'null');\n        result.append('\\n ecLevel: ');\n        result.append(this.ecLevel ? this.ecLevel.toString() : 'null');\n        result.append('\\n version: ');\n        result.append(this.version ? this.version.toString() : 'null');\n        result.append('\\n maskPattern: ');\n        result.append(this.maskPattern.toString());\n        if (this.matrix) {\n            result.append('\\n matrix:\\n');\n            result.append(this.matrix.toString());\n        }\n        else {\n            result.append('\\n matrix: null\\n');\n        }\n        result.append('>>\\n');\n        return result.toString();\n    };\n    QRCode.prototype.setMode = function (value) {\n        this.mode = value;\n    };\n    QRCode.prototype.setECLevel = function (value) {\n        this.ecLevel = value;\n    };\n    QRCode.prototype.setVersion = function (version) {\n        this.version = version;\n    };\n    QRCode.prototype.setMaskPattern = function (value /*int*/) {\n        this.maskPattern = value;\n    };\n    QRCode.prototype.setMatrix = function (value) {\n        this.matrix = value;\n    };\n    // Check if \"mask_pattern\" is valid.\n    QRCode.isValidMaskPattern = function (maskPattern /*int*/) {\n        return maskPattern >= 0 && maskPattern < QRCode.NUM_MASK_PATTERNS;\n    };\n    QRCode.NUM_MASK_PATTERNS = 8;\n    return QRCode;\n}());\nexport default QRCode;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA;;;CAGC,GACD,IAAI,SAAwB;IACxB,SAAS;QACL,IAAI,CAAC,WAAW,GAAG,CAAC;IACxB;IACA,OAAO,SAAS,CAAC,OAAO,GAAG;QACvB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,OAAO,SAAS,CAAC,UAAU,GAAG;QAC1B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,OAAO,SAAS,CAAC,UAAU,GAAG;QAC1B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,OAAO,SAAS,CAAC,cAAc,GAAG;QAC9B,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,SAAS,CAAC,SAAS,GAAG;QACzB,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,WAAW,GACX,OAAO,SAAS,CAAC,QAAQ,GAAG;QACxB,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa,IAAI,QAAQ;QAC1C,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK;QACjD,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK;QACvD,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK;QACvD,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ;QACvC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,MAAM,CAAC;YACd,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;QACtC,OACK;YACD,OAAO,MAAM,CAAC;QAClB;QACA,OAAO,MAAM,CAAC;QACd,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK;QACtC,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK;QACzC,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO;QAC3C,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,OAAO,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,KAAK,GAAN;QAC7C,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,OAAO,SAAS,CAAC,SAAS,GAAG,SAAU,KAAK;QACxC,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,oCAAoC;IACpC,OAAO,kBAAkB,GAAG,SAAU,YAAY,KAAK,GAAN;QAC7C,OAAO,eAAe,KAAK,cAAc,OAAO,iBAAiB;IACrE;IACA,OAAO,iBAAiB,GAAG;IAC3B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/MatrixUtil.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport BitArray from '../../common/BitArray';\nimport Integer from '../../util/Integer';\nimport QRCode from './QRCode';\nimport <PERSON>Util from './MaskUtil';\nimport WriterException from '../../WriterException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar MatrixUtil = /** @class */ (function () {\n    function MatrixUtil() {\n        // do nothing\n    }\n    // Set all cells to -1 (TYPESCRIPTPORT: 255).  -1 (TYPESCRIPTPORT: 255) means that the cell is empty (not set yet).\n    //\n    // JAVAPORT: We shouldn't need to do this at all. The code should be rewritten to begin encoding\n    // with the ByteMatrix initialized all to zero.\n    MatrixUtil.clearMatrix = function (matrix) {\n        // TYPESCRIPTPORT: we use UintArray se changed here from -1 to 255\n        matrix.clear(/*(byte) */ /*-1*/ 255);\n    };\n    // Build 2D matrix of QR Code from \"dataBits\" with \"ecLevel\", \"version\" and \"getMaskPattern\". On\n    // success, store the result in \"matrix\" and return true.\n    MatrixUtil.buildMatrix = function (dataBits, ecLevel, version, maskPattern /*int*/, matrix) {\n        MatrixUtil.clearMatrix(matrix);\n        MatrixUtil.embedBasicPatterns(version, matrix);\n        // Type information appear with any version.\n        MatrixUtil.embedTypeInfo(ecLevel, maskPattern, matrix);\n        // Version info appear if version >= 7.\n        MatrixUtil.maybeEmbedVersionInfo(version, matrix);\n        // Data should be embedded at end.\n        MatrixUtil.embedDataBits(dataBits, maskPattern, matrix);\n    };\n    // Embed basic patterns. On success, modify the matrix and return true.\n    // The basic patterns are:\n    // - Position detection patterns\n    // - Timing patterns\n    // - Dark dot at the left bottom corner\n    // - Position adjustment patterns, if need be\n    MatrixUtil.embedBasicPatterns = function (version, matrix) {\n        // Let's get started with embedding big squares at corners.\n        MatrixUtil.embedPositionDetectionPatternsAndSeparators(matrix);\n        // Then, embed the dark dot at the left bottom corner.\n        MatrixUtil.embedDarkDotAtLeftBottomCorner(matrix);\n        // Position adjustment patterns appear if version >= 2.\n        MatrixUtil.maybeEmbedPositionAdjustmentPatterns(version, matrix);\n        // Timing patterns should be embedded after position adj. patterns.\n        MatrixUtil.embedTimingPatterns(matrix);\n    };\n    // Embed type information. On success, modify the matrix.\n    MatrixUtil.embedTypeInfo = function (ecLevel, maskPattern /*int*/, matrix) {\n        var typeInfoBits = new BitArray();\n        MatrixUtil.makeTypeInfoBits(ecLevel, maskPattern, typeInfoBits);\n        for (var i = 0, size = typeInfoBits.getSize(); i < size; ++i) {\n            // Place bits in LSB to MSB order.  LSB (least significant bit) is the last value in\n            // \"typeInfoBits\".\n            var bit = typeInfoBits.get(typeInfoBits.getSize() - 1 - i);\n            // Type info bits at the left top corner. See 8.9 of JISX0510:2004 (p.46).\n            var coordinates = MatrixUtil.TYPE_INFO_COORDINATES[i];\n            var x1 = coordinates[0];\n            var y1 = coordinates[1];\n            matrix.setBoolean(x1, y1, bit);\n            if (i < 8) {\n                // Right top corner.\n                var x2 = matrix.getWidth() - i - 1;\n                var y2 = 8;\n                matrix.setBoolean(x2, y2, bit);\n            }\n            else {\n                // Left bottom corner.\n                var x2 = 8;\n                var y2 = matrix.getHeight() - 7 + (i - 8);\n                matrix.setBoolean(x2, y2, bit);\n            }\n        }\n    };\n    // Embed version information if need be. On success, modify the matrix and return true.\n    // See 8.10 of JISX0510:2004 (p.47) for how to embed version information.\n    MatrixUtil.maybeEmbedVersionInfo = function (version, matrix) {\n        if (version.getVersionNumber() < 7) { // Version info is necessary if version >= 7.\n            return; // Don't need version info.\n        }\n        var versionInfoBits = new BitArray();\n        MatrixUtil.makeVersionInfoBits(version, versionInfoBits);\n        var bitIndex = 6 * 3 - 1; // It will decrease from 17 to 0.\n        for (var i = 0; i < 6; ++i) {\n            for (var j = 0; j < 3; ++j) {\n                // Place bits in LSB (least significant bit) to MSB order.\n                var bit = versionInfoBits.get(bitIndex);\n                bitIndex--;\n                // Left bottom corner.\n                matrix.setBoolean(i, matrix.getHeight() - 11 + j, bit);\n                // Right bottom corner.\n                matrix.setBoolean(matrix.getHeight() - 11 + j, i, bit);\n            }\n        }\n    };\n    // Embed \"dataBits\" using \"getMaskPattern\". On success, modify the matrix and return true.\n    // For debugging purposes, it skips masking process if \"getMaskPattern\" is -1(TYPESCRIPTPORT: 255).\n    // See 8.7 of JISX0510:2004 (p.38) for how to embed data bits.\n    MatrixUtil.embedDataBits = function (dataBits, maskPattern /*int*/, matrix) {\n        var bitIndex = 0;\n        var direction = -1;\n        // Start from the right bottom cell.\n        var x = matrix.getWidth() - 1;\n        var y = matrix.getHeight() - 1;\n        while (x > 0) {\n            // Skip the vertical timing pattern.\n            if (x === 6) {\n                x -= 1;\n            }\n            while (y >= 0 && y < matrix.getHeight()) {\n                for (var i = 0; i < 2; ++i) {\n                    var xx = x - i;\n                    // Skip the cell if it's not empty.\n                    if (!MatrixUtil.isEmpty(matrix.get(xx, y))) {\n                        continue;\n                    }\n                    var bit = void 0;\n                    if (bitIndex < dataBits.getSize()) {\n                        bit = dataBits.get(bitIndex);\n                        ++bitIndex;\n                    }\n                    else {\n                        // Padding bit. If there is no bit left, we'll fill the left cells with 0, as described\n                        // in 8.4.9 of JISX0510:2004 (p. 24).\n                        bit = false;\n                    }\n                    // Skip masking if mask_pattern is -1 (TYPESCRIPTPORT: 255).\n                    if (maskPattern !== 255 && MaskUtil.getDataMaskBit(maskPattern, xx, y)) {\n                        bit = !bit;\n                    }\n                    matrix.setBoolean(xx, y, bit);\n                }\n                y += direction;\n            }\n            direction = -direction; // Reverse the direction.\n            y += direction;\n            x -= 2; // Move to the left.\n        }\n        // All bits should be consumed.\n        if (bitIndex !== dataBits.getSize()) {\n            throw new WriterException('Not all bits consumed: ' + bitIndex + '/' + dataBits.getSize());\n        }\n    };\n    // Return the position of the most significant bit set (one: to) in the \"value\". The most\n    // significant bit is position 32. If there is no bit set, return 0. Examples:\n    // - findMSBSet(0) => 0\n    // - findMSBSet(1) => 1\n    // - findMSBSet(255) => 8\n    MatrixUtil.findMSBSet = function (value /*int*/) {\n        return 32 - Integer.numberOfLeadingZeros(value);\n    };\n    // Calculate BCH (Bose-Chaudhuri-Hocquenghem) code for \"value\" using polynomial \"poly\". The BCH\n    // code is used for encoding type information and version information.\n    // Example: Calculation of version information of 7.\n    // f(x) is created from 7.\n    //   - 7 = 000111 in 6 bits\n    //   - f(x) = x^2 + x^1 + x^0\n    // g(x) is given by the standard (p. 67)\n    //   - g(x) = x^12 + x^11 + x^10 + x^9 + x^8 + x^5 + x^2 + 1\n    // Multiply f(x) by x^(18 - 6)\n    //   - f'(x) = f(x) * x^(18 - 6)\n    //   - f'(x) = x^14 + x^13 + x^12\n    // Calculate the remainder of f'(x) / g(x)\n    //         x^2\n    //         __________________________________________________\n    //   g(x) )x^14 + x^13 + x^12\n    //         x^14 + x^13 + x^12 + x^11 + x^10 + x^7 + x^4 + x^2\n    //         --------------------------------------------------\n    //                              x^11 + x^10 + x^7 + x^4 + x^2\n    //\n    // The remainder is x^11 + x^10 + x^7 + x^4 + x^2\n    // Encode it in binary: 110010010100\n    // The return value is 0xc94 (1100 1001 0100)\n    //\n    // Since all coefficients in the polynomials are 1 or 0, we can do the calculation by bit\n    // operations. We don't care if coefficients are positive or negative.\n    MatrixUtil.calculateBCHCode = function (value /*int*/, poly /*int*/) {\n        if (poly === 0) {\n            throw new IllegalArgumentException('0 polynomial');\n        }\n        // If poly is \"1 1111 0010 0101\" (version info poly), msbSetInPoly is 13. We'll subtract 1\n        // from 13 to make it 12.\n        var msbSetInPoly = MatrixUtil.findMSBSet(poly);\n        value <<= msbSetInPoly - 1;\n        // Do the division business using exclusive-or operations.\n        while (MatrixUtil.findMSBSet(value) >= msbSetInPoly) {\n            value ^= poly << (MatrixUtil.findMSBSet(value) - msbSetInPoly);\n        }\n        // Now the \"value\" is the remainder (i.e. the BCH code)\n        return value;\n    };\n    // Make bit vector of type information. On success, store the result in \"bits\" and return true.\n    // Encode error correction level and mask pattern. See 8.9 of\n    // JISX0510:2004 (p.45) for details.\n    MatrixUtil.makeTypeInfoBits = function (ecLevel, maskPattern /*int*/, bits) {\n        if (!QRCode.isValidMaskPattern(maskPattern)) {\n            throw new WriterException('Invalid mask pattern');\n        }\n        var typeInfo = (ecLevel.getBits() << 3) | maskPattern;\n        bits.appendBits(typeInfo, 5);\n        var bchCode = MatrixUtil.calculateBCHCode(typeInfo, MatrixUtil.TYPE_INFO_POLY);\n        bits.appendBits(bchCode, 10);\n        var maskBits = new BitArray();\n        maskBits.appendBits(MatrixUtil.TYPE_INFO_MASK_PATTERN, 15);\n        bits.xor(maskBits);\n        if (bits.getSize() !== 15) { // Just in case.\n            throw new WriterException('should not happen but we got: ' + bits.getSize());\n        }\n    };\n    // Make bit vector of version information. On success, store the result in \"bits\" and return true.\n    // See 8.10 of JISX0510:2004 (p.45) for details.\n    MatrixUtil.makeVersionInfoBits = function (version, bits) {\n        bits.appendBits(version.getVersionNumber(), 6);\n        var bchCode = MatrixUtil.calculateBCHCode(version.getVersionNumber(), MatrixUtil.VERSION_INFO_POLY);\n        bits.appendBits(bchCode, 12);\n        if (bits.getSize() !== 18) { // Just in case.\n            throw new WriterException('should not happen but we got: ' + bits.getSize());\n        }\n    };\n    // Check if \"value\" is empty.\n    MatrixUtil.isEmpty = function (value /*int*/) {\n        return value === 255; // -1\n    };\n    MatrixUtil.embedTimingPatterns = function (matrix) {\n        // -8 is for skipping position detection patterns (7: size), and two horizontal/vertical\n        // separation patterns (1: size). Thus, 8 = 7 + 1.\n        for (var i = 8; i < matrix.getWidth() - 8; ++i) {\n            var bit = (i + 1) % 2;\n            // Horizontal line.\n            if (MatrixUtil.isEmpty(matrix.get(i, 6))) {\n                matrix.setNumber(i, 6, bit);\n            }\n            // Vertical line.\n            if (MatrixUtil.isEmpty(matrix.get(6, i))) {\n                matrix.setNumber(6, i, bit);\n            }\n        }\n    };\n    // Embed the lonely dark dot at left bottom corner. JISX0510:2004 (p.46)\n    MatrixUtil.embedDarkDotAtLeftBottomCorner = function (matrix) {\n        if (matrix.get(8, matrix.getHeight() - 8) === 0) {\n            throw new WriterException();\n        }\n        matrix.setNumber(8, matrix.getHeight() - 8, 1);\n    };\n    MatrixUtil.embedHorizontalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var x = 0; x < 8; ++x) {\n            if (!MatrixUtil.isEmpty(matrix.get(xStart + x, yStart))) {\n                throw new WriterException();\n            }\n            matrix.setNumber(xStart + x, yStart, 0);\n        }\n    };\n    MatrixUtil.embedVerticalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 7; ++y) {\n            if (!MatrixUtil.isEmpty(matrix.get(xStart, yStart + y))) {\n                throw new WriterException();\n            }\n            matrix.setNumber(xStart, yStart + y, 0);\n        }\n    };\n    MatrixUtil.embedPositionAdjustmentPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 5; ++y) {\n            var patternY = MatrixUtil.POSITION_ADJUSTMENT_PATTERN[y];\n            for (var x = 0; x < 5; ++x) {\n                matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n            }\n        }\n    };\n    MatrixUtil.embedPositionDetectionPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 7; ++y) {\n            var patternY = MatrixUtil.POSITION_DETECTION_PATTERN[y];\n            for (var x = 0; x < 7; ++x) {\n                matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n            }\n        }\n    };\n    // Embed position detection patterns and surrounding vertical/horizontal separators.\n    MatrixUtil.embedPositionDetectionPatternsAndSeparators = function (matrix) {\n        // Embed three big squares at corners.\n        var pdpWidth = MatrixUtil.POSITION_DETECTION_PATTERN[0].length;\n        // Left top corner.\n        MatrixUtil.embedPositionDetectionPattern(0, 0, matrix);\n        // Right top corner.\n        MatrixUtil.embedPositionDetectionPattern(matrix.getWidth() - pdpWidth, 0, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedPositionDetectionPattern(0, matrix.getWidth() - pdpWidth, matrix);\n        // Embed horizontal separation patterns around the squares.\n        var hspWidth = 8;\n        // Left top corner.\n        MatrixUtil.embedHorizontalSeparationPattern(0, hspWidth - 1, matrix);\n        // Right top corner.\n        MatrixUtil.embedHorizontalSeparationPattern(matrix.getWidth() - hspWidth, hspWidth - 1, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedHorizontalSeparationPattern(0, matrix.getWidth() - hspWidth, matrix);\n        // Embed vertical separation patterns around the squares.\n        var vspSize = 7;\n        // Left top corner.\n        MatrixUtil.embedVerticalSeparationPattern(vspSize, 0, matrix);\n        // Right top corner.\n        MatrixUtil.embedVerticalSeparationPattern(matrix.getHeight() - vspSize - 1, 0, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedVerticalSeparationPattern(vspSize, matrix.getHeight() - vspSize, matrix);\n    };\n    // Embed position adjustment patterns if need be.\n    MatrixUtil.maybeEmbedPositionAdjustmentPatterns = function (version, matrix) {\n        if (version.getVersionNumber() < 2) { // The patterns appear if version >= 2\n            return;\n        }\n        var index = version.getVersionNumber() - 1;\n        var coordinates = MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[index];\n        for (var i = 0, length_1 = coordinates.length; i !== length_1; i++) {\n            var y = coordinates[i];\n            if (y >= 0) {\n                for (var j = 0; j !== length_1; j++) {\n                    var x = coordinates[j];\n                    if (x >= 0 && MatrixUtil.isEmpty(matrix.get(x, y))) {\n                        // If the cell is unset, we embed the position adjustment pattern here.\n                        // -2 is necessary since the x/y coordinates point to the center of the pattern, not the\n                        // left top corner.\n                        MatrixUtil.embedPositionAdjustmentPattern(x - 2, y - 2, matrix);\n                    }\n                }\n            }\n        }\n    };\n    MatrixUtil.POSITION_DETECTION_PATTERN = Array.from([\n        Int32Array.from([1, 1, 1, 1, 1, 1, 1]),\n        Int32Array.from([1, 0, 0, 0, 0, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 0, 0, 0, 0, 1]),\n        Int32Array.from([1, 1, 1, 1, 1, 1, 1]),\n    ]);\n    MatrixUtil.POSITION_ADJUSTMENT_PATTERN = Array.from([\n        Int32Array.from([1, 1, 1, 1, 1]),\n        Int32Array.from([1, 0, 0, 0, 1]),\n        Int32Array.from([1, 0, 1, 0, 1]),\n        Int32Array.from([1, 0, 0, 0, 1]),\n        Int32Array.from([1, 1, 1, 1, 1]),\n    ]);\n    // From Appendix E. Table 1, JIS0510X:2004 (71: p). The table was double-checked by komatsu.\n    MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE = Array.from([\n        Int32Array.from([-1, -1, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 18, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 22, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 30, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 34, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 22, 38, -1, -1, -1, -1]),\n        Int32Array.from([6, 24, 42, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, 46, -1, -1, -1, -1]),\n        Int32Array.from([6, 28, 50, -1, -1, -1, -1]),\n        Int32Array.from([6, 30, 54, -1, -1, -1, -1]),\n        Int32Array.from([6, 32, 58, -1, -1, -1, -1]),\n        Int32Array.from([6, 34, 62, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, 46, 66, -1, -1, -1]),\n        Int32Array.from([6, 26, 48, 70, -1, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, -1, -1, -1]),\n        Int32Array.from([6, 30, 54, 78, -1, -1, -1]),\n        Int32Array.from([6, 30, 56, 82, -1, -1, -1]),\n        Int32Array.from([6, 30, 58, 86, -1, -1, -1]),\n        Int32Array.from([6, 34, 62, 90, -1, -1, -1]),\n        Int32Array.from([6, 28, 50, 72, 94, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, 98, -1, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, -1, -1]),\n        Int32Array.from([6, 28, 54, 80, 106, -1, -1]),\n        Int32Array.from([6, 32, 58, 84, 110, -1, -1]),\n        Int32Array.from([6, 30, 58, 86, 114, -1, -1]),\n        Int32Array.from([6, 34, 62, 90, 118, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, 98, 122, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, 126, -1]),\n        Int32Array.from([6, 26, 52, 78, 104, 130, -1]),\n        Int32Array.from([6, 30, 56, 82, 108, 134, -1]),\n        Int32Array.from([6, 34, 60, 86, 112, 138, -1]),\n        Int32Array.from([6, 30, 58, 86, 114, 142, -1]),\n        Int32Array.from([6, 34, 62, 90, 118, 146, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, 126, 150]),\n        Int32Array.from([6, 24, 50, 76, 102, 128, 154]),\n        Int32Array.from([6, 28, 54, 80, 106, 132, 158]),\n        Int32Array.from([6, 32, 58, 84, 110, 136, 162]),\n        Int32Array.from([6, 26, 54, 82, 110, 138, 166]),\n        Int32Array.from([6, 30, 58, 86, 114, 142, 170]),\n    ]);\n    // Type info cells at the left top corner.\n    MatrixUtil.TYPE_INFO_COORDINATES = Array.from([\n        Int32Array.from([8, 0]),\n        Int32Array.from([8, 1]),\n        Int32Array.from([8, 2]),\n        Int32Array.from([8, 3]),\n        Int32Array.from([8, 4]),\n        Int32Array.from([8, 5]),\n        Int32Array.from([8, 7]),\n        Int32Array.from([8, 8]),\n        Int32Array.from([7, 8]),\n        Int32Array.from([5, 8]),\n        Int32Array.from([4, 8]),\n        Int32Array.from([3, 8]),\n        Int32Array.from([2, 8]),\n        Int32Array.from([1, 8]),\n        Int32Array.from([0, 8]),\n    ]);\n    // From Appendix D in JISX0510:2004 (p. 67)\n    MatrixUtil.VERSION_INFO_POLY = 0x1f25; // 1 1111 0010 0101\n    // From Appendix C in JISX0510:2004 (p.65).\n    MatrixUtil.TYPE_INFO_POLY = 0x537;\n    MatrixUtil.TYPE_INFO_MASK_PATTERN = 0x5412;\n    return MatrixUtil;\n}());\nexport default MatrixUtil;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,6CAA6C;;;AAC7C;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA;;;CAGC,GACD,IAAI,aAA4B;IAC5B,SAAS;IACL,aAAa;IACjB;IACA,mHAAmH;IACnH,EAAE;IACF,gGAAgG;IAChG,+CAA+C;IAC/C,WAAW,WAAW,GAAG,SAAU,MAAM;QACrC,kEAAkE;QAClE,OAAO,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG;IACpC;IACA,gGAAgG;IAChG,yDAAyD;IACzD,WAAW,WAAW,GAAG,SAAU,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,KAAK,GAAN,EAAU,MAAM;QACtF,WAAW,WAAW,CAAC;QACvB,WAAW,kBAAkB,CAAC,SAAS;QACvC,4CAA4C;QAC5C,WAAW,aAAa,CAAC,SAAS,aAAa;QAC/C,uCAAuC;QACvC,WAAW,qBAAqB,CAAC,SAAS;QAC1C,kCAAkC;QAClC,WAAW,aAAa,CAAC,UAAU,aAAa;IACpD;IACA,uEAAuE;IACvE,0BAA0B;IAC1B,gCAAgC;IAChC,oBAAoB;IACpB,uCAAuC;IACvC,6CAA6C;IAC7C,WAAW,kBAAkB,GAAG,SAAU,OAAO,EAAE,MAAM;QACrD,2DAA2D;QAC3D,WAAW,2CAA2C,CAAC;QACvD,sDAAsD;QACtD,WAAW,8BAA8B,CAAC;QAC1C,uDAAuD;QACvD,WAAW,oCAAoC,CAAC,SAAS;QACzD,mEAAmE;QACnE,WAAW,mBAAmB,CAAC;IACnC;IACA,yDAAyD;IACzD,WAAW,aAAa,GAAG,SAAU,OAAO,EAAE,YAAY,KAAK,GAAN,EAAU,MAAM;QACrE,IAAI,eAAe,IAAI,0KAAA,CAAA,UAAQ;QAC/B,WAAW,gBAAgB,CAAC,SAAS,aAAa;QAClD,IAAK,IAAI,IAAI,GAAG,OAAO,aAAa,OAAO,IAAI,IAAI,MAAM,EAAE,EAAG;YAC1D,oFAAoF;YACpF,kBAAkB;YAClB,IAAI,MAAM,aAAa,GAAG,CAAC,aAAa,OAAO,KAAK,IAAI;YACxD,0EAA0E;YAC1E,IAAI,cAAc,WAAW,qBAAqB,CAAC,EAAE;YACrD,IAAI,KAAK,WAAW,CAAC,EAAE;YACvB,IAAI,KAAK,WAAW,CAAC,EAAE;YACvB,OAAO,UAAU,CAAC,IAAI,IAAI;YAC1B,IAAI,IAAI,GAAG;gBACP,oBAAoB;gBACpB,IAAI,KAAK,OAAO,QAAQ,KAAK,IAAI;gBACjC,IAAI,KAAK;gBACT,OAAO,UAAU,CAAC,IAAI,IAAI;YAC9B,OACK;gBACD,sBAAsB;gBACtB,IAAI,KAAK;gBACT,IAAI,KAAK,OAAO,SAAS,KAAK,IAAI,CAAC,IAAI,CAAC;gBACxC,OAAO,UAAU,CAAC,IAAI,IAAI;YAC9B;QACJ;IACJ;IACA,uFAAuF;IACvF,yEAAyE;IACzE,WAAW,qBAAqB,GAAG,SAAU,OAAO,EAAE,MAAM;QACxD,IAAI,QAAQ,gBAAgB,KAAK,GAAG;YAChC,QAAQ,2BAA2B;QACvC;QACA,IAAI,kBAAkB,IAAI,0KAAA,CAAA,UAAQ;QAClC,WAAW,mBAAmB,CAAC,SAAS;QACxC,IAAI,WAAW,IAAI,IAAI,GAAG,iCAAiC;QAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACxB,0DAA0D;gBAC1D,IAAI,MAAM,gBAAgB,GAAG,CAAC;gBAC9B;gBACA,sBAAsB;gBACtB,OAAO,UAAU,CAAC,GAAG,OAAO,SAAS,KAAK,KAAK,GAAG;gBAClD,uBAAuB;gBACvB,OAAO,UAAU,CAAC,OAAO,SAAS,KAAK,KAAK,GAAG,GAAG;YACtD;QACJ;IACJ;IACA,0FAA0F;IAC1F,mGAAmG;IACnG,8DAA8D;IAC9D,WAAW,aAAa,GAAG,SAAU,QAAQ,EAAE,YAAY,KAAK,GAAN,EAAU,MAAM;QACtE,IAAI,WAAW;QACf,IAAI,YAAY,CAAC;QACjB,oCAAoC;QACpC,IAAI,IAAI,OAAO,QAAQ,KAAK;QAC5B,IAAI,IAAI,OAAO,SAAS,KAAK;QAC7B,MAAO,IAAI,EAAG;YACV,oCAAoC;YACpC,IAAI,MAAM,GAAG;gBACT,KAAK;YACT;YACA,MAAO,KAAK,KAAK,IAAI,OAAO,SAAS,GAAI;gBACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;oBACxB,IAAI,KAAK,IAAI;oBACb,mCAAmC;oBACnC,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,KAAK;wBACxC;oBACJ;oBACA,IAAI,MAAM,KAAK;oBACf,IAAI,WAAW,SAAS,OAAO,IAAI;wBAC/B,MAAM,SAAS,GAAG,CAAC;wBACnB,EAAE;oBACN,OACK;wBACD,uFAAuF;wBACvF,qCAAqC;wBACrC,MAAM;oBACV;oBACA,4DAA4D;oBAC5D,IAAI,gBAAgB,OAAO,qLAAA,CAAA,UAAQ,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI;wBACpE,MAAM,CAAC;oBACX;oBACA,OAAO,UAAU,CAAC,IAAI,GAAG;gBAC7B;gBACA,KAAK;YACT;YACA,YAAY,CAAC,WAAW,yBAAyB;YACjD,KAAK;YACL,KAAK,GAAG,oBAAoB;QAChC;QACA,+BAA+B;QAC/B,IAAI,aAAa,SAAS,OAAO,IAAI;YACjC,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,4BAA4B,WAAW,MAAM,SAAS,OAAO;QAC3F;IACJ;IACA,yFAAyF;IACzF,8EAA8E;IAC9E,uBAAuB;IACvB,uBAAuB;IACvB,yBAAyB;IACzB,WAAW,UAAU,GAAG,SAAU,MAAM,KAAK,GAAN;QACnC,OAAO,KAAK,uKAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC;IAC7C;IACA,+FAA+F;IAC/F,sEAAsE;IACtE,oDAAoD;IACpD,0BAA0B;IAC1B,2BAA2B;IAC3B,6BAA6B;IAC7B,wCAAwC;IACxC,4DAA4D;IAC5D,8BAA8B;IAC9B,gCAAgC;IAChC,iCAAiC;IACjC,0CAA0C;IAC1C,cAAc;IACd,6DAA6D;IAC7D,6BAA6B;IAC7B,6DAA6D;IAC7D,6DAA6D;IAC7D,6DAA6D;IAC7D,EAAE;IACF,iDAAiD;IACjD,oCAAoC;IACpC,6CAA6C;IAC7C,EAAE;IACF,yFAAyF;IACzF,sEAAsE;IACtE,WAAW,gBAAgB,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,KAAK,KAAK,GAAN;QACvD,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;QACvC;QACA,0FAA0F;QAC1F,yBAAyB;QACzB,IAAI,eAAe,WAAW,UAAU,CAAC;QACzC,UAAU,eAAe;QACzB,0DAA0D;QAC1D,MAAO,WAAW,UAAU,CAAC,UAAU,aAAc;YACjD,SAAS,QAAS,WAAW,UAAU,CAAC,SAAS;QACrD;QACA,uDAAuD;QACvD,OAAO;IACX;IACA,+FAA+F;IAC/F,6DAA6D;IAC7D,oCAAoC;IACpC,WAAW,gBAAgB,GAAG,SAAU,OAAO,EAAE,YAAY,KAAK,GAAN,EAAU,IAAI;QACtE,IAAI,CAAC,mLAAA,CAAA,UAAM,CAAC,kBAAkB,CAAC,cAAc;YACzC,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI,WAAW,AAAC,QAAQ,OAAO,MAAM,IAAK;QAC1C,KAAK,UAAU,CAAC,UAAU;QAC1B,IAAI,UAAU,WAAW,gBAAgB,CAAC,UAAU,WAAW,cAAc;QAC7E,KAAK,UAAU,CAAC,SAAS;QACzB,IAAI,WAAW,IAAI,0KAAA,CAAA,UAAQ;QAC3B,SAAS,UAAU,CAAC,WAAW,sBAAsB,EAAE;QACvD,KAAK,GAAG,CAAC;QACT,IAAI,KAAK,OAAO,OAAO,IAAI;YACvB,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,mCAAmC,KAAK,OAAO;QAC7E;IACJ;IACA,kGAAkG;IAClG,gDAAgD;IAChD,WAAW,mBAAmB,GAAG,SAAU,OAAO,EAAE,IAAI;QACpD,KAAK,UAAU,CAAC,QAAQ,gBAAgB,IAAI;QAC5C,IAAI,UAAU,WAAW,gBAAgB,CAAC,QAAQ,gBAAgB,IAAI,WAAW,iBAAiB;QAClG,KAAK,UAAU,CAAC,SAAS;QACzB,IAAI,KAAK,OAAO,OAAO,IAAI;YACvB,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,mCAAmC,KAAK,OAAO;QAC7E;IACJ;IACA,6BAA6B;IAC7B,WAAW,OAAO,GAAG,SAAU,MAAM,KAAK,GAAN;QAChC,OAAO,UAAU,KAAK,KAAK;IAC/B;IACA,WAAW,mBAAmB,GAAG,SAAU,MAAM;QAC7C,wFAAwF;QACxF,kDAAkD;QAClD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG,EAAE,EAAG;YAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;YACpB,mBAAmB;YACnB,IAAI,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;gBACtC,OAAO,SAAS,CAAC,GAAG,GAAG;YAC3B;YACA,iBAAiB;YACjB,IAAI,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;gBACtC,OAAO,SAAS,CAAC,GAAG,GAAG;YAC3B;QACJ;IACJ;IACA,wEAAwE;IACxE,WAAW,8BAA8B,GAAG,SAAU,MAAM;QACxD,IAAI,OAAO,GAAG,CAAC,GAAG,OAAO,SAAS,KAAK,OAAO,GAAG;YAC7C,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,OAAO,SAAS,CAAC,GAAG,OAAO,SAAS,KAAK,GAAG;IAChD;IACA,WAAW,gCAAgC,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM;QAC1F,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,SAAS,GAAG,UAAU;gBACrD,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,SAAS,CAAC,SAAS,GAAG,QAAQ;QACzC;IACJ;IACA,WAAW,8BAA8B,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM;QACxF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,QAAQ,SAAS,KAAK;gBACrD,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO,SAAS,CAAC,QAAQ,SAAS,GAAG;QACzC;IACJ;IACA,WAAW,8BAA8B,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM;QACxF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,WAAW,WAAW,2BAA2B,CAAC,EAAE;YACxD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACxB,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;YACxD;QACJ;IACJ;IACA,WAAW,6BAA6B,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM;QACvF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,WAAW,WAAW,0BAA0B,CAAC,EAAE;YACvD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACxB,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;YACxD;QACJ;IACJ;IACA,oFAAoF;IACpF,WAAW,2CAA2C,GAAG,SAAU,MAAM;QACrE,sCAAsC;QACtC,IAAI,WAAW,WAAW,0BAA0B,CAAC,EAAE,CAAC,MAAM;QAC9D,mBAAmB;QACnB,WAAW,6BAA6B,CAAC,GAAG,GAAG;QAC/C,oBAAoB;QACpB,WAAW,6BAA6B,CAAC,OAAO,QAAQ,KAAK,UAAU,GAAG;QAC1E,sBAAsB;QACtB,WAAW,6BAA6B,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU;QAC1E,2DAA2D;QAC3D,IAAI,WAAW;QACf,mBAAmB;QACnB,WAAW,gCAAgC,CAAC,GAAG,WAAW,GAAG;QAC7D,oBAAoB;QACpB,WAAW,gCAAgC,CAAC,OAAO,QAAQ,KAAK,UAAU,WAAW,GAAG;QACxF,sBAAsB;QACtB,WAAW,gCAAgC,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU;QAC7E,yDAAyD;QACzD,IAAI,UAAU;QACd,mBAAmB;QACnB,WAAW,8BAA8B,CAAC,SAAS,GAAG;QACtD,oBAAoB;QACpB,WAAW,8BAA8B,CAAC,OAAO,SAAS,KAAK,UAAU,GAAG,GAAG;QAC/E,sBAAsB;QACtB,WAAW,8BAA8B,CAAC,SAAS,OAAO,SAAS,KAAK,SAAS;IACrF;IACA,iDAAiD;IACjD,WAAW,oCAAoC,GAAG,SAAU,OAAO,EAAE,MAAM;QACvE,IAAI,QAAQ,gBAAgB,KAAK,GAAG;YAChC;QACJ;QACA,IAAI,QAAQ,QAAQ,gBAAgB,KAAK;QACzC,IAAI,cAAc,WAAW,4CAA4C,CAAC,MAAM;QAChF,IAAK,IAAI,IAAI,GAAG,WAAW,YAAY,MAAM,EAAE,MAAM,UAAU,IAAK;YAChE,IAAI,IAAI,WAAW,CAAC,EAAE;YACtB,IAAI,KAAK,GAAG;gBACR,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,IAAK;oBACjC,IAAI,IAAI,WAAW,CAAC,EAAE;oBACtB,IAAI,KAAK,KAAK,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;wBAChD,uEAAuE;wBACvE,wFAAwF;wBACxF,mBAAmB;wBACnB,WAAW,8BAA8B,CAAC,IAAI,GAAG,IAAI,GAAG;oBAC5D;gBACJ;YACJ;QACJ;IACJ;IACA,WAAW,0BAA0B,GAAG,MAAM,IAAI,CAAC;QAC/C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACrC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;KACxC;IACD,WAAW,2BAA2B,GAAG,MAAM,IAAI,CAAC;QAChD,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;KAClC;IACD,4FAA4F;IAC5F,WAAW,4CAA4C,GAAG,MAAM,IAAI,CAAC;QACjE,WAAW,IAAI,CAAC;YAAC,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAI,CAAC;YAAG,CAAC;SAAE;QAC3C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK,CAAC;YAAG,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAK,CAAC;SAAE;QAC5C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK,CAAC;SAAE;QAC7C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAC9C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAC9C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAC9C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAC9C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAC9C,WAAW,IAAI,CAAC;YAAC;YAAG;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;KACjD;IACD,0CAA0C;IAC1C,WAAW,qBAAqB,GAAG,MAAM,IAAI,CAAC;QAC1C,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;QACtB,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;KACzB;IACD,2CAA2C;IAC3C,WAAW,iBAAiB,GAAG,QAAQ,mBAAmB;IAC1D,2CAA2C;IAC3C,WAAW,cAAc,GAAG;IAC5B,WAAW,sBAAsB,GAAG;IACpC,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/BlockPair.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nvar BlockPair = /** @class */ (function () {\n    function BlockPair(dataBytes, errorCorrectionBytes) {\n        this.dataBytes = dataBytes;\n        this.errorCorrectionBytes = errorCorrectionBytes;\n    }\n    BlockPair.prototype.getDataBytes = function () {\n        return this.dataBytes;\n    };\n    BlockPair.prototype.getErrorCorrectionBytes = function () {\n        return this.errorCorrectionBytes;\n    };\n    return BlockPair;\n}());\nexport default BlockPair;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,6CAA6C;;;AAC7C,IAAI,YAA2B;IAC3B,SAAS,UAAU,SAAS,EAAE,oBAAoB;QAC9C,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,oBAAoB,GAAG;IAChC;IACA,UAAU,SAAS,CAAC,YAAY,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,UAAU,SAAS,CAAC,uBAAuB,GAAG;QAC1C,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/encoder/Encoder.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport EncodeHintType from '../../EncodeHintType';\nimport BitArray from '../../common/BitArray';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport Mode from '../decoder/Mode';\nimport Version from '../decoder/Version';\nimport MaskUtil from './MaskUtil';\nimport ByteMatrix from './ByteMatrix';\nimport QRCode from './QRCode';\nimport MatrixUtil from './MatrixUtil';\nimport StringEncoding from '../../util/StringEncoding';\nimport BlockPair from './BlockPair';\nimport WriterException from '../../WriterException';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.Map;*/\n/**\n * <AUTHOR> (Satoru Takabayashi) - creator\n * <AUTHOR> (Daniel Switkin) - ported from C++\n */\nvar Encoder = /** @class */ (function () {\n    // TYPESCRIPTPORT: changed to UTF8, the default for js\n    function Encoder() {\n    }\n    // The mask penalty calculation is complicated.  See Table 21 of JISX0510:2004 (p.45) for details.\n    // Basically it applies four rules and summate all penalties.\n    Encoder.calculateMaskPenalty = function (matrix) {\n        return MaskUtil.applyMaskPenaltyRule1(matrix)\n            + MaskUtil.applyMaskPenaltyRule2(matrix)\n            + MaskUtil.applyMaskPenaltyRule3(matrix)\n            + MaskUtil.applyMaskPenaltyRule4(matrix);\n    };\n    /**\n     * @param content text to encode\n     * @param ecLevel error correction level to use\n     * @return {@link QRCode} representing the encoded QR code\n     * @throws WriterException if encoding can't succeed, because of for example invalid content\n     *   or configuration\n     */\n    // public static encode(content: string, ecLevel: ErrorCorrectionLevel): QRCode /*throws WriterException*/ {\n    //   return encode(content, ecLevel, null)\n    // }\n    Encoder.encode = function (content, ecLevel, hints) {\n        if (hints === void 0) { hints = null; }\n        // Determine what character encoding has been specified by the caller, if any\n        var encoding = Encoder.DEFAULT_BYTE_MODE_ENCODING;\n        var hasEncodingHint = hints !== null && undefined !== hints.get(EncodeHintType.CHARACTER_SET);\n        if (hasEncodingHint) {\n            encoding = hints.get(EncodeHintType.CHARACTER_SET).toString();\n        }\n        // Pick an encoding mode appropriate for the content. Note that this will not attempt to use\n        // multiple modes / segments even if that were more efficient. Twould be nice.\n        var mode = this.chooseMode(content, encoding);\n        // This will store the header information, like mode and\n        // length, as well as \"header\" segments like an ECI segment.\n        var headerBits = new BitArray();\n        // Append ECI segment if applicable\n        if (mode === Mode.BYTE && (hasEncodingHint || Encoder.DEFAULT_BYTE_MODE_ENCODING !== encoding)) {\n            var eci = CharacterSetECI.getCharacterSetECIByName(encoding);\n            if (eci !== undefined) {\n                this.appendECI(eci, headerBits);\n            }\n        }\n        // (With ECI in place,) Write the mode marker\n        this.appendModeInfo(mode, headerBits);\n        // Collect data within the main segment, separately, to count its size if needed. Don't add it to\n        // main payload yet.\n        var dataBits = new BitArray();\n        this.appendBytes(content, mode, dataBits, encoding);\n        var version;\n        if (hints !== null && undefined !== hints.get(EncodeHintType.QR_VERSION)) {\n            var versionNumber = Number.parseInt(hints.get(EncodeHintType.QR_VERSION).toString(), 10);\n            version = Version.getVersionForNumber(versionNumber);\n            var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, version);\n            if (!this.willFit(bitsNeeded, version, ecLevel)) {\n                throw new WriterException('Data too big for requested version');\n            }\n        }\n        else {\n            version = this.recommendVersion(ecLevel, mode, headerBits, dataBits);\n        }\n        var headerAndDataBits = new BitArray();\n        headerAndDataBits.appendBitArray(headerBits);\n        // Find \"length\" of main segment and write it\n        var numLetters = mode === Mode.BYTE ? dataBits.getSizeInBytes() : content.length;\n        this.appendLengthInfo(numLetters, version, mode, headerAndDataBits);\n        // Put data together into the overall payload\n        headerAndDataBits.appendBitArray(dataBits);\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        var numDataBytes = version.getTotalCodewords() - ecBlocks.getTotalECCodewords();\n        // Terminate the bits properly.\n        this.terminateBits(numDataBytes, headerAndDataBits);\n        // Interleave data bits with error correction code.\n        var finalBits = this.interleaveWithECBytes(headerAndDataBits, version.getTotalCodewords(), numDataBytes, ecBlocks.getNumBlocks());\n        var qrCode = new QRCode();\n        qrCode.setECLevel(ecLevel);\n        qrCode.setMode(mode);\n        qrCode.setVersion(version);\n        //  Choose the mask pattern and set to \"qrCode\".\n        var dimension = version.getDimensionForVersion();\n        var matrix = new ByteMatrix(dimension, dimension);\n        var maskPattern = this.chooseMaskPattern(finalBits, ecLevel, version, matrix);\n        qrCode.setMaskPattern(maskPattern);\n        // Build the matrix and set it to \"qrCode\".\n        MatrixUtil.buildMatrix(finalBits, ecLevel, version, maskPattern, matrix);\n        qrCode.setMatrix(matrix);\n        return qrCode;\n    };\n    /**\n     * Decides the smallest version of QR code that will contain all of the provided data.\n     *\n     * @throws WriterException if the data cannot fit in any version\n     */\n    Encoder.recommendVersion = function (ecLevel, mode, headerBits, dataBits) {\n        // Hard part: need to know version to know how many bits length takes. But need to know how many\n        // bits it takes to know version. First we take a guess at version by assuming version will be\n        // the minimum, 1:\n        var provisionalBitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, Version.getVersionForNumber(1));\n        var provisionalVersion = this.chooseVersion(provisionalBitsNeeded, ecLevel);\n        // Use that guess to calculate the right version. I am still not sure this works in 100% of cases.\n        var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, provisionalVersion);\n        return this.chooseVersion(bitsNeeded, ecLevel);\n    };\n    Encoder.calculateBitsNeeded = function (mode, headerBits, dataBits, version) {\n        return headerBits.getSize() + mode.getCharacterCountBits(version) + dataBits.getSize();\n    };\n    /**\n     * @return the code point of the table used in alphanumeric mode or\n     *  -1 if there is no corresponding code in the table.\n     */\n    Encoder.getAlphanumericCode = function (code /*int*/) {\n        if (code < Encoder.ALPHANUMERIC_TABLE.length) {\n            return Encoder.ALPHANUMERIC_TABLE[code];\n        }\n        return -1;\n    };\n    // public static chooseMode(content: string): Mode {\n    //   return chooseMode(content, null);\n    // }\n    /**\n     * Choose the best mode by examining the content. Note that 'encoding' is used as a hint;\n     * if it is Shift_JIS, and the input is only double-byte Kanji, then we return {@link Mode#KANJI}.\n     */\n    Encoder.chooseMode = function (content, encoding) {\n        if (encoding === void 0) { encoding = null; }\n        if (CharacterSetECI.SJIS.getName() === encoding && this.isOnlyDoubleByteKanji(content)) {\n            // Choose Kanji mode if all input are double-byte characters\n            return Mode.KANJI;\n        }\n        var hasNumeric = false;\n        var hasAlphanumeric = false;\n        for (var i = 0, length_1 = content.length; i < length_1; ++i) {\n            var c = content.charAt(i);\n            if (Encoder.isDigit(c)) {\n                hasNumeric = true;\n            }\n            else if (this.getAlphanumericCode(c.charCodeAt(0)) !== -1) {\n                hasAlphanumeric = true;\n            }\n            else {\n                return Mode.BYTE;\n            }\n        }\n        if (hasAlphanumeric) {\n            return Mode.ALPHANUMERIC;\n        }\n        if (hasNumeric) {\n            return Mode.NUMERIC;\n        }\n        return Mode.BYTE;\n    };\n    Encoder.isOnlyDoubleByteKanji = function (content) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, CharacterSetECI.SJIS); // content.getBytes(\"Shift_JIS\"))\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            return false;\n        }\n        var length = bytes.length;\n        if (length % 2 !== 0) {\n            return false;\n        }\n        for (var i = 0; i < length; i += 2) {\n            var byte1 = bytes[i] & 0xFF;\n            if ((byte1 < 0x81 || byte1 > 0x9F) && (byte1 < 0xE0 || byte1 > 0xEB)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Encoder.chooseMaskPattern = function (bits, ecLevel, version, matrix) {\n        var minPenalty = Number.MAX_SAFE_INTEGER; // Lower penalty is better.\n        var bestMaskPattern = -1;\n        // We try all mask patterns to choose the best one.\n        for (var maskPattern = 0; maskPattern < QRCode.NUM_MASK_PATTERNS; maskPattern++) {\n            MatrixUtil.buildMatrix(bits, ecLevel, version, maskPattern, matrix);\n            var penalty = this.calculateMaskPenalty(matrix);\n            if (penalty < minPenalty) {\n                minPenalty = penalty;\n                bestMaskPattern = maskPattern;\n            }\n        }\n        return bestMaskPattern;\n    };\n    Encoder.chooseVersion = function (numInputBits /*int*/, ecLevel) {\n        for (var versionNum = 1; versionNum <= 40; versionNum++) {\n            var version = Version.getVersionForNumber(versionNum);\n            if (Encoder.willFit(numInputBits, version, ecLevel)) {\n                return version;\n            }\n        }\n        throw new WriterException('Data too big');\n    };\n    /**\n     * @return true if the number of input bits will fit in a code with the specified version and\n     * error correction level.\n     */\n    Encoder.willFit = function (numInputBits /*int*/, version, ecLevel) {\n        // In the following comments, we use numbers of Version 7-H.\n        // numBytes = 196\n        var numBytes = version.getTotalCodewords();\n        // getNumECBytes = 130\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        var numEcBytes = ecBlocks.getTotalECCodewords();\n        // getNumDataBytes = 196 - 130 = 66\n        var numDataBytes = numBytes - numEcBytes;\n        var totalInputBytes = (numInputBits + 7) / 8;\n        return numDataBytes >= totalInputBytes;\n    };\n    /**\n     * Terminate bits as described in 8.4.8 and 8.4.9 of JISX0510:2004 (p.24).\n     */\n    Encoder.terminateBits = function (numDataBytes /*int*/, bits) {\n        var capacity = numDataBytes * 8;\n        if (bits.getSize() > capacity) {\n            throw new WriterException('data bits cannot fit in the QR Code' + bits.getSize() + ' > ' +\n                capacity);\n        }\n        for (var i = 0; i < 4 && bits.getSize() < capacity; ++i) {\n            bits.appendBit(false);\n        }\n        // Append termination bits. See 8.4.8 of JISX0510:2004 (p.24) for details.\n        // If the last byte isn't 8-bit aligned, we'll add padding bits.\n        var numBitsInLastByte = bits.getSize() & 0x07;\n        if (numBitsInLastByte > 0) {\n            for (var i = numBitsInLastByte; i < 8; i++) {\n                bits.appendBit(false);\n            }\n        }\n        // If we have more space, we'll fill the space with padding patterns defined in 8.4.9 (p.24).\n        var numPaddingBytes = numDataBytes - bits.getSizeInBytes();\n        for (var i = 0; i < numPaddingBytes; ++i) {\n            bits.appendBits((i & 0x01) === 0 ? 0xEC : 0x11, 8);\n        }\n        if (bits.getSize() !== capacity) {\n            throw new WriterException('Bits size does not equal capacity');\n        }\n    };\n    /**\n     * Get number of data bytes and number of error correction bytes for block id \"blockID\". Store\n     * the result in \"numDataBytesInBlock\", and \"numECBytesInBlock\". See table 12 in 8.5.1 of\n     * JISX0510:2004 (p.30)\n     */\n    Encoder.getNumDataBytesAndNumECBytesForBlockID = function (numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/, blockID /*int*/, numDataBytesInBlock, numECBytesInBlock) {\n        if (blockID >= numRSBlocks) {\n            throw new WriterException('Block ID too large');\n        }\n        // numRsBlocksInGroup2 = 196 % 5 = 1\n        var numRsBlocksInGroup2 = numTotalBytes % numRSBlocks;\n        // numRsBlocksInGroup1 = 5 - 1 = 4\n        var numRsBlocksInGroup1 = numRSBlocks - numRsBlocksInGroup2;\n        // numTotalBytesInGroup1 = 196 / 5 = 39\n        var numTotalBytesInGroup1 = Math.floor(numTotalBytes / numRSBlocks);\n        // numTotalBytesInGroup2 = 39 + 1 = 40\n        var numTotalBytesInGroup2 = numTotalBytesInGroup1 + 1;\n        // numDataBytesInGroup1 = 66 / 5 = 13\n        var numDataBytesInGroup1 = Math.floor(numDataBytes / numRSBlocks);\n        // numDataBytesInGroup2 = 13 + 1 = 14\n        var numDataBytesInGroup2 = numDataBytesInGroup1 + 1;\n        // numEcBytesInGroup1 = 39 - 13 = 26\n        var numEcBytesInGroup1 = numTotalBytesInGroup1 - numDataBytesInGroup1;\n        // numEcBytesInGroup2 = 40 - 14 = 26\n        var numEcBytesInGroup2 = numTotalBytesInGroup2 - numDataBytesInGroup2;\n        // Sanity checks.\n        // 26 = 26\n        if (numEcBytesInGroup1 !== numEcBytesInGroup2) {\n            throw new WriterException('EC bytes mismatch');\n        }\n        // 5 = 4 + 1.\n        if (numRSBlocks !== numRsBlocksInGroup1 + numRsBlocksInGroup2) {\n            throw new WriterException('RS blocks mismatch');\n        }\n        // 196 = (13 + 26) * 4 + (14 + 26) * 1\n        if (numTotalBytes !==\n            ((numDataBytesInGroup1 + numEcBytesInGroup1) *\n                numRsBlocksInGroup1) +\n                ((numDataBytesInGroup2 + numEcBytesInGroup2) *\n                    numRsBlocksInGroup2)) {\n            throw new WriterException('Total bytes mismatch');\n        }\n        if (blockID < numRsBlocksInGroup1) {\n            numDataBytesInBlock[0] = numDataBytesInGroup1;\n            numECBytesInBlock[0] = numEcBytesInGroup1;\n        }\n        else {\n            numDataBytesInBlock[0] = numDataBytesInGroup2;\n            numECBytesInBlock[0] = numEcBytesInGroup2;\n        }\n    };\n    /**\n     * Interleave \"bits\" with corresponding error correction bytes. On success, store the result in\n     * \"result\". The interleave rule is complicated. See 8.6 of JISX0510:2004 (p.37) for details.\n     */\n    Encoder.interleaveWithECBytes = function (bits, numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/) {\n        var e_1, _a, e_2, _b;\n        // \"bits\" must have \"getNumDataBytes\" bytes of data.\n        if (bits.getSizeInBytes() !== numDataBytes) {\n            throw new WriterException('Number of bits and data bytes does not match');\n        }\n        // Step 1.  Divide data bytes into blocks and generate error correction bytes for them. We'll\n        // store the divided data bytes blocks and error correction bytes blocks into \"blocks\".\n        var dataBytesOffset = 0;\n        var maxNumDataBytes = 0;\n        var maxNumEcBytes = 0;\n        // Since, we know the number of reedsolmon blocks, we can initialize the vector with the number.\n        var blocks = new Array(); // new Array<BlockPair>(numRSBlocks)\n        for (var i = 0; i < numRSBlocks; ++i) {\n            var numDataBytesInBlock = new Int32Array(1);\n            var numEcBytesInBlock = new Int32Array(1);\n            Encoder.getNumDataBytesAndNumECBytesForBlockID(numTotalBytes, numDataBytes, numRSBlocks, i, numDataBytesInBlock, numEcBytesInBlock);\n            var size = numDataBytesInBlock[0];\n            var dataBytes = new Uint8Array(size);\n            bits.toBytes(8 * dataBytesOffset, dataBytes, 0, size);\n            var ecBytes = Encoder.generateECBytes(dataBytes, numEcBytesInBlock[0]);\n            blocks.push(new BlockPair(dataBytes, ecBytes));\n            maxNumDataBytes = Math.max(maxNumDataBytes, size);\n            maxNumEcBytes = Math.max(maxNumEcBytes, ecBytes.length);\n            dataBytesOffset += numDataBytesInBlock[0];\n        }\n        if (numDataBytes !== dataBytesOffset) {\n            throw new WriterException('Data bytes does not match offset');\n        }\n        var result = new BitArray();\n        // First, place data blocks.\n        for (var i = 0; i < maxNumDataBytes; ++i) {\n            try {\n                for (var blocks_1 = (e_1 = void 0, __values(blocks)), blocks_1_1 = blocks_1.next(); !blocks_1_1.done; blocks_1_1 = blocks_1.next()) {\n                    var block = blocks_1_1.value;\n                    var dataBytes = block.getDataBytes();\n                    if (i < dataBytes.length) {\n                        result.appendBits(dataBytes[i], 8);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (blocks_1_1 && !blocks_1_1.done && (_a = blocks_1.return)) _a.call(blocks_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        // Then, place error correction blocks.\n        for (var i = 0; i < maxNumEcBytes; ++i) {\n            try {\n                for (var blocks_2 = (e_2 = void 0, __values(blocks)), blocks_2_1 = blocks_2.next(); !blocks_2_1.done; blocks_2_1 = blocks_2.next()) {\n                    var block = blocks_2_1.value;\n                    var ecBytes = block.getErrorCorrectionBytes();\n                    if (i < ecBytes.length) {\n                        result.appendBits(ecBytes[i], 8);\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (blocks_2_1 && !blocks_2_1.done && (_b = blocks_2.return)) _b.call(blocks_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        if (numTotalBytes !== result.getSizeInBytes()) { // Should be same.\n            throw new WriterException('Interleaving error: ' + numTotalBytes + ' and ' +\n                result.getSizeInBytes() + ' differ.');\n        }\n        return result;\n    };\n    Encoder.generateECBytes = function (dataBytes, numEcBytesInBlock /*int*/) {\n        var numDataBytes = dataBytes.length;\n        var toEncode = new Int32Array(numDataBytes + numEcBytesInBlock); // int[numDataBytes + numEcBytesInBlock]\n        for (var i = 0; i < numDataBytes; i++) {\n            toEncode[i] = dataBytes[i] & 0xFF;\n        }\n        new ReedSolomonEncoder(GenericGF.QR_CODE_FIELD_256).encode(toEncode, numEcBytesInBlock);\n        var ecBytes = new Uint8Array(numEcBytesInBlock);\n        for (var i = 0; i < numEcBytesInBlock; i++) {\n            ecBytes[i] = /*(byte) */ toEncode[numDataBytes + i];\n        }\n        return ecBytes;\n    };\n    /**\n     * Append mode info. On success, store the result in \"bits\".\n     */\n    Encoder.appendModeInfo = function (mode, bits) {\n        bits.appendBits(mode.getBits(), 4);\n    };\n    /**\n     * Append length info. On success, store the result in \"bits\".\n     */\n    Encoder.appendLengthInfo = function (numLetters /*int*/, version, mode, bits) {\n        var numBits = mode.getCharacterCountBits(version);\n        if (numLetters >= (1 << numBits)) {\n            throw new WriterException(numLetters + ' is bigger than ' + ((1 << numBits) - 1));\n        }\n        bits.appendBits(numLetters, numBits);\n    };\n    /**\n     * Append \"bytes\" in \"mode\" mode (encoding) into \"bits\". On success, store the result in \"bits\".\n     */\n    Encoder.appendBytes = function (content, mode, bits, encoding) {\n        switch (mode) {\n            case Mode.NUMERIC:\n                Encoder.appendNumericBytes(content, bits);\n                break;\n            case Mode.ALPHANUMERIC:\n                Encoder.appendAlphanumericBytes(content, bits);\n                break;\n            case Mode.BYTE:\n                Encoder.append8BitBytes(content, bits, encoding);\n                break;\n            case Mode.KANJI:\n                Encoder.appendKanjiBytes(content, bits);\n                break;\n            default:\n                throw new WriterException('Invalid mode: ' + mode);\n        }\n    };\n    Encoder.getDigit = function (singleCharacter) {\n        return singleCharacter.charCodeAt(0) - 48;\n    };\n    Encoder.isDigit = function (singleCharacter) {\n        var cn = Encoder.getDigit(singleCharacter);\n        return cn >= 0 && cn <= 9;\n    };\n    Encoder.appendNumericBytes = function (content, bits) {\n        var length = content.length;\n        var i = 0;\n        while (i < length) {\n            var num1 = Encoder.getDigit(content.charAt(i));\n            if (i + 2 < length) {\n                // Encode three numeric letters in ten bits.\n                var num2 = Encoder.getDigit(content.charAt(i + 1));\n                var num3 = Encoder.getDigit(content.charAt(i + 2));\n                bits.appendBits(num1 * 100 + num2 * 10 + num3, 10);\n                i += 3;\n            }\n            else if (i + 1 < length) {\n                // Encode two numeric letters in seven bits.\n                var num2 = Encoder.getDigit(content.charAt(i + 1));\n                bits.appendBits(num1 * 10 + num2, 7);\n                i += 2;\n            }\n            else {\n                // Encode one numeric letter in four bits.\n                bits.appendBits(num1, 4);\n                i++;\n            }\n        }\n    };\n    Encoder.appendAlphanumericBytes = function (content, bits) {\n        var length = content.length;\n        var i = 0;\n        while (i < length) {\n            var code1 = Encoder.getAlphanumericCode(content.charCodeAt(i));\n            if (code1 === -1) {\n                throw new WriterException();\n            }\n            if (i + 1 < length) {\n                var code2 = Encoder.getAlphanumericCode(content.charCodeAt(i + 1));\n                if (code2 === -1) {\n                    throw new WriterException();\n                }\n                // Encode two alphanumeric letters in 11 bits.\n                bits.appendBits(code1 * 45 + code2, 11);\n                i += 2;\n            }\n            else {\n                // Encode one alphanumeric letter in six bits.\n                bits.appendBits(code1, 6);\n                i++;\n            }\n        }\n    };\n    Encoder.append8BitBytes = function (content, bits, encoding) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, encoding);\n        }\n        catch (uee /*: UnsupportedEncodingException*/) {\n            throw new WriterException(uee);\n        }\n        for (var i = 0, length_2 = bytes.length; i !== length_2; i++) {\n            var b = bytes[i];\n            bits.appendBits(b, 8);\n        }\n    };\n    /**\n     * @throws WriterException\n     */\n    Encoder.appendKanjiBytes = function (content, bits) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, CharacterSetECI.SJIS);\n        }\n        catch (uee /*: UnsupportedEncodingException*/) {\n            throw new WriterException(uee);\n        }\n        var length = bytes.length;\n        for (var i = 0; i < length; i += 2) {\n            var byte1 = bytes[i] & 0xFF;\n            var byte2 = bytes[i + 1] & 0xFF;\n            var code = ((byte1 << 8) & 0xFFFFFFFF) | byte2;\n            var subtracted = -1;\n            if (code >= 0x8140 && code <= 0x9ffc) {\n                subtracted = code - 0x8140;\n            }\n            else if (code >= 0xe040 && code <= 0xebbf) {\n                subtracted = code - 0xc140;\n            }\n            if (subtracted === -1) {\n                throw new WriterException('Invalid byte sequence');\n            }\n            var encoded = ((subtracted >> 8) * 0xc0) + (subtracted & 0xff);\n            bits.appendBits(encoded, 13);\n        }\n    };\n    Encoder.appendECI = function (eci, bits) {\n        bits.appendBits(Mode.ECI.getBits(), 4);\n        // This is correct for values up to 127, which is all we need now.\n        bits.appendBits(eci.getValue(), 8);\n    };\n    // The original table is defined in the table 5 of JISX0510:2004 (p.19).\n    Encoder.ALPHANUMERIC_TABLE = Int32Array.from([\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n        36, -1, -1, -1, 37, 38, -1, -1, -1, -1, 39, 40, -1, 41, 42, 43,\n        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 44, -1, -1, -1, -1, -1,\n        -1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,\n        25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, -1, -1, -1, -1, -1,\n    ]);\n    Encoder.DEFAULT_BYTE_MODE_ENCODING = CharacterSetECI.UTF8.getName(); // \"ISO-8859-1\"\n    return Encoder;\n}());\nexport default Encoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,6CAA6C,GAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;;;;;;AAgBA,8CAA8C,GAC9C,6BAA6B,GAC7B,8BAA8B,GAC9B,uBAAuB,GACvB;;;CAGC,GACD,IAAI,UAAyB;IACzB,sDAAsD;IACtD,SAAS,WACT;IACA,kGAAkG;IAClG,6DAA6D;IAC7D,QAAQ,oBAAoB,GAAG,SAAU,MAAM;QAC3C,OAAO,qLAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,UAChC,qLAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,UAC/B,qLAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,UAC/B,qLAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC;IACzC;IACA;;;;;;KAMC,GACD,4GAA4G;IAC5G,0CAA0C;IAC1C,IAAI;IACJ,QAAQ,MAAM,GAAG,SAAU,OAAO,EAAE,OAAO,EAAE,KAAK;QAC9C,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,6EAA6E;QAC7E,IAAI,WAAW,QAAQ,0BAA0B;QACjD,IAAI,kBAAkB,UAAU,QAAQ,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,aAAa;QAC5F,IAAI,iBAAiB;YACjB,WAAW,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,aAAa,EAAE,QAAQ;QAC/D;QACA,4FAA4F;QAC5F,8EAA8E;QAC9E,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS;QACpC,wDAAwD;QACxD,4DAA4D;QAC5D,IAAI,aAAa,IAAI,0KAAA,CAAA,UAAQ;QAC7B,mCAAmC;QACnC,IAAI,SAAS,iLAAA,CAAA,UAAI,CAAC,IAAI,IAAI,CAAC,mBAAmB,QAAQ,0BAA0B,KAAK,QAAQ,GAAG;YAC5F,IAAI,MAAM,iLAAA,CAAA,UAAe,CAAC,wBAAwB,CAAC;YACnD,IAAI,QAAQ,WAAW;gBACnB,IAAI,CAAC,SAAS,CAAC,KAAK;YACxB;QACJ;QACA,6CAA6C;QAC7C,IAAI,CAAC,cAAc,CAAC,MAAM;QAC1B,iGAAiG;QACjG,oBAAoB;QACpB,IAAI,WAAW,IAAI,0KAAA,CAAA,UAAQ;QAC3B,IAAI,CAAC,WAAW,CAAC,SAAS,MAAM,UAAU;QAC1C,IAAI;QACJ,IAAI,UAAU,QAAQ,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU,GAAG;YACtE,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU,EAAE,QAAQ,IAAI;YACrF,UAAU,oLAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;YACtC,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC,MAAM,YAAY,UAAU;YACtE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,SAAS,UAAU;gBAC7C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;YAC9B;QACJ,OACK;YACD,UAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS,MAAM,YAAY;QAC/D;QACA,IAAI,oBAAoB,IAAI,0KAAA,CAAA,UAAQ;QACpC,kBAAkB,cAAc,CAAC;QACjC,6CAA6C;QAC7C,IAAI,aAAa,SAAS,iLAAA,CAAA,UAAI,CAAC,IAAI,GAAG,SAAS,cAAc,KAAK,QAAQ,MAAM;QAChF,IAAI,CAAC,gBAAgB,CAAC,YAAY,SAAS,MAAM;QACjD,6CAA6C;QAC7C,kBAAkB,cAAc,CAAC;QACjC,IAAI,WAAW,QAAQ,mBAAmB,CAAC;QAC3C,IAAI,eAAe,QAAQ,iBAAiB,KAAK,SAAS,mBAAmB;QAC7E,+BAA+B;QAC/B,IAAI,CAAC,aAAa,CAAC,cAAc;QACjC,mDAAmD;QACnD,IAAI,YAAY,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,QAAQ,iBAAiB,IAAI,cAAc,SAAS,YAAY;QAC9H,IAAI,SAAS,IAAI,mLAAA,CAAA,UAAM;QACvB,OAAO,UAAU,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,OAAO,UAAU,CAAC;QAClB,gDAAgD;QAChD,IAAI,YAAY,QAAQ,sBAAsB;QAC9C,IAAI,SAAS,IAAI,uLAAA,CAAA,UAAU,CAAC,WAAW;QACvC,IAAI,cAAc,IAAI,CAAC,iBAAiB,CAAC,WAAW,SAAS,SAAS;QACtE,OAAO,cAAc,CAAC;QACtB,2CAA2C;QAC3C,uLAAA,CAAA,UAAU,CAAC,WAAW,CAAC,WAAW,SAAS,SAAS,aAAa;QACjE,OAAO,SAAS,CAAC;QACjB,OAAO;IACX;IACA;;;;KAIC,GACD,QAAQ,gBAAgB,GAAG,SAAU,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ;QACpE,gGAAgG;QAChG,8FAA8F;QAC9F,kBAAkB;QAClB,IAAI,wBAAwB,IAAI,CAAC,mBAAmB,CAAC,MAAM,YAAY,UAAU,oLAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;QAC7G,IAAI,qBAAqB,IAAI,CAAC,aAAa,CAAC,uBAAuB;QACnE,kGAAkG;QAClG,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC,MAAM,YAAY,UAAU;QACtE,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY;IAC1C;IACA,QAAQ,mBAAmB,GAAG,SAAU,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO;QACvE,OAAO,WAAW,OAAO,KAAK,KAAK,qBAAqB,CAAC,WAAW,SAAS,OAAO;IACxF;IACA;;;KAGC,GACD,QAAQ,mBAAmB,GAAG,SAAU,KAAK,KAAK,GAAN;QACxC,IAAI,OAAO,QAAQ,kBAAkB,CAAC,MAAM,EAAE;YAC1C,OAAO,QAAQ,kBAAkB,CAAC,KAAK;QAC3C;QACA,OAAO,CAAC;IACZ;IACA,oDAAoD;IACpD,sCAAsC;IACtC,IAAI;IACJ;;;KAGC,GACD,QAAQ,UAAU,GAAG,SAAU,OAAO,EAAE,QAAQ;QAC5C,IAAI,aAAa,KAAK,GAAG;YAAE,WAAW;QAAM;QAC5C,IAAI,iLAAA,CAAA,UAAe,CAAC,IAAI,CAAC,OAAO,OAAO,YAAY,IAAI,CAAC,qBAAqB,CAAC,UAAU;YACpF,4DAA4D;YAC5D,OAAO,iLAAA,CAAA,UAAI,CAAC,KAAK;QACrB;QACA,IAAI,aAAa;QACjB,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,GAAG,WAAW,QAAQ,MAAM,EAAE,IAAI,UAAU,EAAE,EAAG;YAC1D,IAAI,IAAI,QAAQ,MAAM,CAAC;YACvB,IAAI,QAAQ,OAAO,CAAC,IAAI;gBACpB,aAAa;YACjB,OACK,IAAI,IAAI,CAAC,mBAAmB,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG;gBACvD,kBAAkB;YACtB,OACK;gBACD,OAAO,iLAAA,CAAA,UAAI,CAAC,IAAI;YACpB;QACJ;QACA,IAAI,iBAAiB;YACjB,OAAO,iLAAA,CAAA,UAAI,CAAC,YAAY;QAC5B;QACA,IAAI,YAAY;YACZ,OAAO,iLAAA,CAAA,UAAI,CAAC,OAAO;QACvB;QACA,OAAO,iLAAA,CAAA,UAAI,CAAC,IAAI;IACpB;IACA,QAAQ,qBAAqB,GAAG,SAAU,OAAO;QAC7C,IAAI;QACJ,IAAI;YACA,QAAQ,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,SAAS,iLAAA,CAAA,UAAe,CAAC,IAAI,GAAG,iCAAiC;QACnG,EACA,OAAO,QAAQ,gCAAgC,KAAI;YAC/C,OAAO;QACX;QACA,IAAI,SAAS,MAAM,MAAM;QACzB,IAAI,SAAS,MAAM,GAAG;YAClB,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;YAChC,IAAI,QAAQ,KAAK,CAAC,EAAE,GAAG;YACvB,IAAI,CAAC,QAAQ,QAAQ,QAAQ,IAAI,KAAK,CAAC,QAAQ,QAAQ,QAAQ,IAAI,GAAG;gBAClE,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,QAAQ,iBAAiB,GAAG,SAAU,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;QAChE,IAAI,aAAa,OAAO,gBAAgB,EAAE,2BAA2B;QACrE,IAAI,kBAAkB,CAAC;QACvB,mDAAmD;QACnD,IAAK,IAAI,cAAc,GAAG,cAAc,mLAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE,cAAe;YAC7E,uLAAA,CAAA,UAAU,CAAC,WAAW,CAAC,MAAM,SAAS,SAAS,aAAa;YAC5D,IAAI,UAAU,IAAI,CAAC,oBAAoB,CAAC;YACxC,IAAI,UAAU,YAAY;gBACtB,aAAa;gBACb,kBAAkB;YACtB;QACJ;QACA,OAAO;IACX;IACA,QAAQ,aAAa,GAAG,SAAU,aAAa,KAAK,GAAN,EAAU,OAAO;QAC3D,IAAK,IAAI,aAAa,GAAG,cAAc,IAAI,aAAc;YACrD,IAAI,UAAU,oLAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;YAC1C,IAAI,QAAQ,OAAO,CAAC,cAAc,SAAS,UAAU;gBACjD,OAAO;YACX;QACJ;QACA,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;IAC9B;IACA;;;KAGC,GACD,QAAQ,OAAO,GAAG,SAAU,aAAa,KAAK,GAAN,EAAU,OAAO,EAAE,OAAO;QAC9D,4DAA4D;QAC5D,iBAAiB;QACjB,IAAI,WAAW,QAAQ,iBAAiB;QACxC,sBAAsB;QACtB,IAAI,WAAW,QAAQ,mBAAmB,CAAC;QAC3C,IAAI,aAAa,SAAS,mBAAmB;QAC7C,mCAAmC;QACnC,IAAI,eAAe,WAAW;QAC9B,IAAI,kBAAkB,CAAC,eAAe,CAAC,IAAI;QAC3C,OAAO,gBAAgB;IAC3B;IACA;;KAEC,GACD,QAAQ,aAAa,GAAG,SAAU,aAAa,KAAK,GAAN,EAAU,IAAI;QACxD,IAAI,WAAW,eAAe;QAC9B,IAAI,KAAK,OAAO,KAAK,UAAU;YAC3B,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,wCAAwC,KAAK,OAAO,KAAK,QAC/E;QACR;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,OAAO,KAAK,UAAU,EAAE,EAAG;YACrD,KAAK,SAAS,CAAC;QACnB;QACA,0EAA0E;QAC1E,gEAAgE;QAChE,IAAI,oBAAoB,KAAK,OAAO,KAAK;QACzC,IAAI,oBAAoB,GAAG;YACvB,IAAK,IAAI,IAAI,mBAAmB,IAAI,GAAG,IAAK;gBACxC,KAAK,SAAS,CAAC;YACnB;QACJ;QACA,6FAA6F;QAC7F,IAAI,kBAAkB,eAAe,KAAK,cAAc;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,EAAE,EAAG;YACtC,KAAK,UAAU,CAAC,CAAC,IAAI,IAAI,MAAM,IAAI,OAAO,MAAM;QACpD;QACA,IAAI,KAAK,OAAO,OAAO,UAAU;YAC7B,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;IACJ;IACA;;;;KAIC,GACD,QAAQ,sCAAsC,GAAG,SAAU,cAAc,KAAK,GAAN,EAAU,aAAa,KAAK,GAAN,EAAU,YAAY,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,mBAAmB,EAAE,iBAAiB;QAChL,IAAI,WAAW,aAAa;YACxB,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,oCAAoC;QACpC,IAAI,sBAAsB,gBAAgB;QAC1C,kCAAkC;QAClC,IAAI,sBAAsB,cAAc;QACxC,uCAAuC;QACvC,IAAI,wBAAwB,KAAK,KAAK,CAAC,gBAAgB;QACvD,sCAAsC;QACtC,IAAI,wBAAwB,wBAAwB;QACpD,qCAAqC;QACrC,IAAI,uBAAuB,KAAK,KAAK,CAAC,eAAe;QACrD,qCAAqC;QACrC,IAAI,uBAAuB,uBAAuB;QAClD,oCAAoC;QACpC,IAAI,qBAAqB,wBAAwB;QACjD,oCAAoC;QACpC,IAAI,qBAAqB,wBAAwB;QACjD,iBAAiB;QACjB,UAAU;QACV,IAAI,uBAAuB,oBAAoB;YAC3C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,aAAa;QACb,IAAI,gBAAgB,sBAAsB,qBAAqB;YAC3D,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,sCAAsC;QACtC,IAAI,kBACA,AAAC,CAAC,uBAAuB,kBAAkB,IACvC,sBACC,CAAC,uBAAuB,kBAAkB,IACvC,qBAAsB;YAC9B,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI,UAAU,qBAAqB;YAC/B,mBAAmB,CAAC,EAAE,GAAG;YACzB,iBAAiB,CAAC,EAAE,GAAG;QAC3B,OACK;YACD,mBAAmB,CAAC,EAAE,GAAG;YACzB,iBAAiB,CAAC,EAAE,GAAG;QAC3B;IACJ;IACA;;;KAGC,GACD,QAAQ,qBAAqB,GAAG,SAAU,IAAI,EAAE,cAAc,KAAK,GAAN,EAAU,aAAa,KAAK,GAAN,EAAU,YAAY,KAAK,GAAN;QACpG,IAAI,KAAK,IAAI,KAAK;QAClB,oDAAoD;QACpD,IAAI,KAAK,cAAc,OAAO,cAAc;YACxC,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,6FAA6F;QAC7F,uFAAuF;QACvF,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,gBAAgB;QACpB,gGAAgG;QAChG,IAAI,SAAS,IAAI,SAAS,oCAAoC;QAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,EAAE,EAAG;YAClC,IAAI,sBAAsB,IAAI,WAAW;YACzC,IAAI,oBAAoB,IAAI,WAAW;YACvC,QAAQ,sCAAsC,CAAC,eAAe,cAAc,aAAa,GAAG,qBAAqB;YACjH,IAAI,OAAO,mBAAmB,CAAC,EAAE;YACjC,IAAI,YAAY,IAAI,WAAW;YAC/B,KAAK,OAAO,CAAC,IAAI,iBAAiB,WAAW,GAAG;YAChD,IAAI,UAAU,QAAQ,eAAe,CAAC,WAAW,iBAAiB,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC,IAAI,sLAAA,CAAA,UAAS,CAAC,WAAW;YACrC,kBAAkB,KAAK,GAAG,CAAC,iBAAiB;YAC5C,gBAAgB,KAAK,GAAG,CAAC,eAAe,QAAQ,MAAM;YACtD,mBAAmB,mBAAmB,CAAC,EAAE;QAC7C;QACA,IAAI,iBAAiB,iBAAiB;YAClC,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI,SAAS,IAAI,0KAAA,CAAA,UAAQ;QACzB,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,EAAE,EAAG;YACtC,IAAI;gBACA,IAAK,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,SAAS,OAAO,GAAG,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;oBAChI,IAAI,QAAQ,WAAW,KAAK;oBAC5B,IAAI,YAAY,MAAM,YAAY;oBAClC,IAAI,IAAI,UAAU,MAAM,EAAE;wBACtB,OAAO,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE;oBACpC;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;gBAC1E,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,uCAAuC;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,EAAE,EAAG;YACpC,IAAI;gBACA,IAAK,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,SAAS,OAAO,GAAG,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;oBAChI,IAAI,QAAQ,WAAW,KAAK;oBAC5B,IAAI,UAAU,MAAM,uBAAuB;oBAC3C,IAAI,IAAI,QAAQ,MAAM,EAAE;wBACpB,OAAO,UAAU,CAAC,OAAO,CAAC,EAAE,EAAE;oBAClC;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;gBAC1E,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,IAAI,kBAAkB,OAAO,cAAc,IAAI;YAC3C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,yBAAyB,gBAAgB,UAC/D,OAAO,cAAc,KAAK;QAClC;QACA,OAAO;IACX;IACA,QAAQ,eAAe,GAAG,SAAU,SAAS,EAAE,kBAAkB,KAAK,GAAN;QAC5D,IAAI,eAAe,UAAU,MAAM;QACnC,IAAI,WAAW,IAAI,WAAW,eAAe,oBAAoB,wCAAwC;QACzG,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QACjC;QACA,IAAI,mMAAA,CAAA,UAAkB,CAAC,0LAAA,CAAA,UAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,UAAU;QACrE,IAAI,UAAU,IAAI,WAAW;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YACxC,OAAO,CAAC,EAAE,GAAG,SAAS,GAAG,QAAQ,CAAC,eAAe,EAAE;QACvD;QACA,OAAO;IACX;IACA;;KAEC,GACD,QAAQ,cAAc,GAAG,SAAU,IAAI,EAAE,IAAI;QACzC,KAAK,UAAU,CAAC,KAAK,OAAO,IAAI;IACpC;IACA;;KAEC,GACD,QAAQ,gBAAgB,GAAG,SAAU,WAAW,KAAK,GAAN,EAAU,OAAO,EAAE,IAAI,EAAE,IAAI;QACxE,IAAI,UAAU,KAAK,qBAAqB,CAAC;QACzC,IAAI,cAAe,KAAK,SAAU;YAC9B,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,aAAa,qBAAqB,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC;QACnF;QACA,KAAK,UAAU,CAAC,YAAY;IAChC;IACA;;KAEC,GACD,QAAQ,WAAW,GAAG,SAAU,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;QACzD,OAAQ;YACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,OAAO;gBACb,QAAQ,kBAAkB,CAAC,SAAS;gBACpC;YACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,YAAY;gBAClB,QAAQ,uBAAuB,CAAC,SAAS;gBACzC;YACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,IAAI;gBACV,QAAQ,eAAe,CAAC,SAAS,MAAM;gBACvC;YACJ,KAAK,iLAAA,CAAA,UAAI,CAAC,KAAK;gBACX,QAAQ,gBAAgB,CAAC,SAAS;gBAClC;YACJ;gBACI,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC,mBAAmB;QACrD;IACJ;IACA,QAAQ,QAAQ,GAAG,SAAU,eAAe;QACxC,OAAO,gBAAgB,UAAU,CAAC,KAAK;IAC3C;IACA,QAAQ,OAAO,GAAG,SAAU,eAAe;QACvC,IAAI,KAAK,QAAQ,QAAQ,CAAC;QAC1B,OAAO,MAAM,KAAK,MAAM;IAC5B;IACA,QAAQ,kBAAkB,GAAG,SAAU,OAAO,EAAE,IAAI;QAChD,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,IAAI;QACR,MAAO,IAAI,OAAQ;YACf,IAAI,OAAO,QAAQ,QAAQ,CAAC,QAAQ,MAAM,CAAC;YAC3C,IAAI,IAAI,IAAI,QAAQ;gBAChB,4CAA4C;gBAC5C,IAAI,OAAO,QAAQ,QAAQ,CAAC,QAAQ,MAAM,CAAC,IAAI;gBAC/C,IAAI,OAAO,QAAQ,QAAQ,CAAC,QAAQ,MAAM,CAAC,IAAI;gBAC/C,KAAK,UAAU,CAAC,OAAO,MAAM,OAAO,KAAK,MAAM;gBAC/C,KAAK;YACT,OACK,IAAI,IAAI,IAAI,QAAQ;gBACrB,4CAA4C;gBAC5C,IAAI,OAAO,QAAQ,QAAQ,CAAC,QAAQ,MAAM,CAAC,IAAI;gBAC/C,KAAK,UAAU,CAAC,OAAO,KAAK,MAAM;gBAClC,KAAK;YACT,OACK;gBACD,0CAA0C;gBAC1C,KAAK,UAAU,CAAC,MAAM;gBACtB;YACJ;QACJ;IACJ;IACA,QAAQ,uBAAuB,GAAG,SAAU,OAAO,EAAE,IAAI;QACrD,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,IAAI;QACR,MAAO,IAAI,OAAQ;YACf,IAAI,QAAQ,QAAQ,mBAAmB,CAAC,QAAQ,UAAU,CAAC;YAC3D,IAAI,UAAU,CAAC,GAAG;gBACd,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,IAAI,IAAI,IAAI,QAAQ;gBAChB,IAAI,QAAQ,QAAQ,mBAAmB,CAAC,QAAQ,UAAU,CAAC,IAAI;gBAC/D,IAAI,UAAU,CAAC,GAAG;oBACd,MAAM,IAAI,uKAAA,CAAA,UAAe;gBAC7B;gBACA,8CAA8C;gBAC9C,KAAK,UAAU,CAAC,QAAQ,KAAK,OAAO;gBACpC,KAAK;YACT,OACK;gBACD,8CAA8C;gBAC9C,KAAK,UAAU,CAAC,OAAO;gBACvB;YACJ;QACJ;IACJ;IACA,QAAQ,eAAe,GAAG,SAAU,OAAO,EAAE,IAAI,EAAE,QAAQ;QACvD,IAAI;QACJ,IAAI;YACA,QAAQ,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,SAAS;QAC3C,EACA,OAAO,IAAI,gCAAgC,KAAI;YAC3C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAK,IAAI,IAAI,GAAG,WAAW,MAAM,MAAM,EAAE,MAAM,UAAU,IAAK;YAC1D,IAAI,IAAI,KAAK,CAAC,EAAE;YAChB,KAAK,UAAU,CAAC,GAAG;QACvB;IACJ;IACA;;KAEC,GACD,QAAQ,gBAAgB,GAAG,SAAU,OAAO,EAAE,IAAI;QAC9C,IAAI;QACJ,IAAI;YACA,QAAQ,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,SAAS,iLAAA,CAAA,UAAe,CAAC,IAAI;QAC/D,EACA,OAAO,IAAI,gCAAgC,KAAI;YAC3C,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI,SAAS,MAAM,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;YAChC,IAAI,QAAQ,KAAK,CAAC,EAAE,GAAG;YACvB,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE,GAAG;YAC3B,IAAI,OAAO,AAAE,SAAS,IAAK,aAAc;YACzC,IAAI,aAAa,CAAC;YAClB,IAAI,QAAQ,UAAU,QAAQ,QAAQ;gBAClC,aAAa,OAAO;YACxB,OACK,IAAI,QAAQ,UAAU,QAAQ,QAAQ;gBACvC,aAAa,OAAO;YACxB;YACA,IAAI,eAAe,CAAC,GAAG;gBACnB,MAAM,IAAI,uKAAA,CAAA,UAAe,CAAC;YAC9B;YACA,IAAI,UAAU,AAAC,CAAC,cAAc,CAAC,IAAI,OAAQ,CAAC,aAAa,IAAI;YAC7D,KAAK,UAAU,CAAC,SAAS;QAC7B;IACJ;IACA,QAAQ,SAAS,GAAG,SAAU,GAAG,EAAE,IAAI;QACnC,KAAK,UAAU,CAAC,iLAAA,CAAA,UAAI,CAAC,GAAG,CAAC,OAAO,IAAI;QACpC,kEAAkE;QAClE,KAAK,UAAU,CAAC,IAAI,QAAQ,IAAI;IACpC;IACA,wEAAwE;IACxE,QAAQ,kBAAkB,GAAG,WAAW,IAAI,CAAC;QACzC,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAC7D,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAC7D;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG;QAAI;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG;QAAI;QAAI,CAAC;QAAG;QAAI;QAAI;QAC5D;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QACnD,CAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAC5D;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;KAChE;IACD,QAAQ,0BAA0B,GAAG,iLAAA,CAAA,UAAe,CAAC,IAAI,CAAC,OAAO,IAAI,eAAe;IACpF,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/qrcode/QRCodeWriter.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport EncodeHintType from '../EncodeHintType';\nimport BitMatrix from '../common/BitMatrix';\nimport ErrorCorrectionLevel from './decoder/ErrorCorrectionLevel';\nimport Encoder from './encoder/Encoder';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport IllegalStateException from '../IllegalStateException';\n/*import java.util.Map;*/\n/**\n * This object renders a QR Code as a BitMatrix 2D array of greyscale values.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar QRCodeWriter = /** @class */ (function () {\n    function QRCodeWriter() {\n    }\n    /*@Override*/\n    // public encode(contents: string, format: BarcodeFormat, width: number /*int*/, height: number /*int*/): BitMatrix\n    //     /*throws WriterException */ {\n    //   return encode(contents, format, width, height, null)\n    // }\n    /*@Override*/\n    QRCodeWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n        if (contents.length === 0) {\n            throw new IllegalArgumentException('Found empty contents');\n        }\n        if (format !== BarcodeFormat.QR_CODE) {\n            throw new IllegalArgumentException('Can only encode QR_CODE, but got ' + format);\n        }\n        if (width < 0 || height < 0) {\n            throw new IllegalArgumentException(\"Requested dimensions are too small: \" + width + \"x\" + height);\n        }\n        var errorCorrectionLevel = ErrorCorrectionLevel.L;\n        var quietZone = QRCodeWriter.QUIET_ZONE_SIZE;\n        if (hints !== null) {\n            if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n                errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n                quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n            }\n        }\n        var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n        return QRCodeWriter.renderResult(code, width, height, quietZone);\n    };\n    // Note that the input matrix uses 0 == white, 1 == black, while the output matrix uses\n    // 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n    QRCodeWriter.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n        var input = code.getMatrix();\n        if (input === null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var qrWidth = inputWidth + (quietZone * 2);\n        var qrHeight = inputHeight + (quietZone * 2);\n        var outputWidth = Math.max(width, qrWidth);\n        var outputHeight = Math.max(height, qrHeight);\n        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n        // handle all the padding from 100x100 (the actual QR) up to 200x160.\n        var leftPadding = Math.floor((outputWidth - (inputWidth * multiple)) / 2);\n        var topPadding = Math.floor((outputHeight - (inputHeight * multiple)) / 2);\n        var output = new BitMatrix(outputWidth, outputHeight);\n        for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY) === 1) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    QRCodeWriter.QUIET_ZONE_SIZE = 4;\n    return QRCodeWriter;\n}());\nexport default QRCodeWriter;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC;;;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,uBAAuB,GACvB;;;;CAIC,GACD,IAAI,eAA8B;IAC9B,SAAS,gBACT;IACA,WAAW,GACX,mHAAmH;IACnH,oCAAoC;IACpC,yDAAyD;IACzD,IAAI;IACJ,WAAW,GACX,aAAa,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,KAAK;QAC5F,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,WAAW,qKAAA,CAAA,UAAa,CAAC,OAAO,EAAE;YAClC,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,sCAAsC;QAC7E;QACA,IAAI,QAAQ,KAAK,SAAS,GAAG;YACzB,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC,yCAAyC,QAAQ,MAAM;QAC9F;QACA,IAAI,uBAAuB,iMAAA,CAAA,UAAoB,CAAC,CAAC;QACjD,IAAI,YAAY,aAAa,eAAe;QAC5C,IAAI,UAAU,MAAM;YAChB,IAAI,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB,GAAG;gBAC1D,uBAAuB,iMAAA,CAAA,UAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB,EAAE,QAAQ;YAC9G;YACA,IAAI,cAAc,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,MAAM,GAAG;gBAChD,YAAY,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,MAAM,EAAE,QAAQ,IAAI;YAC7E;QACJ;QACA,IAAI,OAAO,oLAAA,CAAA,UAAO,CAAC,MAAM,CAAC,UAAU,sBAAsB;QAC1D,OAAO,aAAa,YAAY,CAAC,MAAM,OAAO,QAAQ;IAC1D;IACA,uFAAuF;IACvF,6DAA6D;IAC7D,aAAa,YAAY,GAAG,SAAU,IAAI,EAAE,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN;QAChF,IAAI,QAAQ,KAAK,SAAS;QAC1B,IAAI,UAAU,MAAM;YAChB,MAAM,IAAI,6KAAA,CAAA,UAAqB;QACnC;QACA,IAAI,aAAa,MAAM,QAAQ;QAC/B,IAAI,cAAc,MAAM,SAAS;QACjC,IAAI,UAAU,aAAc,YAAY;QACxC,IAAI,WAAW,cAAe,YAAY;QAC1C,IAAI,cAAc,KAAK,GAAG,CAAC,OAAO;QAClC,IAAI,eAAe,KAAK,GAAG,CAAC,QAAQ;QACpC,IAAI,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,cAAc,UAAU,KAAK,KAAK,CAAC,eAAe;QACrF,+FAA+F;QAC/F,4FAA4F;QAC5F,4FAA4F;QAC5F,qEAAqE;QACrE,IAAI,cAAc,KAAK,KAAK,CAAC,CAAC,cAAe,aAAa,QAAS,IAAI;QACvE,IAAI,aAAa,KAAK,KAAK,CAAC,CAAC,eAAgB,cAAc,QAAS,IAAI;QACxE,IAAI,SAAS,IAAI,2KAAA,CAAA,UAAS,CAAC,aAAa;QACxC,IAAK,IAAI,SAAS,GAAG,UAAU,YAAY,SAAS,aAAa,UAAU,WAAW,SAAU;YAC5F,gDAAgD;YAChD,IAAK,IAAI,SAAS,GAAG,UAAU,aAAa,SAAS,YAAY,UAAU,WAAW,SAAU;gBAC5F,IAAI,MAAM,GAAG,CAAC,QAAQ,YAAY,GAAG;oBACjC,OAAO,SAAS,CAAC,SAAS,SAAS,UAAU;gBACjD;YACJ;QACJ;QACA,OAAO;IACX;IACA,aAAa,eAAe,GAAG;IAC/B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}]}