{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/OneDReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport BitArray from '../common/BitArray';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport NotFoundException from '../NotFoundException';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (<PERSON>)\n * <AUTHOR>\n */\nvar OneDReader = /** @class */ (function () {\n    function OneDReader() {\n    }\n    /*\n    @Override\n    public Result decode(BinaryBitmap image) throws NotFoundException, FormatException {\n      return decode(image, null);\n    }\n    */\n    // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n    // @Override\n    OneDReader.prototype.decode = function (image, hints) {\n        try {\n            return this.doDecode(image, hints);\n        }\n        catch (nfe) {\n            var tryHarder = hints && (hints.get(DecodeHintType.TRY_HARDER) === true);\n            if (tryHarder && image.isRotateSupported()) {\n                var rotatedImage = image.rotateCounterClockwise();\n                var result = this.doDecode(rotatedImage, hints);\n                // Record that we found it rotated 90 degrees CCW / 270 degrees CW\n                var metadata = result.getResultMetadata();\n                var orientation_1 = 270;\n                if (metadata !== null && (metadata.get(ResultMetadataType.ORIENTATION) === true)) {\n                    // But if we found it reversed in doDecode(), add in that result here:\n                    orientation_1 = (orientation_1 + metadata.get(ResultMetadataType.ORIENTATION) % 360);\n                }\n                result.putMetadata(ResultMetadataType.ORIENTATION, orientation_1);\n                // Update result points\n                var points = result.getResultPoints();\n                if (points !== null) {\n                    var height = rotatedImage.getHeight();\n                    for (var i = 0; i < points.length; i++) {\n                        points[i] = new ResultPoint(height - points[i].getY() - 1, points[i].getX());\n                    }\n                }\n                return result;\n            }\n            else {\n                throw new NotFoundException();\n            }\n        }\n    };\n    // @Override\n    OneDReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * We're going to examine rows from the middle outward, searching alternately above and below the\n     * middle, and farther out each time. rowStep is the number of rows between each successive\n     * attempt above and below the middle. So we'd scan row middle, then middle - rowStep, then\n     * middle + rowStep, then middle - (2 * rowStep), etc.\n     * rowStep is bigger as the image is taller, but is always at least 1. We've somewhat arbitrarily\n     * decided that moving up and down by about 1/16 of the image is pretty good; we try more of the\n     * image if \"trying harder\".\n     *\n     * @param image The image to decode\n     * @param hints Any hints that were requested\n     * @return The contents of the decoded barcode\n     * @throws NotFoundException Any spontaneous errors which occur\n     */\n    OneDReader.prototype.doDecode = function (image, hints) {\n        var width = image.getWidth();\n        var height = image.getHeight();\n        var row = new BitArray(width);\n        var tryHarder = hints && (hints.get(DecodeHintType.TRY_HARDER) === true);\n        var rowStep = Math.max(1, height >> (tryHarder ? 8 : 5));\n        var maxLines;\n        if (tryHarder) {\n            maxLines = height; // Look at the whole image, not just the center\n        }\n        else {\n            maxLines = 15; // 15 rows spaced 1/32 apart is roughly the middle half of the image\n        }\n        var middle = Math.trunc(height / 2);\n        for (var x = 0; x < maxLines; x++) {\n            // Scanning from the middle out. Determine which row we're looking at next:\n            var rowStepsAboveOrBelow = Math.trunc((x + 1) / 2);\n            var isAbove = (x & 0x01) === 0; // i.e. is x even?\n            var rowNumber = middle + rowStep * (isAbove ? rowStepsAboveOrBelow : -rowStepsAboveOrBelow);\n            if (rowNumber < 0 || rowNumber >= height) {\n                // Oops, if we run off the top or bottom, stop\n                break;\n            }\n            // Estimate black point for this row and load it:\n            try {\n                row = image.getBlackRow(rowNumber, row);\n            }\n            catch (ignored) {\n                continue;\n            }\n            var _loop_1 = function (attempt) {\n                if (attempt === 1) { // trying again?\n                    row.reverse(); // reverse the row and continue\n                    // This means we will only ever draw result points *once* in the life of this method\n                    // since we want to avoid drawing the wrong points after flipping the row, and,\n                    // don't want to clutter with noise from every single row scan -- just the scans\n                    // that start on the center line.\n                    if (hints && (hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK) === true)) {\n                        var newHints_1 = new Map();\n                        hints.forEach(function (hint, key) { return newHints_1.set(key, hint); });\n                        newHints_1.delete(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n                        hints = newHints_1;\n                    }\n                }\n                try {\n                    // Look for a barcode\n                    var result = this_1.decodeRow(rowNumber, row, hints);\n                    // We found our barcode\n                    if (attempt === 1) {\n                        // But it was upside down, so note that\n                        result.putMetadata(ResultMetadataType.ORIENTATION, 180);\n                        // And remember to flip the result points horizontally.\n                        var points = result.getResultPoints();\n                        if (points !== null) {\n                            points[0] = new ResultPoint(width - points[0].getX() - 1, points[0].getY());\n                            points[1] = new ResultPoint(width - points[1].getX() - 1, points[1].getY());\n                        }\n                    }\n                    return { value: result };\n                }\n                catch (re) {\n                    // continue -- just couldn't decode this row\n                }\n            };\n            var this_1 = this;\n            // While we have the image data in a BitArray, it's fairly cheap to reverse it in place to\n            // handle decoding upside down barcodes.\n            for (var attempt = 0; attempt < 2; attempt++) {\n                var state_1 = _loop_1(attempt);\n                if (typeof state_1 === \"object\")\n                    return state_1.value;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Records the size of successive runs of white and black pixels in a row, starting at a given point.\n     * The values are recorded in the given array, and the number of runs recorded is equal to the size\n     * of the array. If the row starts on a white pixel at the given start point, then the first count\n     * recorded is the run of white pixels starting from that point; likewise it is the count of a run\n     * of black pixels if the row begin on a black pixels at that point.\n     *\n     * @param row row to count from\n     * @param start offset into row to start at\n     * @param counters array into which to record counts\n     * @throws NotFoundException if counters cannot be filled entirely from row before running out\n     *  of pixels\n     */\n    OneDReader.recordPattern = function (row, start, counters) {\n        var numCounters = counters.length;\n        for (var index = 0; index < numCounters; index++)\n            counters[index] = 0;\n        var end = row.getSize();\n        if (start >= end) {\n            throw new NotFoundException();\n        }\n        var isWhite = !row.get(start);\n        var counterPosition = 0;\n        var i = start;\n        while (i < end) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (++counterPosition === numCounters) {\n                    break;\n                }\n                else {\n                    counters[counterPosition] = 1;\n                    isWhite = !isWhite;\n                }\n            }\n            i++;\n        }\n        // If we read fully the last section of pixels and filled up our counters -- or filled\n        // the last counter but ran off the side of the image, OK. Otherwise, a problem.\n        if (!(counterPosition === numCounters || (counterPosition === numCounters - 1 && i === end))) {\n            throw new NotFoundException();\n        }\n    };\n    OneDReader.recordPatternInReverse = function (row, start, counters) {\n        // This could be more efficient I guess\n        var numTransitionsLeft = counters.length;\n        var last = row.get(start);\n        while (start > 0 && numTransitionsLeft >= 0) {\n            if (row.get(--start) !== last) {\n                numTransitionsLeft--;\n                last = !last;\n            }\n        }\n        if (numTransitionsLeft >= 0) {\n            throw new NotFoundException();\n        }\n        OneDReader.recordPattern(row, start + 1, counters);\n    };\n    /**\n     * Determines how closely a set of observed counts of runs of black/white values matches a given\n     * target pattern. This is reported as the ratio of the total variance from the expected pattern\n     * proportions across all pattern elements, to the length of the pattern.\n     *\n     * @param counters observed counters\n     * @param pattern expected pattern\n     * @param maxIndividualVariance The most any counter can differ before we give up\n     * @return ratio of total variance between counters and pattern compared to total pattern size\n     */\n    OneDReader.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n        var numCounters = counters.length;\n        var total = 0;\n        var patternLength = 0;\n        for (var i = 0; i < numCounters; i++) {\n            total += counters[i];\n            patternLength += pattern[i];\n        }\n        if (total < patternLength) {\n            // If we don't even have one pixel per unit of bar width, assume this is too small\n            // to reliably match, so fail:\n            return Number.POSITIVE_INFINITY;\n        }\n        var unitBarWidth = total / patternLength;\n        maxIndividualVariance *= unitBarWidth;\n        var totalVariance = 0.0;\n        for (var x = 0; x < numCounters; x++) {\n            var counter = counters[x];\n            var scaledPattern = pattern[x] * unitBarWidth;\n            var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n            if (variance > maxIndividualVariance) {\n                return Number.POSITIVE_INFINITY;\n            }\n            totalVariance += variance;\n        }\n        return totalVariance / total;\n    };\n    return OneDReader;\n}());\nexport default OneDReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;AACA;AACA;;;;;;AACA;;;;;;CAMC,GACD,IAAI,aAA4B;IAC5B,SAAS,cACT;IACA;;;;;IAKA,GACA,+FAA+F;IAC/F,YAAY;IACZ,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QAChD,IAAI;YACA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;QAChC,EACA,OAAO,KAAK;YACR,IAAI,YAAY,SAAU,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU,MAAM;YACnE,IAAI,aAAa,MAAM,iBAAiB,IAAI;gBACxC,IAAI,eAAe,MAAM,sBAAsB;gBAC/C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc;gBACzC,kEAAkE;gBAClE,IAAI,WAAW,OAAO,iBAAiB;gBACvC,IAAI,gBAAgB;gBACpB,IAAI,aAAa,QAAS,SAAS,GAAG,CAAC,0KAAA,CAAA,UAAkB,CAAC,WAAW,MAAM,MAAO;oBAC9E,sEAAsE;oBACtE,gBAAiB,gBAAgB,SAAS,GAAG,CAAC,0KAAA,CAAA,UAAkB,CAAC,WAAW,IAAI;gBACpF;gBACA,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,WAAW,EAAE;gBACnD,uBAAuB;gBACvB,IAAI,SAAS,OAAO,eAAe;gBACnC,IAAI,WAAW,MAAM;oBACjB,IAAI,SAAS,aAAa,SAAS;oBACnC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACpC,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;oBAC7E;gBACJ;gBACA,OAAO;YACX,OACK;gBACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;QACJ;IACJ;IACA,YAAY;IACZ,WAAW,SAAS,CAAC,KAAK,GAAG;IACzB,aAAa;IACjB;IACA;;;;;;;;;;;;;KAaC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK,EAAE,KAAK;QAClD,IAAI,QAAQ,MAAM,QAAQ;QAC1B,IAAI,SAAS,MAAM,SAAS;QAC5B,IAAI,MAAM,IAAI,0KAAA,CAAA,UAAQ,CAAC;QACvB,IAAI,YAAY,SAAU,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU,MAAM;QACnE,IAAI,UAAU,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,YAAY,IAAI,CAAC;QACtD,IAAI;QACJ,IAAI,WAAW;YACX,WAAW,QAAQ,+CAA+C;QACtE,OACK;YACD,WAAW,IAAI,oEAAoE;QACvF;QACA,IAAI,SAAS,KAAK,KAAK,CAAC,SAAS;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YAC/B,2EAA2E;YAC3E,IAAI,uBAAuB,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI;YAChD,IAAI,UAAU,CAAC,IAAI,IAAI,MAAM,GAAG,kBAAkB;YAClD,IAAI,YAAY,SAAS,UAAU,CAAC,UAAU,uBAAuB,CAAC,oBAAoB;YAC1F,IAAI,YAAY,KAAK,aAAa,QAAQ;gBAEtC;YACJ;YACA,iDAAiD;YACjD,IAAI;gBACA,MAAM,MAAM,WAAW,CAAC,WAAW;YACvC,EACA,OAAO,SAAS;gBACZ;YACJ;YACA,IAAI,UAAU,SAAU,OAAO;gBAC3B,IAAI,YAAY,GAAG;oBACf,IAAI,OAAO,IAAI,+BAA+B;oBAC9C,oFAAoF;oBACpF,+EAA+E;oBAC/E,gFAAgF;oBAChF,iCAAiC;oBACjC,IAAI,SAAU,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B,MAAM,MAAO;wBAC1E,IAAI,aAAa,IAAI;wBACrB,MAAM,OAAO,CAAC,SAAU,IAAI,EAAE,GAAG;4BAAI,OAAO,WAAW,GAAG,CAAC,KAAK;wBAAO;wBACvE,WAAW,MAAM,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B;wBAC3D,QAAQ;oBACZ;gBACJ;gBACA,IAAI;oBACA,qBAAqB;oBACrB,IAAI,SAAS,OAAO,SAAS,CAAC,WAAW,KAAK;oBAC9C,uBAAuB;oBACvB,IAAI,YAAY,GAAG;wBACf,uCAAuC;wBACvC,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,WAAW,EAAE;wBACnD,uDAAuD;wBACvD,IAAI,SAAS,OAAO,eAAe;wBACnC,IAAI,WAAW,MAAM;4BACjB,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;4BACxE,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;wBAC5E;oBACJ;oBACA,OAAO;wBAAE,OAAO;oBAAO;gBAC3B,EACA,OAAO,IAAI;gBACP,4CAA4C;gBAChD;YACJ;YACA,IAAI,SAAS,IAAI;YACjB,0FAA0F;YAC1F,wCAAwC;YACxC,IAAK,IAAI,UAAU,GAAG,UAAU,GAAG,UAAW;gBAC1C,IAAI,UAAU,QAAQ;gBACtB,IAAI,OAAO,YAAY,UACnB,OAAO,QAAQ,KAAK;YAC5B;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA;;;;;;;;;;;;KAYC,GACD,WAAW,aAAa,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,QAAQ;QACrD,IAAI,cAAc,SAAS,MAAM;QACjC,IAAK,IAAI,QAAQ,GAAG,QAAQ,aAAa,QACrC,QAAQ,CAAC,MAAM,GAAG;QACtB,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,SAAS,KAAK;YACd,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,UAAU,CAAC,IAAI,GAAG,CAAC;QACvB,IAAI,kBAAkB;QACtB,IAAI,IAAI;QACR,MAAO,IAAI,IAAK;YACZ,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,EAAE,oBAAoB,aAAa;oBACnC;gBACJ,OACK;oBACD,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B,UAAU,CAAC;gBACf;YACJ;YACA;QACJ;QACA,sFAAsF;QACtF,gFAAgF;QAChF,IAAI,CAAC,CAAC,oBAAoB,eAAgB,oBAAoB,cAAc,KAAK,MAAM,GAAI,GAAG;YAC1F,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,WAAW,sBAAsB,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,QAAQ;QAC9D,uCAAuC;QACvC,IAAI,qBAAqB,SAAS,MAAM;QACxC,IAAI,OAAO,IAAI,GAAG,CAAC;QACnB,MAAO,QAAQ,KAAK,sBAAsB,EAAG;YACzC,IAAI,IAAI,GAAG,CAAC,EAAE,WAAW,MAAM;gBAC3B;gBACA,OAAO,CAAC;YACZ;QACJ;QACA,IAAI,sBAAsB,GAAG;YACzB,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,WAAW,aAAa,CAAC,KAAK,QAAQ,GAAG;IAC7C;IACA;;;;;;;;;KASC,GACD,WAAW,oBAAoB,GAAG,SAAU,QAAQ,EAAE,OAAO,EAAE,qBAAqB;QAChF,IAAI,cAAc,SAAS,MAAM;QACjC,IAAI,QAAQ;QACZ,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,SAAS,QAAQ,CAAC,EAAE;YACpB,iBAAiB,OAAO,CAAC,EAAE;QAC/B;QACA,IAAI,QAAQ,eAAe;YACvB,kFAAkF;YAClF,8BAA8B;YAC9B,OAAO,OAAO,iBAAiB;QACnC;QACA,IAAI,eAAe,QAAQ;QAC3B,yBAAyB;QACzB,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,IAAI,UAAU,QAAQ,CAAC,EAAE;YACzB,IAAI,gBAAgB,OAAO,CAAC,EAAE,GAAG;YACjC,IAAI,WAAW,UAAU,gBAAgB,UAAU,gBAAgB,gBAAgB;YACnF,IAAI,WAAW,uBAAuB;gBAClC,OAAO,OAAO,iBAAiB;YACnC;YACA,iBAAiB;QACrB;QACA,OAAO,gBAAgB;IAC3B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/Code128Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\n// import Reader from '../Reader';\nimport Result from '../Result';\n// import ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes Code 128 barcodes.</p>\n *\n * <AUTHOR> Owen\n */\nvar Code128Reader = /** @class */ (function (_super) {\n    __extends(Code128Reader, _super);\n    function Code128Reader() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Code128Reader.findStartPattern = function (row) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        var counterPosition = 0;\n        var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = 6;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === (patternLength - 1)) {\n                    var bestVariance = Code128Reader.MAX_AVG_VARIANCE;\n                    var bestMatch = -1;\n                    for (var startCode = Code128Reader.CODE_START_A; startCode <= Code128Reader.CODE_START_C; startCode++) {\n                        var variance = OneDReader.patternMatchVariance(counters, Code128Reader.CODE_PATTERNS[startCode], Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n                        if (variance < bestVariance) {\n                            bestVariance = variance;\n                            bestMatch = startCode;\n                        }\n                    }\n                    // Look for whitespace before start pattern, >= 50% of width of start pattern\n                    if (bestMatch >= 0 &&\n                        row.isRange(Math.max(0, patternStart - (i - patternStart) / 2), patternStart, false)) {\n                        return Int32Array.from([patternStart, i, bestMatch]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters = counters.slice(2, counters.length);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    Code128Reader.decodeCode = function (row, counters, rowOffset) {\n        OneDReader.recordPattern(row, rowOffset, counters);\n        var bestVariance = Code128Reader.MAX_AVG_VARIANCE; // worst variance we'll accept\n        var bestMatch = -1;\n        for (var d = 0; d < Code128Reader.CODE_PATTERNS.length; d++) {\n            var pattern = Code128Reader.CODE_PATTERNS[d];\n            var variance = this.patternMatchVariance(counters, pattern, Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = d;\n            }\n        }\n        // TODO We're overlooking the fact that the STOP pattern has 7 values, not 6.\n        if (bestMatch >= 0) {\n            return bestMatch;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    Code128Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var convertFNC1 = hints && (hints.get(DecodeHintType.ASSUME_GS1) === true);\n        var startPatternInfo = Code128Reader.findStartPattern(row);\n        var startCode = startPatternInfo[2];\n        var currentRawCodesIndex = 0;\n        var rawCodes = new Uint8Array(20);\n        rawCodes[currentRawCodesIndex++] = startCode;\n        var codeSet;\n        switch (startCode) {\n            case Code128Reader.CODE_START_A:\n                codeSet = Code128Reader.CODE_CODE_A;\n                break;\n            case Code128Reader.CODE_START_B:\n                codeSet = Code128Reader.CODE_CODE_B;\n                break;\n            case Code128Reader.CODE_START_C:\n                codeSet = Code128Reader.CODE_CODE_C;\n                break;\n            default:\n                throw new FormatException();\n        }\n        var done = false;\n        var isNextShifted = false;\n        var result = '';\n        var lastStart = startPatternInfo[0];\n        var nextStart = startPatternInfo[1];\n        var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n        var lastCode = 0;\n        var code = 0;\n        var checksumTotal = startCode;\n        var multiplier = 0;\n        var lastCharacterWasPrintable = true;\n        var upperMode = false;\n        var shiftUpperMode = false;\n        while (!done) {\n            var unshift = isNextShifted;\n            isNextShifted = false;\n            // Save off last code\n            lastCode = code;\n            // Decode another code from image\n            code = Code128Reader.decodeCode(row, counters, nextStart);\n            rawCodes[currentRawCodesIndex++] = code;\n            // Remember whether the last code was printable or not (excluding CODE_STOP)\n            if (code !== Code128Reader.CODE_STOP) {\n                lastCharacterWasPrintable = true;\n            }\n            // Add to checksum computation (if not CODE_STOP of course)\n            if (code !== Code128Reader.CODE_STOP) {\n                multiplier++;\n                checksumTotal += multiplier * code;\n            }\n            // Advance to where the next code will to start\n            lastStart = nextStart;\n            nextStart += counters.reduce(function (previous, current) { return previous + current; }, 0);\n            // Take care of illegal start codes\n            switch (code) {\n                case Code128Reader.CODE_START_A:\n                case Code128Reader.CODE_START_B:\n                case Code128Reader.CODE_START_C:\n                    throw new FormatException();\n            }\n            switch (codeSet) {\n                case Code128Reader.CODE_CODE_A:\n                    if (code < 64) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code));\n                        }\n                        else {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code + 128));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else if (code < 96) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((code - 64));\n                        }\n                        else {\n                            result += String.fromCharCode((code + 64));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else {\n                        // Don't let CODE_STOP, which always appears, affect whether whether we think the last\n                        // code was printable or not.\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_FNC_2:\n                            case Code128Reader.CODE_FNC_3:\n                                // do nothing?\n                                break;\n                            case Code128Reader.CODE_FNC_4_A:\n                                if (!upperMode && shiftUpperMode) {\n                                    upperMode = true;\n                                    shiftUpperMode = false;\n                                }\n                                else if (upperMode && shiftUpperMode) {\n                                    upperMode = false;\n                                    shiftUpperMode = false;\n                                }\n                                else {\n                                    shiftUpperMode = true;\n                                }\n                                break;\n                            case Code128Reader.CODE_SHIFT:\n                                isNextShifted = true;\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_CODE_B:\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_CODE_C:\n                                codeSet = Code128Reader.CODE_CODE_C;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n                case Code128Reader.CODE_CODE_B:\n                    if (code < 96) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code));\n                        }\n                        else {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code + 128));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else {\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_FNC_2:\n                            case Code128Reader.CODE_FNC_3:\n                                // do nothing?\n                                break;\n                            case Code128Reader.CODE_FNC_4_B:\n                                if (!upperMode && shiftUpperMode) {\n                                    upperMode = true;\n                                    shiftUpperMode = false;\n                                }\n                                else if (upperMode && shiftUpperMode) {\n                                    upperMode = false;\n                                    shiftUpperMode = false;\n                                }\n                                else {\n                                    shiftUpperMode = true;\n                                }\n                                break;\n                            case Code128Reader.CODE_SHIFT:\n                                isNextShifted = true;\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_A:\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_C:\n                                codeSet = Code128Reader.CODE_CODE_C;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n                case Code128Reader.CODE_CODE_C:\n                    if (code < 100) {\n                        if (code < 10) {\n                            result += '0';\n                        }\n                        result += code;\n                    }\n                    else {\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_CODE_A:\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_B:\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n            }\n            // Unshift back to another code set if we were shifted\n            if (unshift) {\n                codeSet = codeSet === Code128Reader.CODE_CODE_A ? Code128Reader.CODE_CODE_B : Code128Reader.CODE_CODE_A;\n            }\n        }\n        var lastPatternSize = nextStart - lastStart;\n        // Check for ample whitespace following pattern, but, to do this we first need to remember that\n        // we fudged decoding CODE_STOP since it actually has 7 bars, not 6. There is a black bar left\n        // to read off. Would be slightly better to properly read. Here we just skip it:\n        nextStart = row.getNextUnset(nextStart);\n        if (!row.isRange(nextStart, Math.min(row.getSize(), nextStart + (nextStart - lastStart) / 2), false)) {\n            throw new NotFoundException();\n        }\n        // Pull out from sum the value of the penultimate check code\n        checksumTotal -= multiplier * lastCode;\n        // lastCode is the checksum then:\n        if (checksumTotal % 103 !== lastCode) {\n            throw new ChecksumException();\n        }\n        // Need to pull out the check digits from string\n        var resultLength = result.length;\n        if (resultLength === 0) {\n            // false positive\n            throw new NotFoundException();\n        }\n        // Only bother if the result had at least one character, and if the checksum digit happened to\n        // be a printable character. If it was just interpreted as a control code, nothing to remove.\n        if (resultLength > 0 && lastCharacterWasPrintable) {\n            if (codeSet === Code128Reader.CODE_CODE_C) {\n                result = result.substring(0, resultLength - 2);\n            }\n            else {\n                result = result.substring(0, resultLength - 1);\n            }\n        }\n        var left = (startPatternInfo[1] + startPatternInfo[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        var rawCodesSize = rawCodes.length;\n        var rawBytes = new Uint8Array(rawCodesSize);\n        for (var i = 0; i < rawCodesSize; i++) {\n            rawBytes[i] = rawCodes[i];\n        }\n        var points = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n        return new Result(result, rawBytes, 0, points, BarcodeFormat.CODE_128, new Date().getTime());\n    };\n    Code128Reader.CODE_PATTERNS = [\n        Int32Array.from([2, 1, 2, 2, 2, 2]),\n        Int32Array.from([2, 2, 2, 1, 2, 2]),\n        Int32Array.from([2, 2, 2, 2, 2, 1]),\n        Int32Array.from([1, 2, 1, 2, 2, 3]),\n        Int32Array.from([1, 2, 1, 3, 2, 2]),\n        Int32Array.from([1, 3, 1, 2, 2, 2]),\n        Int32Array.from([1, 2, 2, 2, 1, 3]),\n        Int32Array.from([1, 2, 2, 3, 1, 2]),\n        Int32Array.from([1, 3, 2, 2, 1, 2]),\n        Int32Array.from([2, 2, 1, 2, 1, 3]),\n        Int32Array.from([2, 2, 1, 3, 1, 2]),\n        Int32Array.from([2, 3, 1, 2, 1, 2]),\n        Int32Array.from([1, 1, 2, 2, 3, 2]),\n        Int32Array.from([1, 2, 2, 1, 3, 2]),\n        Int32Array.from([1, 2, 2, 2, 3, 1]),\n        Int32Array.from([1, 1, 3, 2, 2, 2]),\n        Int32Array.from([1, 2, 3, 1, 2, 2]),\n        Int32Array.from([1, 2, 3, 2, 2, 1]),\n        Int32Array.from([2, 2, 3, 2, 1, 1]),\n        Int32Array.from([2, 2, 1, 1, 3, 2]),\n        Int32Array.from([2, 2, 1, 2, 3, 1]),\n        Int32Array.from([2, 1, 3, 2, 1, 2]),\n        Int32Array.from([2, 2, 3, 1, 1, 2]),\n        Int32Array.from([3, 1, 2, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 2, 2, 2]),\n        Int32Array.from([3, 2, 1, 1, 2, 2]),\n        Int32Array.from([3, 2, 1, 2, 2, 1]),\n        Int32Array.from([3, 1, 2, 2, 1, 2]),\n        Int32Array.from([3, 2, 2, 1, 1, 2]),\n        Int32Array.from([3, 2, 2, 2, 1, 1]),\n        Int32Array.from([2, 1, 2, 1, 2, 3]),\n        Int32Array.from([2, 1, 2, 3, 2, 1]),\n        Int32Array.from([2, 3, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 1, 3, 2, 3]),\n        Int32Array.from([1, 3, 1, 1, 2, 3]),\n        Int32Array.from([1, 3, 1, 3, 2, 1]),\n        Int32Array.from([1, 1, 2, 3, 1, 3]),\n        Int32Array.from([1, 3, 2, 1, 1, 3]),\n        Int32Array.from([1, 3, 2, 3, 1, 1]),\n        Int32Array.from([2, 1, 1, 3, 1, 3]),\n        Int32Array.from([2, 3, 1, 1, 1, 3]),\n        Int32Array.from([2, 3, 1, 3, 1, 1]),\n        Int32Array.from([1, 1, 2, 1, 3, 3]),\n        Int32Array.from([1, 1, 2, 3, 3, 1]),\n        Int32Array.from([1, 3, 2, 1, 3, 1]),\n        Int32Array.from([1, 1, 3, 1, 2, 3]),\n        Int32Array.from([1, 1, 3, 3, 2, 1]),\n        Int32Array.from([1, 3, 3, 1, 2, 1]),\n        Int32Array.from([3, 1, 3, 1, 2, 1]),\n        Int32Array.from([2, 1, 1, 3, 3, 1]),\n        Int32Array.from([2, 3, 1, 1, 3, 1]),\n        Int32Array.from([2, 1, 3, 1, 1, 3]),\n        Int32Array.from([2, 1, 3, 3, 1, 1]),\n        Int32Array.from([2, 1, 3, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 2, 3]),\n        Int32Array.from([3, 1, 1, 3, 2, 1]),\n        Int32Array.from([3, 3, 1, 1, 2, 1]),\n        Int32Array.from([3, 1, 2, 1, 1, 3]),\n        Int32Array.from([3, 1, 2, 3, 1, 1]),\n        Int32Array.from([3, 3, 2, 1, 1, 1]),\n        Int32Array.from([3, 1, 4, 1, 1, 1]),\n        Int32Array.from([2, 2, 1, 4, 1, 1]),\n        Int32Array.from([4, 3, 1, 1, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 2, 4]),\n        Int32Array.from([1, 1, 1, 4, 2, 2]),\n        Int32Array.from([1, 2, 1, 1, 2, 4]),\n        Int32Array.from([1, 2, 1, 4, 2, 1]),\n        Int32Array.from([1, 4, 1, 1, 2, 2]),\n        Int32Array.from([1, 4, 1, 2, 2, 1]),\n        Int32Array.from([1, 1, 2, 2, 1, 4]),\n        Int32Array.from([1, 1, 2, 4, 1, 2]),\n        Int32Array.from([1, 2, 2, 1, 1, 4]),\n        Int32Array.from([1, 2, 2, 4, 1, 1]),\n        Int32Array.from([1, 4, 2, 1, 1, 2]),\n        Int32Array.from([1, 4, 2, 2, 1, 1]),\n        Int32Array.from([2, 4, 1, 2, 1, 1]),\n        Int32Array.from([2, 2, 1, 1, 1, 4]),\n        Int32Array.from([4, 1, 3, 1, 1, 1]),\n        Int32Array.from([2, 4, 1, 1, 1, 2]),\n        Int32Array.from([1, 3, 4, 1, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 4, 2]),\n        Int32Array.from([1, 2, 1, 1, 4, 2]),\n        Int32Array.from([1, 2, 1, 2, 4, 1]),\n        Int32Array.from([1, 1, 4, 2, 1, 2]),\n        Int32Array.from([1, 2, 4, 1, 1, 2]),\n        Int32Array.from([1, 2, 4, 2, 1, 1]),\n        Int32Array.from([4, 1, 1, 2, 1, 2]),\n        Int32Array.from([4, 2, 1, 1, 1, 2]),\n        Int32Array.from([4, 2, 1, 2, 1, 1]),\n        Int32Array.from([2, 1, 2, 1, 4, 1]),\n        Int32Array.from([2, 1, 4, 1, 2, 1]),\n        Int32Array.from([4, 1, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 1, 1, 4, 3]),\n        Int32Array.from([1, 1, 1, 3, 4, 1]),\n        Int32Array.from([1, 3, 1, 1, 4, 1]),\n        Int32Array.from([1, 1, 4, 1, 1, 3]),\n        Int32Array.from([1, 1, 4, 3, 1, 1]),\n        Int32Array.from([4, 1, 1, 1, 1, 3]),\n        Int32Array.from([4, 1, 1, 3, 1, 1]),\n        Int32Array.from([1, 1, 3, 1, 4, 1]),\n        Int32Array.from([1, 1, 4, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 4, 1]),\n        Int32Array.from([4, 1, 1, 1, 3, 1]),\n        Int32Array.from([2, 1, 1, 4, 1, 2]),\n        Int32Array.from([2, 1, 1, 2, 1, 4]),\n        Int32Array.from([2, 1, 1, 2, 3, 2]),\n        Int32Array.from([2, 3, 3, 1, 1, 1, 2]),\n    ];\n    Code128Reader.MAX_AVG_VARIANCE = 0.25;\n    Code128Reader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n    Code128Reader.CODE_SHIFT = 98;\n    Code128Reader.CODE_CODE_C = 99;\n    Code128Reader.CODE_CODE_B = 100;\n    Code128Reader.CODE_CODE_A = 101;\n    Code128Reader.CODE_FNC_1 = 102;\n    Code128Reader.CODE_FNC_2 = 97;\n    Code128Reader.CODE_FNC_3 = 96;\n    Code128Reader.CODE_FNC_4_A = 101;\n    Code128Reader.CODE_FNC_4_B = 100;\n    Code128Reader.CODE_START_A = 103;\n    Code128Reader.CODE_START_B = 104;\n    Code128Reader.CODE_START_C = 105;\n    Code128Reader.CODE_STOP = 106;\n    return Code128Reader;\n}(OneDReader));\nexport default Code128Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,0DAA0D;AAC1D;AACA;AAvBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;;;;AAYA;;;;CAIC,GACD,IAAI,gBAA+B,SAAU,MAAM;IAC/C,UAAU,eAAe;IACzB,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,cAAc,gBAAgB,GAAG,SAAU,GAAG;QAC1C,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,YAAY,IAAI,UAAU,CAAC;QAC/B,IAAI,kBAAkB;QACtB,IAAI,WAAW,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACjD,IAAI,eAAe;QACnB,IAAI,UAAU;QACd,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAqB,gBAAgB,GAAI;oBACzC,IAAI,eAAe,cAAc,gBAAgB;oBACjD,IAAI,YAAY,CAAC;oBACjB,IAAK,IAAI,YAAY,cAAc,YAAY,EAAE,aAAa,cAAc,YAAY,EAAE,YAAa;wBACnG,IAAI,WAAW,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,cAAc,aAAa,CAAC,UAAU,EAAE,cAAc,uBAAuB;wBACtI,IAAI,WAAW,cAAc;4BACzB,eAAe;4BACf,YAAY;wBAChB;oBACJ;oBACA,6EAA6E;oBAC7E,IAAI,aAAa,KACb,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,YAAY,IAAI,IAAI,cAAc,QAAQ;wBACtF,OAAO,WAAW,IAAI,CAAC;4BAAC;4BAAc;4BAAG;yBAAU;oBACvD;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,WAAW,SAAS,KAAK,CAAC,GAAG,SAAS,MAAM;oBAC5C,QAAQ,CAAC,kBAAkB,EAAE,GAAG;oBAChC,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,cAAc,UAAU,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,SAAS;QACzD,0KAAA,CAAA,UAAU,CAAC,aAAa,CAAC,KAAK,WAAW;QACzC,IAAI,eAAe,cAAc,gBAAgB,EAAE,8BAA8B;QACjF,IAAI,YAAY,CAAC;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,aAAa,CAAC,MAAM,EAAE,IAAK;YACzD,IAAI,UAAU,cAAc,aAAa,CAAC,EAAE;YAC5C,IAAI,WAAW,IAAI,CAAC,oBAAoB,CAAC,UAAU,SAAS,cAAc,uBAAuB;YACjG,IAAI,WAAW,cAAc;gBACzB,eAAe;gBACf,YAAY;YAChB;QACJ;QACA,6EAA6E;QAC7E,IAAI,aAAa,GAAG;YAChB,OAAO;QACX,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC/D,IAAI,cAAc,SAAU,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,UAAU,MAAM;QACrE,IAAI,mBAAmB,cAAc,gBAAgB,CAAC;QACtD,IAAI,YAAY,gBAAgB,CAAC,EAAE;QACnC,IAAI,uBAAuB;QAC3B,IAAI,WAAW,IAAI,WAAW;QAC9B,QAAQ,CAAC,uBAAuB,GAAG;QACnC,IAAI;QACJ,OAAQ;YACJ,KAAK,cAAc,YAAY;gBAC3B,UAAU,cAAc,WAAW;gBACnC;YACJ,KAAK,cAAc,YAAY;gBAC3B,UAAU,cAAc,WAAW;gBACnC;YACJ,KAAK,cAAc,YAAY;gBAC3B,UAAU,cAAc,WAAW;gBACnC;YACJ;gBACI,MAAM,IAAI,uKAAA,CAAA,UAAe;QACjC;QACA,IAAI,OAAO;QACX,IAAI,gBAAgB;QACpB,IAAI,SAAS;QACb,IAAI,YAAY,gBAAgB,CAAC,EAAE;QACnC,IAAI,YAAY,gBAAgB,CAAC,EAAE;QACnC,IAAI,WAAW,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QACjD,IAAI,WAAW;QACf,IAAI,OAAO;QACX,IAAI,gBAAgB;QACpB,IAAI,aAAa;QACjB,IAAI,4BAA4B;QAChC,IAAI,YAAY;QAChB,IAAI,iBAAiB;QACrB,MAAO,CAAC,KAAM;YACV,IAAI,UAAU;YACd,gBAAgB;YAChB,qBAAqB;YACrB,WAAW;YACX,iCAAiC;YACjC,OAAO,cAAc,UAAU,CAAC,KAAK,UAAU;YAC/C,QAAQ,CAAC,uBAAuB,GAAG;YACnC,4EAA4E;YAC5E,IAAI,SAAS,cAAc,SAAS,EAAE;gBAClC,4BAA4B;YAChC;YACA,2DAA2D;YAC3D,IAAI,SAAS,cAAc,SAAS,EAAE;gBAClC;gBACA,iBAAiB,aAAa;YAClC;YACA,+CAA+C;YAC/C,YAAY;YACZ,aAAa,SAAS,MAAM,CAAC,SAAU,QAAQ,EAAE,OAAO;gBAAI,OAAO,WAAW;YAAS,GAAG;YAC1F,mCAAmC;YACnC,OAAQ;gBACJ,KAAK,cAAc,YAAY;gBAC/B,KAAK,cAAc,YAAY;gBAC/B,KAAK,cAAc,YAAY;oBAC3B,MAAM,IAAI,uKAAA,CAAA,UAAe;YACjC;YACA,OAAQ;gBACJ,KAAK,cAAc,WAAW;oBAC1B,IAAI,OAAO,IAAI;wBACX,IAAI,mBAAmB,WAAW;4BAC9B,UAAU,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK;wBACvD,OACK;4BACD,UAAU,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,OAAO;wBAC9D;wBACA,iBAAiB;oBACrB,OACK,IAAI,OAAO,IAAI;wBAChB,IAAI,mBAAmB,WAAW;4BAC9B,UAAU,OAAO,YAAY,CAAE,OAAO;wBAC1C,OACK;4BACD,UAAU,OAAO,YAAY,CAAE,OAAO;wBAC1C;wBACA,iBAAiB;oBACrB,OACK;wBACD,sFAAsF;wBACtF,6BAA6B;wBAC7B,IAAI,SAAS,cAAc,SAAS,EAAE;4BAClC,4BAA4B;wBAChC;wBACA,OAAQ;4BACJ,KAAK,cAAc,UAAU;gCACzB,IAAI,aAAa;oCACb,IAAI,OAAO,MAAM,KAAK,GAAG;wCACrB,iFAAiF;wCACjF,iEAAiE;wCACjE,UAAU;oCACd,OACK;wCACD,gFAAgF;wCAChF,UAAU,OAAO,YAAY,CAAC;oCAClC;gCACJ;gCACA;4BACJ,KAAK,cAAc,UAAU;4BAC7B,KAAK,cAAc,UAAU;gCAEzB;4BACJ,KAAK,cAAc,YAAY;gCAC3B,IAAI,CAAC,aAAa,gBAAgB;oCAC9B,YAAY;oCACZ,iBAAiB;gCACrB,OACK,IAAI,aAAa,gBAAgB;oCAClC,YAAY;oCACZ,iBAAiB;gCACrB,OACK;oCACD,iBAAiB;gCACrB;gCACA;4BACJ,KAAK,cAAc,UAAU;gCACzB,gBAAgB;gCAChB,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,SAAS;gCACxB,OAAO;gCACP;wBACR;oBACJ;oBACA;gBACJ,KAAK,cAAc,WAAW;oBAC1B,IAAI,OAAO,IAAI;wBACX,IAAI,mBAAmB,WAAW;4BAC9B,UAAU,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK;wBACvD,OACK;4BACD,UAAU,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,OAAO;wBAC9D;wBACA,iBAAiB;oBACrB,OACK;wBACD,IAAI,SAAS,cAAc,SAAS,EAAE;4BAClC,4BAA4B;wBAChC;wBACA,OAAQ;4BACJ,KAAK,cAAc,UAAU;gCACzB,IAAI,aAAa;oCACb,IAAI,OAAO,MAAM,KAAK,GAAG;wCACrB,iFAAiF;wCACjF,iEAAiE;wCACjE,UAAU;oCACd,OACK;wCACD,gFAAgF;wCAChF,UAAU,OAAO,YAAY,CAAC;oCAClC;gCACJ;gCACA;4BACJ,KAAK,cAAc,UAAU;4BAC7B,KAAK,cAAc,UAAU;gCAEzB;4BACJ,KAAK,cAAc,YAAY;gCAC3B,IAAI,CAAC,aAAa,gBAAgB;oCAC9B,YAAY;oCACZ,iBAAiB;gCACrB,OACK,IAAI,aAAa,gBAAgB;oCAClC,YAAY;oCACZ,iBAAiB;gCACrB,OACK;oCACD,iBAAiB;gCACrB;gCACA;4BACJ,KAAK,cAAc,UAAU;gCACzB,gBAAgB;gCAChB,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,SAAS;gCACxB,OAAO;gCACP;wBACR;oBACJ;oBACA;gBACJ,KAAK,cAAc,WAAW;oBAC1B,IAAI,OAAO,KAAK;wBACZ,IAAI,OAAO,IAAI;4BACX,UAAU;wBACd;wBACA,UAAU;oBACd,OACK;wBACD,IAAI,SAAS,cAAc,SAAS,EAAE;4BAClC,4BAA4B;wBAChC;wBACA,OAAQ;4BACJ,KAAK,cAAc,UAAU;gCACzB,IAAI,aAAa;oCACb,IAAI,OAAO,MAAM,KAAK,GAAG;wCACrB,iFAAiF;wCACjF,iEAAiE;wCACjE,UAAU;oCACd,OACK;wCACD,gFAAgF;wCAChF,UAAU,OAAO,YAAY,CAAC;oCAClC;gCACJ;gCACA;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,WAAW;gCAC1B,UAAU,cAAc,WAAW;gCACnC;4BACJ,KAAK,cAAc,SAAS;gCACxB,OAAO;gCACP;wBACR;oBACJ;oBACA;YACR;YACA,sDAAsD;YACtD,IAAI,SAAS;gBACT,UAAU,YAAY,cAAc,WAAW,GAAG,cAAc,WAAW,GAAG,cAAc,WAAW;YAC3G;QACJ;QACA,IAAI,kBAAkB,YAAY;QAClC,+FAA+F;QAC/F,8FAA8F;QAC9F,gFAAgF;QAChF,YAAY,IAAI,YAAY,CAAC;QAC7B,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,IAAI,YAAY,CAAC,YAAY,SAAS,IAAI,IAAI,QAAQ;YAClG,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,4DAA4D;QAC5D,iBAAiB,aAAa;QAC9B,iCAAiC;QACjC,IAAI,gBAAgB,QAAQ,UAAU;YAClC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,gDAAgD;QAChD,IAAI,eAAe,OAAO,MAAM;QAChC,IAAI,iBAAiB,GAAG;YACpB,iBAAiB;YACjB,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,8FAA8F;QAC9F,6FAA6F;QAC7F,IAAI,eAAe,KAAK,2BAA2B;YAC/C,IAAI,YAAY,cAAc,WAAW,EAAE;gBACvC,SAAS,OAAO,SAAS,CAAC,GAAG,eAAe;YAChD,OACK;gBACD,SAAS,OAAO,SAAS,CAAC,GAAG,eAAe;YAChD;QACJ;QACA,IAAI,OAAO,CAAC,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,IAAI;QACzD,IAAI,QAAQ,YAAY,kBAAkB;QAC1C,IAAI,eAAe,SAAS,MAAM;QAClC,IAAI,WAAW,IAAI,WAAW;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QAC7B;QACA,IAAI,SAAS;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO;SAAW;QAClF,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,QAAQ,UAAU,GAAG,QAAQ,qKAAA,CAAA,UAAa,CAAC,QAAQ,EAAE,IAAI,OAAO,OAAO;IAC7F;IACA,cAAc,aAAa,GAAG;QAC1B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAClC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;KACxC;IACD,cAAc,gBAAgB,GAAG;IACjC,cAAc,uBAAuB,GAAG;IACxC,cAAc,UAAU,GAAG;IAC3B,cAAc,WAAW,GAAG;IAC5B,cAAc,WAAW,GAAG;IAC5B,cAAc,WAAW,GAAG;IAC5B,cAAc,UAAU,GAAG;IAC3B,cAAc,UAAU,GAAG;IAC3B,cAAc,UAAU,GAAG;IAC3B,cAAc,YAAY,GAAG;IAC7B,cAAc,YAAY,GAAG;IAC7B,cAAc,YAAY,GAAG;IAC7B,cAAc,YAAY,GAAG;IAC7B,cAAc,YAAY,GAAG;IAC7B,cAAc,SAAS,GAAG;IAC1B,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/Code39Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 39 barcodes. Supports \"Full ASCII Code 39\" if USE_CODE_39_EXTENDED_MODE is set.</p>\n *\n * <AUTHOR> Owen\n * @see Code93Reader\n */\nvar Code39Reader = /** @class */ (function (_super) {\n    __extends(Code39Reader, _super);\n    /**\n     * Creates a reader that assumes all encoded data is data, and does not treat the final\n     * character as a check digit. It will not decoded \"extended Code 39\" sequences.\n     */\n    // public Code39Reader() {\n    //   this(false);\n    // }\n    /**\n     * Creates a reader that can be configured to check the last character as a check digit.\n     * It will not decoded \"extended Code 39\" sequences.\n     *\n     * @param usingCheckDigit if true, treat the last data character as a check digit, not\n     * data, and verify that the checksum passes.\n     */\n    // public Code39Reader(boolean usingCheckDigit) {\n    //   this(usingCheckDigit, false);\n    // }\n    /**\n     * Creates a reader that can be configured to check the last character as a check digit,\n     * or optionally attempt to decode \"extended Code 39\" sequences that are used to encode\n     * the full ASCII character set.\n     *\n     * @param usingCheckDigit if true, treat the last data character as a check digit, not\n     * data, and verify that the checksum passes.\n     * @param extendedMode if true, will attempt to decode extended Code 39 sequences in the\n     * text.\n     */\n    function Code39Reader(usingCheckDigit, extendedMode) {\n        if (usingCheckDigit === void 0) { usingCheckDigit = false; }\n        if (extendedMode === void 0) { extendedMode = false; }\n        var _this = _super.call(this) || this;\n        _this.usingCheckDigit = usingCheckDigit;\n        _this.extendedMode = extendedMode;\n        _this.decodeRowResult = '';\n        _this.counters = new Int32Array(9);\n        return _this;\n    }\n    Code39Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var theCounters = this.counters;\n        theCounters.fill(0);\n        this.decodeRowResult = '';\n        var start = Code39Reader.findAsteriskPattern(row, theCounters);\n        // Read off white space\n        var nextStart = row.getNextSet(start[1]);\n        var end = row.getSize();\n        var decodedChar;\n        var lastStart;\n        do {\n            Code39Reader.recordPattern(row, nextStart, theCounters);\n            var pattern = Code39Reader.toNarrowWidePattern(theCounters);\n            if (pattern < 0) {\n                throw new NotFoundException();\n            }\n            decodedChar = Code39Reader.patternToChar(pattern);\n            this.decodeRowResult += decodedChar;\n            lastStart = nextStart;\n            try {\n                for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n                    var counter = theCounters_1_1.value;\n                    nextStart += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Read off white space\n            nextStart = row.getNextSet(nextStart);\n        } while (decodedChar !== '*');\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n        // Look for whitespace after pattern:\n        var lastPatternSize = 0;\n        try {\n            for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n                var counter = theCounters_2_1.value;\n                lastPatternSize += counter;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        var whiteSpaceAfterEnd = nextStart - lastStart - lastPatternSize;\n        // If 50% of last pattern size, following last pattern, is not whitespace, fail\n        // (but if it's whitespace to the very end of the image, that's OK)\n        if (nextStart !== end && (whiteSpaceAfterEnd * 2) < lastPatternSize) {\n            throw new NotFoundException();\n        }\n        if (this.usingCheckDigit) {\n            var max = this.decodeRowResult.length - 1;\n            var total = 0;\n            for (var i = 0; i < max; i++) {\n                total += Code39Reader.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(i));\n            }\n            if (this.decodeRowResult.charAt(max) !== Code39Reader.ALPHABET_STRING.charAt(total % 43)) {\n                throw new ChecksumException();\n            }\n            this.decodeRowResult = this.decodeRowResult.substring(0, max);\n        }\n        if (this.decodeRowResult.length === 0) {\n            // false positive\n            throw new NotFoundException();\n        }\n        var resultString;\n        if (this.extendedMode) {\n            resultString = Code39Reader.decodeExtended(this.decodeRowResult);\n        }\n        else {\n            resultString = this.decodeRowResult;\n        }\n        var left = (start[1] + start[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_39, new Date().getTime());\n    };\n    Code39Reader.findAsteriskPattern = function (row, counters) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = counters.length;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    // Look for whitespace before start pattern, >= 50% of width of start pattern\n                    if (this.toNarrowWidePattern(counters) === Code39Reader.ASTERISK_ENCODING &&\n                        row.isRange(Math.max(0, patternStart - Math.floor((i - patternStart) / 2)), patternStart, false)) {\n                        return [patternStart, i];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters.copyWithin(0, 2, 2 + counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    // For efficiency, returns -1 on failure. Not throwing here saved as many as 700 exceptions\n    // per image when using some of our blackbox images.\n    Code39Reader.toNarrowWidePattern = function (counters) {\n        var e_3, _a;\n        var numCounters = counters.length;\n        var maxNarrowCounter = 0;\n        var wideCounters;\n        do {\n            var minCounter = 0x7fffffff;\n            try {\n                for (var counters_1 = (e_3 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    if (counter < minCounter && counter > maxNarrowCounter) {\n                        minCounter = counter;\n                    }\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            maxNarrowCounter = minCounter;\n            wideCounters = 0;\n            var totalWideCountersWidth = 0;\n            var pattern = 0;\n            for (var i = 0; i < numCounters; i++) {\n                var counter = counters[i];\n                if (counter > maxNarrowCounter) {\n                    pattern |= 1 << (numCounters - 1 - i);\n                    wideCounters++;\n                    totalWideCountersWidth += counter;\n                }\n            }\n            if (wideCounters === 3) {\n                // Found 3 wide counters, but are they close enough in width?\n                // We can perform a cheap, conservative check to see if any individual\n                // counter is more than 1.5 times the average:\n                for (var i = 0; i < numCounters && wideCounters > 0; i++) {\n                    var counter = counters[i];\n                    if (counter > maxNarrowCounter) {\n                        wideCounters--;\n                        // totalWideCountersWidth = 3 * average, so this checks if counter >= 3/2 * average\n                        if ((counter * 2) >= totalWideCountersWidth) {\n                            return -1;\n                        }\n                    }\n                }\n                return pattern;\n            }\n        } while (wideCounters > 3);\n        return -1;\n    };\n    Code39Reader.patternToChar = function (pattern) {\n        for (var i = 0; i < Code39Reader.CHARACTER_ENCODINGS.length; i++) {\n            if (Code39Reader.CHARACTER_ENCODINGS[i] === pattern) {\n                return Code39Reader.ALPHABET_STRING.charAt(i);\n            }\n        }\n        if (pattern === Code39Reader.ASTERISK_ENCODING) {\n            return '*';\n        }\n        throw new NotFoundException();\n    };\n    Code39Reader.decodeExtended = function (encoded) {\n        var length = encoded.length;\n        var decoded = '';\n        for (var i = 0; i < length; i++) {\n            var c = encoded.charAt(i);\n            if (c === '+' || c === '$' || c === '%' || c === '/') {\n                var next = encoded.charAt(i + 1);\n                var decodedChar = '\\0';\n                switch (c) {\n                    case '+':\n                        // +A to +Z map to a to z\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '$':\n                        // $A to $Z map to control codes SH to SB\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '%':\n                        // %A to %E map to control codes ESC to US\n                        if (next >= 'A' && next <= 'E') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n                        }\n                        else if (next >= 'F' && next <= 'J') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n                        }\n                        else if (next >= 'K' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n                        }\n                        else if (next >= 'P' && next <= 'T') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n                        }\n                        else if (next === 'U') {\n                            decodedChar = '\\0';\n                        }\n                        else if (next === 'V') {\n                            decodedChar = '@';\n                        }\n                        else if (next === 'W') {\n                            decodedChar = '`';\n                        }\n                        else if (next === 'X' || next === 'Y' || next === 'Z') {\n                            decodedChar = '\\x7f';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '/':\n                        // /A to /O map to ! to , and /Z maps to :\n                        if (next >= 'A' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n                        }\n                        else if (next === 'Z') {\n                            decodedChar = ':';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n                decoded += decodedChar;\n                // bump up i again since we read two characters\n                i++;\n            }\n            else {\n                decoded += c;\n            }\n        }\n        return decoded;\n    };\n    Code39Reader.ALPHABET_STRING = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';\n    /**\n     * These represent the encodings of characters, as patterns of wide and narrow bars.\n     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow,\n     * with 1s representing \"wide\" and 0s representing narrow.\n     */\n    Code39Reader.CHARACTER_ENCODINGS = [\n        0x034, 0x121, 0x061, 0x160, 0x031, 0x130, 0x070, 0x025, 0x124, 0x064,\n        0x109, 0x049, 0x148, 0x019, 0x118, 0x058, 0x00D, 0x10C, 0x04C, 0x01C,\n        0x103, 0x043, 0x142, 0x013, 0x112, 0x052, 0x007, 0x106, 0x046, 0x016,\n        0x181, 0x0C1, 0x1C0, 0x091, 0x190, 0x0D0, 0x085, 0x184, 0x0C4, 0x0A8,\n        0x0A2, 0x08A, 0x02A // /-%\n    ];\n    Code39Reader.ASTERISK_ENCODING = 0x094;\n    return Code39Reader;\n}(OneDReader));\nexport default Code39Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;AASA;;;;;CAKC,GACD,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB;;;KAGC,GACD,0BAA0B;IAC1B,iBAAiB;IACjB,IAAI;IACJ;;;;;;KAMC,GACD,iDAAiD;IACjD,kCAAkC;IAClC,IAAI;IACJ;;;;;;;;;KASC,GACD,SAAS,aAAa,eAAe,EAAE,YAAY;QAC/C,IAAI,oBAAoB,KAAK,GAAG;YAAE,kBAAkB;QAAO;QAC3D,IAAI,iBAAiB,KAAK,GAAG;YAAE,eAAe;QAAO;QACrD,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,eAAe,GAAG;QACxB,MAAM,YAAY,GAAG;QACrB,MAAM,eAAe,GAAG;QACxB,MAAM,QAAQ,GAAG,IAAI,WAAW;QAChC,OAAO;IACX;IACA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC9D,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,cAAc,IAAI,CAAC,QAAQ;QAC/B,YAAY,IAAI,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,QAAQ,aAAa,mBAAmB,CAAC,KAAK;QAClD,uBAAuB;QACvB,IAAI,YAAY,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACvC,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI;QACJ,IAAI;QACJ,GAAG;YACC,aAAa,aAAa,CAAC,KAAK,WAAW;YAC3C,IAAI,UAAU,aAAa,mBAAmB,CAAC;YAC/C,IAAI,UAAU,GAAG;gBACb,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,cAAc,aAAa,aAAa,CAAC;YACzC,IAAI,CAAC,eAAe,IAAI;YACxB,YAAY;YACZ,IAAI;gBACA,IAAK,IAAI,gBAAgB,CAAC,MAAM,KAAK,GAAG,SAAS,YAAY,GAAG,kBAAkB,cAAc,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,kBAAkB,cAAc,IAAI,GAAI;oBACnK,IAAI,UAAU,gBAAgB,KAAK;oBACnC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC;gBACzF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,uBAAuB;YACvB,YAAY,IAAI,UAAU,CAAC;QAC/B,QAAS,gBAAgB,IAAK;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,kBAAkB;QAC7G,qCAAqC;QACrC,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAK,IAAI,gBAAgB,SAAS,cAAc,kBAAkB,cAAc,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,kBAAkB,cAAc,IAAI,GAAI;gBACnJ,IAAI,UAAU,gBAAgB,KAAK;gBACnC,mBAAmB;YACvB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC;YACzF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,qBAAqB,YAAY,YAAY;QACjD,+EAA+E;QAC/E,mEAAmE;QACnE,IAAI,cAAc,OAAO,AAAC,qBAAqB,IAAK,iBAAiB;YACjE,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG;YACxC,IAAI,QAAQ;YACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC1B,SAAS,aAAa,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAC9E;YACA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,aAAa,eAAe,CAAC,MAAM,CAAC,QAAQ,KAAK;gBACtF,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG;QAC7D;QACA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,GAAG;YACnC,iBAAiB;YACjB,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,eAAe,aAAa,cAAc,CAAC,IAAI,CAAC,eAAe;QACnE,OACK;YACD,eAAe,IAAI,CAAC,eAAe;QACvC;QACA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,QAAQ,YAAY,kBAAkB;QAC1C,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAAM,GAAG;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO;SAAW,EAAE,qKAAA,CAAA,UAAa,CAAC,OAAO,EAAE,IAAI,OAAO,OAAO;IAC7J;IACA,aAAa,mBAAmB,GAAG,SAAU,GAAG,EAAE,QAAQ;QACtD,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,YAAY,IAAI,UAAU,CAAC;QAC/B,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,IAAI,UAAU;QACd,IAAI,gBAAgB,SAAS,MAAM;QACnC,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,gBAAgB,GAAG;oBACvC,6EAA6E;oBAC7E,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,aAAa,iBAAiB,IACrE,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,KAAK,KAAK,CAAC,CAAC,IAAI,YAAY,IAAI,KAAK,cAAc,QAAQ;wBAClG,OAAO;4BAAC;4BAAc;yBAAE;oBAC5B;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,SAAS,UAAU,CAAC,GAAG,GAAG,IAAI,kBAAkB;oBAChD,QAAQ,CAAC,kBAAkB,EAAE,GAAG;oBAChC,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,2FAA2F;IAC3F,oDAAoD;IACpD,aAAa,mBAAmB,GAAG,SAAU,QAAQ;QACjD,IAAI,KAAK;QACT,IAAI,cAAc,SAAS,MAAM;QACjC,IAAI,mBAAmB;QACvB,IAAI;QACJ,GAAG;YACC,IAAI,aAAa;YACjB,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,IAAI,UAAU,cAAc,UAAU,kBAAkB;wBACpD,aAAa;oBACjB;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,mBAAmB;YACnB,eAAe;YACf,IAAI,yBAAyB;YAC7B,IAAI,UAAU;YACd,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gBAClC,IAAI,UAAU,QAAQ,CAAC,EAAE;gBACzB,IAAI,UAAU,kBAAkB;oBAC5B,WAAW,KAAM,cAAc,IAAI;oBACnC;oBACA,0BAA0B;gBAC9B;YACJ;YACA,IAAI,iBAAiB,GAAG;gBACpB,6DAA6D;gBAC7D,sEAAsE;gBACtE,8CAA8C;gBAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,eAAe,GAAG,IAAK;oBACtD,IAAI,UAAU,QAAQ,CAAC,EAAE;oBACzB,IAAI,UAAU,kBAAkB;wBAC5B;wBACA,mFAAmF;wBACnF,IAAI,AAAC,UAAU,KAAM,wBAAwB;4BACzC,OAAO,CAAC;wBACZ;oBACJ;gBACJ;gBACA,OAAO;YACX;QACJ,QAAS,eAAe,EAAG;QAC3B,OAAO,CAAC;IACZ;IACA,aAAa,aAAa,GAAG,SAAU,OAAO;QAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,mBAAmB,CAAC,MAAM,EAAE,IAAK;YAC9D,IAAI,aAAa,mBAAmB,CAAC,EAAE,KAAK,SAAS;gBACjD,OAAO,aAAa,eAAe,CAAC,MAAM,CAAC;YAC/C;QACJ;QACA,IAAI,YAAY,aAAa,iBAAiB,EAAE;YAC5C,OAAO;QACX;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,aAAa,cAAc,GAAG,SAAU,OAAO;QAC3C,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,IAAI,QAAQ,MAAM,CAAC;YACvB,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;gBAClD,IAAI,OAAO,QAAQ,MAAM,CAAC,IAAI;gBAC9B,IAAI,cAAc;gBAClB,OAAQ;oBACJ,KAAK;wBACD,yBAAyB;wBACzB,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,yCAAyC;wBACzC,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,0CAA0C;wBAC1C,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;wBAClB,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;wBAClB,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;wBAClB,OACK,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;4BACnD,cAAc;wBAClB,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,0CAA0C;wBAC1C,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;wBAClB,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;gBACR;gBACA,WAAW;gBACX,+CAA+C;gBAC/C;YACJ,OACK;gBACD,WAAW;YACf;QACJ;QACA,OAAO;IACX;IACA,aAAa,eAAe,GAAG;IAC/B;;;;KAIC,GACD,aAAa,mBAAmB,GAAG;QAC/B;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO,MAAM,MAAM;KAC7B;IACD,aAAa,iBAAiB,GAAG;IACjC,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/Code93Reader.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\n//import com.google.zxing.ResultMetadataType;\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 93 barcodes.</p>\n *\n * <AUTHOR> Owen\n * @see Code39Reader\n */\nvar Code93Reader = /** @class */ (function (_super) {\n    __extends(Code93Reader, _super);\n    //public Code93Reader() {\n    //  decodeRowResult = new StringBuilder(20);\n    //  counters = new int[6];\n    //}\n    function Code93Reader() {\n        var _this = _super.call(this) || this;\n        _this.decodeRowResult = '';\n        _this.counters = new Int32Array(6);\n        return _this;\n    }\n    Code93Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var start = this.findAsteriskPattern(row);\n        // Read off white space\n        var nextStart = row.getNextSet(start[1]);\n        var end = row.getSize();\n        var theCounters = this.counters;\n        theCounters.fill(0);\n        this.decodeRowResult = '';\n        var decodedChar;\n        var lastStart;\n        do {\n            Code93Reader.recordPattern(row, nextStart, theCounters);\n            var pattern = this.toPattern(theCounters);\n            if (pattern < 0) {\n                throw new NotFoundException();\n            }\n            decodedChar = this.patternToChar(pattern);\n            this.decodeRowResult += decodedChar;\n            lastStart = nextStart;\n            try {\n                for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n                    var counter = theCounters_1_1.value;\n                    nextStart += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Read off white space\n            nextStart = row.getNextSet(nextStart);\n        } while (decodedChar !== '*');\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n        var lastPatternSize = 0;\n        try {\n            for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n                var counter = theCounters_2_1.value;\n                lastPatternSize += counter;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // Should be at least one more black module\n        if (nextStart === end || !row.get(nextStart)) {\n            throw new NotFoundException();\n        }\n        if (this.decodeRowResult.length < 2) {\n            // false positive -- need at least 2 checksum digits\n            throw new NotFoundException();\n        }\n        this.checkChecksums(this.decodeRowResult);\n        // Remove checksum digits\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 2);\n        var resultString = this.decodeExtended(this.decodeRowResult);\n        var left = (start[1] + start[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_93, new Date().getTime());\n    };\n    Code93Reader.prototype.findAsteriskPattern = function (row) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        this.counters.fill(0);\n        var theCounters = this.counters;\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = theCounters.length;\n        var counterPosition = 0;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                theCounters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (this.toPattern(theCounters) === Code93Reader.ASTERISK_ENCODING) {\n                        return new Int32Array([patternStart, i]);\n                    }\n                    patternStart += theCounters[0] + theCounters[1];\n                    theCounters.copyWithin(0, 2, 2 + counterPosition - 1);\n                    theCounters[counterPosition - 1] = 0;\n                    theCounters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                theCounters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException;\n    };\n    Code93Reader.prototype.toPattern = function (counters) {\n        var e_3, _a;\n        var sum = 0;\n        try {\n            for (var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                var counter = counters_1_1.value;\n                sum += counter;\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        var pattern = 0;\n        var max = counters.length;\n        for (var i = 0; i < max; i++) {\n            var scaled = Math.round(counters[i] * 9.0 / sum);\n            if (scaled < 1 || scaled > 4) {\n                return -1;\n            }\n            if ((i & 0x01) === 0) {\n                for (var j = 0; j < scaled; j++) {\n                    pattern = (pattern << 1) | 0x01;\n                }\n            }\n            else {\n                pattern <<= scaled;\n            }\n        }\n        return pattern;\n    };\n    Code93Reader.prototype.patternToChar = function (pattern) {\n        for (var i = 0; i < Code93Reader.CHARACTER_ENCODINGS.length; i++) {\n            if (Code93Reader.CHARACTER_ENCODINGS[i] === pattern) {\n                return Code93Reader.ALPHABET_STRING.charAt(i);\n            }\n        }\n        throw new NotFoundException();\n    };\n    Code93Reader.prototype.decodeExtended = function (encoded) {\n        var length = encoded.length;\n        var decoded = '';\n        for (var i = 0; i < length; i++) {\n            var c = encoded.charAt(i);\n            if (c >= 'a' && c <= 'd') {\n                if (i >= length - 1) {\n                    throw new FormatException();\n                }\n                var next = encoded.charAt(i + 1);\n                var decodedChar = '\\0';\n                switch (c) {\n                    case 'd':\n                        // +A to +Z map to a to z\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'a':\n                        // $A to $Z map to control codes SH to SB\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'b':\n                        if (next >= 'A' && next <= 'E') {\n                            // %A to %E map to control codes ESC to USep\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n                        }\n                        else if (next >= 'F' && next <= 'J') {\n                            // %F to %J map to ; < = > ?\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n                        }\n                        else if (next >= 'K' && next <= 'O') {\n                            // %K to %O map to [ \\ ] ^ _\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n                        }\n                        else if (next >= 'P' && next <= 'T') {\n                            // %P to %T map to { | } ~ DEL\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n                        }\n                        else if (next === 'U') {\n                            // %U map to NUL\n                            decodedChar = '\\0';\n                        }\n                        else if (next === 'V') {\n                            // %V map to @\n                            decodedChar = '@';\n                        }\n                        else if (next === 'W') {\n                            // %W map to `\n                            decodedChar = '`';\n                        }\n                        else if (next >= 'X' && next <= 'Z') {\n                            // %X to %Z all map to DEL (127)\n                            decodedChar = String.fromCharCode(127);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'c':\n                        // /A to /O map to ! to , and /Z maps to :\n                        if (next >= 'A' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n                        }\n                        else if (next === 'Z') {\n                            decodedChar = ':';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n                decoded += decodedChar;\n                // bump up i again since we read two characters\n                i++;\n            }\n            else {\n                decoded += c;\n            }\n        }\n        return decoded;\n    };\n    Code93Reader.prototype.checkChecksums = function (result) {\n        var length = result.length;\n        this.checkOneChecksum(result, length - 2, 20);\n        this.checkOneChecksum(result, length - 1, 15);\n    };\n    Code93Reader.prototype.checkOneChecksum = function (result, checkPosition, weightMax) {\n        var weight = 1;\n        var total = 0;\n        for (var i = checkPosition - 1; i >= 0; i--) {\n            total += weight * Code93Reader.ALPHABET_STRING.indexOf(result.charAt(i));\n            if (++weight > weightMax) {\n                weight = 1;\n            }\n        }\n        if (result.charAt(checkPosition) !== Code93Reader.ALPHABET_STRING[total % 47]) {\n            throw new ChecksumException;\n        }\n    };\n    // Note that 'abcd' are dummy characters in place of control characters.\n    Code93Reader.ALPHABET_STRING = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*\";\n    /**\n     * These represent the encodings of characters, as patterns of wide and narrow bars.\n     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow.\n     */\n    Code93Reader.CHARACTER_ENCODINGS = [\n        0x114, 0x148, 0x144, 0x142, 0x128, 0x124, 0x122, 0x150, 0x112, 0x10A,\n        0x1A8, 0x1A4, 0x1A2, 0x194, 0x192, 0x18A, 0x168, 0x164, 0x162, 0x134,\n        0x11A, 0x158, 0x14C, 0x146, 0x12C, 0x116, 0x1B4, 0x1B2, 0x1AC, 0x1A6,\n        0x196, 0x19A, 0x16C, 0x166, 0x136, 0x13A,\n        0x12E, 0x1D4, 0x1D2, 0x1CA, 0x16E, 0x176, 0x1AE,\n        0x126, 0x1DA, 0x1D6, 0x132, 0x15E,\n    ];\n    Code93Reader.ASTERISK_ENCODING = Code93Reader.CHARACTER_ENCODINGS[47];\n    return Code93Reader;\n}(OneDReader));\nexport default Code93Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AAhCA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;AAUA;;;;;CAKC,GACD,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB,yBAAyB;IACzB,4CAA4C;IAC5C,0BAA0B;IAC1B,GAAG;IACH,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,eAAe,GAAG;QACxB,MAAM,QAAQ,GAAG,IAAI,WAAW;QAChC,OAAO;IACX;IACA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC9D,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACrC,uBAAuB;QACvB,IAAI,YAAY,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACvC,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,cAAc,IAAI,CAAC,QAAQ;QAC/B,YAAY,IAAI,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI;QACJ,IAAI;QACJ,GAAG;YACC,aAAa,aAAa,CAAC,KAAK,WAAW;YAC3C,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC;YAC7B,IAAI,UAAU,GAAG;gBACb,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,cAAc,IAAI,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,eAAe,IAAI;YACxB,YAAY;YACZ,IAAI;gBACA,IAAK,IAAI,gBAAgB,CAAC,MAAM,KAAK,GAAG,SAAS,YAAY,GAAG,kBAAkB,cAAc,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,kBAAkB,cAAc,IAAI,GAAI;oBACnK,IAAI,UAAU,gBAAgB,KAAK;oBACnC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC;gBACzF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,uBAAuB;YACvB,YAAY,IAAI,UAAU,CAAC;QAC/B,QAAS,gBAAgB,IAAK;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,kBAAkB;QAC7G,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAK,IAAI,gBAAgB,SAAS,cAAc,kBAAkB,cAAc,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,kBAAkB,cAAc,IAAI,GAAI;gBACnJ,IAAI,UAAU,gBAAgB,KAAK;gBACnC,mBAAmB;YACvB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC;YACzF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,2CAA2C;QAC3C,IAAI,cAAc,OAAO,CAAC,IAAI,GAAG,CAAC,YAAY;YAC1C,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;YACjC,oDAAoD;YACpD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;QACxC,yBAAyB;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG;QACvF,IAAI,eAAe,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;QAC3D,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,QAAQ,YAAY,kBAAkB;QAC1C,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAAM,GAAG;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO;SAAW,EAAE,qKAAA,CAAA,UAAa,CAAC,OAAO,EAAE,IAAI,OAAO,OAAO;IAC7J;IACA,aAAa,SAAS,CAAC,mBAAmB,GAAG,SAAU,GAAG;QACtD,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,YAAY,IAAI,UAAU,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,cAAc,IAAI,CAAC,QAAQ;QAC/B,IAAI,eAAe;QACnB,IAAI,UAAU;QACd,IAAI,gBAAgB,YAAY,MAAM;QACtC,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,WAAW,CAAC,gBAAgB;YAChC,OACK;gBACD,IAAI,oBAAoB,gBAAgB,GAAG;oBACvC,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,aAAa,iBAAiB,EAAE;wBAChE,OAAO,IAAI,WAAW;4BAAC;4BAAc;yBAAE;oBAC3C;oBACA,gBAAgB,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;oBAC/C,YAAY,UAAU,CAAC,GAAG,GAAG,IAAI,kBAAkB;oBACnD,WAAW,CAAC,kBAAkB,EAAE,GAAG;oBACnC,WAAW,CAAC,gBAAgB,GAAG;oBAC/B;gBACJ,OACK;oBACD;gBACJ;gBACA,WAAW,CAAC,gBAAgB,GAAG;gBAC/B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,QAAQ;QACjD,IAAI,KAAK;QACT,IAAI,MAAM;QACV,IAAI;YACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;gBAC9H,IAAI,UAAU,aAAa,KAAK;gBAChC,OAAO;YACX;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;YAChF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,UAAU;QACd,IAAI,MAAM,SAAS,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM;YAC5C,IAAI,SAAS,KAAK,SAAS,GAAG;gBAC1B,OAAO,CAAC;YACZ;YACA,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;gBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC7B,UAAU,AAAC,WAAW,IAAK;gBAC/B;YACJ,OACK;gBACD,YAAY;YAChB;QACJ;QACA,OAAO;IACX;IACA,aAAa,SAAS,CAAC,aAAa,GAAG,SAAU,OAAO;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,mBAAmB,CAAC,MAAM,EAAE,IAAK;YAC9D,IAAI,aAAa,mBAAmB,CAAC,EAAE,KAAK,SAAS;gBACjD,OAAO,aAAa,eAAe,CAAC,MAAM,CAAC;YAC/C;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,aAAa,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO;QACrD,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,IAAI,QAAQ,MAAM,CAAC;YACvB,IAAI,KAAK,OAAO,KAAK,KAAK;gBACtB,IAAI,KAAK,SAAS,GAAG;oBACjB,MAAM,IAAI,uKAAA,CAAA,UAAe;gBAC7B;gBACA,IAAI,OAAO,QAAQ,MAAM,CAAC,IAAI;gBAC9B,IAAI,cAAc;gBAClB,OAAQ;oBACJ,KAAK;wBACD,yBAAyB;wBACzB,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,yCAAyC;wBACzC,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,4CAA4C;4BAC5C,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,4BAA4B;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,4BAA4B;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,8BAA8B;4BAC9B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,SAAS,KAAK;4BACnB,gBAAgB;4BAChB,cAAc;wBAClB,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;4BACd,cAAc;wBAClB,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;4BACd,cAAc;wBAClB,OACK,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BACjC,gCAAgC;4BAChC,cAAc,OAAO,YAAY,CAAC;wBACtC,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,0CAA0C;wBAC1C,IAAI,QAAQ,OAAO,QAAQ,KAAK;4BAC5B,cAAc,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;wBAC3D,OACK,IAAI,SAAS,KAAK;4BACnB,cAAc;wBAClB,OACK;4BACD,MAAM,IAAI,uKAAA,CAAA,UAAe;wBAC7B;wBACA;gBACR;gBACA,WAAW;gBACX,+CAA+C;gBAC/C;YACJ,OACK;gBACD,WAAW;YACf;QACJ;QACA,OAAO;IACX;IACA,aAAa,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;QACpD,IAAI,SAAS,OAAO,MAAM;QAC1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS,GAAG;QAC1C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS,GAAG;IAC9C;IACA,aAAa,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM,EAAE,aAAa,EAAE,SAAS;QAChF,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,gBAAgB,GAAG,KAAK,GAAG,IAAK;YACzC,SAAS,SAAS,aAAa,eAAe,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC;YACrE,IAAI,EAAE,SAAS,WAAW;gBACtB,SAAS;YACb;QACJ;QACA,IAAI,OAAO,MAAM,CAAC,mBAAmB,aAAa,eAAe,CAAC,QAAQ,GAAG,EAAE;YAC3E,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,wEAAwE;IACxE,aAAa,eAAe,GAAG;IAC/B;;;KAGC,GACD,aAAa,mBAAmB,GAAG;QAC/B;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC/D;QAAO;QAAO;QAAO;QAAO;QAAO;QACnC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAC1C;QAAO;QAAO;QAAO;QAAO;KAC/B;IACD,aAAa,iBAAiB,GAAG,aAAa,mBAAmB,CAAC,GAAG;IACrE,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/ITFReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport StringBuilder from '../util/StringBuilder';\nimport System from '../util/System';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes ITF barcodes.</p>\n *\n * <AUTHOR>\n */\nvar ITFReader = /** @class */ (function (_super) {\n    __extends(ITFReader, _super);\n    function ITFReader() {\n        // private static W = 3; // Pixel width of a 3x wide line\n        // private static w = 2; // Pixel width of a 2x wide line\n        // private static N = 1; // Pixed width of a narrow line\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        // Stores the actual narrow line width of the image being decoded.\n        _this.narrowLineWidth = -1;\n        return _this;\n    }\n    // See ITFWriter.PATTERNS\n    /*\n  \n    /!**\n     * Patterns of Wide / Narrow lines to indicate each digit\n     *!/\n    */\n    ITFReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a;\n        // Find out where the Middle section (payload) starts & ends\n        var startRange = this.decodeStart(row);\n        var endRange = this.decodeEnd(row);\n        var result = new StringBuilder();\n        ITFReader.decodeMiddle(row, startRange[1], endRange[0], result);\n        var resultString = result.toString();\n        var allowedLengths = null;\n        if (hints != null) {\n            allowedLengths = hints.get(DecodeHintType.ALLOWED_LENGTHS);\n        }\n        if (allowedLengths == null) {\n            allowedLengths = ITFReader.DEFAULT_ALLOWED_LENGTHS;\n        }\n        // To avoid false positives with 2D barcodes (and other patterns), make\n        // an assumption that the decoded string must be a 'standard' length if it's short\n        var length = resultString.length;\n        var lengthOK = false;\n        var maxAllowedLength = 0;\n        try {\n            for (var allowedLengths_1 = __values(allowedLengths), allowedLengths_1_1 = allowedLengths_1.next(); !allowedLengths_1_1.done; allowedLengths_1_1 = allowedLengths_1.next()) {\n                var value = allowedLengths_1_1.value;\n                if (length === value) {\n                    lengthOK = true;\n                    break;\n                }\n                if (value > maxAllowedLength) {\n                    maxAllowedLength = value;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (allowedLengths_1_1 && !allowedLengths_1_1.done && (_a = allowedLengths_1.return)) _a.call(allowedLengths_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (!lengthOK && length > maxAllowedLength) {\n            lengthOK = true;\n        }\n        if (!lengthOK) {\n            throw new FormatException();\n        }\n        var points = [new ResultPoint(startRange[1], rowNumber), new ResultPoint(endRange[0], rowNumber)];\n        var resultReturn = new Result(resultString, null, // no natural byte representation for these barcodes\n        0, points, BarcodeFormat.ITF, new Date().getTime());\n        return resultReturn;\n    };\n    /*\n    /!**\n     * @param row          row of black/white values to search\n     * @param payloadStart offset of start pattern\n     * @param resultString {@link StringBuilder} to append decoded chars to\n     * @throws NotFoundException if decoding could not complete successfully\n     *!/*/\n    ITFReader.decodeMiddle = function (row, payloadStart, payloadEnd, resultString) {\n        // Digits are interleaved in pairs - 5 black lines for one digit, and the\n        // 5\n        // interleaved white lines for the second digit.\n        // Therefore, need to scan 10 lines and then\n        // split these into two arrays\n        var counterDigitPair = new Int32Array(10); // 10\n        var counterBlack = new Int32Array(5); // 5\n        var counterWhite = new Int32Array(5); // 5\n        counterDigitPair.fill(0);\n        counterBlack.fill(0);\n        counterWhite.fill(0);\n        while (payloadStart < payloadEnd) {\n            // Get 10 runs of black/white.\n            OneDReader.recordPattern(row, payloadStart, counterDigitPair);\n            // Split them into each array\n            for (var k = 0; k < 5; k++) {\n                var twoK = 2 * k;\n                counterBlack[k] = counterDigitPair[twoK];\n                counterWhite[k] = counterDigitPair[twoK + 1];\n            }\n            var bestMatch = ITFReader.decodeDigit(counterBlack);\n            resultString.append(bestMatch.toString());\n            bestMatch = this.decodeDigit(counterWhite);\n            resultString.append(bestMatch.toString());\n            counterDigitPair.forEach(function (counterDigit) {\n                payloadStart += counterDigit;\n            });\n        }\n    };\n    /*/!**\n     * Identify where the start of the middle / payload section starts.\n     *\n     * @param row row of black/white values to search\n     * @return Array, containing index of start of 'start block' and end of\n     *         'start block'\n     *!/*/\n    ITFReader.prototype.decodeStart = function (row) {\n        var endStart = ITFReader.skipWhiteSpace(row);\n        var startPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.START_PATTERN);\n        // Determine the width of a narrow line in pixels. We can do this by\n        // getting the width of the start pattern and dividing by 4 because its\n        // made up of 4 narrow lines.\n        this.narrowLineWidth = (startPattern[1] - startPattern[0]) / 4;\n        this.validateQuietZone(row, startPattern[0]);\n        return startPattern;\n    };\n    /*/!**\n     * The start & end patterns must be pre/post fixed by a quiet zone. This\n     * zone must be at least 10 times the width of a narrow line.  Scan back until\n     * we either get to the start of the barcode or match the necessary number of\n     * quiet zone pixels.\n     *\n     * Note: Its assumed the row is reversed when using this method to find\n     * quiet zone after the end pattern.\n     *\n     * ref: http://www.barcode-1.net/i25code.html\n     *\n     * @param row bit array representing the scanned barcode.\n     * @param startPattern index into row of the start or end pattern.\n     * @throws NotFoundException if the quiet zone cannot be found\n     *!/*/\n    ITFReader.prototype.validateQuietZone = function (row, startPattern) {\n        var quietCount = this.narrowLineWidth * 10; // expect to find this many pixels of quiet zone\n        // if there are not so many pixel at all let's try as many as possible\n        quietCount = quietCount < startPattern ? quietCount : startPattern;\n        for (var i = startPattern - 1; quietCount > 0 && i >= 0; i--) {\n            if (row.get(i)) {\n                break;\n            }\n            quietCount--;\n        }\n        if (quietCount !== 0) {\n            // Unable to find the necessary number of quiet zone pixels.\n            throw new NotFoundException();\n        }\n    };\n    /*\n    /!**\n     * Skip all whitespace until we get to the first black line.\n     *\n     * @param row row of black/white values to search\n     * @return index of the first black line.\n     * @throws NotFoundException Throws exception if no black lines are found in the row\n     *!/*/\n    ITFReader.skipWhiteSpace = function (row) {\n        var width = row.getSize();\n        var endStart = row.getNextSet(0);\n        if (endStart === width) {\n            throw new NotFoundException();\n        }\n        return endStart;\n    };\n    /*/!**\n     * Identify where the end of the middle / payload section ends.\n     *\n     * @param row row of black/white values to search\n     * @return Array, containing index of start of 'end block' and end of 'end\n     *         block'\n     *!/*/\n    ITFReader.prototype.decodeEnd = function (row) {\n        // For convenience, reverse the row and then\n        // search from 'the start' for the end block\n        row.reverse();\n        try {\n            var endStart = ITFReader.skipWhiteSpace(row);\n            var endPattern = void 0;\n            try {\n                endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[0]);\n            }\n            catch (error) {\n                if (error instanceof NotFoundException) {\n                    endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[1]);\n                }\n            }\n            // The start & end patterns must be pre/post fixed by a quiet zone. This\n            // zone must be at least 10 times the width of a narrow line.\n            // ref: http://www.barcode-1.net/i25code.html\n            this.validateQuietZone(row, endPattern[0]);\n            // Now recalculate the indices of where the 'endblock' starts & stops to\n            // accommodate\n            // the reversed nature of the search\n            var temp = endPattern[0];\n            endPattern[0] = row.getSize() - endPattern[1];\n            endPattern[1] = row.getSize() - temp;\n            return endPattern;\n        }\n        finally {\n            // Put the row back the right way.\n            row.reverse();\n        }\n    };\n    /*\n    /!**\n     * @param row       row of black/white values to search\n     * @param rowOffset position to start search\n     * @param pattern   pattern of counts of number of black and white pixels that are\n     *                  being searched for as a pattern\n     * @return start/end horizontal offset of guard pattern, as an array of two\n     *         ints\n     * @throws NotFoundException if pattern is not found\n     *!/*/\n    ITFReader.findGuardPattern = function (row, rowOffset, pattern) {\n        var patternLength = pattern.length;\n        var counters = new Int32Array(patternLength);\n        var width = row.getSize();\n        var isWhite = false;\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        counters.fill(0);\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE) < ITFReader.MAX_AVG_VARIANCE) {\n                        return [patternStart, x];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /*/!**\n     * Attempts to decode a sequence of ITF black/white lines into single\n     * digit.\n     *\n     * @param counters the counts of runs of observed black/white/black/... values\n     * @return The decoded digit\n     * @throws NotFoundException if digit cannot be decoded\n     *!/*/\n    ITFReader.decodeDigit = function (counters) {\n        var bestVariance = ITFReader.MAX_AVG_VARIANCE; // worst variance we'll accept\n        var bestMatch = -1;\n        var max = ITFReader.PATTERNS.length;\n        for (var i = 0; i < max; i++) {\n            var pattern = ITFReader.PATTERNS[i];\n            var variance = OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = i;\n            }\n            else if (variance === bestVariance) {\n                // if we find a second 'best match' with the same variance, we can not reliably report to have a suitable match\n                bestMatch = -1;\n            }\n        }\n        if (bestMatch >= 0) {\n            return bestMatch % 10;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    ITFReader.PATTERNS = [\n        Int32Array.from([1, 1, 2, 2, 1]),\n        Int32Array.from([2, 1, 1, 1, 2]),\n        Int32Array.from([1, 2, 1, 1, 2]),\n        Int32Array.from([2, 2, 1, 1, 1]),\n        Int32Array.from([1, 1, 2, 1, 2]),\n        Int32Array.from([2, 1, 2, 1, 1]),\n        Int32Array.from([1, 2, 2, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 2]),\n        Int32Array.from([2, 1, 1, 2, 1]),\n        Int32Array.from([1, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 3, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 3]),\n        Int32Array.from([1, 3, 1, 1, 3]),\n        Int32Array.from([3, 3, 1, 1, 1]),\n        Int32Array.from([1, 1, 3, 1, 3]),\n        Int32Array.from([3, 1, 3, 1, 1]),\n        Int32Array.from([1, 3, 3, 1, 1]),\n        Int32Array.from([1, 1, 1, 3, 3]),\n        Int32Array.from([3, 1, 1, 3, 1]),\n        Int32Array.from([1, 3, 1, 3, 1]) // 9\n    ];\n    ITFReader.MAX_AVG_VARIANCE = 0.38;\n    ITFReader.MAX_INDIVIDUAL_VARIANCE = 0.5;\n    /* /!** Valid ITF lengths. Anything longer than the largest value is also allowed. *!/*/\n    ITFReader.DEFAULT_ALLOWED_LENGTHS = [6, 8, 10, 12, 14];\n    /*/!**\n     * Start/end guard pattern.\n     *\n     * Note: The end pattern is reversed because the row is reversed before\n     * searching for the END_PATTERN\n     *!/*/\n    ITFReader.START_PATTERN = Int32Array.from([1, 1, 1, 1]);\n    ITFReader.END_PATTERN_REVERSED = [\n        Int32Array.from([1, 1, 2]),\n        Int32Array.from([1, 1, 3]) // 3x\n    ];\n    return ITFReader;\n}(OneDReader));\nexport default ITFReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjCA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;AAWA;;;;CAIC,GACD,IAAI,YAA2B,SAAU,MAAM;IAC3C,UAAU,WAAW;IACrB,SAAS;QACL,yDAAyD;QACzD,yDAAyD;QACzD,wDAAwD;QACxD,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,kEAAkE;QAClE,MAAM,eAAe,GAAG,CAAC;QACzB,OAAO;IACX;IACA,yBAAyB;IACzB;;;;;IAKA,GACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC3D,IAAI,KAAK;QACT,4DAA4D;QAC5D,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;QAC9B,UAAU,YAAY,CAAC,KAAK,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;QACxD,IAAI,eAAe,OAAO,QAAQ;QAClC,IAAI,iBAAiB;QACrB,IAAI,SAAS,MAAM;YACf,iBAAiB,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,eAAe;QAC7D;QACA,IAAI,kBAAkB,MAAM;YACxB,iBAAiB,UAAU,uBAAuB;QACtD;QACA,uEAAuE;QACvE,kFAAkF;QAClF,IAAI,SAAS,aAAa,MAAM;QAChC,IAAI,WAAW;QACf,IAAI,mBAAmB;QACvB,IAAI;YACA,IAAK,IAAI,mBAAmB,SAAS,iBAAiB,qBAAqB,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,EAAE,qBAAqB,iBAAiB,IAAI,GAAI;gBACxK,IAAI,QAAQ,mBAAmB,KAAK;gBACpC,IAAI,WAAW,OAAO;oBAClB,WAAW;oBACX;gBACJ;gBACA,IAAI,QAAQ,kBAAkB;oBAC1B,mBAAmB;gBACvB;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,sBAAsB,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,iBAAiB,MAAM,GAAG,GAAG,IAAI,CAAC;YAClG,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,CAAC,YAAY,SAAS,kBAAkB;YACxC,WAAW;QACf;QACA,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI,SAAS;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU,CAAC,EAAE,EAAE;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,EAAE,EAAE;SAAW;QACjG,IAAI,eAAe,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAC5C,GAAG,QAAQ,qKAAA,CAAA,UAAa,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO;QAChD,OAAO;IACX;IACA;;;;;;QAMI,GACJ,UAAU,YAAY,GAAG,SAAU,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY;QAC1E,yEAAyE;QACzE,IAAI;QACJ,gDAAgD;QAChD,4CAA4C;QAC5C,8BAA8B;QAC9B,IAAI,mBAAmB,IAAI,WAAW,KAAK,KAAK;QAChD,IAAI,eAAe,IAAI,WAAW,IAAI,IAAI;QAC1C,IAAI,eAAe,IAAI,WAAW,IAAI,IAAI;QAC1C,iBAAiB,IAAI,CAAC;QACtB,aAAa,IAAI,CAAC;QAClB,aAAa,IAAI,CAAC;QAClB,MAAO,eAAe,WAAY;YAC9B,8BAA8B;YAC9B,0KAAA,CAAA,UAAU,CAAC,aAAa,CAAC,KAAK,cAAc;YAC5C,6BAA6B;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,OAAO,IAAI;gBACf,YAAY,CAAC,EAAE,GAAG,gBAAgB,CAAC,KAAK;gBACxC,YAAY,CAAC,EAAE,GAAG,gBAAgB,CAAC,OAAO,EAAE;YAChD;YACA,IAAI,YAAY,UAAU,WAAW,CAAC;YACtC,aAAa,MAAM,CAAC,UAAU,QAAQ;YACtC,YAAY,IAAI,CAAC,WAAW,CAAC;YAC7B,aAAa,MAAM,CAAC,UAAU,QAAQ;YACtC,iBAAiB,OAAO,CAAC,SAAU,YAAY;gBAC3C,gBAAgB;YACpB;QACJ;IACJ;IACA;;;;;;QAMI,GACJ,UAAU,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;QAC3C,IAAI,WAAW,UAAU,cAAc,CAAC;QACxC,IAAI,eAAe,UAAU,gBAAgB,CAAC,KAAK,UAAU,UAAU,aAAa;QACpF,oEAAoE;QACpE,uEAAuE;QACvE,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,IAAI;QAC7D,IAAI,CAAC,iBAAiB,CAAC,KAAK,YAAY,CAAC,EAAE;QAC3C,OAAO;IACX;IACA;;;;;;;;;;;;;;QAcI,GACJ,UAAU,SAAS,CAAC,iBAAiB,GAAG,SAAU,GAAG,EAAE,YAAY;QAC/D,IAAI,aAAa,IAAI,CAAC,eAAe,GAAG,IAAI,gDAAgD;QAC5F,sEAAsE;QACtE,aAAa,aAAa,eAAe,aAAa;QACtD,IAAK,IAAI,IAAI,eAAe,GAAG,aAAa,KAAK,KAAK,GAAG,IAAK;YAC1D,IAAI,IAAI,GAAG,CAAC,IAAI;gBACZ;YACJ;YACA;QACJ;QACA,IAAI,eAAe,GAAG;YAClB,4DAA4D;YAC5D,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA;;;;;;;QAOI,GACJ,UAAU,cAAc,GAAG,SAAU,GAAG;QACpC,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,WAAW,IAAI,UAAU,CAAC;QAC9B,IAAI,aAAa,OAAO;YACpB,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,OAAO;IACX;IACA;;;;;;QAMI,GACJ,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,IAAI,OAAO;QACX,IAAI;YACA,IAAI,WAAW,UAAU,cAAc,CAAC;YACxC,IAAI,aAAa,KAAK;YACtB,IAAI;gBACA,aAAa,UAAU,gBAAgB,CAAC,KAAK,UAAU,UAAU,oBAAoB,CAAC,EAAE;YAC5F,EACA,OAAO,OAAO;gBACV,IAAI,iBAAiB,yKAAA,CAAA,UAAiB,EAAE;oBACpC,aAAa,UAAU,gBAAgB,CAAC,KAAK,UAAU,UAAU,oBAAoB,CAAC,EAAE;gBAC5F;YACJ;YACA,wEAAwE;YACxE,6DAA6D;YAC7D,6CAA6C;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,UAAU,CAAC,EAAE;YACzC,wEAAwE;YACxE,cAAc;YACd,oCAAoC;YACpC,IAAI,OAAO,UAAU,CAAC,EAAE;YACxB,UAAU,CAAC,EAAE,GAAG,IAAI,OAAO,KAAK,UAAU,CAAC,EAAE;YAC7C,UAAU,CAAC,EAAE,GAAG,IAAI,OAAO,KAAK;YAChC,OAAO;QACX,SACQ;YACJ,kCAAkC;YAClC,IAAI,OAAO;QACf;IACJ;IACA;;;;;;;;;QASI,GACJ,UAAU,gBAAgB,GAAG,SAAU,GAAG,EAAE,SAAS,EAAE,OAAO;QAC1D,IAAI,gBAAgB,QAAQ,MAAM;QAClC,IAAI,WAAW,IAAI,WAAW;QAC9B,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,UAAU;QACd,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,SAAS,IAAI,CAAC;QACd,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,gBAAgB,GAAG;oBACvC,IAAI,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,SAAS,UAAU,uBAAuB,IAAI,UAAU,gBAAgB,EAAE;wBACpH,OAAO;4BAAC;4BAAc;yBAAE;oBAC5B;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,kBAAkB;oBAC7D,QAAQ,CAAC,kBAAkB,EAAE,GAAG;oBAChC,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA;;;;;;;QAOI,GACJ,UAAU,WAAW,GAAG,SAAU,QAAQ;QACtC,IAAI,eAAe,UAAU,gBAAgB,EAAE,8BAA8B;QAC7E,IAAI,YAAY,CAAC;QACjB,IAAI,MAAM,UAAU,QAAQ,CAAC,MAAM;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,UAAU,UAAU,QAAQ,CAAC,EAAE;YACnC,IAAI,WAAW,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,SAAS,UAAU,uBAAuB;YACnG,IAAI,WAAW,cAAc;gBACzB,eAAe;gBACf,YAAY;YAChB,OACK,IAAI,aAAa,cAAc;gBAChC,+GAA+G;gBAC/G,YAAY,CAAC;YACjB;QACJ;QACA,IAAI,aAAa,GAAG;YAChB,OAAO,YAAY;QACvB,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,UAAU,QAAQ,GAAG;QACjB,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,EAAE,IAAI;KACxC;IACD,UAAU,gBAAgB,GAAG;IAC7B,UAAU,uBAAuB,GAAG;IACpC,sFAAsF,GACtF,UAAU,uBAAuB,GAAG;QAAC;QAAG;QAAG;QAAI;QAAI;KAAG;IACtD;;;;;QAKI,GACJ,UAAU,aAAa,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;KAAE;IACtD,UAAU,oBAAoB,GAAG;QAC7B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;SAAE;QACzB,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;SAAE,EAAE,KAAK;KACnC;IACD,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/AbstractUPCEANReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport OneDReader from './OneDReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar AbstractUPCEANReader = /** @class */ (function (_super) {\n    __extends(AbstractUPCEANReader, _super);\n    function AbstractUPCEANReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.decodeRowStringBuffer = '';\n        return _this;\n    }\n    // private final UPCEANExtensionSupport extensionReader;\n    // private final EANManufacturerOrgSupport eanManSupport;\n    /*\n    protected UPCEANReader() {\n        decodeRowStringBuffer = new StringBuilder(20);\n        extensionReader = new UPCEANExtensionSupport();\n        eanManSupport = new EANManufacturerOrgSupport();\n    }\n    */\n    AbstractUPCEANReader.findStartGuardPattern = function (row) {\n        var foundStart = false;\n        var startRange;\n        var nextStart = 0;\n        var counters = Int32Array.from([0, 0, 0]);\n        while (!foundStart) {\n            counters = Int32Array.from([0, 0, 0]);\n            startRange = AbstractUPCEANReader.findGuardPattern(row, nextStart, false, this.START_END_PATTERN, counters);\n            var start = startRange[0];\n            nextStart = startRange[1];\n            var quietStart = start - (nextStart - start);\n            if (quietStart >= 0) {\n                foundStart = row.isRange(quietStart, start, false);\n            }\n        }\n        return startRange;\n    };\n    AbstractUPCEANReader.checkChecksum = function (s) {\n        return AbstractUPCEANReader.checkStandardUPCEANChecksum(s);\n    };\n    AbstractUPCEANReader.checkStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        if (length === 0)\n            return false;\n        var check = parseInt(s.charAt(length - 1), 10);\n        return AbstractUPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n    };\n    AbstractUPCEANReader.getStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        sum *= 3;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        return (1000 - sum) % 10;\n    };\n    AbstractUPCEANReader.decodeEnd = function (row, endStart) {\n        return AbstractUPCEANReader.findGuardPattern(row, endStart, false, AbstractUPCEANReader.START_END_PATTERN, new Int32Array(AbstractUPCEANReader.START_END_PATTERN.length).fill(0));\n    };\n    /**\n     * @throws NotFoundException\n     */\n    AbstractUPCEANReader.findGuardPatternWithoutCounters = function (row, rowOffset, whiteFirst, pattern) {\n        return this.findGuardPattern(row, rowOffset, whiteFirst, pattern, new Int32Array(pattern.length));\n    };\n    /**\n     * @param row row of black/white values to search\n     * @param rowOffset position to start search\n     * @param whiteFirst if true, indicates that the pattern specifies white/black/white/...\n     * pixel counts, otherwise, it is interpreted as black/white/black/...\n     * @param pattern pattern of counts of number of black and white pixels that are being\n     * searched for as a pattern\n     * @param counters array of counters, as long as pattern, to re-use\n     * @return start/end horizontal offset of guard pattern, as an array of two ints\n     * @throws NotFoundException if pattern is not found\n     */\n    AbstractUPCEANReader.findGuardPattern = function (row, rowOffset, whiteFirst, pattern, counters) {\n        var width = row.getSize();\n        rowOffset = whiteFirst ? row.getNextUnset(rowOffset) : row.getNextSet(rowOffset);\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        var patternLength = pattern.length;\n        var isWhite = whiteFirst;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE) < AbstractUPCEANReader.MAX_AVG_VARIANCE) {\n                        return Int32Array.from([patternStart, x]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    var slice = counters.slice(2, counters.length);\n                    for (var i = 0; i < counterPosition - 1; i++) {\n                        counters[i] = slice[i];\n                    }\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    AbstractUPCEANReader.decodeDigit = function (row, counters, rowOffset, patterns) {\n        this.recordPattern(row, rowOffset, counters);\n        var bestVariance = this.MAX_AVG_VARIANCE;\n        var bestMatch = -1;\n        var max = patterns.length;\n        for (var i = 0; i < max; i++) {\n            var pattern = patterns[i];\n            var variance = OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = i;\n            }\n        }\n        if (bestMatch >= 0) {\n            return bestMatch;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    // These two values are critical for determining how permissive the decoding will be.\n    // We've arrived at these values through a lot of trial and error. Setting them any higher\n    // lets false positives creep in quickly.\n    AbstractUPCEANReader.MAX_AVG_VARIANCE = 0.48;\n    AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n    /**\n     * Start/end guard pattern.\n     */\n    AbstractUPCEANReader.START_END_PATTERN = Int32Array.from([1, 1, 1]);\n    /**\n     * Pattern marking the middle of a UPC/EAN pattern, separating the two halves.\n     */\n    AbstractUPCEANReader.MIDDLE_PATTERN = Int32Array.from([1, 1, 1, 1, 1]);\n    /**\n     * end guard pattern.\n     */\n    AbstractUPCEANReader.END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n    /**\n     * \"Odd\", or \"L\" patterns used to encode UPC/EAN digits.\n     */\n    AbstractUPCEANReader.L_PATTERNS = [\n        Int32Array.from([3, 2, 1, 1]),\n        Int32Array.from([2, 2, 2, 1]),\n        Int32Array.from([2, 1, 2, 2]),\n        Int32Array.from([1, 4, 1, 1]),\n        Int32Array.from([1, 1, 3, 2]),\n        Int32Array.from([1, 2, 3, 1]),\n        Int32Array.from([1, 1, 1, 4]),\n        Int32Array.from([1, 3, 1, 2]),\n        Int32Array.from([1, 2, 1, 3]),\n        Int32Array.from([3, 1, 1, 2]),\n    ];\n    return AbstractUPCEANReader;\n}(OneDReader));\nexport default AbstractUPCEANReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA;;;;;;;CAOC,GACD,IAAI,uBAAsC,SAAU,MAAM;IACtD,UAAU,sBAAsB;IAChC,SAAS;QACL,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,qBAAqB,GAAG;QAC9B,OAAO;IACX;IACA,wDAAwD;IACxD,yDAAyD;IACzD;;;;;;IAMA,GACA,qBAAqB,qBAAqB,GAAG,SAAU,GAAG;QACtD,IAAI,aAAa;QACjB,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,WAAW,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;SAAE;QACxC,MAAO,CAAC,WAAY;YAChB,WAAW,WAAW,IAAI,CAAC;gBAAC;gBAAG;gBAAG;aAAE;YACpC,aAAa,qBAAqB,gBAAgB,CAAC,KAAK,WAAW,OAAO,IAAI,CAAC,iBAAiB,EAAE;YAClG,IAAI,QAAQ,UAAU,CAAC,EAAE;YACzB,YAAY,UAAU,CAAC,EAAE;YACzB,IAAI,aAAa,QAAQ,CAAC,YAAY,KAAK;YAC3C,IAAI,cAAc,GAAG;gBACjB,aAAa,IAAI,OAAO,CAAC,YAAY,OAAO;YAChD;QACJ;QACA,OAAO;IACX;IACA,qBAAqB,aAAa,GAAG,SAAU,CAAC;QAC5C,OAAO,qBAAqB,2BAA2B,CAAC;IAC5D;IACA,qBAAqB,2BAA2B,GAAG,SAAU,CAAC;QAC1D,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,WAAW,GACX,OAAO;QACX,IAAI,QAAQ,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI;QAC3C,OAAO,qBAAqB,yBAAyB,CAAC,EAAE,SAAS,CAAC,GAAG,SAAS,QAAQ;IAC1F;IACA,qBAAqB,yBAAyB,GAAG,SAAU,CAAC;QACxD,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,IAAI,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YACvD,IAAI,QAAQ,KAAK,QAAQ,GAAG;gBACxB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO;QACX;QACA,OAAO;QACP,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,IAAI,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YACvD,IAAI,QAAQ,KAAK,QAAQ,GAAG;gBACxB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO;QACX;QACA,OAAO,CAAC,OAAO,GAAG,IAAI;IAC1B;IACA,qBAAqB,SAAS,GAAG,SAAU,GAAG,EAAE,QAAQ;QACpD,OAAO,qBAAqB,gBAAgB,CAAC,KAAK,UAAU,OAAO,qBAAqB,iBAAiB,EAAE,IAAI,WAAW,qBAAqB,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC;IAClL;IACA;;KAEC,GACD,qBAAqB,+BAA+B,GAAG,SAAU,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO;QAChG,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,WAAW,YAAY,SAAS,IAAI,WAAW,QAAQ,MAAM;IACnG;IACA;;;;;;;;;;KAUC,GACD,qBAAqB,gBAAgB,GAAG,SAAU,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ;QAC3F,IAAI,QAAQ,IAAI,OAAO;QACvB,YAAY,aAAa,IAAI,YAAY,CAAC,aAAa,IAAI,UAAU,CAAC;QACtE,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,IAAI,gBAAgB,QAAQ,MAAM;QAClC,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,gBAAgB,GAAG;oBACvC,IAAI,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,SAAS,qBAAqB,uBAAuB,IAAI,qBAAqB,gBAAgB,EAAE;wBAC1I,OAAO,WAAW,IAAI,CAAC;4BAAC;4BAAc;yBAAE;oBAC5C;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,IAAI,QAAQ,SAAS,KAAK,CAAC,GAAG,SAAS,MAAM;oBAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,GAAG,IAAK;wBAC1C,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;oBAC1B;oBACA,QAAQ,CAAC,kBAAkB,EAAE,GAAG;oBAChC,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,qBAAqB,WAAW,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;QAC3E,IAAI,CAAC,aAAa,CAAC,KAAK,WAAW;QACnC,IAAI,eAAe,IAAI,CAAC,gBAAgB;QACxC,IAAI,YAAY,CAAC;QACjB,IAAI,MAAM,SAAS,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,UAAU,QAAQ,CAAC,EAAE;YACzB,IAAI,WAAW,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,SAAS,qBAAqB,uBAAuB;YAC9G,IAAI,WAAW,cAAc;gBACzB,eAAe;gBACf,YAAY;YAChB;QACJ;QACA,IAAI,aAAa,GAAG;YAChB,OAAO;QACX,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,qFAAqF;IACrF,0FAA0F;IAC1F,yCAAyC;IACzC,qBAAqB,gBAAgB,GAAG;IACxC,qBAAqB,uBAAuB,GAAG;IAC/C;;KAEC,GACD,qBAAqB,iBAAiB,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE;IAClE;;KAEC,GACD,qBAAqB,cAAc,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE;IACrE;;KAEC,GACD,qBAAqB,WAAW,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IACrE;;KAEC,GACD,qBAAqB,UAAU,GAAG;QAC9B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;KAC/B;IACD,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCEANExtension5Support.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\n// import UPCEANReader from './UPCEANReader';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension2Support\n */\nvar UPCEANExtension5Support = /** @class */ (function () {\n    function UPCEANExtension5Support() {\n        this.CHECK_DIGIT_ENCODINGS = [0x18, 0x14, 0x12, 0x11, 0x0C, 0x06, 0x03, 0x0A, 0x09, 0x05];\n        this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        this.decodeRowStringBuffer = '';\n    }\n    UPCEANExtension5Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n        var result = this.decodeRowStringBuffer;\n        var end = this.decodeMiddle(row, extensionStartRange, result);\n        var resultString = result.toString();\n        var extensionData = UPCEANExtension5Support.parseExtensionString(resultString);\n        var resultPoints = [\n            new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),\n            new ResultPoint(end, rowNumber)\n        ];\n        var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n        if (extensionData != null) {\n            extensionResult.putAllMetadata(extensionData);\n        }\n        return extensionResult;\n    };\n    UPCEANExtension5Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var lgPatternFound = 0;\n        for (var x = 0; x < 5 && rowOffset < end; x++) {\n            var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                lgPatternFound |= 1 << (4 - x);\n            }\n            if (x !== 4) {\n                // Read off separator if not last\n                rowOffset = row.getNextSet(rowOffset);\n                rowOffset = row.getNextUnset(rowOffset);\n            }\n        }\n        if (resultString.length !== 5) {\n            throw new NotFoundException();\n        }\n        var checkDigit = this.determineCheckDigit(lgPatternFound);\n        if (UPCEANExtension5Support.extensionChecksum(resultString.toString()) !== checkDigit) {\n            throw new NotFoundException();\n        }\n        return rowOffset;\n    };\n    UPCEANExtension5Support.extensionChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n        }\n        sum *= 3;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n        }\n        sum *= 3;\n        return sum % 10;\n    };\n    UPCEANExtension5Support.prototype.determineCheckDigit = function (lgPatternFound) {\n        for (var d = 0; d < 10; d++) {\n            if (lgPatternFound === this.CHECK_DIGIT_ENCODINGS[d]) {\n                return d;\n            }\n        }\n        throw new NotFoundException();\n    };\n    UPCEANExtension5Support.parseExtensionString = function (raw) {\n        if (raw.length !== 5) {\n            return null;\n        }\n        var value = UPCEANExtension5Support.parseExtension5String(raw);\n        if (value == null) {\n            return null;\n        }\n        return new Map([[ResultMetadataType.SUGGESTED_PRICE, value]]);\n    };\n    UPCEANExtension5Support.parseExtension5String = function (raw) {\n        var currency;\n        switch (raw.charAt(0)) {\n            case '0':\n                currency = '£';\n                break;\n            case '5':\n                currency = '$';\n                break;\n            case '9':\n                // Reference: http://www.jollytech.com\n                switch (raw) {\n                    case '90000':\n                        // No suggested retail price\n                        return null;\n                    case '99991':\n                        // Complementary\n                        return '0.00';\n                    case '99990':\n                        return 'Used';\n                }\n                // Otherwise... unknown currency?\n                currency = '';\n                break;\n            default:\n                currency = '';\n                break;\n        }\n        var rawAmount = parseInt(raw.substring(1));\n        var unitsString = (rawAmount / 100).toString();\n        var hundredths = rawAmount % 100;\n        var hundredthsString = hundredths < 10 ? '0' + hundredths : hundredths.toString(); // fixme\n        return currency + unitsString + '.' + hundredthsString;\n    };\n    return UPCEANExtension5Support;\n}());\nexport default UPCEANExtension5Support;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AAjBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAQA;;CAEC,GACD,IAAI,0BAAyC;IACzC,SAAS;QACL,IAAI,CAAC,qBAAqB,GAAG;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACzF,IAAI,CAAC,oBAAoB,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QACxD,IAAI,CAAC,qBAAqB,GAAG;IACjC;IACA,wBAAwB,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,mBAAmB;QACvF,IAAI,SAAS,IAAI,CAAC,qBAAqB;QACvC,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,qBAAqB;QACtD,IAAI,eAAe,OAAO,QAAQ;QAClC,IAAI,gBAAgB,wBAAwB,oBAAoB,CAAC;QACjE,IAAI,eAAe;YACf,IAAI,mKAAA,CAAA,UAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,IAAI,KAAK;YACzE,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK;SACxB;QACD,IAAI,kBAAkB,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAAM,GAAG,cAAc,qKAAA,CAAA,UAAa,CAAC,iBAAiB,EAAE,IAAI,OAAO,OAAO;QACzH,IAAI,iBAAiB,MAAM;YACvB,gBAAgB,cAAc,CAAC;QACnC;QACA,OAAO;IACX;IACA,wBAAwB,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,YAAY;QACpF,IAAI,KAAK;QACT,IAAI,WAAW,IAAI,CAAC,oBAAoB;QACxC,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,YAAY,UAAU,CAAC,EAAE;QAC7B,IAAI,iBAAiB;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,oLAAA,CAAA,UAAoB,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,oLAAA,CAAA,UAAoB,CAAC,gBAAgB;YAChH,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,YAAY;YACrE,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,aAAa,IAAI;gBACjB,kBAAkB,KAAM,IAAI;YAChC;YACA,IAAI,MAAM,GAAG;gBACT,iCAAiC;gBACjC,YAAY,IAAI,UAAU,CAAC;gBAC3B,YAAY,IAAI,YAAY,CAAC;YACjC;QACJ;QACA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC3B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC1C,IAAI,wBAAwB,iBAAiB,CAAC,aAAa,QAAQ,QAAQ,YAAY;YACnF,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,OAAO;IACX;IACA,wBAAwB,iBAAiB,GAAG,SAAU,CAAC;QACnD,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,OAAO,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;QACtD;QACA,OAAO;QACP,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,OAAO,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;QACtD;QACA,OAAO;QACP,OAAO,MAAM;IACjB;IACA,wBAAwB,SAAS,CAAC,mBAAmB,GAAG,SAAU,cAAc;QAC5E,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YACzB,IAAI,mBAAmB,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE;gBAClD,OAAO;YACX;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,wBAAwB,oBAAoB,GAAG,SAAU,GAAG;QACxD,IAAI,IAAI,MAAM,KAAK,GAAG;YAClB,OAAO;QACX;QACA,IAAI,QAAQ,wBAAwB,qBAAqB,CAAC;QAC1D,IAAI,SAAS,MAAM;YACf,OAAO;QACX;QACA,OAAO,IAAI,IAAI;YAAC;gBAAC,0KAAA,CAAA,UAAkB,CAAC,eAAe;gBAAE;aAAM;SAAC;IAChE;IACA,wBAAwB,qBAAqB,GAAG,SAAU,GAAG;QACzD,IAAI;QACJ,OAAQ,IAAI,MAAM,CAAC;YACf,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;gBACD,sCAAsC;gBACtC,OAAQ;oBACJ,KAAK;wBACD,4BAA4B;wBAC5B,OAAO;oBACX,KAAK;wBACD,gBAAgB;wBAChB,OAAO;oBACX,KAAK;wBACD,OAAO;gBACf;gBACA,iCAAiC;gBACjC,WAAW;gBACX;YACJ;gBACI,WAAW;gBACX;QACR;QACA,IAAI,YAAY,SAAS,IAAI,SAAS,CAAC;QACvC,IAAI,cAAc,CAAC,YAAY,GAAG,EAAE,QAAQ;QAC5C,IAAI,aAAa,YAAY;QAC7B,IAAI,mBAAmB,aAAa,KAAK,MAAM,aAAa,WAAW,QAAQ,IAAI,QAAQ;QAC3F,OAAO,WAAW,cAAc,MAAM;IAC1C;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCEANExtension2Support.js"], "sourcesContent": ["/*\n * Copyright (C) 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension5Support\n */\nvar UPCEANExtension2Support = /** @class */ (function () {\n    function UPCEANExtension2Support() {\n        this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        this.decodeRowStringBuffer = '';\n    }\n    UPCEANExtension2Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n        var result = this.decodeRowStringBuffer;\n        var end = this.decodeMiddle(row, extensionStartRange, result);\n        var resultString = result.toString();\n        var extensionData = UPCEANExtension2Support.parseExtensionString(resultString);\n        var resultPoints = [\n            new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),\n            new ResultPoint(end, rowNumber)\n        ];\n        var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n        if (extensionData != null) {\n            extensionResult.putAllMetadata(extensionData);\n        }\n        return extensionResult;\n    };\n    UPCEANExtension2Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var checkParity = 0;\n        for (var x = 0; x < 2 && rowOffset < end; x++) {\n            var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                checkParity |= 1 << (1 - x);\n            }\n            if (x !== 1) {\n                // Read off separator if not last\n                rowOffset = row.getNextSet(rowOffset);\n                rowOffset = row.getNextUnset(rowOffset);\n            }\n        }\n        if (resultString.length !== 2) {\n            throw new NotFoundException();\n        }\n        if (parseInt(resultString.toString()) % 4 !== checkParity) {\n            throw new NotFoundException();\n        }\n        return rowOffset;\n    };\n    UPCEANExtension2Support.parseExtensionString = function (raw) {\n        if (raw.length !== 2) {\n            return null;\n        }\n        return new Map([[ResultMetadataType.ISSUE_NUMBER, parseInt(raw)]]);\n    };\n    return UPCEANExtension2Support;\n}());\nexport default UPCEANExtension2Support;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD;AACA;AACA;AACA;AACA;AACA;AAhBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAOA;;CAEC,GACD,IAAI,0BAAyC;IACzC,SAAS;QACL,IAAI,CAAC,oBAAoB,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QACxD,IAAI,CAAC,qBAAqB,GAAG;IACjC;IACA,wBAAwB,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,mBAAmB;QACvF,IAAI,SAAS,IAAI,CAAC,qBAAqB;QACvC,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,qBAAqB;QACtD,IAAI,eAAe,OAAO,QAAQ;QAClC,IAAI,gBAAgB,wBAAwB,oBAAoB,CAAC;QACjE,IAAI,eAAe;YACf,IAAI,mKAAA,CAAA,UAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,IAAI,KAAK;YACzE,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK;SACxB;QACD,IAAI,kBAAkB,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAAM,GAAG,cAAc,qKAAA,CAAA,UAAa,CAAC,iBAAiB,EAAE,IAAI,OAAO,OAAO;QACzH,IAAI,iBAAiB,MAAM;YACvB,gBAAgB,cAAc,CAAC;QACnC;QACA,OAAO;IACX;IACA,wBAAwB,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,YAAY;QACpF,IAAI,KAAK;QACT,IAAI,WAAW,IAAI,CAAC,oBAAoB;QACxC,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,YAAY,UAAU,CAAC,EAAE;QAC7B,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,oLAAA,CAAA,UAAoB,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,oLAAA,CAAA,UAAoB,CAAC,gBAAgB;YAChH,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,YAAY;YACrE,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,aAAa,IAAI;gBACjB,eAAe,KAAM,IAAI;YAC7B;YACA,IAAI,MAAM,GAAG;gBACT,iCAAiC;gBACjC,YAAY,IAAI,UAAU,CAAC;gBAC3B,YAAY,IAAI,YAAY,CAAC;YACjC;QACJ;QACA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC3B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,SAAS,aAAa,QAAQ,MAAM,MAAM,aAAa;YACvD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,OAAO;IACX;IACA,wBAAwB,oBAAoB,GAAG,SAAU,GAAG;QACxD,IAAI,IAAI,MAAM,KAAK,GAAG;YAClB,OAAO;QACX;QACA,OAAO,IAAI,IAAI;YAAC;gBAAC,0KAAA,CAAA,UAAkB,CAAC,YAAY;gBAAE,SAAS;aAAK;SAAC;IACrE;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCEANExtensionSupport.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport UPCEANExtension5Support from './UPCEANExtension5Support';\nimport UPCEANExtension2Support from './UPCEANExtension2Support';\nvar UPCEANExtensionSupport = /** @class */ (function () {\n    function UPCEANExtensionSupport() {\n    }\n    UPCEANExtensionSupport.decodeRow = function (rowNumber, row, rowOffset) {\n        var extensionStartRange = AbstractUPCEANReader.findGuardPattern(row, rowOffset, false, this.EXTENSION_START_PATTERN, new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));\n        try {\n            // return null;\n            var fiveSupport = new UPCEANExtension5Support();\n            return fiveSupport.decodeRow(rowNumber, row, extensionStartRange);\n        }\n        catch (err) {\n            // return null;\n            var twoSupport = new UPCEANExtension2Support();\n            return twoSupport.decodeRow(rowNumber, row, extensionStartRange);\n        }\n    };\n    UPCEANExtensionSupport.EXTENSION_START_PATTERN = Int32Array.from([1, 1, 2]);\n    return UPCEANExtensionSupport;\n}());\nexport default UPCEANExtensionSupport;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;;;;AACA,IAAI,yBAAwC;IACxC,SAAS,0BACT;IACA,uBAAuB,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,SAAS;QAClE,IAAI,sBAAsB,oLAAA,CAAA,UAAoB,CAAC,gBAAgB,CAAC,KAAK,WAAW,OAAO,IAAI,CAAC,uBAAuB,EAAE,IAAI,WAAW,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC;QAC9K,IAAI;YACA,eAAe;YACf,IAAI,cAAc,IAAI,uLAAA,CAAA,UAAuB;YAC7C,OAAO,YAAY,SAAS,CAAC,WAAW,KAAK;QACjD,EACA,OAAO,KAAK;YACR,eAAe;YACf,IAAI,aAAa,IAAI,uLAAA,CAAA,UAAuB;YAC5C,OAAO,WAAW,SAAS,CAAC,WAAW,KAAK;QAChD;IACJ;IACA,uBAAuB,uBAAuB,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;KAAE;IAC1E,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCEANReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport UPCEANExtensionSupport from './UPCEANExtensionSupport';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\nimport ChecksumException from '../ChecksumException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar UPCEANReader = /** @class */ (function (_super) {\n    __extends(UPCEANReader, _super);\n    function UPCEANReader() {\n        var _this = _super.call(this) || this;\n        _this.decodeRowStringBuffer = '';\n        UPCEANReader.L_AND_G_PATTERNS = UPCEANReader.L_PATTERNS.map(function (arr) { return Int32Array.from(arr); });\n        for (var i = 10; i < 20; i++) {\n            var widths = UPCEANReader.L_PATTERNS[i - 10];\n            var reversedWidths = new Int32Array(widths.length);\n            for (var j = 0; j < widths.length; j++) {\n                reversedWidths[j] = widths[widths.length - j - 1];\n            }\n            UPCEANReader.L_AND_G_PATTERNS[i] = reversedWidths;\n        }\n        return _this;\n    }\n    UPCEANReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var startGuardRange = UPCEANReader.findStartGuardPattern(row);\n        var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n        if (resultPointCallback != null) {\n            var resultPoint_1 = new ResultPoint((startGuardRange[0] + startGuardRange[1]) / 2.0, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_1);\n        }\n        var budello = this.decodeMiddle(row, startGuardRange, this.decodeRowStringBuffer);\n        var endStart = budello.rowOffset;\n        var result = budello.resultString;\n        if (resultPointCallback != null) {\n            var resultPoint_2 = new ResultPoint(endStart, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_2);\n        }\n        var endRange = UPCEANReader.decodeEnd(row, endStart);\n        if (resultPointCallback != null) {\n            var resultPoint_3 = new ResultPoint((endRange[0] + endRange[1]) / 2.0, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_3);\n        }\n        // Make sure there is a quiet zone at least as big as the end pattern after the barcode. The\n        // spec might want more whitespace, but in practice this is the maximum we can count on.\n        var end = endRange[1];\n        var quietEnd = end + (end - endRange[0]);\n        if (quietEnd >= row.getSize() || !row.isRange(end, quietEnd, false)) {\n            throw new NotFoundException();\n        }\n        var resultString = result.toString();\n        // UPC/EAN should never be less than 8 chars anyway\n        if (resultString.length < 8) {\n            throw new FormatException();\n        }\n        if (!UPCEANReader.checkChecksum(resultString)) {\n            throw new ChecksumException();\n        }\n        var left = (startGuardRange[1] + startGuardRange[0]) / 2.0;\n        var right = (endRange[1] + endRange[0]) / 2.0;\n        var format = this.getBarcodeFormat();\n        var resultPoint = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n        var decodeResult = new Result(resultString, null, 0, resultPoint, format, new Date().getTime());\n        var extensionLength = 0;\n        try {\n            var extensionResult = UPCEANExtensionSupport.decodeRow(rowNumber, row, endRange[1]);\n            decodeResult.putMetadata(ResultMetadataType.UPC_EAN_EXTENSION, extensionResult.getText());\n            decodeResult.putAllMetadata(extensionResult.getResultMetadata());\n            decodeResult.addResultPoints(extensionResult.getResultPoints());\n            extensionLength = extensionResult.getText().length;\n        }\n        catch (err) {\n        }\n        var allowedExtensions = hints == null ? null : hints.get(DecodeHintType.ALLOWED_EAN_EXTENSIONS);\n        if (allowedExtensions != null) {\n            var valid = false;\n            for (var length_1 in allowedExtensions) {\n                if (extensionLength.toString() === length_1) { // check me\n                    valid = true;\n                    break;\n                }\n            }\n            if (!valid) {\n                throw new NotFoundException();\n            }\n        }\n        if (format === BarcodeFormat.EAN_13 || format === BarcodeFormat.UPC_A) {\n            // let countryID = eanManSupport.lookupContryIdentifier(resultString); todo\n            // if (countryID != null) {\n            //     decodeResult.putMetadata(ResultMetadataType.POSSIBLE_COUNTRY, countryID);\n            // }\n        }\n        return decodeResult;\n    };\n    UPCEANReader.checkChecksum = function (s) {\n        return UPCEANReader.checkStandardUPCEANChecksum(s);\n    };\n    UPCEANReader.checkStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        if (length === 0)\n            return false;\n        var check = parseInt(s.charAt(length - 1), 10);\n        return UPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n    };\n    UPCEANReader.getStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        sum *= 3;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        return (1000 - sum) % 10;\n    };\n    UPCEANReader.decodeEnd = function (row, endStart) {\n        return UPCEANReader.findGuardPattern(row, endStart, false, UPCEANReader.START_END_PATTERN, new Int32Array(UPCEANReader.START_END_PATTERN.length).fill(0));\n    };\n    return UPCEANReader;\n}(AbstractUPCEANReader));\nexport default UPCEANReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;;;;;;AAWA;;;;;;;CAOC,GACD,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,qBAAqB,GAAG;QAC9B,aAAa,gBAAgB,GAAG,aAAa,UAAU,CAAC,GAAG,CAAC,SAAU,GAAG;YAAI,OAAO,WAAW,IAAI,CAAC;QAAM;QAC1G,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAK;YAC1B,IAAI,SAAS,aAAa,UAAU,CAAC,IAAI,GAAG;YAC5C,IAAI,iBAAiB,IAAI,WAAW,OAAO,MAAM;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACpC,cAAc,CAAC,EAAE,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,EAAE;YACrD;YACA,aAAa,gBAAgB,CAAC,EAAE,GAAG;QACvC;QACA,OAAO;IACX;IACA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC9D,IAAI,kBAAkB,aAAa,qBAAqB,CAAC;QACzD,IAAI,sBAAsB,SAAS,OAAO,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B;QACpG,IAAI,uBAAuB,MAAM;YAC7B,IAAI,gBAAgB,IAAI,mKAAA,CAAA,UAAW,CAAC,CAAC,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE,IAAI,KAAK;YACrF,oBAAoB,wBAAwB,CAAC;QACjD;QACA,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,iBAAiB,IAAI,CAAC,qBAAqB;QAChF,IAAI,WAAW,QAAQ,SAAS;QAChC,IAAI,SAAS,QAAQ,YAAY;QACjC,IAAI,uBAAuB,MAAM;YAC7B,IAAI,gBAAgB,IAAI,mKAAA,CAAA,UAAW,CAAC,UAAU;YAC9C,oBAAoB,wBAAwB,CAAC;QACjD;QACA,IAAI,WAAW,aAAa,SAAS,CAAC,KAAK;QAC3C,IAAI,uBAAuB,MAAM;YAC7B,IAAI,gBAAgB,IAAI,mKAAA,CAAA,UAAW,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI,KAAK;YACvE,oBAAoB,wBAAwB,CAAC;QACjD;QACA,4FAA4F;QAC5F,wFAAwF;QACxF,IAAI,MAAM,QAAQ,CAAC,EAAE;QACrB,IAAI,WAAW,MAAM,CAAC,MAAM,QAAQ,CAAC,EAAE;QACvC,IAAI,YAAY,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,QAAQ;YACjE,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,eAAe,OAAO,QAAQ;QAClC,mDAAmD;QACnD,IAAI,aAAa,MAAM,GAAG,GAAG;YACzB,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,IAAI,CAAC,aAAa,aAAa,CAAC,eAAe;YAC3C,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE,IAAI;QACvD,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;QAC1C,IAAI,SAAS,IAAI,CAAC,gBAAgB;QAClC,IAAI,cAAc;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO;SAAW;QACvF,IAAI,eAAe,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,MAAM,GAAG,aAAa,QAAQ,IAAI,OAAO,OAAO;QAC5F,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAI,kBAAkB,sLAAA,CAAA,UAAsB,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,EAAE;YAClF,aAAa,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,iBAAiB,EAAE,gBAAgB,OAAO;YACtF,aAAa,cAAc,CAAC,gBAAgB,iBAAiB;YAC7D,aAAa,eAAe,CAAC,gBAAgB,eAAe;YAC5D,kBAAkB,gBAAgB,OAAO,GAAG,MAAM;QACtD,EACA,OAAO,KAAK,CACZ;QACA,IAAI,oBAAoB,SAAS,OAAO,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,sBAAsB;QAC9F,IAAI,qBAAqB,MAAM;YAC3B,IAAI,QAAQ;YACZ,IAAK,IAAI,YAAY,kBAAmB;gBACpC,IAAI,gBAAgB,QAAQ,OAAO,UAAU;oBACzC,QAAQ;oBACR;gBACJ;YACJ;YACA,IAAI,CAAC,OAAO;gBACR,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;QACJ;QACA,IAAI,WAAW,qKAAA,CAAA,UAAa,CAAC,MAAM,IAAI,WAAW,qKAAA,CAAA,UAAa,CAAC,KAAK,EAAE;QACnE,2EAA2E;QAC3E,2BAA2B;QAC3B,gFAAgF;QAChF,IAAI;QACR;QACA,OAAO;IACX;IACA,aAAa,aAAa,GAAG,SAAU,CAAC;QACpC,OAAO,aAAa,2BAA2B,CAAC;IACpD;IACA,aAAa,2BAA2B,GAAG,SAAU,CAAC;QAClD,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,WAAW,GACX,OAAO;QACX,IAAI,QAAQ,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI;QAC3C,OAAO,aAAa,yBAAyB,CAAC,EAAE,SAAS,CAAC,GAAG,SAAS,QAAQ;IAClF;IACA,aAAa,yBAAyB,GAAG,SAAU,CAAC;QAChD,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,IAAI,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YACvD,IAAI,QAAQ,KAAK,QAAQ,GAAG;gBACxB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO;QACX;QACA,OAAO;QACP,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,EAAG;YACrC,IAAI,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YACvD,IAAI,QAAQ,KAAK,QAAQ,GAAG;gBACxB,MAAM,IAAI,uKAAA,CAAA,UAAe;YAC7B;YACA,OAAO;QACX;QACA,OAAO,CAAC,OAAO,GAAG,IAAI;IAC1B;IACA,aAAa,SAAS,GAAG,SAAU,GAAG,EAAE,QAAQ;QAC5C,OAAO,aAAa,gBAAgB,CAAC,KAAK,UAAU,OAAO,aAAa,iBAAiB,EAAE,IAAI,WAAW,aAAa,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC;IAC1J;IACA,OAAO;AACX,EAAE,oLAAA,CAAA,UAAoB;uCACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/EAN13Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport UPCEANReader from './UPCEANReader';\nimport NotFoundException from '../NotFoundException';\n/**\n * <p>Implements decoding of the EAN-13 format.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar EAN13Reader = /** @class */ (function (_super) {\n    __extends(EAN13Reader, _super);\n    function EAN13Reader() {\n        var _this = _super.call(this) || this;\n        _this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        return _this;\n    }\n    EAN13Reader.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a, e_2, _b;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var lgPatternFound = 0;\n        for (var x = 0; x < 6 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_AND_G_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                lgPatternFound |= 1 << (5 - x);\n            }\n        }\n        resultString = EAN13Reader.determineFirstDigit(resultString, lgPatternFound);\n        var middleRange = UPCEANReader.findGuardPattern(row, rowOffset, true, UPCEANReader.MIDDLE_PATTERN, new Int32Array(UPCEANReader.MIDDLE_PATTERN.length).fill(0));\n        rowOffset = middleRange[1];\n        for (var x = 0; x < 6 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch));\n            try {\n                for (var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()) {\n                    var counter = counters_2_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        return { rowOffset: rowOffset, resultString: resultString };\n    };\n    EAN13Reader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.EAN_13;\n    };\n    EAN13Reader.determineFirstDigit = function (resultString, lgPatternFound) {\n        for (var d = 0; d < 10; d++) {\n            if (lgPatternFound === this.FIRST_DIGIT_ENCODINGS[d]) {\n                resultString = String.fromCharCode(('0'.charCodeAt(0) + d)) + resultString;\n                return resultString;\n            }\n        }\n        throw new NotFoundException();\n    };\n    EAN13Reader.FIRST_DIGIT_ENCODINGS = [0x00, 0x0B, 0x0D, 0xE, 0x13, 0x19, 0x1C, 0x15, 0x16, 0x1A];\n    return EAN13Reader;\n}(UPCEANReader));\nexport default EAN13Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD;AACA;AACA;AA1BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;AAIA;;;;;;CAMC,GACD,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,oBAAoB,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QACzD,OAAO;IACX;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,YAAY;QACxE,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,WAAW,IAAI,CAAC,oBAAoB;QACxC,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,YAAY,UAAU,CAAC,EAAE;QAC7B,IAAI,iBAAiB;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,4KAAA,CAAA,UAAY,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,4KAAA,CAAA,UAAY,CAAC,gBAAgB;YAChG,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,YAAY;YACrE,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,aAAa,IAAI;gBACjB,kBAAkB,KAAM,IAAI;YAChC;QACJ;QACA,eAAe,YAAY,mBAAmB,CAAC,cAAc;QAC7D,IAAI,cAAc,4KAAA,CAAA,UAAY,CAAC,gBAAgB,CAAC,KAAK,WAAW,MAAM,4KAAA,CAAA,UAAY,CAAC,cAAc,EAAE,IAAI,WAAW,4KAAA,CAAA,UAAY,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC;QAC3J,YAAY,WAAW,CAAC,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,4KAAA,CAAA,UAAY,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,4KAAA,CAAA,UAAY,CAAC,UAAU;YAC1F,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK;YACzD,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,OAAO;YAAE,WAAW;YAAW,cAAc;QAAa;IAC9D;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG;QACrC,OAAO,qKAAA,CAAA,UAAa,CAAC,MAAM;IAC/B;IACA,YAAY,mBAAmB,GAAG,SAAU,YAAY,EAAE,cAAc;QACpE,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YACzB,IAAI,mBAAmB,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE;gBAClD,eAAe,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,KAAM;gBAC9D,OAAO;YACX;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,YAAY,qBAAqB,GAAG;QAAC;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAC/F,OAAO;AACX,EAAE,4KAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/EAN8Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport UPCEANReader from './UPCEANReader';\n/**\n * <p>Implements decoding of the EAN-8 format.</p>\n *\n * <AUTHOR> Owen\n */\nvar EAN8Reader = /** @class */ (function (_super) {\n    __extends(EAN8Reader, _super);\n    function EAN8Reader() {\n        var _this = _super.call(this) || this;\n        _this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        return _this;\n    }\n    EAN8Reader.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a, e_2, _b;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        for (var x = 0; x < 4 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        var middleRange = UPCEANReader.findGuardPattern(row, rowOffset, true, UPCEANReader.MIDDLE_PATTERN, new Int32Array(UPCEANReader.MIDDLE_PATTERN.length).fill(0));\n        rowOffset = middleRange[1];\n        for (var x = 0; x < 4 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch));\n            try {\n                for (var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()) {\n                    var counter = counters_2_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        return { rowOffset: rowOffset, resultString: resultString };\n    };\n    EAN8Reader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.EAN_8;\n    };\n    return EAN8Reader;\n}(UPCEANReader));\nexport default EAN8Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD;AACA;AAzBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAGA;;;;CAIC,GACD,IAAI,aAA4B,SAAU,MAAM;IAC5C,UAAU,YAAY;IACtB,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,oBAAoB,GAAG,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QACzD,OAAO;IACX;IACA,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,YAAY;QACvE,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,WAAW,IAAI,CAAC,oBAAoB;QACxC,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,YAAY,UAAU,CAAC,EAAE;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,4KAAA,CAAA,UAAY,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,4KAAA,CAAA,UAAY,CAAC,UAAU;YAC1F,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK;YACzD,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,IAAI,cAAc,4KAAA,CAAA,UAAY,CAAC,gBAAgB,CAAC,KAAK,WAAW,MAAM,4KAAA,CAAA,UAAY,CAAC,cAAc,EAAE,IAAI,WAAW,4KAAA,CAAA,UAAY,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC;QAC3J,YAAY,WAAW,CAAC,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,4KAAA,CAAA,UAAY,CAAC,WAAW,CAAC,KAAK,UAAU,WAAW,4KAAA,CAAA,UAAY,CAAC,UAAU;YAC1F,gBAAgB,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK;YACzD,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,OAAO;YAAE,WAAW;YAAW,cAAc;QAAa;IAC9D;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,qKAAA,CAAA,UAAa,CAAC,KAAK;IAC9B;IACA,OAAO;AACX,EAAE,4KAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCAReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport Result from '../Result';\nimport NotFoundException from '../NotFoundException';\nimport EAN13Reader from './EAN13Reader';\nimport UPCEANReader from './UPCEANReader';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Sam Rudloff)\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCAReader.java\n *\n * @experimental\n */\nvar UPCAReader = /** @class */ (function (_super) {\n    __extends(UPCAReader, _super);\n    function UPCAReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.ean13Reader = new EAN13Reader();\n        return _this;\n    }\n    // @Override\n    UPCAReader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.UPC_A;\n    };\n    // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n    // @Override\n    UPCAReader.prototype.decode = function (image, hints) {\n        return this.maybeReturnResult(this.ean13Reader.decode(image));\n    };\n    // @Override\n    UPCAReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        return this.maybeReturnResult(this.ean13Reader.decodeRow(rowNumber, row, hints));\n    };\n    // @Override\n    UPCAReader.prototype.decodeMiddle = function (row, startRange, resultString) {\n        return this.ean13Reader.decodeMiddle(row, startRange, resultString);\n    };\n    UPCAReader.prototype.maybeReturnResult = function (result) {\n        var text = result.getText();\n        if (text.charAt(0) === '0') {\n            var upcaResult = new Result(text.substring(1), null, null, result.getResultPoints(), BarcodeFormat.UPC_A);\n            if (result.getResultMetadata() != null) {\n                upcaResult.putAllMetadata(result.getResultMetadata());\n            }\n            return upcaResult;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    UPCAReader.prototype.reset = function () {\n        this.ean13Reader.reset();\n    };\n    return UPCAReader;\n}(UPCEANReader));\nexport default UPCAReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AAlBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;AAOA;;;;;;;;;;;CAWC,GACD,IAAI,aAA4B,SAAU,MAAM;IAC5C,UAAU,YAAY;IACtB,SAAS;QACL,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,WAAW,GAAG,IAAI,2KAAA,CAAA,UAAW;QACnC,OAAO;IACX;IACA,YAAY;IACZ,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,qKAAA,CAAA,UAAa,CAAC,KAAK;IAC9B;IACA,+FAA+F;IAC/F,YAAY;IACZ,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAC1D;IACA,YAAY;IACZ,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC5D,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,KAAK;IAC7E;IACA,YAAY;IACZ,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,YAAY;QACvE,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,YAAY;IAC1D;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,MAAM;QACrD,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,KAAK,MAAM,CAAC,OAAO,KAAK;YACxB,IAAI,aAAa,IAAI,8JAAA,CAAA,UAAM,CAAC,KAAK,SAAS,CAAC,IAAI,MAAM,MAAM,OAAO,eAAe,IAAI,qKAAA,CAAA,UAAa,CAAC,KAAK;YACxG,IAAI,OAAO,iBAAiB,MAAM,MAAM;gBACpC,WAAW,cAAc,CAAC,OAAO,iBAAiB;YACtD;YACA,OAAO;QACX,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,WAAW,SAAS,CAAC,KAAK,GAAG;QACzB,IAAI,CAAC,WAAW,CAAC,KAAK;IAC1B;IACA,OAAO;AACX,EAAE,4KAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/UPCEReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport UPCEANReader from './UPCEANReader';\nimport StringBuilder from '../util/StringBuilder';\nimport NotFoundException from '../NotFoundException';\nimport BarcodeFormat from '../BarcodeFormat';\n// package com.google.zxing.oned;\n// import com.google.zxing.BarcodeFormat;\n// import com.google.zxing.FormatException;\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.common.BitArray;\n/**\n * <p>Implements decoding of the UPC-E format.</p>\n * <p><a href=\"http://www.barcodeisland.com/upce.phtml\">This</a> is a great reference for\n * UPC-E information.</p>\n *\n * <AUTHOR> Owen\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCEReader.java\n *\n * @experimental\n */\nvar UPCEReader = /** @class */ (function (_super) {\n    __extends(UPCEReader, _super);\n    function UPCEReader() {\n        var _this = _super.call(this) || this;\n        _this.decodeMiddleCounters = new Int32Array(4);\n        return _this;\n    }\n    /**\n     * @throws NotFoundException\n     */\n    // @Override\n    UPCEReader.prototype.decodeMiddle = function (row, startRange, result) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters.map(function (x) { return x; });\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var lgPatternFound = 0;\n        for (var x = 0; x < 6 && rowOffset < end; x++) {\n            var bestMatch = UPCEReader.decodeDigit(row, counters, rowOffset, UPCEReader.L_AND_G_PATTERNS);\n            result += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                lgPatternFound |= 1 << (5 - x);\n            }\n        }\n        UPCEReader.determineNumSysAndCheckDigit(new StringBuilder(result), lgPatternFound);\n        return rowOffset;\n    };\n    /**\n     * @throws NotFoundException\n     */\n    // @Override\n    UPCEReader.prototype.decodeEnd = function (row, endStart) {\n        return UPCEReader.findGuardPatternWithoutCounters(row, endStart, true, UPCEReader.MIDDLE_END_PATTERN);\n    };\n    /**\n     * @throws FormatException\n     */\n    // @Override\n    UPCEReader.prototype.checkChecksum = function (s) {\n        return UPCEANReader.checkChecksum(UPCEReader.convertUPCEtoUPCA(s));\n    };\n    /**\n     * @throws NotFoundException\n     */\n    UPCEReader.determineNumSysAndCheckDigit = function (resultString, lgPatternFound) {\n        for (var numSys = 0; numSys <= 1; numSys++) {\n            for (var d = 0; d < 10; d++) {\n                if (lgPatternFound === this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[numSys][d]) {\n                    resultString.insert(0, /*(char)*/ ('0' + numSys));\n                    resultString.append(/*(char)*/ ('0' + d));\n                    return;\n                }\n            }\n        }\n        throw NotFoundException.getNotFoundInstance();\n    };\n    // @Override\n    UPCEReader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.UPC_E;\n    };\n    /**\n     * Expands a UPC-E value back into its full, equivalent UPC-A code value.\n     *\n     * @param upce UPC-E code as string of digits\n     * @return equivalent UPC-A code as string of digits\n     */\n    UPCEReader.convertUPCEtoUPCA = function (upce) {\n        // the following line is equivalent to upce.getChars(1, 7, upceChars, 0);\n        var upceChars = upce.slice(1, 7).split('').map(function (x) { return x.charCodeAt(0); });\n        var result = new StringBuilder( /*12*/);\n        result.append(upce.charAt(0));\n        var lastChar = upceChars[5];\n        switch (lastChar) {\n            case 0:\n            case 1:\n            case 2:\n                result.appendChars(upceChars, 0, 2);\n                result.append(lastChar);\n                result.append('0000');\n                result.appendChars(upceChars, 2, 3);\n                break;\n            case 3:\n                result.appendChars(upceChars, 0, 3);\n                result.append('00000');\n                result.appendChars(upceChars, 3, 2);\n                break;\n            case 4:\n                result.appendChars(upceChars, 0, 4);\n                result.append('00000');\n                result.append(upceChars[4]);\n                break;\n            default:\n                result.appendChars(upceChars, 0, 5);\n                result.append('0000');\n                result.append(lastChar);\n                break;\n        }\n        // Only append check digit in conversion if supplied\n        if (upce.length >= 8) {\n            result.append(upce.charAt(7));\n        }\n        return result.toString();\n    };\n    /**\n     * The pattern that marks the middle, and end, of a UPC-E pattern.\n     * There is no \"second half\" to a UPC-E barcode.\n     */\n    UPCEReader.MIDDLE_END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n    // For an UPC-E barcode, the final digit is represented by the parities used\n    // to encode the middle six digits, according to the table below.\n    //\n    //                Parity of next 6 digits\n    //    Digit   0     1     2     3     4     5\n    //       0    Even   Even  Even Odd  Odd   Odd\n    //       1    Even   Even  Odd  Even Odd   Odd\n    //       2    Even   Even  Odd  Odd  Even  Odd\n    //       3    Even   Even  Odd  Odd  Odd   Even\n    //       4    Even   Odd   Even Even Odd   Odd\n    //       5    Even   Odd   Odd  Even Even  Odd\n    //       6    Even   Odd   Odd  Odd  Even  Even\n    //       7    Even   Odd   Even Odd  Even  Odd\n    //       8    Even   Odd   Even Odd  Odd   Even\n    //       9    Even   Odd   Odd  Even Odd   Even\n    //\n    // The encoding is represented by the following array, which is a bit pattern\n    // using Odd = 0 and Even = 1. For example, 5 is represented by:\n    //\n    //              Odd Even Even Odd Odd Even\n    // in binary:\n    //                0    1    1   0   0    1   == 0x19\n    //\n    /**\n     * See {@link #L_AND_G_PATTERNS}; these values similarly represent patterns of\n     * even-odd parity encodings of digits that imply both the number system (0 or 1)\n     * used, and the check digit.\n     */\n    UPCEReader.NUMSYS_AND_CHECK_DIGIT_PATTERNS = [\n        Int32Array.from([0x38, 0x34, 0x32, 0x31, 0x2C, 0x26, 0x23, 0x2A, 0x29, 0x25]),\n        Int32Array.from([0x07, 0x0B, 0x0D, 0x0E, 0x13, 0x19, 0x1C, 0x15, 0x16, 0x1]),\n    ];\n    return UPCEReader;\n}(UPCEANReader));\nexport default UPCEReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD;AACA;AACA;AACA;AA3BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;AAKA,iCAAiC;AACjC,yCAAyC;AACzC,2CAA2C;AAC3C,6CAA6C;AAC7C,2CAA2C;AAC3C;;;;;;;;;;CAUC,GACD,IAAI,aAA4B,SAAU,MAAM;IAC5C,UAAU,YAAY;IACtB,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,oBAAoB,GAAG,IAAI,WAAW;QAC5C,OAAO;IACX;IACA;;KAEC,GACD,YAAY;IACZ,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,MAAM;QACjE,IAAI,KAAK;QACT,IAAI,WAAW,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO;QAAG;QACtE,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,YAAY,UAAU,CAAC,EAAE;QAC7B,IAAI,iBAAiB;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK,IAAK;YAC3C,IAAI,YAAY,WAAW,WAAW,CAAC,KAAK,UAAU,WAAW,WAAW,gBAAgB;YAC5F,UAAU,OAAO,YAAY,CAAE,IAAI,UAAU,CAAC,KAAK,YAAY;YAC/D,IAAI;gBACA,IAAK,IAAI,aAAa,CAAC,MAAM,KAAK,GAAG,SAAS,SAAS,GAAG,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9I,IAAI,UAAU,aAAa,KAAK;oBAChC,aAAa;gBACjB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,aAAa,IAAI;gBACjB,kBAAkB,KAAM,IAAI;YAChC;QACJ;QACA,WAAW,4BAA4B,CAAC,IAAI,6KAAA,CAAA,UAAa,CAAC,SAAS;QACnE,OAAO;IACX;IACA;;KAEC,GACD,YAAY;IACZ,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG,EAAE,QAAQ;QACpD,OAAO,WAAW,+BAA+B,CAAC,KAAK,UAAU,MAAM,WAAW,kBAAkB;IACxG;IACA;;KAEC,GACD,YAAY;IACZ,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,CAAC;QAC5C,OAAO,4KAAA,CAAA,UAAY,CAAC,aAAa,CAAC,WAAW,iBAAiB,CAAC;IACnE;IACA;;KAEC,GACD,WAAW,4BAA4B,GAAG,SAAU,YAAY,EAAE,cAAc;QAC5E,IAAK,IAAI,SAAS,GAAG,UAAU,GAAG,SAAU;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBACzB,IAAI,mBAAmB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAAE,EAAE;oBACpE,aAAa,MAAM,CAAC,GAAe,MAAM;oBACzC,aAAa,MAAM,CAAa,MAAM;oBACtC;gBACJ;YACJ;QACJ;QACA,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;IAC/C;IACA,YAAY;IACZ,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,qKAAA,CAAA,UAAa,CAAC,KAAK;IAC9B;IACA;;;;;KAKC,GACD,WAAW,iBAAiB,GAAG,SAAU,IAAI;QACzC,yEAAyE;QACzE,IAAI,YAAY,KAAK,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,EAAE,UAAU,CAAC;QAAI;QACtF,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;QAC9B,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;QAC1B,IAAI,WAAW,SAAS,CAAC,EAAE;QAC3B,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC,OAAO,MAAM,CAAC;gBACd,OAAO,MAAM,CAAC;gBACd,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC;YACJ,KAAK;gBACD,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC,OAAO,MAAM,CAAC;gBACd,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC;YACJ,KAAK;gBACD,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC,OAAO,MAAM,CAAC;gBACd,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE;gBAC1B;YACJ;gBACI,OAAO,WAAW,CAAC,WAAW,GAAG;gBACjC,OAAO,MAAM,CAAC;gBACd,OAAO,MAAM,CAAC;gBACd;QACR;QACA,oDAAoD;QACpD,IAAI,KAAK,MAAM,IAAI,GAAG;YAClB,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;QAC9B;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA;;;KAGC,GACD,WAAW,kBAAkB,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IAClE,4EAA4E;IAC5E,iEAAiE;IACjE,EAAE;IACF,yCAAyC;IACzC,6CAA6C;IAC7C,8CAA8C;IAC9C,8CAA8C;IAC9C,8CAA8C;IAC9C,+CAA+C;IAC/C,8CAA8C;IAC9C,8CAA8C;IAC9C,+CAA+C;IAC/C,8CAA8C;IAC9C,+CAA+C;IAC/C,+CAA+C;IAC/C,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,EAAE;IACF,0CAA0C;IAC1C,aAAa;IACb,oDAAoD;IACpD,EAAE;IACF;;;;KAIC,GACD,WAAW,+BAA+B,GAAG;QACzC,WAAW,IAAI,CAAC;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QAC5E,WAAW,IAAI,CAAC;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAI;KAC9E;IACD,OAAO;AACX,EAAE,4KAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/MultiFormatUPCEANReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport Result from '../Result';\nimport OneDReader from './OneDReader';\nimport EAN13Reader from './EAN13Reader';\nimport EAN8Reader from './EAN8Reader';\nimport UPCAReader from './UPCAReader';\nimport NotFoundException from '../NotFoundException';\nimport UPCEReader from './UPCEReader';\n/**\n * <p>A reader that can read all available UPC/EAN formats. If a caller wants to try to\n * read all such formats, it is most efficient to use this implementation rather than invoke\n * individual readers.</p>\n *\n * <AUTHOR> Owen\n */\nvar MultiFormatUPCEANReader = /** @class */ (function (_super) {\n    __extends(MultiFormatUPCEANReader, _super);\n    function MultiFormatUPCEANReader(hints) {\n        var _this = _super.call(this) || this;\n        var possibleFormats = hints == null ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n        var readers = [];\n        if (possibleFormats != null) {\n            if (possibleFormats.indexOf(BarcodeFormat.EAN_13) > -1) {\n                readers.push(new EAN13Reader());\n            }\n            if (possibleFormats.indexOf(BarcodeFormat.UPC_A) > -1) {\n                readers.push(new UPCAReader());\n            }\n            if (possibleFormats.indexOf(BarcodeFormat.EAN_8) > -1) {\n                readers.push(new EAN8Reader());\n            }\n            if (possibleFormats.indexOf(BarcodeFormat.UPC_E) > -1) {\n                readers.push(new UPCEReader());\n            }\n        }\n        if (readers.length === 0) {\n            readers.push(new EAN13Reader());\n            readers.push(new UPCAReader());\n            readers.push(new EAN8Reader());\n            readers.push(new UPCEReader());\n        }\n        _this.readers = readers;\n        return _this;\n    }\n    MultiFormatUPCEANReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var reader = _c.value;\n                try {\n                    // const result: Result = reader.decodeRow(rowNumber, row, startGuardPattern, hints);\n                    var result = reader.decodeRow(rowNumber, row, hints);\n                    // Special case: a 12-digit code encoded in UPC-A is identical to a \"0\"\n                    // followed by those 12 digits encoded as EAN-13. Each will recognize such a code,\n                    // UPC-A as a 12-digit string and EAN-13 as a 13-digit string starting with \"0\".\n                    // Individually these are correct and their readers will both read such a code\n                    // and correctly call it EAN-13, or UPC-A, respectively.\n                    //\n                    // In this case, if we've been looking for both types, we'd like to call it\n                    // a UPC-A code. But for efficiency we only run the EAN-13 decoder to also read\n                    // UPC-A. So we special case it here, and convert an EAN-13 result to a UPC-A\n                    // result if appropriate.\n                    //\n                    // But, don't return UPC-A if UPC-A was not a requested format!\n                    var ean13MayBeUPCA = result.getBarcodeFormat() === BarcodeFormat.EAN_13 &&\n                        result.getText().charAt(0) === '0';\n                    // @SuppressWarnings(\"unchecked\")\n                    var possibleFormats = hints == null ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n                    var canReturnUPCA = possibleFormats == null || possibleFormats.includes(BarcodeFormat.UPC_A);\n                    if (ean13MayBeUPCA && canReturnUPCA) {\n                        var rawBytes = result.getRawBytes();\n                        // Transfer the metadata across\n                        var resultUPCA = new Result(result.getText().substring(1), rawBytes, (rawBytes ? rawBytes.length : null), result.getResultPoints(), BarcodeFormat.UPC_A);\n                        resultUPCA.putAllMetadata(result.getResultMetadata());\n                        return resultUPCA;\n                    }\n                    return result;\n                }\n                catch (err) {\n                    // continue;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        throw new NotFoundException();\n    };\n    MultiFormatUPCEANReader.prototype.reset = function () {\n        var e_2, _a;\n        try {\n            for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var reader = _c.value;\n                reader.reset();\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    return MultiFormatUPCEANReader;\n}(OneDReader));\nexport default MultiFormatUPCEANReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;AAUA;;;;;;CAMC,GACD,IAAI,0BAAyC,SAAU,MAAM;IACzD,UAAU,yBAAyB;IACnC,SAAS,wBAAwB,KAAK;QAClC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,IAAI,kBAAkB,SAAS,OAAO,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB;QACtF,IAAI,UAAU,EAAE;QAChB,IAAI,mBAAmB,MAAM;YACzB,IAAI,gBAAgB,OAAO,CAAC,qKAAA,CAAA,UAAa,CAAC,MAAM,IAAI,CAAC,GAAG;gBACpD,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAW;YAChC;YACA,IAAI,gBAAgB,OAAO,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,IAAI,CAAC,GAAG;gBACnD,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;YAC/B;YACA,IAAI,gBAAgB,OAAO,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,IAAI,CAAC,GAAG;gBACnD,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;YAC/B;YACA,IAAI,gBAAgB,OAAO,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,IAAI,CAAC,GAAG;gBACnD,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;YAC/B;QACJ;QACA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACtB,QAAQ,IAAI,CAAC,IAAI,2KAAA,CAAA,UAAW;YAC5B,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;YAC3B,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;YAC3B,QAAQ,IAAI,CAAC,IAAI,0KAAA,CAAA,UAAU;QAC/B;QACA,MAAM,OAAO,GAAG;QAChB,OAAO;IACX;IACA,wBAAwB,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QACzE,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC5E,IAAI,SAAS,GAAG,KAAK;gBACrB,IAAI;oBACA,qFAAqF;oBACrF,IAAI,SAAS,OAAO,SAAS,CAAC,WAAW,KAAK;oBAC9C,uEAAuE;oBACvE,kFAAkF;oBAClF,gFAAgF;oBAChF,8EAA8E;oBAC9E,wDAAwD;oBACxD,EAAE;oBACF,2EAA2E;oBAC3E,+EAA+E;oBAC/E,6EAA6E;oBAC7E,yBAAyB;oBACzB,EAAE;oBACF,+DAA+D;oBAC/D,IAAI,iBAAiB,OAAO,gBAAgB,OAAO,qKAAA,CAAA,UAAa,CAAC,MAAM,IACnE,OAAO,OAAO,GAAG,MAAM,CAAC,OAAO;oBACnC,iCAAiC;oBACjC,IAAI,kBAAkB,SAAS,OAAO,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB;oBACtF,IAAI,gBAAgB,mBAAmB,QAAQ,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK;oBAC3F,IAAI,kBAAkB,eAAe;wBACjC,IAAI,WAAW,OAAO,WAAW;wBACjC,+BAA+B;wBAC/B,IAAI,aAAa,IAAI,8JAAA,CAAA,UAAM,CAAC,OAAO,OAAO,GAAG,SAAS,CAAC,IAAI,UAAW,WAAW,SAAS,MAAM,GAAG,MAAO,OAAO,eAAe,IAAI,qKAAA,CAAA,UAAa,CAAC,KAAK;wBACvJ,WAAW,cAAc,CAAC,OAAO,iBAAiB;wBAClD,OAAO;oBACX;oBACA,OAAO;gBACX,EACA,OAAO,KAAK;gBACR,YAAY;gBAChB;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,wBAAwB,SAAS,CAAC,KAAK,GAAG;QACtC,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC5E,IAAI,SAAS,GAAG,KAAK;gBACrB,OAAO,KAAK;YAChB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/CodaBarReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes CodaBar barcodes. </p>\n *\n * <AUTHOR> @dodobelieve\n * @see CodaBarReader\n */\nvar CodaBarReader = /** @class */ (function (_super) {\n    __extends(CodaBarReader, _super);\n    function CodaBarReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.CODA_BAR_CHAR_SET = {\n            nnnnnww: '0',\n            nnnnwwn: '1',\n            nnnwnnw: '2',\n            wwnnnnn: '3',\n            nnwnnwn: '4',\n            wnnnnwn: '5',\n            nwnnnnw: '6',\n            nwnnwnn: '7',\n            nwwnnnn: '8',\n            wnnwnnn: '9',\n            nnnwwnn: '-',\n            nnwwnnn: '$',\n            wnnnwnw: ':',\n            wnwnnnw: '/',\n            wnwnwnn: '.',\n            nnwwwww: '+',\n            nnwwnwn: 'A',\n            nwnwnnw: 'B',\n            nnnwnww: 'C',\n            nnnwwwn: 'D'\n        };\n        return _this;\n    }\n    CodaBarReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var validRowData = this.getValidRowData(row);\n        if (!validRowData)\n            throw new NotFoundException();\n        var retStr = this.codaBarDecodeRow(validRowData.row);\n        if (!retStr)\n            throw new NotFoundException();\n        return new Result(retStr, null, 0, [new ResultPoint(validRowData.left, rowNumber), new ResultPoint(validRowData.right, rowNumber)], BarcodeFormat.CODABAR, new Date().getTime());\n    };\n    /**\n     * converts bit array to valid data array(lengths of black bits and white bits)\n     * @param row bit array to convert\n     */\n    CodaBarReader.prototype.getValidRowData = function (row) {\n        var booleanArr = row.toArray();\n        var startIndex = booleanArr.indexOf(true);\n        if (startIndex === -1)\n            return null;\n        var lastIndex = booleanArr.lastIndexOf(true);\n        if (lastIndex <= startIndex)\n            return null;\n        booleanArr = booleanArr.slice(startIndex, lastIndex + 1);\n        var result = [];\n        var lastBit = booleanArr[0];\n        var bitLength = 1;\n        for (var i = 1; i < booleanArr.length; i++) {\n            if (booleanArr[i] === lastBit) {\n                bitLength++;\n            }\n            else {\n                lastBit = booleanArr[i];\n                result.push(bitLength);\n                bitLength = 1;\n            }\n        }\n        result.push(bitLength);\n        // CodaBar code data valid\n        if (result.length < 23 && (result.length + 1) % 8 !== 0)\n            return null;\n        return { row: result, left: startIndex, right: lastIndex };\n    };\n    /**\n     * decode codabar code\n     * @param row row to cecode\n     */\n    CodaBarReader.prototype.codaBarDecodeRow = function (row) {\n        var code = [];\n        var barThreshold = Math.ceil(row.reduce(function (pre, item) { return (pre + item) / 2; }, 0));\n        // Read one encoded character at a time.\n        while (row.length > 0) {\n            var seg = row.splice(0, 8).splice(0, 7);\n            var key = seg.map(function (len) { return (len < barThreshold ? 'n' : 'w'); }).join('');\n            if (this.CODA_BAR_CHAR_SET[key] === undefined)\n                return null;\n            code.push(this.CODA_BAR_CHAR_SET[key]);\n        }\n        var strCode = code.join('');\n        if (this.validCodaBarString(strCode))\n            return strCode;\n        return null;\n    };\n    /**\n     * check if the string is a CodaBar string\n     * @param src string to determine\n     */\n    CodaBarReader.prototype.validCodaBarString = function (src) {\n        var reg = /^[A-D].{1,}[A-D]$/;\n        return reg.test(src);\n    };\n    return CodaBarReader;\n}(OneDReader));\nexport default CodaBarReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AAlBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;AAOA;;;;;CAKC,GACD,IAAI,gBAA+B,SAAU,MAAM;IAC/C,UAAU,eAAe;IACzB,SAAS;QACL,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,iBAAiB,GAAG;YACtB,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;QACb;QACA,OAAO;IACX;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC/D,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC;QACxC,IAAI,CAAC,cACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG;QACnD,IAAI,CAAC,QACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,QAAQ,MAAM,GAAG;YAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,aAAa,IAAI,EAAE;YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,aAAa,KAAK,EAAE;SAAW,EAAE,qKAAA,CAAA,UAAa,CAAC,OAAO,EAAE,IAAI,OAAO,OAAO;IACjL;IACA;;;KAGC,GACD,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,GAAG;QACnD,IAAI,aAAa,IAAI,OAAO;QAC5B,IAAI,aAAa,WAAW,OAAO,CAAC;QACpC,IAAI,eAAe,CAAC,GAChB,OAAO;QACX,IAAI,YAAY,WAAW,WAAW,CAAC;QACvC,IAAI,aAAa,YACb,OAAO;QACX,aAAa,WAAW,KAAK,CAAC,YAAY,YAAY;QACtD,IAAI,SAAS,EAAE;QACf,IAAI,UAAU,UAAU,CAAC,EAAE;QAC3B,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,IAAI,UAAU,CAAC,EAAE,KAAK,SAAS;gBAC3B;YACJ,OACK;gBACD,UAAU,UAAU,CAAC,EAAE;gBACvB,OAAO,IAAI,CAAC;gBACZ,YAAY;YAChB;QACJ;QACA,OAAO,IAAI,CAAC;QACZ,0BAA0B;QAC1B,IAAI,OAAO,MAAM,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,IAAI,MAAM,GAClD,OAAO;QACX,OAAO;YAAE,KAAK;YAAQ,MAAM;YAAY,OAAO;QAAU;IAC7D;IACA;;;KAGC,GACD,cAAc,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACpD,IAAI,OAAO,EAAE;QACb,IAAI,eAAe,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;YAAI,OAAO,CAAC,MAAM,IAAI,IAAI;QAAG,GAAG;QAC3F,wCAAwC;QACxC,MAAO,IAAI,MAAM,GAAG,EAAG;YACnB,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG;YACrC,IAAI,MAAM,IAAI,GAAG,CAAC,SAAU,GAAG;gBAAI,OAAQ,MAAM,eAAe,MAAM;YAAM,GAAG,IAAI,CAAC;YACpF,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,WAChC,OAAO;YACX,KAAK,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI;QACzC;QACA,IAAI,UAAU,KAAK,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UACxB,OAAO;QACX,OAAO;IACX;IACA;;;KAGC,GACD,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QACtD,IAAI,MAAM;QACV,OAAO,IAAI,IAAI,CAAC;IACpB;IACA,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/AbstractRSSReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport MathUtils from '../../common/detector/MathUtils';\nimport NotFoundException from '../../NotFoundException';\nimport OneDReader from '../OneDReader';\n// import Integer from '../../util/Integer';\n// import Float from '../../util/Float';\nvar AbstractRSSReader = /** @class */ (function (_super) {\n    __extends(AbstractRSSReader, _super);\n    function AbstractRSSReader() {\n        var _this = _super.call(this) || this;\n        _this.decodeFinderCounters = new Int32Array(4);\n        _this.dataCharacterCounters = new Int32Array(8);\n        _this.oddRoundingErrors = new Array(4);\n        _this.evenRoundingErrors = new Array(4);\n        _this.oddCounts = new Array(_this.dataCharacterCounters.length / 2);\n        _this.evenCounts = new Array(_this.dataCharacterCounters.length / 2);\n        return _this;\n    }\n    AbstractRSSReader.prototype.getDecodeFinderCounters = function () {\n        return this.decodeFinderCounters;\n    };\n    AbstractRSSReader.prototype.getDataCharacterCounters = function () {\n        return this.dataCharacterCounters;\n    };\n    AbstractRSSReader.prototype.getOddRoundingErrors = function () {\n        return this.oddRoundingErrors;\n    };\n    AbstractRSSReader.prototype.getEvenRoundingErrors = function () {\n        return this.evenRoundingErrors;\n    };\n    AbstractRSSReader.prototype.getOddCounts = function () {\n        return this.oddCounts;\n    };\n    AbstractRSSReader.prototype.getEvenCounts = function () {\n        return this.evenCounts;\n    };\n    AbstractRSSReader.prototype.parseFinderValue = function (counters, finderPatterns) {\n        for (var value = 0; value < finderPatterns.length; value++) {\n            if (OneDReader.patternMatchVariance(counters, finderPatterns[value], AbstractRSSReader.MAX_INDIVIDUAL_VARIANCE) < AbstractRSSReader.MAX_AVG_VARIANCE) {\n                return value;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * @param array values to sum\n     * @return sum of values\n     * @deprecated call {@link MathUtils#sum(int[])}\n     */\n    AbstractRSSReader.count = function (array) {\n        return MathUtils.sum(new Int32Array(array));\n    };\n    AbstractRSSReader.increment = function (array, errors) {\n        var index = 0;\n        var biggestError = errors[0];\n        for (var i = 1; i < array.length; i++) {\n            if (errors[i] > biggestError) {\n                biggestError = errors[i];\n                index = i;\n            }\n        }\n        array[index]++;\n    };\n    AbstractRSSReader.decrement = function (array, errors) {\n        var index = 0;\n        var biggestError = errors[0];\n        for (var i = 1; i < array.length; i++) {\n            if (errors[i] < biggestError) {\n                biggestError = errors[i];\n                index = i;\n            }\n        }\n        array[index]--;\n    };\n    AbstractRSSReader.isFinderPattern = function (counters) {\n        var e_1, _a;\n        var firstTwoSum = counters[0] + counters[1];\n        var sum = firstTwoSum + counters[2] + counters[3];\n        var ratio = firstTwoSum / sum;\n        if (ratio >= AbstractRSSReader.MIN_FINDER_PATTERN_RATIO && ratio <= AbstractRSSReader.MAX_FINDER_PATTERN_RATIO) {\n            // passes ratio test in spec, but see if the counts are unreasonable\n            var minCounter = Number.MAX_SAFE_INTEGER;\n            var maxCounter = Number.MIN_SAFE_INTEGER;\n            try {\n                for (var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    if (counter > maxCounter) {\n                        maxCounter = counter;\n                    }\n                    if (counter < minCounter) {\n                        minCounter = counter;\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return maxCounter < 10 * minCounter;\n        }\n        return false;\n    };\n    AbstractRSSReader.MAX_AVG_VARIANCE = 0.2;\n    AbstractRSSReader.MAX_INDIVIDUAL_VARIANCE = 0.45;\n    AbstractRSSReader.MIN_FINDER_PATTERN_RATIO = 9.5 / 12.0;\n    AbstractRSSReader.MAX_FINDER_PATTERN_RATIO = 12.5 / 14.0;\n    return AbstractRSSReader;\n}(OneDReader));\nexport default AbstractRSSReader;\n"], "names": [], "mappings": ";;;AAwBA;AACA;AACA;AA1BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;AAIA,4CAA4C;AAC5C,wCAAwC;AACxC,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS;QACL,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,oBAAoB,GAAG,IAAI,WAAW;QAC5C,MAAM,qBAAqB,GAAG,IAAI,WAAW;QAC7C,MAAM,iBAAiB,GAAG,IAAI,MAAM;QACpC,MAAM,kBAAkB,GAAG,IAAI,MAAM;QACrC,MAAM,SAAS,GAAG,IAAI,MAAM,MAAM,qBAAqB,CAAC,MAAM,GAAG;QACjE,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,qBAAqB,CAAC,MAAM,GAAG;QAClE,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,uBAAuB,GAAG;QAClD,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,kBAAkB,SAAS,CAAC,wBAAwB,GAAG;QACnD,OAAO,IAAI,CAAC,qBAAqB;IACrC;IACA,kBAAkB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,kBAAkB,SAAS,CAAC,qBAAqB,GAAG;QAChD,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,kBAAkB,SAAS,CAAC,YAAY,GAAG;QACvC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,kBAAkB,SAAS,CAAC,aAAa,GAAG;QACxC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,QAAQ,EAAE,cAAc;QAC7E,IAAK,IAAI,QAAQ,GAAG,QAAQ,eAAe,MAAM,EAAE,QAAS;YACxD,IAAI,0KAAA,CAAA,UAAU,CAAC,oBAAoB,CAAC,UAAU,cAAc,CAAC,MAAM,EAAE,kBAAkB,uBAAuB,IAAI,kBAAkB,gBAAgB,EAAE;gBAClJ,OAAO;YACX;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA;;;;KAIC,GACD,kBAAkB,KAAK,GAAG,SAAU,KAAK;QACrC,OAAO,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW;IACxC;IACA,kBAAkB,SAAS,GAAG,SAAU,KAAK,EAAE,MAAM;QACjD,IAAI,QAAQ;QACZ,IAAI,eAAe,MAAM,CAAC,EAAE;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,MAAM,CAAC,EAAE,GAAG,cAAc;gBAC1B,eAAe,MAAM,CAAC,EAAE;gBACxB,QAAQ;YACZ;QACJ;QACA,KAAK,CAAC,MAAM;IAChB;IACA,kBAAkB,SAAS,GAAG,SAAU,KAAK,EAAE,MAAM;QACjD,IAAI,QAAQ;QACZ,IAAI,eAAe,MAAM,CAAC,EAAE;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,MAAM,CAAC,EAAE,GAAG,cAAc;gBAC1B,eAAe,MAAM,CAAC,EAAE;gBACxB,QAAQ;YACZ;QACJ;QACA,KAAK,CAAC,MAAM;IAChB;IACA,kBAAkB,eAAe,GAAG,SAAU,QAAQ;QAClD,IAAI,KAAK;QACT,IAAI,cAAc,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QAC3C,IAAI,MAAM,cAAc,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QACjD,IAAI,QAAQ,cAAc;QAC1B,IAAI,SAAS,kBAAkB,wBAAwB,IAAI,SAAS,kBAAkB,wBAAwB,EAAE;YAC5G,oEAAoE;YACpE,IAAI,aAAa,OAAO,gBAAgB;YACxC,IAAI,aAAa,OAAO,gBAAgB;YACxC,IAAI;gBACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9H,IAAI,UAAU,aAAa,KAAK;oBAChC,IAAI,UAAU,YAAY;wBACtB,aAAa;oBACjB;oBACA,IAAI,UAAU,YAAY;wBACtB,aAAa;oBACjB;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,OAAO,aAAa,KAAK;QAC7B;QACA,OAAO;IACX;IACA,kBAAkB,gBAAgB,GAAG;IACrC,kBAAkB,uBAAuB,GAAG;IAC5C,kBAAkB,wBAAwB,GAAG,MAAM;IACnD,kBAAkB,wBAAwB,GAAG,OAAO;IACpD,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/DataCharacter.js"], "sourcesContent": ["var DataCharacter = /** @class */ (function () {\n    function DataCharacter(value, checksumPortion) {\n        this.value = value;\n        this.checksumPortion = checksumPortion;\n    }\n    DataCharacter.prototype.getValue = function () {\n        return this.value;\n    };\n    DataCharacter.prototype.getChecksumPortion = function () {\n        return this.checksumPortion;\n    };\n    DataCharacter.prototype.toString = function () {\n        return this.value + '(' + this.checksumPortion + ')';\n    };\n    DataCharacter.prototype.equals = function (o) {\n        if (!(o instanceof DataCharacter)) {\n            return false;\n        }\n        var that = o;\n        return this.value === that.value && this.checksumPortion === that.checksumPortion;\n    };\n    DataCharacter.prototype.hashCode = function () {\n        return this.value ^ this.checksumPortion;\n    };\n    return DataCharacter;\n}());\nexport default DataCharacter;\n"], "names": [], "mappings": ";;;AAAA,IAAI,gBAA+B;IAC/B,SAAS,cAAc,KAAK,EAAE,eAAe;QACzC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,cAAc,SAAS,CAAC,kBAAkB,GAAG;QACzC,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,GAAG;IACrD;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACxC,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;YAC/B,OAAO;QACX;QACA,IAAI,OAAO;QACX,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,eAAe;IACrF;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe;IAC5C;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/FinderPattern.js"], "sourcesContent": ["import ResultPoint from '../../ResultPoint';\nvar FinderPattern = /** @class */ (function () {\n    function FinderPattern(value, startEnd, start, end, rowNumber) {\n        this.value = value;\n        this.startEnd = startEnd;\n        this.value = value;\n        this.startEnd = startEnd;\n        this.resultPoints = new Array();\n        this.resultPoints.push(new ResultPoint(start, rowNumber));\n        this.resultPoints.push(new ResultPoint(end, rowNumber));\n    }\n    FinderPattern.prototype.getValue = function () {\n        return this.value;\n    };\n    FinderPattern.prototype.getStartEnd = function () {\n        return this.startEnd;\n    };\n    FinderPattern.prototype.getResultPoints = function () {\n        return this.resultPoints;\n    };\n    FinderPattern.prototype.equals = function (o) {\n        if (!(o instanceof FinderPattern)) {\n            return false;\n        }\n        var that = o;\n        return this.value === that.value;\n    };\n    FinderPattern.prototype.hashCode = function () {\n        return this.value;\n    };\n    return FinderPattern;\n}());\nexport default FinderPattern;\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,gBAA+B;IAC/B,SAAS,cAAc,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS;QACzD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO;QAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,KAAK;IAChD;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,cAAc,SAAS,CAAC,WAAW,GAAG;QAClC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,cAAc,SAAS,CAAC,eAAe,GAAG;QACtC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACxC,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;YAC/B,OAAO;QACX;QACA,IAAI,OAAO;QACX,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK;IACpC;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/RSSUtils.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * RSS util functions.\n */\nvar RSSUtils = /** @class */ (function () {\n    function RSSUtils() {\n    }\n    RSSUtils.getRSSvalue = function (widths, maxWidth, noNarrow) {\n        var e_1, _a;\n        var n = 0;\n        try {\n            for (var widths_1 = __values(widths), widths_1_1 = widths_1.next(); !widths_1_1.done; widths_1_1 = widths_1.next()) {\n                var width = widths_1_1.value;\n                n += width;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (widths_1_1 && !widths_1_1.done && (_a = widths_1.return)) _a.call(widths_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var val = 0;\n        var narrowMask = 0;\n        var elements = widths.length;\n        for (var bar = 0; bar < elements - 1; bar++) {\n            var elmWidth = void 0;\n            for (elmWidth = 1, narrowMask |= 1 << bar; elmWidth < widths[bar]; elmWidth++, narrowMask &= ~(1 << bar)) {\n                var subVal = RSSUtils.combins(n - elmWidth - 1, elements - bar - 2);\n                if (noNarrow && (narrowMask === 0) && (n - elmWidth - (elements - bar - 1) >= elements - bar - 1)) {\n                    subVal -= RSSUtils.combins(n - elmWidth - (elements - bar), elements - bar - 2);\n                }\n                if (elements - bar - 1 > 1) {\n                    var lessVal = 0;\n                    for (var mxwElement = n - elmWidth - (elements - bar - 2); mxwElement > maxWidth; mxwElement--) {\n                        lessVal += RSSUtils.combins(n - elmWidth - mxwElement - 1, elements - bar - 3);\n                    }\n                    subVal -= lessVal * (elements - 1 - bar);\n                }\n                else if (n - elmWidth > maxWidth) {\n                    subVal--;\n                }\n                val += subVal;\n            }\n            n -= elmWidth;\n        }\n        return val;\n    };\n    RSSUtils.combins = function (n, r) {\n        var maxDenom;\n        var minDenom;\n        if (n - r > r) {\n            minDenom = r;\n            maxDenom = n - r;\n        }\n        else {\n            minDenom = n - r;\n            maxDenom = r;\n        }\n        var val = 1;\n        var j = 1;\n        for (var i = n; i > maxDenom; i--) {\n            val *= i;\n            if (j <= minDenom) {\n                val /= j;\n                j++;\n            }\n        }\n        while ((j <= minDenom)) {\n            val /= j;\n            j++;\n        }\n        return val;\n    };\n    return RSSUtils;\n}());\nexport default RSSUtils;\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;AACA;;CAEC,GACD,IAAI,WAA0B;IAC1B,SAAS,YACT;IACA,SAAS,WAAW,GAAG,SAAU,MAAM,EAAE,QAAQ,EAAE,QAAQ;QACvD,IAAI,KAAK;QACT,IAAI,IAAI;QACR,IAAI;YACA,IAAK,IAAI,WAAW,SAAS,SAAS,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;gBAChH,IAAI,QAAQ,WAAW,KAAK;gBAC5B,KAAK;YACT;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;YAC1E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,MAAM;QACV,IAAI,aAAa;QACjB,IAAI,WAAW,OAAO,MAAM;QAC5B,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,GAAG,MAAO;YACzC,IAAI,WAAW,KAAK;YACpB,IAAK,WAAW,GAAG,cAAc,KAAK,KAAK,WAAW,MAAM,CAAC,IAAI,EAAE,YAAY,cAAc,CAAC,CAAC,KAAK,GAAG,EAAG;gBACtG,IAAI,SAAS,SAAS,OAAO,CAAC,IAAI,WAAW,GAAG,WAAW,MAAM;gBACjE,IAAI,YAAa,eAAe,KAAO,IAAI,WAAW,CAAC,WAAW,MAAM,CAAC,KAAK,WAAW,MAAM,GAAI;oBAC/F,UAAU,SAAS,OAAO,CAAC,IAAI,WAAW,CAAC,WAAW,GAAG,GAAG,WAAW,MAAM;gBACjF;gBACA,IAAI,WAAW,MAAM,IAAI,GAAG;oBACxB,IAAI,UAAU;oBACd,IAAK,IAAI,aAAa,IAAI,WAAW,CAAC,WAAW,MAAM,CAAC,GAAG,aAAa,UAAU,aAAc;wBAC5F,WAAW,SAAS,OAAO,CAAC,IAAI,WAAW,aAAa,GAAG,WAAW,MAAM;oBAChF;oBACA,UAAU,UAAU,CAAC,WAAW,IAAI,GAAG;gBAC3C,OACK,IAAI,IAAI,WAAW,UAAU;oBAC9B;gBACJ;gBACA,OAAO;YACX;YACA,KAAK;QACT;QACA,OAAO;IACX;IACA,SAAS,OAAO,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,IAAI,GAAG;YACX,WAAW;YACX,WAAW,IAAI;QACnB,OACK;YACD,WAAW,IAAI;YACf,WAAW;QACf;QACA,IAAI,MAAM;QACV,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YAC/B,OAAO;YACP,IAAI,KAAK,UAAU;gBACf,OAAO;gBACP;YACJ;QACJ;QACA,MAAQ,KAAK,SAAW;YACpB,OAAO;YACP;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/BitArrayBuilder.js"], "sourcesContent": ["import BitArray from '../../../common/BitArray';\nvar BitArrayBuilder = /** @class */ (function () {\n    function BitArrayBuilder() {\n    }\n    BitArrayBuilder.buildBitArray = function (pairs) {\n        var charNumber = pairs.length * 2 - 1;\n        if (pairs[pairs.length - 1].getRightChar() == null) {\n            charNumber -= 1;\n        }\n        var size = 12 * charNumber;\n        var binary = new BitArray(size);\n        var accPos = 0;\n        var firstPair = pairs[0];\n        var firstValue = firstPair.getRightChar().getValue();\n        for (var i = 11; i >= 0; --i) {\n            if ((firstValue & (1 << i)) !== 0) {\n                binary.set(accPos);\n            }\n            accPos++;\n        }\n        for (var i = 1; i < pairs.length; ++i) {\n            var currentPair = pairs[i];\n            var leftValue = currentPair.getLeftChar().getValue();\n            for (var j = 11; j >= 0; --j) {\n                if ((leftValue & (1 << j)) !== 0) {\n                    binary.set(accPos);\n                }\n                accPos++;\n            }\n            if (currentPair.getRightChar() !== null) {\n                var rightValue = currentPair.getRightChar().getValue();\n                for (var j = 11; j >= 0; --j) {\n                    if ((rightValue & (1 << j)) !== 0) {\n                        binary.set(accPos);\n                    }\n                    accPos++;\n                }\n            }\n        }\n        return binary;\n    };\n    return BitArrayBuilder;\n}());\nexport default BitArrayBuilder;\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,kBAAiC;IACjC,SAAS,mBACT;IACA,gBAAgB,aAAa,GAAG,SAAU,KAAK;QAC3C,IAAI,aAAa,MAAM,MAAM,GAAG,IAAI;QACpC,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,MAAM,MAAM;YAChD,cAAc;QAClB;QACA,IAAI,OAAO,KAAK;QAChB,IAAI,SAAS,IAAI,0KAAA,CAAA,UAAQ,CAAC;QAC1B,IAAI,SAAS;QACb,IAAI,YAAY,KAAK,CAAC,EAAE;QACxB,IAAI,aAAa,UAAU,YAAY,GAAG,QAAQ;QAClD,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,EAAG;YAC1B,IAAI,CAAC,aAAc,KAAK,CAAE,MAAM,GAAG;gBAC/B,OAAO,GAAG,CAAC;YACf;YACA;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,IAAI,cAAc,KAAK,CAAC,EAAE;YAC1B,IAAI,YAAY,YAAY,WAAW,GAAG,QAAQ;YAClD,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,EAAG;gBAC1B,IAAI,CAAC,YAAa,KAAK,CAAE,MAAM,GAAG;oBAC9B,OAAO,GAAG,CAAC;gBACf;gBACA;YACJ;YACA,IAAI,YAAY,YAAY,OAAO,MAAM;gBACrC,IAAI,aAAa,YAAY,YAAY,GAAG,QAAQ;gBACpD,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,EAAG;oBAC1B,IAAI,CAAC,aAAc,KAAK,CAAE,MAAM,GAAG;wBAC/B,OAAO,GAAG,CAAC;oBACf;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/BlockParsedResult.js"], "sourcesContent": ["var BlockParsedResult = /** @class */ (function () {\n    function BlockParsedResult(finished, decodedInformation) {\n        if (decodedInformation) {\n            this.decodedInformation = null;\n        }\n        else {\n            this.finished = finished;\n            this.decodedInformation = decodedInformation;\n        }\n    }\n    BlockParsedResult.prototype.getDecodedInformation = function () {\n        return this.decodedInformation;\n    };\n    BlockParsedResult.prototype.isFinished = function () {\n        return this.finished;\n    };\n    return BlockParsedResult;\n}());\nexport default BlockParsedResult;\n"], "names": [], "mappings": ";;;AAAA,IAAI,oBAAmC;IACnC,SAAS,kBAAkB,QAAQ,EAAE,kBAAkB;QACnD,IAAI,oBAAoB;YACpB,IAAI,CAAC,kBAAkB,GAAG;QAC9B,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,kBAAkB,GAAG;QAC9B;IACJ;IACA,kBAAkB,SAAS,CAAC,qBAAqB,GAAG;QAChD,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,kBAAkB,SAAS,CAAC,UAAU,GAAG;QACrC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js"], "sourcesContent": ["var DecodedObject = /** @class */ (function () {\n    function DecodedObject(newPosition) {\n        this.newPosition = newPosition;\n    }\n    DecodedObject.prototype.getNewPosition = function () {\n        return this.newPosition;\n    };\n    return DecodedObject;\n}());\nexport default DecodedObject;\n"], "names": [], "mappings": ";;;AAAA,IAAI,gBAA+B;IAC/B,SAAS,cAAc,WAAW;QAC9B,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,cAAc,SAAS,CAAC,cAAc,GAAG;QACrC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/DecodedChar.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DecodedObject from './DecodedObject';\nvar DecodedChar = /** @class */ (function (_super) {\n    __extends(DecodedChar, _super);\n    function DecodedChar(newPosition, value) {\n        var _this = _super.call(this, newPosition) || this;\n        _this.value = value;\n        return _this;\n    }\n    DecodedChar.prototype.getValue = function () {\n        return this.value;\n    };\n    DecodedChar.prototype.isFNC1 = function () {\n        return this.value === DecodedChar.FNC1;\n    };\n    DecodedChar.FNC1 = '$';\n    return DecodedChar;\n}(DecodedObject));\nexport default DecodedChar;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS,YAAY,WAAW,EAAE,KAAK;QACnC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;QAClD,MAAM,KAAK,GAAG;QACd,OAAO;IACX;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,YAAY,SAAS,CAAC,MAAM,GAAG;QAC3B,OAAO,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI;IAC1C;IACA,YAAY,IAAI,GAAG;IACnB,OAAO;AACX,EAAE,4MAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/DecodedInformation.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DecodedObject from './DecodedObject';\nvar DecodedInformation = /** @class */ (function (_super) {\n    __extends(DecodedInformation, _super);\n    function DecodedInformation(newPosition, newString, remainingValue) {\n        var _this = _super.call(this, newPosition) || this;\n        if (remainingValue) {\n            _this.remaining = true;\n            _this.remainingValue = _this.remainingValue;\n        }\n        else {\n            _this.remaining = false;\n            _this.remainingValue = 0;\n        }\n        _this.newString = newString;\n        return _this;\n    }\n    DecodedInformation.prototype.getNewString = function () {\n        return this.newString;\n    };\n    DecodedInformation.prototype.isRemaining = function () {\n        return this.remaining;\n    };\n    DecodedInformation.prototype.getRemainingValue = function () {\n        return this.remainingValue;\n    };\n    return DecodedInformation;\n}(DecodedObject));\nexport default DecodedInformation;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,qBAAoC,SAAU,MAAM;IACpD,UAAU,oBAAoB;IAC9B,SAAS,mBAAmB,WAAW,EAAE,SAAS,EAAE,cAAc;QAC9D,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;QAClD,IAAI,gBAAgB;YAChB,MAAM,SAAS,GAAG;YAClB,MAAM,cAAc,GAAG,MAAM,cAAc;QAC/C,OACK;YACD,MAAM,SAAS,GAAG;YAClB,MAAM,cAAc,GAAG;QAC3B;QACA,MAAM,SAAS,GAAG;QAClB,OAAO;IACX;IACA,mBAAmB,SAAS,CAAC,YAAY,GAAG;QACxC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,mBAAmB,SAAS,CAAC,WAAW,GAAG;QACvC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,mBAAmB,SAAS,CAAC,iBAAiB,GAAG;QAC7C,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,OAAO;AACX,EAAE,4MAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/DecodedNumeric.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport FormatException from '../../../../FormatException';\nimport DecodedObject from './DecodedObject';\nvar DecodedNumeric = /** @class */ (function (_super) {\n    __extends(DecodedNumeric, _super);\n    function DecodedNumeric(newPosition, firstDigit, secondDigit) {\n        var _this = _super.call(this, newPosition) || this;\n        if (firstDigit < 0 || firstDigit > 10 || secondDigit < 0 || secondDigit > 10) {\n            throw new FormatException();\n        }\n        _this.firstDigit = firstDigit;\n        _this.secondDigit = secondDigit;\n        return _this;\n    }\n    DecodedNumeric.prototype.getFirstDigit = function () {\n        return this.firstDigit;\n    };\n    DecodedNumeric.prototype.getSecondDigit = function () {\n        return this.secondDigit;\n    };\n    DecodedNumeric.prototype.getValue = function () {\n        return this.firstDigit * 10 + this.secondDigit;\n    };\n    DecodedNumeric.prototype.isFirstDigitFNC1 = function () {\n        return this.firstDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.prototype.isSecondDigitFNC1 = function () {\n        return this.secondDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.prototype.isAnyFNC1 = function () {\n        return this.firstDigit === DecodedNumeric.FNC1 || this.secondDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.FNC1 = 10;\n    return DecodedNumeric;\n}(DecodedObject));\nexport default DecodedNumeric;\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,iBAAgC,SAAU,MAAM;IAChD,UAAU,gBAAgB;IAC1B,SAAS,eAAe,WAAW,EAAE,UAAU,EAAE,WAAW;QACxD,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;QAClD,IAAI,aAAa,KAAK,aAAa,MAAM,cAAc,KAAK,cAAc,IAAI;YAC1E,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,MAAM,UAAU,GAAG;QACnB,MAAM,WAAW,GAAG;QACpB,OAAO;IACX;IACA,eAAe,SAAS,CAAC,aAAa,GAAG;QACrC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACtC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,eAAe,SAAS,CAAC,QAAQ,GAAG;QAChC,OAAO,IAAI,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,WAAW;IAClD;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG;QACxC,OAAO,IAAI,CAAC,UAAU,KAAK,eAAe,IAAI;IAClD;IACA,eAAe,SAAS,CAAC,iBAAiB,GAAG;QACzC,OAAO,IAAI,CAAC,WAAW,KAAK,eAAe,IAAI;IACnD;IACA,eAAe,SAAS,CAAC,SAAS,GAAG;QACjC,OAAO,IAAI,CAAC,UAAU,KAAK,eAAe,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,eAAe,IAAI;IAC9F;IACA,eAAe,IAAI,GAAG;IACtB,OAAO;AACX,EAAE,4MAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/FieldParser.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport NotFoundException from '../../../../NotFoundException';\nvar FieldParser = /** @class */ (function () {\n    function FieldParser() {\n    }\n    FieldParser.parseFieldsInGeneralPurpose = function (rawInformation) {\n        var e_1, _a, e_2, _b, e_3, _c, e_4, _d;\n        if (!rawInformation) {\n            return null;\n        }\n        // Processing 2-digit AIs\n        if (rawInformation.length < 2) {\n            throw new NotFoundException();\n        }\n        var firstTwoDigits = rawInformation.substring(0, 2);\n        try {\n            for (var _e = __values(FieldParser.TWO_DIGIT_DATA_LENGTH), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var dataLength = _f.value;\n                if (dataLength[0] === firstTwoDigits) {\n                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {\n                        return FieldParser.processVariableAI(2, dataLength[2], rawInformation);\n                    }\n                    return FieldParser.processFixedAI(2, dataLength[1], rawInformation);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (rawInformation.length < 3) {\n            throw new NotFoundException();\n        }\n        var firstThreeDigits = rawInformation.substring(0, 3);\n        try {\n            for (var _g = __values(FieldParser.THREE_DIGIT_DATA_LENGTH), _h = _g.next(); !_h.done; _h = _g.next()) {\n                var dataLength = _h.value;\n                if (dataLength[0] === firstThreeDigits) {\n                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {\n                        return FieldParser.processVariableAI(3, dataLength[2], rawInformation);\n                    }\n                    return FieldParser.processFixedAI(3, dataLength[1], rawInformation);\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        try {\n            for (var _j = __values(FieldParser.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH), _k = _j.next(); !_k.done; _k = _j.next()) {\n                var dataLength = _k.value;\n                if (dataLength[0] === firstThreeDigits) {\n                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {\n                        return FieldParser.processVariableAI(4, dataLength[2], rawInformation);\n                    }\n                    return FieldParser.processFixedAI(4, dataLength[1], rawInformation);\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        if (rawInformation.length < 4) {\n            throw new NotFoundException();\n        }\n        var firstFourDigits = rawInformation.substring(0, 4);\n        try {\n            for (var _l = __values(FieldParser.FOUR_DIGIT_DATA_LENGTH), _m = _l.next(); !_m.done; _m = _l.next()) {\n                var dataLength = _m.value;\n                if (dataLength[0] === firstFourDigits) {\n                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {\n                        return FieldParser.processVariableAI(4, dataLength[2], rawInformation);\n                    }\n                    return FieldParser.processFixedAI(4, dataLength[1], rawInformation);\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        throw new NotFoundException();\n    };\n    FieldParser.processFixedAI = function (aiSize, fieldSize, rawInformation) {\n        if (rawInformation.length < aiSize) {\n            throw new NotFoundException();\n        }\n        var ai = rawInformation.substring(0, aiSize);\n        if (rawInformation.length < aiSize + fieldSize) {\n            throw new NotFoundException();\n        }\n        var field = rawInformation.substring(aiSize, aiSize + fieldSize);\n        var remaining = rawInformation.substring(aiSize + fieldSize);\n        var result = '(' + ai + ')' + field;\n        var parsedAI = FieldParser.parseFieldsInGeneralPurpose(remaining);\n        return parsedAI == null ? result : result + parsedAI;\n    };\n    FieldParser.processVariableAI = function (aiSize, variableFieldSize, rawInformation) {\n        var ai = rawInformation.substring(0, aiSize);\n        var maxSize;\n        if (rawInformation.length < aiSize + variableFieldSize) {\n            maxSize = rawInformation.length;\n        }\n        else {\n            maxSize = aiSize + variableFieldSize;\n        }\n        var field = rawInformation.substring(aiSize, maxSize);\n        var remaining = rawInformation.substring(maxSize);\n        var result = '(' + ai + ')' + field;\n        var parsedAI = FieldParser.parseFieldsInGeneralPurpose(remaining);\n        return parsedAI == null ? result : result + parsedAI;\n    };\n    FieldParser.VARIABLE_LENGTH = [];\n    FieldParser.TWO_DIGIT_DATA_LENGTH = [\n        ['00', 18],\n        ['01', 14],\n        ['02', 14],\n        ['10', FieldParser.VARIABLE_LENGTH, 20],\n        ['11', 6],\n        ['12', 6],\n        ['13', 6],\n        ['15', 6],\n        ['17', 6],\n        ['20', 2],\n        ['21', FieldParser.VARIABLE_LENGTH, 20],\n        ['22', FieldParser.VARIABLE_LENGTH, 29],\n        ['30', FieldParser.VARIABLE_LENGTH, 8],\n        ['37', FieldParser.VARIABLE_LENGTH, 8],\n        // internal company codes\n        ['90', FieldParser.VARIABLE_LENGTH, 30],\n        ['91', FieldParser.VARIABLE_LENGTH, 30],\n        ['92', FieldParser.VARIABLE_LENGTH, 30],\n        ['93', FieldParser.VARIABLE_LENGTH, 30],\n        ['94', FieldParser.VARIABLE_LENGTH, 30],\n        ['95', FieldParser.VARIABLE_LENGTH, 30],\n        ['96', FieldParser.VARIABLE_LENGTH, 30],\n        ['97', FieldParser.VARIABLE_LENGTH, 3],\n        ['98', FieldParser.VARIABLE_LENGTH, 30],\n        ['99', FieldParser.VARIABLE_LENGTH, 30],\n    ];\n    FieldParser.THREE_DIGIT_DATA_LENGTH = [\n        // Same format as above\n        ['240', FieldParser.VARIABLE_LENGTH, 30],\n        ['241', FieldParser.VARIABLE_LENGTH, 30],\n        ['242', FieldParser.VARIABLE_LENGTH, 6],\n        ['250', FieldParser.VARIABLE_LENGTH, 30],\n        ['251', FieldParser.VARIABLE_LENGTH, 30],\n        ['253', FieldParser.VARIABLE_LENGTH, 17],\n        ['254', FieldParser.VARIABLE_LENGTH, 20],\n        ['400', FieldParser.VARIABLE_LENGTH, 30],\n        ['401', FieldParser.VARIABLE_LENGTH, 30],\n        ['402', 17],\n        ['403', FieldParser.VARIABLE_LENGTH, 30],\n        ['410', 13],\n        ['411', 13],\n        ['412', 13],\n        ['413', 13],\n        ['414', 13],\n        ['420', FieldParser.VARIABLE_LENGTH, 20],\n        ['421', FieldParser.VARIABLE_LENGTH, 15],\n        ['422', 3],\n        ['423', FieldParser.VARIABLE_LENGTH, 15],\n        ['424', 3],\n        ['425', 3],\n        ['426', 3],\n    ];\n    FieldParser.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH = [\n        // Same format as above\n        ['310', 6],\n        ['311', 6],\n        ['312', 6],\n        ['313', 6],\n        ['314', 6],\n        ['315', 6],\n        ['316', 6],\n        ['320', 6],\n        ['321', 6],\n        ['322', 6],\n        ['323', 6],\n        ['324', 6],\n        ['325', 6],\n        ['326', 6],\n        ['327', 6],\n        ['328', 6],\n        ['329', 6],\n        ['330', 6],\n        ['331', 6],\n        ['332', 6],\n        ['333', 6],\n        ['334', 6],\n        ['335', 6],\n        ['336', 6],\n        ['340', 6],\n        ['341', 6],\n        ['342', 6],\n        ['343', 6],\n        ['344', 6],\n        ['345', 6],\n        ['346', 6],\n        ['347', 6],\n        ['348', 6],\n        ['349', 6],\n        ['350', 6],\n        ['351', 6],\n        ['352', 6],\n        ['353', 6],\n        ['354', 6],\n        ['355', 6],\n        ['356', 6],\n        ['357', 6],\n        ['360', 6],\n        ['361', 6],\n        ['362', 6],\n        ['363', 6],\n        ['364', 6],\n        ['365', 6],\n        ['366', 6],\n        ['367', 6],\n        ['368', 6],\n        ['369', 6],\n        ['390', FieldParser.VARIABLE_LENGTH, 15],\n        ['391', FieldParser.VARIABLE_LENGTH, 18],\n        ['392', FieldParser.VARIABLE_LENGTH, 15],\n        ['393', FieldParser.VARIABLE_LENGTH, 18],\n        ['703', FieldParser.VARIABLE_LENGTH, 30],\n    ];\n    FieldParser.FOUR_DIGIT_DATA_LENGTH = [\n        // Same format as above\n        ['7001', 13],\n        ['7002', FieldParser.VARIABLE_LENGTH, 30],\n        ['7003', 10],\n        ['8001', 14],\n        ['8002', FieldParser.VARIABLE_LENGTH, 20],\n        ['8003', FieldParser.VARIABLE_LENGTH, 30],\n        ['8004', FieldParser.VARIABLE_LENGTH, 30],\n        ['8005', 6],\n        ['8006', 18],\n        ['8007', FieldParser.VARIABLE_LENGTH, 30],\n        ['8008', FieldParser.VARIABLE_LENGTH, 12],\n        ['8018', 18],\n        ['8020', FieldParser.VARIABLE_LENGTH, 25],\n        ['8100', 6],\n        ['8101', 10],\n        ['8102', 2],\n        ['8110', FieldParser.VARIABLE_LENGTH, 70],\n        ['8200', FieldParser.VARIABLE_LENGTH, 70],\n    ];\n    return FieldParser;\n}());\nexport default FieldParser;\n"], "names": [], "mappings": ";;;AAWA;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;AAEA,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA,YAAY,2BAA2B,GAAG,SAAU,cAAc;QAC9D,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QACpC,IAAI,CAAC,gBAAgB;YACjB,OAAO;QACX;QACA,yBAAyB;QACzB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC3B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,iBAAiB,eAAe,SAAS,CAAC,GAAG;QACjD,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,YAAY,qBAAqB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACjG,IAAI,aAAa,GAAG,KAAK;gBACzB,IAAI,UAAU,CAAC,EAAE,KAAK,gBAAgB;oBAClC,IAAI,UAAU,CAAC,EAAE,KAAK,YAAY,eAAe,EAAE;wBAC/C,OAAO,YAAY,iBAAiB,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,YAAY,cAAc,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;gBACxD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,eAAe,MAAM,GAAG,GAAG;YAC3B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,mBAAmB,eAAe,SAAS,CAAC,GAAG;QACnD,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,YAAY,uBAAuB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACnG,IAAI,aAAa,GAAG,KAAK;gBACzB,IAAI,UAAU,CAAC,EAAE,KAAK,kBAAkB;oBACpC,IAAI,UAAU,CAAC,EAAE,KAAK,YAAY,eAAe,EAAE;wBAC/C,OAAO,YAAY,iBAAiB,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,YAAY,cAAc,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;gBACxD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,YAAY,kCAAkC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC9G,IAAI,aAAa,GAAG,KAAK;gBACzB,IAAI,UAAU,CAAC,EAAE,KAAK,kBAAkB;oBACpC,IAAI,UAAU,CAAC,EAAE,KAAK,YAAY,eAAe,EAAE;wBAC/C,OAAO,YAAY,iBAAiB,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,YAAY,cAAc,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;gBACxD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,eAAe,MAAM,GAAG,GAAG;YAC3B,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,kBAAkB,eAAe,SAAS,CAAC,GAAG;QAClD,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,YAAY,sBAAsB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAClG,IAAI,aAAa,GAAG,KAAK;gBACzB,IAAI,UAAU,CAAC,EAAE,KAAK,iBAAiB;oBACnC,IAAI,UAAU,CAAC,EAAE,KAAK,YAAY,eAAe,EAAE;wBAC/C,OAAO,YAAY,iBAAiB,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,YAAY,cAAc,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE;gBACxD;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,YAAY,cAAc,GAAG,SAAU,MAAM,EAAE,SAAS,EAAE,cAAc;QACpE,IAAI,eAAe,MAAM,GAAG,QAAQ;YAChC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,KAAK,eAAe,SAAS,CAAC,GAAG;QACrC,IAAI,eAAe,MAAM,GAAG,SAAS,WAAW;YAC5C,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,QAAQ,eAAe,SAAS,CAAC,QAAQ,SAAS;QACtD,IAAI,YAAY,eAAe,SAAS,CAAC,SAAS;QAClD,IAAI,SAAS,MAAM,KAAK,MAAM;QAC9B,IAAI,WAAW,YAAY,2BAA2B,CAAC;QACvD,OAAO,YAAY,OAAO,SAAS,SAAS;IAChD;IACA,YAAY,iBAAiB,GAAG,SAAU,MAAM,EAAE,iBAAiB,EAAE,cAAc;QAC/E,IAAI,KAAK,eAAe,SAAS,CAAC,GAAG;QACrC,IAAI;QACJ,IAAI,eAAe,MAAM,GAAG,SAAS,mBAAmB;YACpD,UAAU,eAAe,MAAM;QACnC,OACK;YACD,UAAU,SAAS;QACvB;QACA,IAAI,QAAQ,eAAe,SAAS,CAAC,QAAQ;QAC7C,IAAI,YAAY,eAAe,SAAS,CAAC;QACzC,IAAI,SAAS,MAAM,KAAK,MAAM;QAC9B,IAAI,WAAW,YAAY,2BAA2B,CAAC;QACvD,OAAO,YAAY,OAAO,SAAS,SAAS;IAChD;IACA,YAAY,eAAe,GAAG,EAAE;IAChC,YAAY,qBAAqB,GAAG;QAChC;YAAC;YAAM;SAAG;QACV;YAAC;YAAM;SAAG;QACV;YAAC;YAAM;SAAG;QACV;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM;SAAE;QACT;YAAC;YAAM;SAAE;QACT;YAAC;YAAM;SAAE;QACT;YAAC;YAAM;SAAE;QACT;YAAC;YAAM;SAAE;QACT;YAAC;YAAM;SAAE;QACT;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAE;QACtC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAE;QACtC,yBAAyB;QACzB;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAE;QACtC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;QACvC;YAAC;YAAM,YAAY,eAAe;YAAE;SAAG;KAC1C;IACD,YAAY,uBAAuB,GAAG;QAClC,uBAAuB;QACvB;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAE;QACvC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO;SAAG;QACX;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO;SAAG;QACX;YAAC;YAAO;SAAG;QACX;YAAC;YAAO;SAAG;QACX;YAAC;YAAO;SAAG;QACX;YAAC;YAAO;SAAG;QACX;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO;SAAE;QACV;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;KACb;IACD,YAAY,kCAAkC,GAAG;QAC7C,uBAAuB;QACvB;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO;SAAE;QACV;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;QACxC;YAAC;YAAO,YAAY,eAAe;YAAE;SAAG;KAC3C;IACD,YAAY,sBAAsB,GAAG;QACjC,uBAAuB;QACvB;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ;SAAE;QACX;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ;SAAE;QACX;YAAC;YAAQ;SAAG;QACZ;YAAC;YAAQ;SAAE;QACX;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;QACzC;YAAC;YAAQ,YAAY,eAAe;YAAE;SAAG;KAC5C;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/GeneralAppIdDecoder.js"], "sourcesContent": ["import FormatException from '../../../../FormatException';\nimport IllegalStateException from '../../../../IllegalStateException';\nimport StringBuilder from '../../../../util/StringBuilder';\nimport BlockParsedResult from './BlockParsedResult';\nimport DecodedChar from './DecodedChar';\nimport DecodedInformation from './DecodedInformation';\nimport DecodedNumeric from './DecodedNumeric';\nimport FieldParser from './FieldParser';\nvar GeneralAppIdDecoder = /** @class */ (function () {\n    function GeneralAppIdDecoder(information) {\n        this.buffer = new StringBuilder();\n        this.information = information;\n    }\n    GeneralAppIdDecoder.prototype.decodeAllCodes = function (buff, initialPosition) {\n        var currentPosition = initialPosition;\n        var remaining = null;\n        do {\n            var info = this.decodeGeneralPurposeField(currentPosition, remaining);\n            var parsedFields = FieldParser.parseFieldsInGeneralPurpose(info.getNewString());\n            if (parsedFields != null) {\n                buff.append(parsedFields);\n            }\n            if (info.isRemaining()) {\n                remaining = '' + info.getRemainingValue();\n            }\n            else {\n                remaining = null;\n            }\n            if (currentPosition === info.getNewPosition()) { // No step forward!\n                break;\n            }\n            currentPosition = info.getNewPosition();\n        } while (true);\n        return buff.toString();\n    };\n    GeneralAppIdDecoder.prototype.isStillNumeric = function (pos) {\n        // It's numeric if it still has 7 positions\n        // and one of the first 4 bits is \"1\".\n        if (pos + 7 > this.information.getSize()) {\n            return pos + 4 <= this.information.getSize();\n        }\n        for (var i = pos; i < pos + 3; ++i) {\n            if (this.information.get(i)) {\n                return true;\n            }\n        }\n        return this.information.get(pos + 3);\n    };\n    GeneralAppIdDecoder.prototype.decodeNumeric = function (pos) {\n        if (pos + 7 > this.information.getSize()) {\n            var numeric_1 = this.extractNumericValueFromBitArray(pos, 4);\n            if (numeric_1 === 0) {\n                return new DecodedNumeric(this.information.getSize(), DecodedNumeric.FNC1, DecodedNumeric.FNC1);\n            }\n            return new DecodedNumeric(this.information.getSize(), numeric_1 - 1, DecodedNumeric.FNC1);\n        }\n        var numeric = this.extractNumericValueFromBitArray(pos, 7);\n        var digit1 = (numeric - 8) / 11;\n        var digit2 = (numeric - 8) % 11;\n        return new DecodedNumeric(pos + 7, digit1, digit2);\n    };\n    GeneralAppIdDecoder.prototype.extractNumericValueFromBitArray = function (pos, bits) {\n        return GeneralAppIdDecoder.extractNumericValueFromBitArray(this.information, pos, bits);\n    };\n    GeneralAppIdDecoder.extractNumericValueFromBitArray = function (information, pos, bits) {\n        var value = 0;\n        for (var i = 0; i < bits; ++i) {\n            if (information.get(pos + i)) {\n                value |= 1 << (bits - i - 1);\n            }\n        }\n        return value;\n    };\n    GeneralAppIdDecoder.prototype.decodeGeneralPurposeField = function (pos, remaining) {\n        // this.buffer.setLength(0);\n        this.buffer.setLengthToZero();\n        if (remaining != null) {\n            this.buffer.append(remaining);\n        }\n        this.current.setPosition(pos);\n        var lastDecoded = this.parseBlocks();\n        if (lastDecoded != null && lastDecoded.isRemaining()) {\n            return new DecodedInformation(this.current.getPosition(), this.buffer.toString(), lastDecoded.getRemainingValue());\n        }\n        return new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n    };\n    GeneralAppIdDecoder.prototype.parseBlocks = function () {\n        var isFinished;\n        var result;\n        do {\n            var initialPosition = this.current.getPosition();\n            if (this.current.isAlpha()) {\n                result = this.parseAlphaBlock();\n                isFinished = result.isFinished();\n            }\n            else if (this.current.isIsoIec646()) {\n                result = this.parseIsoIec646Block();\n                isFinished = result.isFinished();\n            }\n            else { // it must be numeric\n                result = this.parseNumericBlock();\n                isFinished = result.isFinished();\n            }\n            var positionChanged = initialPosition !== this.current.getPosition();\n            if (!positionChanged && !isFinished) {\n                break;\n            }\n        } while (!isFinished);\n        return result.getDecodedInformation();\n    };\n    GeneralAppIdDecoder.prototype.parseNumericBlock = function () {\n        while (this.isStillNumeric(this.current.getPosition())) {\n            var numeric = this.decodeNumeric(this.current.getPosition());\n            this.current.setPosition(numeric.getNewPosition());\n            if (numeric.isFirstDigitFNC1()) {\n                var information = void 0;\n                if (numeric.isSecondDigitFNC1()) {\n                    information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                }\n                else {\n                    information = new DecodedInformation(this.current.getPosition(), this.buffer.toString(), numeric.getSecondDigit());\n                }\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(numeric.getFirstDigit());\n            if (numeric.isSecondDigitFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(numeric.getSecondDigit());\n        }\n        if (this.isNumericToAlphaNumericLatch(this.current.getPosition())) {\n            this.current.setAlpha();\n            this.current.incrementPosition(4);\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.parseIsoIec646Block = function () {\n        while (this.isStillIsoIec646(this.current.getPosition())) {\n            var iso = this.decodeIsoIec646(this.current.getPosition());\n            this.current.setPosition(iso.getNewPosition());\n            if (iso.isFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(iso.getValue());\n        }\n        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n            this.current.incrementPosition(3);\n            this.current.setNumeric();\n        }\n        else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n            if (this.current.getPosition() + 5 < this.information.getSize()) {\n                this.current.incrementPosition(5);\n            }\n            else {\n                this.current.setPosition(this.information.getSize());\n            }\n            this.current.setAlpha();\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.parseAlphaBlock = function () {\n        while (this.isStillAlpha(this.current.getPosition())) {\n            var alpha = this.decodeAlphanumeric(this.current.getPosition());\n            this.current.setPosition(alpha.getNewPosition());\n            if (alpha.isFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information); // end of the char block\n            }\n            this.buffer.append(alpha.getValue());\n        }\n        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n            this.current.incrementPosition(3);\n            this.current.setNumeric();\n        }\n        else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n            if (this.current.getPosition() + 5 < this.information.getSize()) {\n                this.current.incrementPosition(5);\n            }\n            else {\n                this.current.setPosition(this.information.getSize());\n            }\n            this.current.setIsoIec646();\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.isStillIsoIec646 = function (pos) {\n        if (pos + 5 > this.information.getSize()) {\n            return false;\n        }\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue >= 5 && fiveBitValue < 16) {\n            return true;\n        }\n        if (pos + 7 > this.information.getSize()) {\n            return false;\n        }\n        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n        if (sevenBitValue >= 64 && sevenBitValue < 116) {\n            return true;\n        }\n        if (pos + 8 > this.information.getSize()) {\n            return false;\n        }\n        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n        return eightBitValue >= 232 && eightBitValue < 253;\n    };\n    GeneralAppIdDecoder.prototype.decodeIsoIec646 = function (pos) {\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue === 15) {\n            return new DecodedChar(pos + 5, DecodedChar.FNC1);\n        }\n        if (fiveBitValue >= 5 && fiveBitValue < 15) {\n            return new DecodedChar(pos + 5, ('0' + (fiveBitValue - 5)));\n        }\n        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n        if (sevenBitValue >= 64 && sevenBitValue < 90) {\n            return new DecodedChar(pos + 7, ('' + (sevenBitValue + 1)));\n        }\n        if (sevenBitValue >= 90 && sevenBitValue < 116) {\n            return new DecodedChar(pos + 7, ('' + (sevenBitValue + 7)));\n        }\n        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n        var c;\n        switch (eightBitValue) {\n            case 232:\n                c = '!';\n                break;\n            case 233:\n                c = '\"';\n                break;\n            case 234:\n                c = '%';\n                break;\n            case 235:\n                c = '&';\n                break;\n            case 236:\n                c = '\\'';\n                break;\n            case 237:\n                c = '(';\n                break;\n            case 238:\n                c = ')';\n                break;\n            case 239:\n                c = '*';\n                break;\n            case 240:\n                c = '+';\n                break;\n            case 241:\n                c = ',';\n                break;\n            case 242:\n                c = '-';\n                break;\n            case 243:\n                c = '.';\n                break;\n            case 244:\n                c = '/';\n                break;\n            case 245:\n                c = ':';\n                break;\n            case 246:\n                c = ';';\n                break;\n            case 247:\n                c = '<';\n                break;\n            case 248:\n                c = '=';\n                break;\n            case 249:\n                c = '>';\n                break;\n            case 250:\n                c = '?';\n                break;\n            case 251:\n                c = '_';\n                break;\n            case 252:\n                c = ' ';\n                break;\n            default:\n                throw new FormatException();\n        }\n        return new DecodedChar(pos + 8, c);\n    };\n    GeneralAppIdDecoder.prototype.isStillAlpha = function (pos) {\n        if (pos + 5 > this.information.getSize()) {\n            return false;\n        }\n        // We now check if it's a valid 5-bit value (0..9 and FNC1)\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue >= 5 && fiveBitValue < 16) {\n            return true;\n        }\n        if (pos + 6 > this.information.getSize()) {\n            return false;\n        }\n        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n        return sixBitValue >= 16 && sixBitValue < 63; // 63 not included\n    };\n    GeneralAppIdDecoder.prototype.decodeAlphanumeric = function (pos) {\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue === 15) {\n            return new DecodedChar(pos + 5, DecodedChar.FNC1);\n        }\n        if (fiveBitValue >= 5 && fiveBitValue < 15) {\n            return new DecodedChar(pos + 5, ('0' + (fiveBitValue - 5)));\n        }\n        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n        if (sixBitValue >= 32 && sixBitValue < 58) {\n            return new DecodedChar(pos + 6, ('' + (sixBitValue + 33)));\n        }\n        var c;\n        switch (sixBitValue) {\n            case 58:\n                c = '*';\n                break;\n            case 59:\n                c = ',';\n                break;\n            case 60:\n                c = '-';\n                break;\n            case 61:\n                c = '.';\n                break;\n            case 62:\n                c = '/';\n                break;\n            default:\n                throw new IllegalStateException('Decoding invalid alphanumeric value: ' + sixBitValue);\n        }\n        return new DecodedChar(pos + 6, c);\n    };\n    GeneralAppIdDecoder.prototype.isAlphaTo646ToAlphaLatch = function (pos) {\n        if (pos + 1 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = 0; i < 5 && i + pos < this.information.getSize(); ++i) {\n            if (i === 2) {\n                if (!this.information.get(pos + 2)) {\n                    return false;\n                }\n            }\n            else if (this.information.get(pos + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    GeneralAppIdDecoder.prototype.isAlphaOr646ToNumericLatch = function (pos) {\n        // Next is alphanumeric if there are 3 positions and they are all zeros\n        if (pos + 3 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = pos; i < pos + 3; ++i) {\n            if (this.information.get(i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    GeneralAppIdDecoder.prototype.isNumericToAlphaNumericLatch = function (pos) {\n        // Next is alphanumeric if there are 4 positions and they are all zeros, or\n        // if there is a subset of this just before the end of the symbol\n        if (pos + 1 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = 0; i < 4 && i + pos < this.information.getSize(); ++i) {\n            if (this.information.get(pos + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    return GeneralAppIdDecoder;\n}());\nexport default GeneralAppIdDecoder;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,sBAAqC;IACrC,SAAS,oBAAoB,WAAW;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,6KAAA,CAAA,UAAa;QAC/B,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,oBAAoB,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI,EAAE,eAAe;QAC1E,IAAI,kBAAkB;QACtB,IAAI,YAAY;QAChB,GAAG;YACC,IAAI,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YAC3D,IAAI,eAAe,0MAAA,CAAA,UAAW,CAAC,2BAA2B,CAAC,KAAK,YAAY;YAC5E,IAAI,gBAAgB,MAAM;gBACtB,KAAK,MAAM,CAAC;YAChB;YACA,IAAI,KAAK,WAAW,IAAI;gBACpB,YAAY,KAAK,KAAK,iBAAiB;YAC3C,OACK;gBACD,YAAY;YAChB;YACA,IAAI,oBAAoB,KAAK,cAAc,IAAI;gBAC3C;YACJ;YACA,kBAAkB,KAAK,cAAc;QACzC,QAAS,KAAM;QACf,OAAO,KAAK,QAAQ;IACxB;IACA,oBAAoB,SAAS,CAAC,cAAc,GAAG,SAAU,GAAG;QACxD,2CAA2C;QAC3C,sCAAsC;QACtC,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO;QAC9C;QACA,IAAK,IAAI,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,EAAG;YAChC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI;gBACzB,OAAO;YACX;QACJ;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM;IACtC;IACA,oBAAoB,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG;QACvD,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,IAAI,YAAY,IAAI,CAAC,+BAA+B,CAAC,KAAK;YAC1D,IAAI,cAAc,GAAG;gBACjB,OAAO,IAAI,6MAAA,CAAA,UAAc,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,6MAAA,CAAA,UAAc,CAAC,IAAI,EAAE,6MAAA,CAAA,UAAc,CAAC,IAAI;YAClG;YACA,OAAO,IAAI,6MAAA,CAAA,UAAc,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,YAAY,GAAG,6MAAA,CAAA,UAAc,CAAC,IAAI;QAC5F;QACA,IAAI,UAAU,IAAI,CAAC,+BAA+B,CAAC,KAAK;QACxD,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI;QAC7B,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI;QAC7B,OAAO,IAAI,6MAAA,CAAA,UAAc,CAAC,MAAM,GAAG,QAAQ;IAC/C;IACA,oBAAoB,SAAS,CAAC,+BAA+B,GAAG,SAAU,GAAG,EAAE,IAAI;QAC/E,OAAO,oBAAoB,+BAA+B,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK;IACtF;IACA,oBAAoB,+BAA+B,GAAG,SAAU,WAAW,EAAE,GAAG,EAAE,IAAI;QAClF,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;YAC3B,IAAI,YAAY,GAAG,CAAC,MAAM,IAAI;gBAC1B,SAAS,KAAM,OAAO,IAAI;YAC9B;QACJ;QACA,OAAO;IACX;IACA,oBAAoB,SAAS,CAAC,yBAAyB,GAAG,SAAU,GAAG,EAAE,SAAS;QAC9E,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,eAAe;QAC3B,IAAI,aAAa,MAAM;YACnB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACvB;QACA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QACzB,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,IAAI,eAAe,QAAQ,YAAY,WAAW,IAAI;YAClD,OAAO,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,YAAY,iBAAiB;QACnH;QACA,OAAO,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClF;IACA,oBAAoB,SAAS,CAAC,WAAW,GAAG;QACxC,IAAI;QACJ,IAAI;QACJ,GAAG;YACC,IAAI,kBAAkB,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI;gBACxB,SAAS,IAAI,CAAC,eAAe;gBAC7B,aAAa,OAAO,UAAU;YAClC,OACK,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI;gBACjC,SAAS,IAAI,CAAC,mBAAmB;gBACjC,aAAa,OAAO,UAAU;YAClC,OACK;gBACD,SAAS,IAAI,CAAC,iBAAiB;gBAC/B,aAAa,OAAO,UAAU;YAClC;YACA,IAAI,kBAAkB,oBAAoB,IAAI,CAAC,OAAO,CAAC,WAAW;YAClE,IAAI,CAAC,mBAAmB,CAAC,YAAY;gBACjC;YACJ;QACJ,QAAS,CAAC,WAAY;QACtB,OAAO,OAAO,qBAAqB;IACvC;IACA,oBAAoB,SAAS,CAAC,iBAAiB,GAAG;QAC9C,MAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAK;YACpD,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;YACzD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,cAAc;YAC/C,IAAI,QAAQ,gBAAgB,IAAI;gBAC5B,IAAI,cAAc,KAAK;gBACvB,IAAI,QAAQ,iBAAiB,IAAI;oBAC7B,cAAc,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzF,OACK;oBACD,cAAc,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,cAAc;gBACnH;gBACA,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,MAAM;YACvC;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,aAAa;YACxC,IAAI,QAAQ,iBAAiB,IAAI;gBAC7B,IAAI,cAAc,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzF,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,MAAM;YACvC;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,cAAc;QAC7C;QACA,IAAI,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK;YAC/D,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;QACnC;QACA,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC;IACjC;IACA,oBAAoB,SAAS,CAAC,mBAAmB,GAAG;QAChD,MAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAK;YACtD,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;YACvD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,cAAc;YAC3C,IAAI,IAAI,MAAM,IAAI;gBACd,IAAI,cAAc,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzF,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,MAAM;YACvC;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,QAAQ;QACnC;QACA,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK;YAC7D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,UAAU;QAC3B,OACK,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK;YAChE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;gBAC7D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YACnC,OACK;gBACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;YACrD;YACA,IAAI,CAAC,OAAO,CAAC,QAAQ;QACzB;QACA,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC;IACjC;IACA,oBAAoB,SAAS,CAAC,eAAe,GAAG;QAC5C,MAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAK;YAClD,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;YAC5D,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,cAAc;YAC7C,IAAI,MAAM,MAAM,IAAI;gBAChB,IAAI,cAAc,IAAI,iNAAA,CAAA,UAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzF,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,MAAM,cAAc,wBAAwB;YAC7E;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,QAAQ;QACrC;QACA,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK;YAC7D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,UAAU;QAC3B,OACK,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK;YAChE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;gBAC7D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YACnC,OACK;gBACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;YACrD;YACA,IAAI,CAAC,OAAO,CAAC,YAAY;QAC7B;QACA,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC;IACjC;IACA,oBAAoB,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QAC1D,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAI,eAAe,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC7D,IAAI,gBAAgB,KAAK,eAAe,IAAI;YACxC,OAAO;QACX;QACA,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAI,gBAAgB,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC9D,IAAI,iBAAiB,MAAM,gBAAgB,KAAK;YAC5C,OAAO;QACX;QACA,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAI,gBAAgB,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC9D,OAAO,iBAAiB,OAAO,gBAAgB;IACnD;IACA,oBAAoB,SAAS,CAAC,eAAe,GAAG,SAAU,GAAG;QACzD,IAAI,eAAe,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC7D,IAAI,iBAAiB,IAAI;YACrB,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAG,0MAAA,CAAA,UAAW,CAAC,IAAI;QACpD;QACA,IAAI,gBAAgB,KAAK,eAAe,IAAI;YACxC,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAI,MAAM,CAAC,eAAe,CAAC;QAC5D;QACA,IAAI,gBAAgB,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC9D,IAAI,iBAAiB,MAAM,gBAAgB,IAAI;YAC3C,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAI,KAAK,CAAC,gBAAgB,CAAC;QAC5D;QACA,IAAI,iBAAiB,MAAM,gBAAgB,KAAK;YAC5C,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAI,KAAK,CAAC,gBAAgB,CAAC;QAC5D;QACA,IAAI,gBAAgB,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC9D,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ;gBACI,MAAM,IAAI,uKAAA,CAAA,UAAe;QACjC;QACA,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAG;IACpC;IACA,oBAAoB,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG;QACtD,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,2DAA2D;QAC3D,IAAI,eAAe,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC7D,IAAI,gBAAgB,KAAK,eAAe,IAAI;YACxC,OAAO;QACX;QACA,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAI,cAAc,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC5D,OAAO,eAAe,MAAM,cAAc,IAAI,kBAAkB;IACpE;IACA,oBAAoB,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QAC5D,IAAI,eAAe,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC7D,IAAI,iBAAiB,IAAI;YACrB,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAG,0MAAA,CAAA,UAAW,CAAC,IAAI;QACpD;QACA,IAAI,gBAAgB,KAAK,eAAe,IAAI;YACxC,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAI,MAAM,CAAC,eAAe,CAAC;QAC5D;QACA,IAAI,cAAc,IAAI,CAAC,+BAA+B,CAAC,KAAK;QAC5D,IAAI,eAAe,MAAM,cAAc,IAAI;YACvC,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAI,KAAK,CAAC,cAAc,EAAE;QAC3D;QACA,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ,KAAK;gBACD,IAAI;gBACJ;YACJ;gBACI,MAAM,IAAI,6KAAA,CAAA,UAAqB,CAAC,0CAA0C;QAClF;QACA,OAAO,IAAI,0MAAA,CAAA,UAAW,CAAC,MAAM,GAAG;IACpC;IACA,oBAAoB,SAAS,CAAC,wBAAwB,GAAG,SAAU,GAAG;QAClE,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,EAAG;YAChE,IAAI,MAAM,GAAG;gBACT,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,IAAI;oBAChC,OAAO;gBACX;YACJ,OACK,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,IAAI;gBACpC,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,oBAAoB,SAAS,CAAC,0BAA0B,GAAG,SAAU,GAAG;QACpE,uEAAuE;QACvE,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,EAAG;YAChC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI;gBACzB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,oBAAoB,SAAS,CAAC,4BAA4B,GAAG,SAAU,GAAG;QACtE,2EAA2E;QAC3E,iEAAiE;QACjE,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;YACtC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,EAAG;YAChE,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,IAAI;gBAC/B,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js"], "sourcesContent": ["import GeneralAppIdDecoder from './GeneralAppIdDecoder';\nvar AbstractExpandedDecoder = /** @class */ (function () {\n    function AbstractExpandedDecoder(information) {\n        this.information = information;\n        this.generalDecoder = new GeneralAppIdDecoder(information);\n    }\n    AbstractExpandedDecoder.prototype.getInformation = function () {\n        return this.information;\n    };\n    AbstractExpandedDecoder.prototype.getGeneralDecoder = function () {\n        return this.generalDecoder;\n    };\n    return AbstractExpandedDecoder;\n}());\nexport default AbstractExpandedDecoder;\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,0BAAyC;IACzC,SAAS,wBAAwB,WAAW;QACxC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,kNAAA,CAAA,UAAmB,CAAC;IAClD;IACA,wBAAwB,SAAS,CAAC,cAAc,GAAG;QAC/C,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,wBAAwB,SAAS,CAAC,iBAAiB,GAAG;QAClD,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AbstractExpandedDecoder from './AbstractExpandedDecoder';\nvar AI01decoder = /** @class */ (function (_super) {\n    __extends(AI01decoder, _super);\n    function AI01decoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01decoder.prototype.encodeCompressedGtin = function (buf, currentPos) {\n        buf.append('(01)');\n        var initialPosition = buf.length();\n        buf.append('9');\n        this.encodeCompressedGtinWithoutAI(buf, currentPos, initialPosition);\n    };\n    AI01decoder.prototype.encodeCompressedGtinWithoutAI = function (buf, currentPos, initialBufferPosition) {\n        for (var i = 0; i < 4; ++i) {\n            var currentBlock = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos + 10 * i, 10);\n            if (currentBlock / 100 === 0) {\n                buf.append('0');\n            }\n            if (currentBlock / 10 === 0) {\n                buf.append('0');\n            }\n            buf.append(currentBlock);\n        }\n        AI01decoder.appendCheckDigit(buf, initialBufferPosition);\n    };\n    AI01decoder.appendCheckDigit = function (buf, currentPos) {\n        var checkDigit = 0;\n        for (var i = 0; i < 13; i++) {\n            // let digit = buf.charAt(i + currentPos) - '0';\n            // To be checked\n            var digit = buf.charAt(i + currentPos).charCodeAt(0) - '0'.charCodeAt(0);\n            checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;\n        }\n        checkDigit = 10 - (checkDigit % 10);\n        if (checkDigit === 10) {\n            checkDigit = 0;\n        }\n        buf.append(checkDigit);\n    };\n    AI01decoder.GTIN_SIZE = 40;\n    return AI01decoder;\n}(AbstractExpandedDecoder));\nexport default AI01decoder;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS,YAAY,WAAW;QAC5B,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,YAAY,SAAS,CAAC,oBAAoB,GAAG,SAAU,GAAG,EAAE,UAAU;QAClE,IAAI,MAAM,CAAC;QACX,IAAI,kBAAkB,IAAI,MAAM;QAChC,IAAI,MAAM,CAAC;QACX,IAAI,CAAC,6BAA6B,CAAC,KAAK,YAAY;IACxD;IACA,YAAY,SAAS,CAAC,6BAA6B,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,qBAAqB;QAClG,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,eAAe,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,aAAa,KAAK,GAAG;YACjG,IAAI,eAAe,QAAQ,GAAG;gBAC1B,IAAI,MAAM,CAAC;YACf;YACA,IAAI,eAAe,OAAO,GAAG;gBACzB,IAAI,MAAM,CAAC;YACf;YACA,IAAI,MAAM,CAAC;QACf;QACA,YAAY,gBAAgB,CAAC,KAAK;IACtC;IACA,YAAY,gBAAgB,GAAG,SAAU,GAAG,EAAE,UAAU;QACpD,IAAI,aAAa;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YACzB,gDAAgD;YAChD,gBAAgB;YAChB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YACtE,cAAc,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,QAAQ;QACjD;QACA,aAAa,KAAM,aAAa;QAChC,IAAI,eAAe,IAAI;YACnB,aAAa;QACjB;QACA,IAAI,MAAM,CAAC;IACf;IACA,YAAY,SAAS,GAAG;IACxB,OAAO;AACX,EAAE,sNAAA,CAAA,UAAuB;uCACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01AndOtherAIs.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01AndOtherAIs = /** @class */ (function (_super) {\n    __extends(AI01AndOtherAIs, _super);\n    // the second one is the encodation method, and the other two are for the variable length\n    function AI01AndOtherAIs(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01AndOtherAIs.prototype.parseInformation = function () {\n        var buff = new StringBuilder();\n        buff.append('(01)');\n        var initialGtinPosition = buff.length();\n        var firstGtinDigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01AndOtherAIs.HEADER_SIZE, 4);\n        buff.append(firstGtinDigit);\n        this.encodeCompressedGtinWithoutAI(buff, AI01AndOtherAIs.HEADER_SIZE + 4, initialGtinPosition);\n        return this.getGeneralDecoder().decodeAllCodes(buff, AI01AndOtherAIs.HEADER_SIZE + 44);\n    };\n    AI01AndOtherAIs.HEADER_SIZE = 1 + 1 + 2; // first bit encodes the linkage flag,\n    return AI01AndOtherAIs;\n}(AI01decoder));\nexport default AI01AndOtherAIs;\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,yFAAyF;IACzF,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QACzC,IAAI,OAAO,IAAI,6KAAA,CAAA,UAAa;QAC5B,KAAK,MAAM,CAAC;QACZ,IAAI,sBAAsB,KAAK,MAAM;QACrC,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,gBAAgB,WAAW,EAAE;QAC3G,KAAK,MAAM,CAAC;QACZ,IAAI,CAAC,6BAA6B,CAAC,MAAM,gBAAgB,WAAW,GAAG,GAAG;QAC1E,OAAO,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,MAAM,gBAAgB,WAAW,GAAG;IACvF;IACA,gBAAgB,WAAW,GAAG,IAAI,IAAI,GAAG,sCAAsC;IAC/E,OAAO;AACX,EAAE,0MAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AnyAIDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport StringBuilder from '../../../../util/StringBuilder';\nimport AbstractExpandedDecoder from './AbstractExpandedDecoder';\nvar AnyAIDecoder = /** @class */ (function (_super) {\n    __extends(AnyAIDecoder, _super);\n    function AnyAIDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AnyAIDecoder.prototype.parseInformation = function () {\n        var buf = new StringBuilder();\n        return this.getGeneralDecoder().decodeAllCodes(buf, AnyAIDecoder.HEADER_SIZE);\n    };\n    AnyAIDecoder.HEADER_SIZE = 2 + 1 + 2;\n    return AnyAIDecoder;\n}(AbstractExpandedDecoder));\nexport default AnyAIDecoder;\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB,SAAS,aAAa,WAAW;QAC7B,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,MAAM,IAAI,6KAAA,CAAA,UAAa;QAC3B,OAAO,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,KAAK,aAAa,WAAW;IAChF;IACA,aAAa,WAAW,GAAG,IAAI,IAAI;IACnC,OAAO;AACX,EAAE,sNAAA,CAAA,UAAuB;uCACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01weightDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nvar AI01weightDecoder = /** @class */ (function (_super) {\n    __extends(AI01weightDecoder, _super);\n    function AI01weightDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01weightDecoder.prototype.encodeCompressedWeight = function (buf, currentPos, weightSize) {\n        var originalWeightNumeric = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, weightSize);\n        this.addWeightCode(buf, originalWeightNumeric);\n        var weightNumeric = this.checkWeight(originalWeightNumeric);\n        var currentDivisor = 100000;\n        for (var i = 0; i < 5; ++i) {\n            if (weightNumeric / currentDivisor === 0) {\n                buf.append('0');\n            }\n            currentDivisor /= 10;\n        }\n        buf.append(weightNumeric);\n    };\n    return AI01weightDecoder;\n}(AI01decoder));\nexport default AI01weightDecoder;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS,kBAAkB,WAAW;QAClC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,kBAAkB,SAAS,CAAC,sBAAsB,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,UAAU;QACtF,IAAI,wBAAwB,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,YAAY;QACjG,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,gBAAgB,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,iBAAiB;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACxB,IAAI,gBAAgB,mBAAmB,GAAG;gBACtC,IAAI,MAAM,CAAC;YACf;YACA,kBAAkB;QACtB;QACA,IAAI,MAAM,CAAC;IACf;IACA,OAAO;AACX,EAAE,0MAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01weightDecoder from './AI01weightDecoder';\nimport StringBuilder from '../../../../util/StringBuilder';\nimport NotFoundException from '../../../../NotFoundException';\nvar AI013x0xDecoder = /** @class */ (function (_super) {\n    __extends(AI013x0xDecoder, _super);\n    function AI013x0xDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI013x0xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() !==\n            AI013x0xDecoder.HEADER_SIZE +\n                AI01weightDecoder.GTIN_SIZE +\n                AI013x0xDecoder.WEIGHT_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI013x0xDecoder.HEADER_SIZE);\n        this.encodeCompressedWeight(buf, AI013x0xDecoder.HEADER_SIZE + AI01weightDecoder.GTIN_SIZE, AI013x0xDecoder.WEIGHT_SIZE);\n        return buf.toString();\n    };\n    AI013x0xDecoder.HEADER_SIZE = 4 + 1;\n    AI013x0xDecoder.WEIGHT_SIZE = 15;\n    return AI013x0xDecoder;\n}(AI01weightDecoder));\nexport default AI013x0xDecoder;\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QACzC,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,OAC7B,gBAAgB,WAAW,GACvB,gNAAA,CAAA,UAAiB,CAAC,SAAS,GAC3B,gBAAgB,WAAW,EAAE;YACjC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,MAAM,IAAI,6KAAA,CAAA,UAAa;QAC3B,IAAI,CAAC,oBAAoB,CAAC,KAAK,gBAAgB,WAAW;QAC1D,IAAI,CAAC,sBAAsB,CAAC,KAAK,gBAAgB,WAAW,GAAG,gNAAA,CAAA,UAAiB,CAAC,SAAS,EAAE,gBAAgB,WAAW;QACvH,OAAO,IAAI,QAAQ;IACvB;IACA,gBAAgB,WAAW,GAAG,IAAI;IAClC,gBAAgB,WAAW,GAAG;IAC9B,OAAO;AACX,EAAE,gNAAA,CAAA,UAAiB;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI013103decoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI013x0xDecoder from './AI013x0xDecoder';\nvar AI013103decoder = /** @class */ (function (_super) {\n    __extends(AI013103decoder, _super);\n    function AI013103decoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI013103decoder.prototype.addWeightCode = function (buf, weight) {\n        buf.append('(3103)');\n    };\n    AI013103decoder.prototype.checkWeight = function (weight) {\n        return weight;\n    };\n    return AI013103decoder;\n}(AI013x0xDecoder));\nexport default AI013103decoder;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM;QAC3D,IAAI,MAAM,CAAC;IACf;IACA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;QACpD,OAAO;IACX;IACA,OAAO;AACX,EAAE,8MAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01320xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI013x0xDecoder from './AI013x0xDecoder';\nvar AI01320xDecoder = /** @class */ (function (_super) {\n    __extends(AI01320xDecoder, _super);\n    function AI01320xDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01320xDecoder.prototype.addWeightCode = function (buf, weight) {\n        if (weight < 10000) {\n            buf.append('(3202)');\n        }\n        else {\n            buf.append('(3203)');\n        }\n    };\n    AI01320xDecoder.prototype.checkWeight = function (weight) {\n        if (weight < 10000) {\n            return weight;\n        }\n        return weight - 10000;\n    };\n    return AI01320xDecoder;\n}(AI013x0xDecoder));\nexport default AI01320xDecoder;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM;QAC3D,IAAI,SAAS,OAAO;YAChB,IAAI,MAAM,CAAC;QACf,OACK;YACD,IAAI,MAAM,CAAC;QACf;IACJ;IACA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;QACpD,IAAI,SAAS,OAAO;YAChB,OAAO;QACX;QACA,OAAO,SAAS;IACpB;IACA,OAAO;AACX,EAAE,8MAAA,CAAA,UAAe;uCACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01392xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01392xDecoder = /** @class */ (function (_super) {\n    __extends(AI01392xDecoder, _super);\n    function AI01392xDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01392xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() < AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI01392xDecoder.HEADER_SIZE);\n        var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE, AI01392xDecoder.LAST_DIGIT_SIZE);\n        buf.append('(392');\n        buf.append(lastAIdigit);\n        buf.append(')');\n        var decodedInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE + AI01392xDecoder.LAST_DIGIT_SIZE, null);\n        buf.append(decodedInformation.getNewString());\n        return buf.toString();\n    };\n    AI01392xDecoder.HEADER_SIZE = 5 + 1 + 2;\n    AI01392xDecoder.LAST_DIGIT_SIZE = 2;\n    return AI01392xDecoder;\n}(AI01decoder));\nexport default AI01392xDecoder;\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QACzC,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,KAAK,gBAAgB,WAAW,GAAG,0MAAA,CAAA,UAAW,CAAC,SAAS,EAAE;YACvF,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,MAAM,IAAI,6KAAA,CAAA,UAAa;QAC3B,IAAI,CAAC,oBAAoB,CAAC,KAAK,gBAAgB,WAAW;QAC1D,IAAI,cAAc,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,gBAAgB,WAAW,GAAG,0MAAA,CAAA,UAAW,CAAC,SAAS,EAAE,gBAAgB,eAAe;QAC/J,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC;QACX,IAAI,qBAAqB,IAAI,CAAC,iBAAiB,GAAG,yBAAyB,CAAC,gBAAgB,WAAW,GAAG,0MAAA,CAAA,UAAW,CAAC,SAAS,GAAG,gBAAgB,eAAe,EAAE;QACnK,IAAI,MAAM,CAAC,mBAAmB,YAAY;QAC1C,OAAO,IAAI,QAAQ;IACvB;IACA,gBAAgB,WAAW,GAAG,IAAI,IAAI;IACtC,gBAAgB,eAAe,GAAG;IAClC,OAAO;AACX,EAAE,0MAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI01393xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01393xDecoder = /** @class */ (function (_super) {\n    __extends(AI01393xDecoder, _super);\n    function AI01393xDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01393xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() <\n            AI01393xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI01393xDecoder.HEADER_SIZE);\n        var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01393xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE, AI01393xDecoder.LAST_DIGIT_SIZE);\n        buf.append('(393');\n        buf.append(lastAIdigit);\n        buf.append(')');\n        var firstThreeDigits = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01393xDecoder.HEADER_SIZE +\n            AI01decoder.GTIN_SIZE +\n            AI01393xDecoder.LAST_DIGIT_SIZE, AI01393xDecoder.FIRST_THREE_DIGITS_SIZE);\n        if (firstThreeDigits / 100 === 0) {\n            buf.append('0');\n        }\n        if (firstThreeDigits / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(firstThreeDigits);\n        var generalInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01393xDecoder.HEADER_SIZE +\n            AI01decoder.GTIN_SIZE +\n            AI01393xDecoder.LAST_DIGIT_SIZE +\n            AI01393xDecoder.FIRST_THREE_DIGITS_SIZE, null);\n        buf.append(generalInformation.getNewString());\n        return buf.toString();\n    };\n    AI01393xDecoder.HEADER_SIZE = 5 + 1 + 2;\n    AI01393xDecoder.LAST_DIGIT_SIZE = 2;\n    AI01393xDecoder.FIRST_THREE_DIGITS_SIZE = 10;\n    return AI01393xDecoder;\n}(AI01decoder));\nexport default AI01393xDecoder;\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,WAAW;QAChC,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;IACjD;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QACzC,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,KAC7B,gBAAgB,WAAW,GAAG,0MAAA,CAAA,UAAW,CAAC,SAAS,EAAE;YACrD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,MAAM,IAAI,6KAAA,CAAA,UAAa;QAC3B,IAAI,CAAC,oBAAoB,CAAC,KAAK,gBAAgB,WAAW;QAC1D,IAAI,cAAc,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,gBAAgB,WAAW,GAAG,0MAAA,CAAA,UAAW,CAAC,SAAS,EAAE,gBAAgB,eAAe;QAC/J,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC;QACX,IAAI,mBAAmB,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,gBAAgB,WAAW,GACvG,0MAAA,CAAA,UAAW,CAAC,SAAS,GACrB,gBAAgB,eAAe,EAAE,gBAAgB,uBAAuB;QAC5E,IAAI,mBAAmB,QAAQ,GAAG;YAC9B,IAAI,MAAM,CAAC;QACf;QACA,IAAI,mBAAmB,OAAO,GAAG;YAC7B,IAAI,MAAM,CAAC;QACf;QACA,IAAI,MAAM,CAAC;QACX,IAAI,qBAAqB,IAAI,CAAC,iBAAiB,GAAG,yBAAyB,CAAC,gBAAgB,WAAW,GACnG,0MAAA,CAAA,UAAW,CAAC,SAAS,GACrB,gBAAgB,eAAe,GAC/B,gBAAgB,uBAAuB,EAAE;QAC7C,IAAI,MAAM,CAAC,mBAAmB,YAAY;QAC1C,OAAO,IAAI,QAAQ;IACvB;IACA,gBAAgB,WAAW,GAAG,IAAI,IAAI;IACtC,gBAAgB,eAAe,GAAG;IAClC,gBAAgB,uBAAuB,GAAG;IAC1C,OAAO;AACX,EAAE,0MAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0x1xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01weightDecoder from './AI01weightDecoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI013x0x1xDecoder = /** @class */ (function (_super) {\n    __extends(AI013x0x1xDecoder, _super);\n    function AI013x0x1xDecoder(information, firstAIdigits, dateCode) {\n        var _this = _super.call(this, information) || this;\n        _this.dateCode = dateCode;\n        _this.firstAIdigits = firstAIdigits;\n        return _this;\n    }\n    AI013x0x1xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() !==\n            AI013x0x1xDecoder.HEADER_SIZE +\n                AI013x0x1xDecoder.GTIN_SIZE +\n                AI013x0x1xDecoder.WEIGHT_SIZE +\n                AI013x0x1xDecoder.DATE_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI013x0x1xDecoder.HEADER_SIZE);\n        this.encodeCompressedWeight(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE, AI013x0x1xDecoder.WEIGHT_SIZE);\n        this.encodeCompressedDate(buf, AI013x0x1xDecoder.HEADER_SIZE +\n            AI013x0x1xDecoder.GTIN_SIZE +\n            AI013x0x1xDecoder.WEIGHT_SIZE);\n        return buf.toString();\n    };\n    AI013x0x1xDecoder.prototype.encodeCompressedDate = function (buf, currentPos) {\n        var numericDate = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, AI013x0x1xDecoder.DATE_SIZE);\n        if (numericDate === 38400) {\n            return;\n        }\n        buf.append('(');\n        buf.append(this.dateCode);\n        buf.append(')');\n        var day = numericDate % 32;\n        numericDate /= 32;\n        var month = (numericDate % 12) + 1;\n        numericDate /= 12;\n        var year = numericDate;\n        if (year / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(year);\n        if (month / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(month);\n        if (day / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(day);\n    };\n    AI013x0x1xDecoder.prototype.addWeightCode = function (buf, weight) {\n        buf.append('(');\n        buf.append(this.firstAIdigits);\n        buf.append(weight / 100000);\n        buf.append(')');\n    };\n    AI013x0x1xDecoder.prototype.checkWeight = function (weight) {\n        return weight % 100000;\n    };\n    AI013x0x1xDecoder.HEADER_SIZE = 7 + 1;\n    AI013x0x1xDecoder.WEIGHT_SIZE = 20;\n    AI013x0x1xDecoder.DATE_SIZE = 16;\n    return AI013x0x1xDecoder;\n}(AI01weightDecoder));\nexport default AI013x0x1xDecoder;\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS,kBAAkB,WAAW,EAAE,aAAa,EAAE,QAAQ;QAC3D,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;QAClD,MAAM,QAAQ,GAAG;QACjB,MAAM,aAAa,GAAG;QACtB,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,gBAAgB,GAAG;QAC3C,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,OAC7B,kBAAkB,WAAW,GACzB,kBAAkB,SAAS,GAC3B,kBAAkB,WAAW,GAC7B,kBAAkB,SAAS,EAAE;YACjC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,MAAM,IAAI,6KAAA,CAAA,UAAa;QAC3B,IAAI,CAAC,oBAAoB,CAAC,KAAK,kBAAkB,WAAW;QAC5D,IAAI,CAAC,sBAAsB,CAAC,KAAK,kBAAkB,WAAW,GAAG,kBAAkB,SAAS,EAAE,kBAAkB,WAAW;QAC3H,IAAI,CAAC,oBAAoB,CAAC,KAAK,kBAAkB,WAAW,GACxD,kBAAkB,SAAS,GAC3B,kBAAkB,WAAW;QACjC,OAAO,IAAI,QAAQ;IACvB;IACA,kBAAkB,SAAS,CAAC,oBAAoB,GAAG,SAAU,GAAG,EAAE,UAAU;QACxE,IAAI,cAAc,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC,YAAY,kBAAkB,SAAS;QAClH,IAAI,gBAAgB,OAAO;YACvB;QACJ;QACA,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ;QACxB,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,cAAc;QACxB,eAAe;QACf,IAAI,QAAQ,AAAC,cAAc,KAAM;QACjC,eAAe;QACf,IAAI,OAAO;QACX,IAAI,OAAO,OAAO,GAAG;YACjB,IAAI,MAAM,CAAC;QACf;QACA,IAAI,MAAM,CAAC;QACX,IAAI,QAAQ,OAAO,GAAG;YAClB,IAAI,MAAM,CAAC;QACf;QACA,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,OAAO,GAAG;YAChB,IAAI,MAAM,CAAC;QACf;QACA,IAAI,MAAM,CAAC;IACf;IACA,kBAAkB,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,MAAM;QAC7D,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa;QAC7B,IAAI,MAAM,CAAC,SAAS;QACpB,IAAI,MAAM,CAAC;IACf;IACA,kBAAkB,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;QACtD,OAAO,SAAS;IACpB;IACA,kBAAkB,WAAW,GAAG,IAAI;IACpC,kBAAkB,WAAW,GAAG;IAChC,kBAAkB,SAAS,GAAG;IAC9B,OAAO;AACX,EAAE,gNAAA,CAAA,UAAiB;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js"], "sourcesContent": ["import IllegalStateException from '../../../../IllegalStateException';\nimport GeneralAppIdDecoder from './GeneralAppIdDecoder';\nimport AI01AndOtherAIs from './AI01AndOtherAIs';\nimport AnyAIDecoder from './AnyAIDecoder';\nimport AI013103decoder from './AI013103decoder';\nimport AI01320xDecoder from './AI01320xDecoder';\nimport AI01392xDecoder from './AI01392xDecoder';\nimport AI01393xDecoder from './AI01393xDecoder';\nimport AI013x0x1xDecoder from './AI013x0x1xDecoder';\nexport function createDecoder(information) {\n    try {\n        if (information.get(1)) {\n            return new AI01AndOtherAIs(information);\n        }\n        if (!information.get(2)) {\n            return new AnyAIDecoder(information);\n        }\n        var fourBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 4);\n        switch (fourBitEncodationMethod) {\n            case 4: return new AI013103decoder(information);\n            case 5: return new AI01320xDecoder(information);\n        }\n        var fiveBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 5);\n        switch (fiveBitEncodationMethod) {\n            case 12: return new AI01392xDecoder(information);\n            case 13: return new AI01393xDecoder(information);\n        }\n        var sevenBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 7);\n        switch (sevenBitEncodationMethod) {\n            case 56: return new AI013x0x1xDecoder(information, '310', '11');\n            case 57: return new AI013x0x1xDecoder(information, '320', '11');\n            case 58: return new AI013x0x1xDecoder(information, '310', '13');\n            case 59: return new AI013x0x1xDecoder(information, '320', '13');\n            case 60: return new AI013x0x1xDecoder(information, '310', '15');\n            case 61: return new AI013x0x1xDecoder(information, '320', '15');\n            case 62: return new AI013x0x1xDecoder(information, '310', '17');\n            case 63: return new AI013x0x1xDecoder(information, '320', '17');\n        }\n    }\n    catch (e) {\n        console.log(e);\n        throw new IllegalStateException('unknown decoder: ' + information);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,SAAS,cAAc,WAAW;IACrC,IAAI;QACA,IAAI,YAAY,GAAG,CAAC,IAAI;YACpB,OAAO,IAAI,8MAAA,CAAA,UAAe,CAAC;QAC/B;QACA,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI;YACrB,OAAO,IAAI,2MAAA,CAAA,UAAY,CAAC;QAC5B;QACA,IAAI,0BAA0B,kNAAA,CAAA,UAAmB,CAAC,+BAA+B,CAAC,aAAa,GAAG;QAClG,OAAQ;YACJ,KAAK;gBAAG,OAAO,IAAI,8MAAA,CAAA,UAAe,CAAC;YACnC,KAAK;gBAAG,OAAO,IAAI,8MAAA,CAAA,UAAe,CAAC;QACvC;QACA,IAAI,0BAA0B,kNAAA,CAAA,UAAmB,CAAC,+BAA+B,CAAC,aAAa,GAAG;QAClG,OAAQ;YACJ,KAAK;gBAAI,OAAO,IAAI,8MAAA,CAAA,UAAe,CAAC;YACpC,KAAK;gBAAI,OAAO,IAAI,8MAAA,CAAA,UAAe,CAAC;QACxC;QACA,IAAI,2BAA2B,kNAAA,CAAA,UAAmB,CAAC,+BAA+B,CAAC,aAAa,GAAG;QACnG,OAAQ;YACJ,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;YAC1D,KAAK;gBAAI,OAAO,IAAI,gNAAA,CAAA,UAAiB,CAAC,aAAa,OAAO;QAC9D;IACJ,EACA,OAAO,GAAG;QACN,QAAQ,GAAG,CAAC;QACZ,MAAM,IAAI,6KAAA,CAAA,UAAqB,CAAC,sBAAsB;IAC1D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/ExpandedPair.js"], "sourcesContent": ["var ExpandedPair = /** @class */ (function () {\n    function ExpandedPair(leftChar, rightChar, finderPatter, mayBeLast) {\n        this.leftchar = leftChar;\n        this.rightchar = rightChar;\n        this.finderpattern = finderPatter;\n        this.maybeLast = mayBeLast;\n    }\n    ExpandedPair.prototype.mayBeLast = function () {\n        return this.maybeLast;\n    };\n    ExpandedPair.prototype.getLeftChar = function () {\n        return this.leftchar;\n    };\n    ExpandedPair.prototype.getRightChar = function () {\n        return this.rightchar;\n    };\n    ExpandedPair.prototype.getFinderPattern = function () {\n        return this.finderpattern;\n    };\n    ExpandedPair.prototype.mustBeLast = function () {\n        return this.rightchar == null;\n    };\n    ExpandedPair.prototype.toString = function () {\n        return '[ ' + this.leftchar + ', ' + this.rightchar + ' : ' + (this.finderpattern == null ? 'null' : this.finderpattern.getValue()) + ' ]';\n    };\n    ExpandedPair.equals = function (o1, o2) {\n        if (!(o1 instanceof ExpandedPair)) {\n            return false;\n        }\n        return ExpandedPair.equalsOrNull(o1.leftchar, o2.leftchar) &&\n            ExpandedPair.equalsOrNull(o1.rightchar, o2.rightchar) &&\n            ExpandedPair.equalsOrNull(o1.finderpattern, o2.finderpattern);\n    };\n    ExpandedPair.equalsOrNull = function (o1, o2) {\n        return o1 === null ? o2 === null : ExpandedPair.equals(o1, o2);\n    };\n    ExpandedPair.prototype.hashCode = function () {\n        // return ExpandedPair.hashNotNull(leftChar) ^ hashNotNull(rightChar) ^ hashNotNull(finderPattern);\n        var value = this.leftchar.getValue() ^ this.rightchar.getValue() ^ this.finderpattern.getValue();\n        return value;\n    };\n    return ExpandedPair;\n}());\nexport default ExpandedPair;\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAA8B;IAC9B,SAAS,aAAa,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS;QAC9D,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,aAAa,SAAS,CAAC,SAAS,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,aAAa,SAAS,CAAC,WAAW,GAAG;QACjC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,aAAa,SAAS,CAAC,YAAY,GAAG;QAClC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,aAAa,SAAS,CAAC,UAAU,GAAG;QAChC,OAAO,IAAI,CAAC,SAAS,IAAI;IAC7B;IACA,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,OAAO,OAAO,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,OAAO,SAAS,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI;IAC1I;IACA,aAAa,MAAM,GAAG,SAAU,EAAE,EAAE,EAAE;QAClC,IAAI,CAAC,CAAC,cAAc,YAAY,GAAG;YAC/B,OAAO;QACX;QACA,OAAO,aAAa,YAAY,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ,KACrD,aAAa,YAAY,CAAC,GAAG,SAAS,EAAE,GAAG,SAAS,KACpD,aAAa,YAAY,CAAC,GAAG,aAAa,EAAE,GAAG,aAAa;IACpE;IACA,aAAa,YAAY,GAAG,SAAU,EAAE,EAAE,EAAE;QACxC,OAAO,OAAO,OAAO,OAAO,OAAO,aAAa,MAAM,CAAC,IAAI;IAC/D;IACA,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,mGAAmG;QACnG,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,aAAa,CAAC,QAAQ;QAC9F,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/ExpandedRow.js"], "sourcesContent": ["var ExpandedRow = /** @class */ (function () {\n    function ExpandedRow(pairs, rowNumber, wasReversed) {\n        this.pairs = pairs;\n        this.rowNumber = rowNumber;\n        this.wasReversed = wasReversed;\n    }\n    ExpandedRow.prototype.getPairs = function () {\n        return this.pairs;\n    };\n    ExpandedRow.prototype.getRowNumber = function () {\n        return this.rowNumber;\n    };\n    ExpandedRow.prototype.isReversed = function () {\n        return this.wasReversed;\n    };\n    // check implementation\n    ExpandedRow.prototype.isEquivalent = function (otherPairs) {\n        return this.checkEqualitity(this, otherPairs);\n    };\n    // @Override\n    ExpandedRow.prototype.toString = function () {\n        return '{ ' + this.pairs + ' }';\n    };\n    /**\n     * Two rows are equal if they contain the same pairs in the same order.\n     */\n    // @Override\n    // check implementation\n    ExpandedRow.prototype.equals = function (o1, o2) {\n        if (!(o1 instanceof ExpandedRow)) {\n            return false;\n        }\n        return this.checkEqualitity(o1, o2) && o1.wasReversed === o2.wasReversed;\n    };\n    ExpandedRow.prototype.checkEqualitity = function (pair1, pair2) {\n        if (!pair1 || !pair2)\n            return;\n        var result;\n        pair1.forEach(function (e1, i) {\n            pair2.forEach(function (e2) {\n                if (e1.getLeftChar().getValue() === e2.getLeftChar().getValue() && e1.getRightChar().getValue() === e2.getRightChar().getValue() && e1.getFinderPatter().getValue() === e2.getFinderPatter().getValue()) {\n                    result = true;\n                }\n            });\n        });\n        return result;\n    };\n    return ExpandedRow;\n}());\nexport default ExpandedRow;\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAA6B;IAC7B,SAAS,YAAY,KAAK,EAAE,SAAS,EAAE,WAAW;QAC9C,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,YAAY,SAAS,CAAC,YAAY,GAAG;QACjC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,YAAY,SAAS,CAAC,UAAU,GAAG;QAC/B,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,uBAAuB;IACvB,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,UAAU;QACrD,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;IACtC;IACA,YAAY;IACZ,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,GAAG;IAC/B;IACA;;KAEC,GACD,YAAY;IACZ,uBAAuB;IACvB,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,EAAE,EAAE;QAC3C,IAAI,CAAC,CAAC,cAAc,WAAW,GAAG;YAC9B,OAAO;QACX;QACA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,OAAO,GAAG,WAAW,KAAK,GAAG,WAAW;IAC5E;IACA,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1D,IAAI,CAAC,SAAS,CAAC,OACX;QACJ,IAAI;QACJ,MAAM,OAAO,CAAC,SAAU,EAAE,EAAE,CAAC;YACzB,MAAM,OAAO,CAAC,SAAU,EAAE;gBACtB,IAAI,GAAG,WAAW,GAAG,QAAQ,OAAO,GAAG,WAAW,GAAG,QAAQ,MAAM,GAAG,YAAY,GAAG,QAAQ,OAAO,GAAG,YAAY,GAAG,QAAQ,MAAM,GAAG,eAAe,GAAG,QAAQ,OAAO,GAAG,eAAe,GAAG,QAAQ,IAAI;oBACrM,SAAS;gBACb;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../../../BarcodeFormat';\nimport MathUtils from '../../../common/detector/MathUtils';\n// import FormatException from '../../../FormatException';\nimport NotFoundException from '../../../NotFoundException';\nimport Result from '../../../Result';\nimport System from '../../../util/System';\nimport AbstractRSSReader from '../../rss/AbstractRSSReader';\nimport DataCharacter from '../../rss/DataCharacter';\nimport FinderPattern from '../../rss/FinderPattern';\nimport RSSUtils from '../../rss/RSSUtils';\nimport BitArrayBuilder from './BitArrayBuilder';\nimport { createDecoder } from './decoders/AbstractExpandedDecoderComplement';\nimport ExpandedPair from './ExpandedPair';\nimport ExpandedRow from './ExpandedRow';\n// import java.util.ArrayList;\n// import java.util.Iterator;\n// import java.util.List;\n// import java.util.Map;\n// import java.util.Collections;\n/** @experimental */\nvar RSSExpandedReader = /** @class */ (function (_super) {\n    __extends(RSSExpandedReader, _super);\n    function RSSExpandedReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.pairs = new Array(RSSExpandedReader.MAX_PAIRS);\n        _this.rows = new Array();\n        _this.startEnd = [2];\n        return _this;\n    }\n    RSSExpandedReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        // Rows can start with even pattern in case in prev rows there where odd number of patters.\n        // So lets try twice\n        // this.pairs.clear();\n        this.pairs.length = 0;\n        this.startFromEven = false;\n        try {\n            return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n        }\n        catch (e) {\n            // OK\n            // console.log(e);\n        }\n        this.pairs.length = 0;\n        this.startFromEven = true;\n        return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n    };\n    RSSExpandedReader.prototype.reset = function () {\n        this.pairs.length = 0;\n        this.rows.length = 0;\n    };\n    // Not private for testing\n    RSSExpandedReader.prototype.decodeRow2pairs = function (rowNumber, row) {\n        var done = false;\n        while (!done) {\n            try {\n                this.pairs.push(this.retrieveNextPair(row, this.pairs, rowNumber));\n            }\n            catch (error) {\n                if (error instanceof NotFoundException) {\n                    if (!this.pairs.length) {\n                        throw new NotFoundException();\n                    }\n                    // exit this loop when retrieveNextPair() fails and throws\n                    done = true;\n                }\n            }\n        }\n        // TODO: verify sequence of finder patterns as in checkPairSequence()\n        if (this.checkChecksum()) {\n            return this.pairs;\n        }\n        var tryStackedDecode;\n        if (this.rows.length) {\n            tryStackedDecode = true;\n        }\n        else {\n            tryStackedDecode = false;\n        }\n        // let tryStackedDecode = !this.rows.isEmpty();\n        this.storeRow(rowNumber, false); // TODO: deal with reversed rows\n        if (tryStackedDecode) {\n            // When the image is 180-rotated, then rows are sorted in wrong direction.\n            // Try twice with both the directions.\n            var ps = this.checkRowsBoolean(false);\n            if (ps != null) {\n                return ps;\n            }\n            ps = this.checkRowsBoolean(true);\n            if (ps != null) {\n                return ps;\n            }\n        }\n        throw new NotFoundException();\n    };\n    // Need to Verify\n    RSSExpandedReader.prototype.checkRowsBoolean = function (reverse) {\n        // Limit number of rows we are checking\n        // We use recursive algorithm with pure complexity and don't want it to take forever\n        // Stacked barcode can have up to 11 rows, so 25 seems reasonable enough\n        if (this.rows.length > 25) {\n            this.rows.length = 0; // We will never have a chance to get result, so clear it\n            return null;\n        }\n        this.pairs.length = 0;\n        if (reverse) {\n            this.rows = this.rows.reverse();\n            // Collections.reverse(this.rows);\n        }\n        var ps = null;\n        try {\n            ps = this.checkRows(new Array(), 0);\n        }\n        catch (e) {\n            // OK\n            console.log(e);\n        }\n        if (reverse) {\n            this.rows = this.rows.reverse();\n            // Collections.reverse(this.rows);\n        }\n        return ps;\n    };\n    // Try to construct a valid rows sequence\n    // Recursion is used to implement backtracking\n    RSSExpandedReader.prototype.checkRows = function (collectedRows, currentRow) {\n        var e_1, _a;\n        for (var i = currentRow; i < this.rows.length; i++) {\n            var row = this.rows[i];\n            this.pairs.length = 0;\n            try {\n                for (var collectedRows_1 = (e_1 = void 0, __values(collectedRows)), collectedRows_1_1 = collectedRows_1.next(); !collectedRows_1_1.done; collectedRows_1_1 = collectedRows_1.next()) {\n                    var collectedRow = collectedRows_1_1.value;\n                    this.pairs.push(collectedRow.getPairs());\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (collectedRows_1_1 && !collectedRows_1_1.done && (_a = collectedRows_1.return)) _a.call(collectedRows_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            this.pairs.push(row.getPairs());\n            if (!RSSExpandedReader.isValidSequence(this.pairs)) {\n                continue;\n            }\n            if (this.checkChecksum()) {\n                return this.pairs;\n            }\n            var rs = new Array(collectedRows);\n            rs.push(row);\n            try {\n                // Recursion: try to add more rows\n                return this.checkRows(rs, i + 1);\n            }\n            catch (e) {\n                // We failed, try the next candidate\n                console.log(e);\n            }\n        }\n        throw new NotFoundException();\n    };\n    // Whether the pairs form a valid find pattern sequence,\n    // either complete or a prefix\n    RSSExpandedReader.isValidSequence = function (pairs) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(RSSExpandedReader.FINDER_PATTERN_SEQUENCES), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var sequence = _c.value;\n                if (pairs.length > sequence.length) {\n                    continue;\n                }\n                var stop_1 = true;\n                for (var j = 0; j < pairs.length; j++) {\n                    if (pairs[j].getFinderPattern().getValue() !== sequence[j]) {\n                        stop_1 = false;\n                        break;\n                    }\n                }\n                if (stop_1) {\n                    return true;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return false;\n    };\n    RSSExpandedReader.prototype.storeRow = function (rowNumber, wasReversed) {\n        // Discard if duplicate above or below; otherwise insert in order by row number.\n        var insertPos = 0;\n        var prevIsSame = false;\n        var nextIsSame = false;\n        while (insertPos < this.rows.length) {\n            var erow = this.rows[insertPos];\n            if (erow.getRowNumber() > rowNumber) {\n                nextIsSame = erow.isEquivalent(this.pairs);\n                break;\n            }\n            prevIsSame = erow.isEquivalent(this.pairs);\n            insertPos++;\n        }\n        if (nextIsSame || prevIsSame) {\n            return;\n        }\n        // When the row was partially decoded (e.g. 2 pairs found instead of 3),\n        // it will prevent us from detecting the barcode.\n        // Try to merge partial rows\n        // Check whether the row is part of an allready detected row\n        if (RSSExpandedReader.isPartialRow(this.pairs, this.rows)) {\n            return;\n        }\n        this.rows.push(insertPos, new ExpandedRow(this.pairs, rowNumber, wasReversed));\n        this.removePartialRows(this.pairs, this.rows);\n    };\n    // Remove all the rows that contains only specified pairs\n    RSSExpandedReader.prototype.removePartialRows = function (pairs, rows) {\n        var e_3, _a, e_4, _b, e_5, _c;\n        try {\n            // for (Iterator<ExpandedRow> iterator = rows.iterator(); iterator.hasNext();) {\n            //   ExpandedRow r = iterator.next();\n            //   if (r.getPairs().size() == pairs.size()) {\n            //     continue;\n            //   }\n            //   boolean allFound = true;\n            //   for (ExpandedPair p : r.getPairs()) {\n            //     boolean found = false;\n            //     for (ExpandedPair pp : pairs) {\n            //       if (p.equals(pp)) {\n            //         found = true;\n            //         break;\n            //       }\n            //     }\n            //     if (!found) {\n            //       allFound = false;\n            //       break;\n            //     }\n            //   }\n            //   if (allFound) {\n            //     // 'pairs' contains all the pairs from the row 'r'\n            //     iterator.remove();\n            //   }\n            // }\n            for (var rows_1 = __values(rows), rows_1_1 = rows_1.next(); !rows_1_1.done; rows_1_1 = rows_1.next()) {\n                var row = rows_1_1.value;\n                if (row.getPairs().length === pairs.length) {\n                    continue;\n                }\n                var allFound = true;\n                try {\n                    for (var _d = (e_4 = void 0, __values(row.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var p = _e.value;\n                        var found = false;\n                        try {\n                            for (var pairs_1 = (e_5 = void 0, __values(pairs)), pairs_1_1 = pairs_1.next(); !pairs_1_1.done; pairs_1_1 = pairs_1.next()) {\n                                var pp = pairs_1_1.value;\n                                if (ExpandedPair.equals(p, pp)) {\n                                    found = true;\n                                    break;\n                                }\n                            }\n                        }\n                        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                        finally {\n                            try {\n                                if (pairs_1_1 && !pairs_1_1.done && (_c = pairs_1.return)) _c.call(pairs_1);\n                            }\n                            finally { if (e_5) throw e_5.error; }\n                        }\n                        if (!found) {\n                            allFound = false;\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (rows_1_1 && !rows_1_1.done && (_a = rows_1.return)) _a.call(rows_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    // Returns true when one of the rows already contains all the pairs\n    RSSExpandedReader.isPartialRow = function (pairs, rows) {\n        var e_6, _a, e_7, _b, e_8, _c;\n        try {\n            for (var rows_2 = __values(rows), rows_2_1 = rows_2.next(); !rows_2_1.done; rows_2_1 = rows_2.next()) {\n                var r = rows_2_1.value;\n                var allFound = true;\n                try {\n                    for (var pairs_2 = (e_7 = void 0, __values(pairs)), pairs_2_1 = pairs_2.next(); !pairs_2_1.done; pairs_2_1 = pairs_2.next()) {\n                        var p = pairs_2_1.value;\n                        var found = false;\n                        try {\n                            for (var _d = (e_8 = void 0, __values(r.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n                                var pp = _e.value;\n                                if (p.equals(pp)) {\n                                    found = true;\n                                    break;\n                                }\n                            }\n                        }\n                        catch (e_8_1) { e_8 = { error: e_8_1 }; }\n                        finally {\n                            try {\n                                if (_e && !_e.done && (_c = _d.return)) _c.call(_d);\n                            }\n                            finally { if (e_8) throw e_8.error; }\n                        }\n                        if (!found) {\n                            allFound = false;\n                            break;\n                        }\n                    }\n                }\n                catch (e_7_1) { e_7 = { error: e_7_1 }; }\n                finally {\n                    try {\n                        if (pairs_2_1 && !pairs_2_1.done && (_b = pairs_2.return)) _b.call(pairs_2);\n                    }\n                    finally { if (e_7) throw e_7.error; }\n                }\n                if (allFound) {\n                    // the row 'r' contain all the pairs from 'pairs'\n                    return true;\n                }\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (rows_2_1 && !rows_2_1.done && (_a = rows_2.return)) _a.call(rows_2);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n        return false;\n    };\n    // Only used for unit testing\n    RSSExpandedReader.prototype.getRows = function () {\n        return this.rows;\n    };\n    // Not private for unit testing\n    RSSExpandedReader.constructResult = function (pairs) {\n        var binary = BitArrayBuilder.buildBitArray(pairs);\n        var decoder = createDecoder(binary);\n        var resultingString = decoder.parseInformation();\n        var firstPoints = pairs[0].getFinderPattern().getResultPoints();\n        var lastPoints = pairs[pairs.length - 1]\n            .getFinderPattern()\n            .getResultPoints();\n        var points = [firstPoints[0], firstPoints[1], lastPoints[0], lastPoints[1]];\n        return new Result(resultingString, null, null, points, BarcodeFormat.RSS_EXPANDED, null);\n    };\n    RSSExpandedReader.prototype.checkChecksum = function () {\n        var firstPair = this.pairs.get(0);\n        var checkCharacter = firstPair.getLeftChar();\n        var firstCharacter = firstPair.getRightChar();\n        if (firstCharacter === null) {\n            return false;\n        }\n        var checksum = firstCharacter.getChecksumPortion();\n        var s = 2;\n        for (var i = 1; i < this.pairs.size(); ++i) {\n            var currentPair = this.pairs.get(i);\n            checksum += currentPair.getLeftChar().getChecksumPortion();\n            s++;\n            var currentRightChar = currentPair.getRightChar();\n            if (currentRightChar != null) {\n                checksum += currentRightChar.getChecksumPortion();\n                s++;\n            }\n        }\n        checksum %= 211;\n        var checkCharacterValue = 211 * (s - 4) + checksum;\n        return checkCharacterValue === checkCharacter.getValue();\n    };\n    RSSExpandedReader.getNextSecondBar = function (row, initialPos) {\n        var currentPos;\n        if (row.get(initialPos)) {\n            currentPos = row.getNextUnset(initialPos);\n            currentPos = row.getNextSet(currentPos);\n        }\n        else {\n            currentPos = row.getNextSet(initialPos);\n            currentPos = row.getNextUnset(currentPos);\n        }\n        return currentPos;\n    };\n    // not private for testing\n    RSSExpandedReader.prototype.retrieveNextPair = function (row, previousPairs, rowNumber) {\n        var isOddPattern = previousPairs.length % 2 === 0;\n        if (this.startFromEven) {\n            isOddPattern = !isOddPattern;\n        }\n        var pattern;\n        var keepFinding = true;\n        var forcedOffset = -1;\n        do {\n            this.findNextPair(row, previousPairs, forcedOffset);\n            pattern = this.parseFoundFinderPattern(row, rowNumber, isOddPattern);\n            if (pattern === null) {\n                forcedOffset = RSSExpandedReader.getNextSecondBar(row, this.startEnd[0]);\n            }\n            else {\n                keepFinding = false;\n            }\n        } while (keepFinding);\n        // When stacked symbol is split over multiple rows, there's no way to guess if this pair can be last or not.\n        // boolean mayBeLast = checkPairSequence(previousPairs, pattern);\n        var leftChar = this.decodeDataCharacter(row, pattern, isOddPattern, true);\n        if (!this.isEmptyPair(previousPairs) &&\n            previousPairs[previousPairs.length - 1].mustBeLast()) {\n            throw new NotFoundException();\n        }\n        var rightChar;\n        try {\n            rightChar = this.decodeDataCharacter(row, pattern, isOddPattern, false);\n        }\n        catch (e) {\n            rightChar = null;\n            console.log(e);\n        }\n        return new ExpandedPair(leftChar, rightChar, pattern, true);\n    };\n    RSSExpandedReader.prototype.isEmptyPair = function (pairs) {\n        if (pairs.length === 0) {\n            return true;\n        }\n        return false;\n    };\n    RSSExpandedReader.prototype.findNextPair = function (row, previousPairs, forcedOffset) {\n        var counters = this.getDecodeFinderCounters();\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var width = row.getSize();\n        var rowOffset;\n        if (forcedOffset >= 0) {\n            rowOffset = forcedOffset;\n        }\n        else if (this.isEmptyPair(previousPairs)) {\n            rowOffset = 0;\n        }\n        else {\n            var lastPair = previousPairs[previousPairs.length - 1];\n            rowOffset = lastPair.getFinderPattern().getStartEnd()[1];\n        }\n        var searchingEvenPair = previousPairs.length % 2 !== 0;\n        if (this.startFromEven) {\n            searchingEvenPair = !searchingEvenPair;\n        }\n        var isWhite = false;\n        while (rowOffset < width) {\n            isWhite = !row.get(rowOffset);\n            if (!isWhite) {\n                break;\n            }\n            rowOffset++;\n        }\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === 3) {\n                    if (searchingEvenPair) {\n                        RSSExpandedReader.reverseCounters(counters);\n                    }\n                    if (RSSExpandedReader.isFinderPattern(counters)) {\n                        this.startEnd[0] = patternStart;\n                        this.startEnd[1] = x;\n                        return;\n                    }\n                    if (searchingEvenPair) {\n                        RSSExpandedReader.reverseCounters(counters);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters[0] = counters[2];\n                    counters[1] = counters[3];\n                    counters[2] = 0;\n                    counters[3] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    RSSExpandedReader.reverseCounters = function (counters) {\n        var length = counters.length;\n        for (var i = 0; i < length / 2; ++i) {\n            var tmp = counters[i];\n            counters[i] = counters[length - i - 1];\n            counters[length - i - 1] = tmp;\n        }\n    };\n    RSSExpandedReader.prototype.parseFoundFinderPattern = function (row, rowNumber, oddPattern) {\n        // Actually we found elements 2-5.\n        var firstCounter;\n        var start;\n        var end;\n        if (oddPattern) {\n            // If pattern number is odd, we need to locate element 1 *before* the current block.\n            var firstElementStart = this.startEnd[0] - 1;\n            // Locate element 1\n            while (firstElementStart >= 0 && !row.get(firstElementStart)) {\n                firstElementStart--;\n            }\n            firstElementStart++;\n            firstCounter = this.startEnd[0] - firstElementStart;\n            start = firstElementStart;\n            end = this.startEnd[1];\n        }\n        else {\n            // If pattern number is even, the pattern is reversed, so we need to locate element 1 *after* the current block.\n            start = this.startEnd[0];\n            end = row.getNextUnset(this.startEnd[1] + 1);\n            firstCounter = end - this.startEnd[1];\n        }\n        // Make 'counters' hold 1-4\n        var counters = this.getDecodeFinderCounters();\n        System.arraycopy(counters, 0, counters, 1, counters.length - 1);\n        counters[0] = firstCounter;\n        var value;\n        try {\n            value = this.parseFinderValue(counters, RSSExpandedReader.FINDER_PATTERNS);\n        }\n        catch (e) {\n            return null;\n        }\n        // return new FinderPattern(value, new int[] { start, end }, start, end, rowNumber});\n        return new FinderPattern(value, [start, end], start, end, rowNumber);\n    };\n    RSSExpandedReader.prototype.decodeDataCharacter = function (row, pattern, isOddPattern, leftChar) {\n        var counters = this.getDataCharacterCounters();\n        for (var x = 0; x < counters.length; x++) {\n            counters[x] = 0;\n        }\n        if (leftChar) {\n            RSSExpandedReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n        }\n        else {\n            RSSExpandedReader.recordPattern(row, pattern.getStartEnd()[1], counters);\n            // reverse it\n            for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n                var temp = counters[i];\n                counters[i] = counters[j];\n                counters[j] = temp;\n            }\n        } // counters[] has the pixels of the module\n        var numModules = 17; // left and right data characters have all the same length\n        var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n        // Sanity check: element width for pattern and the character should match\n        var expectedElementWidth = (pattern.getStartEnd()[1] - pattern.getStartEnd()[0]) / 15.0;\n        if (Math.abs(elementWidth - expectedElementWidth) / expectedElementWidth >\n            0.3) {\n            throw new NotFoundException();\n        }\n        var oddCounts = this.getOddCounts();\n        var evenCounts = this.getEvenCounts();\n        var oddRoundingErrors = this.getOddRoundingErrors();\n        var evenRoundingErrors = this.getEvenRoundingErrors();\n        for (var i = 0; i < counters.length; i++) {\n            var value_1 = (1.0 * counters[i]) / elementWidth;\n            var count = value_1 + 0.5; // Round\n            if (count < 1) {\n                if (value_1 < 0.3) {\n                    throw new NotFoundException();\n                }\n                count = 1;\n            }\n            else if (count > 8) {\n                if (value_1 > 8.7) {\n                    throw new NotFoundException();\n                }\n                count = 8;\n            }\n            var offset = i / 2;\n            if ((i & 0x01) === 0) {\n                oddCounts[offset] = count;\n                oddRoundingErrors[offset] = value_1 - count;\n            }\n            else {\n                evenCounts[offset] = count;\n                evenRoundingErrors[offset] = value_1 - count;\n            }\n        }\n        this.adjustOddEvenCounts(numModules);\n        var weightRowNumber = 4 * pattern.getValue() + (isOddPattern ? 0 : 2) + (leftChar ? 0 : 1) - 1;\n        var oddSum = 0;\n        var oddChecksumPortion = 0;\n        for (var i = oddCounts.length - 1; i >= 0; i--) {\n            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i];\n                oddChecksumPortion += oddCounts[i] * weight;\n            }\n            oddSum += oddCounts[i];\n        }\n        var evenChecksumPortion = 0;\n        // int evenSum = 0;\n        for (var i = evenCounts.length - 1; i >= 0; i--) {\n            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i + 1];\n                evenChecksumPortion += evenCounts[i] * weight;\n            }\n            // evenSum += evenCounts[i];\n        }\n        var checksumPortion = oddChecksumPortion + evenChecksumPortion;\n        if ((oddSum & 0x01) !== 0 || oddSum > 13 || oddSum < 4) {\n            throw new NotFoundException();\n        }\n        var group = (13 - oddSum) / 2;\n        var oddWidest = RSSExpandedReader.SYMBOL_WIDEST[group];\n        var evenWidest = 9 - oddWidest;\n        var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n        var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n        var tEven = RSSExpandedReader.EVEN_TOTAL_SUBSET[group];\n        var gSum = RSSExpandedReader.GSUM[group];\n        var value = vOdd * tEven + vEven + gSum;\n        return new DataCharacter(value, checksumPortion);\n    };\n    RSSExpandedReader.isNotA1left = function (pattern, isOddPattern, leftChar) {\n        // A1: pattern.getValue is 0 (A), and it's an oddPattern, and it is a left char\n        return !(pattern.getValue() === 0 && isOddPattern && leftChar);\n    };\n    RSSExpandedReader.prototype.adjustOddEvenCounts = function (numModules) {\n        var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n        var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n        var incrementOdd = false;\n        var decrementOdd = false;\n        if (oddSum > 13) {\n            decrementOdd = true;\n        }\n        else if (oddSum < 4) {\n            incrementOdd = true;\n        }\n        var incrementEven = false;\n        var decrementEven = false;\n        if (evenSum > 13) {\n            decrementEven = true;\n        }\n        else if (evenSum < 4) {\n            incrementEven = true;\n        }\n        var mismatch = oddSum + evenSum - numModules;\n        var oddParityBad = (oddSum & 0x01) === 1;\n        var evenParityBad = (evenSum & 0x01) === 0;\n        if (mismatch === 1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementEven = true;\n            }\n        }\n        else if (mismatch === -1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementEven = true;\n            }\n        }\n        else if (mismatch === 0) {\n            if (oddParityBad) {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Both bad\n                if (oddSum < evenSum) {\n                    incrementOdd = true;\n                    decrementEven = true;\n                }\n                else {\n                    decrementOdd = true;\n                    incrementEven = true;\n                }\n            }\n            else {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Nothing to do!\n            }\n        }\n        else {\n            throw new NotFoundException();\n        }\n        if (incrementOdd) {\n            if (decrementOdd) {\n                throw new NotFoundException();\n            }\n            RSSExpandedReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementOdd) {\n            RSSExpandedReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (incrementEven) {\n            if (decrementEven) {\n                throw new NotFoundException();\n            }\n            RSSExpandedReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementEven) {\n            RSSExpandedReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n        }\n    };\n    RSSExpandedReader.SYMBOL_WIDEST = [7, 5, 4, 3, 1];\n    RSSExpandedReader.EVEN_TOTAL_SUBSET = [4, 20, 52, 104, 204];\n    RSSExpandedReader.GSUM = [0, 348, 1388, 2948, 3988];\n    RSSExpandedReader.FINDER_PATTERNS = [\n        Int32Array.from([1, 8, 4, 1]),\n        Int32Array.from([3, 6, 4, 1]),\n        Int32Array.from([3, 4, 6, 1]),\n        Int32Array.from([3, 2, 8, 1]),\n        Int32Array.from([2, 6, 5, 1]),\n        Int32Array.from([2, 2, 9, 1]),\n    ];\n    RSSExpandedReader.WEIGHTS = [\n        [1, 3, 9, 27, 81, 32, 96, 77],\n        [20, 60, 180, 118, 143, 7, 21, 63],\n        [189, 145, 13, 39, 117, 140, 209, 205],\n        [193, 157, 49, 147, 19, 57, 171, 91],\n        [62, 186, 136, 197, 169, 85, 44, 132],\n        [185, 133, 188, 142, 4, 12, 36, 108],\n        [113, 128, 173, 97, 80, 29, 87, 50],\n        [150, 28, 84, 41, 123, 158, 52, 156],\n        [46, 138, 203, 187, 139, 206, 196, 166],\n        [76, 17, 51, 153, 37, 111, 122, 155],\n        [43, 129, 176, 106, 107, 110, 119, 146],\n        [16, 48, 144, 10, 30, 90, 59, 177],\n        [109, 116, 137, 200, 178, 112, 125, 164],\n        [70, 210, 208, 202, 184, 130, 179, 115],\n        [134, 191, 151, 31, 93, 68, 204, 190],\n        [148, 22, 66, 198, 172, 94, 71, 2],\n        [6, 18, 54, 162, 64, 192, 154, 40],\n        [120, 149, 25, 75, 14, 42, 126, 167],\n        [79, 26, 78, 23, 69, 207, 199, 175],\n        [103, 98, 83, 38, 114, 131, 182, 124],\n        [161, 61, 183, 127, 170, 88, 53, 159],\n        [55, 165, 73, 8, 24, 72, 5, 15],\n        [45, 135, 194, 160, 58, 174, 100, 89],\n    ];\n    RSSExpandedReader.FINDER_PAT_A = 0;\n    RSSExpandedReader.FINDER_PAT_B = 1;\n    RSSExpandedReader.FINDER_PAT_C = 2;\n    RSSExpandedReader.FINDER_PAT_D = 3;\n    RSSExpandedReader.FINDER_PAT_E = 4;\n    RSSExpandedReader.FINDER_PAT_F = 5;\n    RSSExpandedReader.FINDER_PATTERN_SEQUENCES = [\n        [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_C,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_E,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n    ];\n    RSSExpandedReader.MAX_PAIRS = 11;\n    return RSSExpandedReader;\n}(AbstractRSSReader));\nexport default RSSExpandedReader;\n"], "names": [], "mappings": ";;;AAwBA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArCA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;;;;;AAeA,8BAA8B;AAC9B,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB;AACxB,gCAAgC;AAChC,kBAAkB,GAClB,IAAI,oBAAmC,SAAU,MAAM;IACnD,UAAU,mBAAmB;IAC7B,SAAS;QACL,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,KAAK,GAAG,IAAI,MAAM,kBAAkB,SAAS;QACnD,MAAM,IAAI,GAAG,IAAI;QACjB,MAAM,QAAQ,GAAG;YAAC;SAAE;QACpB,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QACnE,2FAA2F;QAC3F,oBAAoB;QACpB,sBAAsB;QACtB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI;YACA,OAAO,kBAAkB,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW;QAC7E,EACA,OAAO,GAAG;QACN,KAAK;QACL,kBAAkB;QACtB;QACA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,aAAa,GAAG;QACrB,OAAO,kBAAkB,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW;IAC7E;IACA,kBAAkB,SAAS,CAAC,KAAK,GAAG;QAChC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;IACvB;IACA,0BAA0B;IAC1B,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,SAAS,EAAE,GAAG;QAClE,IAAI,OAAO;QACX,MAAO,CAAC,KAAM;YACV,IAAI;gBACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YAC3D,EACA,OAAO,OAAO;gBACV,IAAI,iBAAiB,yKAAA,CAAA,UAAiB,EAAE;oBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;wBACpB,MAAM,IAAI,yKAAA,CAAA,UAAiB;oBAC/B;oBACA,0DAA0D;oBAC1D,OAAO;gBACX;YACJ;QACJ;QACA,qEAAqE;QACrE,IAAI,IAAI,CAAC,aAAa,IAAI;YACtB,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAClB,mBAAmB;QACvB,OACK;YACD,mBAAmB;QACvB;QACA,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,CAAC,WAAW,QAAQ,gCAAgC;QACjE,IAAI,kBAAkB;YAClB,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,KAAK,IAAI,CAAC,gBAAgB,CAAC;YAC/B,IAAI,MAAM,MAAM;gBACZ,OAAO;YACX;YACA,KAAK,IAAI,CAAC,gBAAgB,CAAC;YAC3B,IAAI,MAAM,MAAM;gBACZ,OAAO;YACX;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,iBAAiB;IACjB,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;QAC5D,uCAAuC;QACvC,oFAAoF;QACpF,wEAAwE;QACxE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;YACvB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,yDAAyD;YAC/E,OAAO;QACX;QACA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,SAAS;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO;QAC7B,kCAAkC;QACtC;QACA,IAAI,KAAK;QACT,IAAI;YACA,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS;QACrC,EACA,OAAO,GAAG;YACN,KAAK;YACL,QAAQ,GAAG,CAAC;QAChB;QACA,IAAI,SAAS;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO;QAC7B,kCAAkC;QACtC;QACA,OAAO;IACX;IACA,yCAAyC;IACzC,8CAA8C;IAC9C,kBAAkB,SAAS,CAAC,SAAS,GAAG,SAAU,aAAa,EAAE,UAAU;QACvE,IAAI,KAAK;QACT,IAAK,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YAChD,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YACpB,IAAI;gBACA,IAAK,IAAI,kBAAkB,CAAC,MAAM,KAAK,GAAG,SAAS,cAAc,GAAG,oBAAoB,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,IAAI,EAAE,oBAAoB,gBAAgB,IAAI,GAAI;oBACjL,IAAI,eAAe,kBAAkB,KAAK;oBAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,QAAQ;gBACzC;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,qBAAqB,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,gBAAgB,MAAM,GAAG,GAAG,IAAI,CAAC;gBAC/F,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ;YAC5B,IAAI,CAAC,kBAAkB,eAAe,CAAC,IAAI,CAAC,KAAK,GAAG;gBAChD;YACJ;YACA,IAAI,IAAI,CAAC,aAAa,IAAI;gBACtB,OAAO,IAAI,CAAC,KAAK;YACrB;YACA,IAAI,KAAK,IAAI,MAAM;YACnB,GAAG,IAAI,CAAC;YACR,IAAI;gBACA,kCAAkC;gBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI;YAClC,EACA,OAAO,GAAG;gBACN,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;YAChB;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,wDAAwD;IACxD,8BAA8B;IAC9B,kBAAkB,eAAe,GAAG,SAAU,KAAK;QAC/C,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,kBAAkB,wBAAwB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC1G,IAAI,WAAW,GAAG,KAAK;gBACvB,IAAI,MAAM,MAAM,GAAG,SAAS,MAAM,EAAE;oBAChC;gBACJ;gBACA,IAAI,SAAS;gBACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,IAAI,KAAK,CAAC,EAAE,CAAC,gBAAgB,GAAG,QAAQ,OAAO,QAAQ,CAAC,EAAE,EAAE;wBACxD,SAAS;wBACT;oBACJ;gBACJ;gBACA,IAAI,QAAQ;oBACR,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS,EAAE,WAAW;QACnE,gFAAgF;QAChF,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI,aAAa;QACjB,MAAO,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAE;YACjC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;YAC/B,IAAI,KAAK,YAAY,KAAK,WAAW;gBACjC,aAAa,KAAK,YAAY,CAAC,IAAI,CAAC,KAAK;gBACzC;YACJ;YACA,aAAa,KAAK,YAAY,CAAC,IAAI,CAAC,KAAK;YACzC;QACJ;QACA,IAAI,cAAc,YAAY;YAC1B;QACJ;QACA,wEAAwE;QACxE,iDAAiD;QACjD,4BAA4B;QAC5B,4DAA4D;QAC5D,IAAI,kBAAkB,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG;YACvD;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW;QACjE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI;IAChD;IACA,yDAAyD;IACzD,kBAAkB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,IAAI;QACjE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QAC3B,IAAI;YACA,gFAAgF;YAChF,qCAAqC;YACrC,+CAA+C;YAC/C,gBAAgB;YAChB,MAAM;YACN,6BAA6B;YAC7B,0CAA0C;YAC1C,6BAA6B;YAC7B,sCAAsC;YACtC,4BAA4B;YAC5B,wBAAwB;YACxB,iBAAiB;YACjB,UAAU;YACV,QAAQ;YACR,oBAAoB;YACpB,0BAA0B;YAC1B,eAAe;YACf,QAAQ;YACR,MAAM;YACN,oBAAoB;YACpB,yDAAyD;YACzD,yBAAyB;YACzB,MAAM;YACN,IAAI;YACJ,IAAK,IAAI,SAAS,SAAS,OAAO,WAAW,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,WAAW,OAAO,IAAI,GAAI;gBAClG,IAAI,MAAM,SAAS,KAAK;gBACxB,IAAI,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,MAAM,EAAE;oBACxC;gBACJ;gBACA,IAAI,WAAW;gBACf,IAAI;oBACA,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,IAAI,QAAQ,GAAG,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;wBAC9F,IAAI,IAAI,GAAG,KAAK;wBAChB,IAAI,QAAQ;wBACZ,IAAI;4BACA,IAAK,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,SAAS,MAAM,GAAG,YAAY,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE,YAAY,QAAQ,IAAI,GAAI;gCACzH,IAAI,KAAK,UAAU,KAAK;gCACxB,IAAI,+LAAA,CAAA,UAAY,CAAC,MAAM,CAAC,GAAG,KAAK;oCAC5B,QAAQ;oCACR;gCACJ;4BACJ;wBACJ,EACA,OAAO,OAAO;4BAAE,MAAM;gCAAE,OAAO;4BAAM;wBAAG,SAChC;4BACJ,IAAI;gCACA,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC;4BACvE,SACQ;gCAAE,IAAI,KAAK,MAAM,IAAI,KAAK;4BAAE;wBACxC;wBACA,IAAI,CAAC,OAAO;4BACR,WAAW;wBACf;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;oBACpD,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,OAAO,MAAM,GAAG,GAAG,IAAI,CAAC;YACpE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA,mEAAmE;IACnE,kBAAkB,YAAY,GAAG,SAAU,KAAK,EAAE,IAAI;QAClD,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QAC3B,IAAI;YACA,IAAK,IAAI,SAAS,SAAS,OAAO,WAAW,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,WAAW,OAAO,IAAI,GAAI;gBAClG,IAAI,IAAI,SAAS,KAAK;gBACtB,IAAI,WAAW;gBACf,IAAI;oBACA,IAAK,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,SAAS,MAAM,GAAG,YAAY,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE,YAAY,QAAQ,IAAI,GAAI;wBACzH,IAAI,IAAI,UAAU,KAAK;wBACvB,IAAI,QAAQ;wBACZ,IAAI;4BACA,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,EAAE,QAAQ,GAAG,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gCAC5F,IAAI,KAAK,GAAG,KAAK;gCACjB,IAAI,EAAE,MAAM,CAAC,KAAK;oCACd,QAAQ;oCACR;gCACJ;4BACJ;wBACJ,EACA,OAAO,OAAO;4BAAE,MAAM;gCAAE,OAAO;4BAAM;wBAAG,SAChC;4BACJ,IAAI;gCACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;4BACpD,SACQ;gCAAE,IAAI,KAAK,MAAM,IAAI,KAAK;4BAAE;wBACxC;wBACA,IAAI,CAAC,OAAO;4BACR,WAAW;4BACX;wBACJ;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC;oBACvE,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;gBACA,IAAI,UAAU;oBACV,iDAAiD;oBACjD,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,OAAO,MAAM,GAAG,GAAG,IAAI,CAAC;YACpE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,6BAA6B;IAC7B,kBAAkB,SAAS,CAAC,OAAO,GAAG;QAClC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,+BAA+B;IAC/B,kBAAkB,eAAe,GAAG,SAAU,KAAK;QAC/C,IAAI,SAAS,kMAAA,CAAA,UAAe,CAAC,aAAa,CAAC;QAC3C,IAAI,UAAU,CAAA,GAAA,gOAAA,CAAA,gBAAa,AAAD,EAAE;QAC5B,IAAI,kBAAkB,QAAQ,gBAAgB;QAC9C,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC,gBAAgB,GAAG,eAAe;QAC7D,IAAI,aAAa,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CACnC,gBAAgB,GAChB,eAAe;QACpB,IAAI,SAAS;YAAC,WAAW,CAAC,EAAE;YAAE,WAAW,CAAC,EAAE;YAAE,UAAU,CAAC,EAAE;YAAE,UAAU,CAAC,EAAE;SAAC;QAC3E,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,iBAAiB,MAAM,MAAM,QAAQ,qKAAA,CAAA,UAAa,CAAC,YAAY,EAAE;IACvF;IACA,kBAAkB,SAAS,CAAC,aAAa,GAAG;QACxC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC/B,IAAI,iBAAiB,UAAU,WAAW;QAC1C,IAAI,iBAAiB,UAAU,YAAY;QAC3C,IAAI,mBAAmB,MAAM;YACzB,OAAO;QACX;QACA,IAAI,WAAW,eAAe,kBAAkB;QAChD,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,EAAG;YACxC,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACjC,YAAY,YAAY,WAAW,GAAG,kBAAkB;YACxD;YACA,IAAI,mBAAmB,YAAY,YAAY;YAC/C,IAAI,oBAAoB,MAAM;gBAC1B,YAAY,iBAAiB,kBAAkB;gBAC/C;YACJ;QACJ;QACA,YAAY;QACZ,IAAI,sBAAsB,MAAM,CAAC,IAAI,CAAC,IAAI;QAC1C,OAAO,wBAAwB,eAAe,QAAQ;IAC1D;IACA,kBAAkB,gBAAgB,GAAG,SAAU,GAAG,EAAE,UAAU;QAC1D,IAAI;QACJ,IAAI,IAAI,GAAG,CAAC,aAAa;YACrB,aAAa,IAAI,YAAY,CAAC;YAC9B,aAAa,IAAI,UAAU,CAAC;QAChC,OACK;YACD,aAAa,IAAI,UAAU,CAAC;YAC5B,aAAa,IAAI,YAAY,CAAC;QAClC;QACA,OAAO;IACX;IACA,0BAA0B;IAC1B,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,aAAa,EAAE,SAAS;QAClF,IAAI,eAAe,cAAc,MAAM,GAAG,MAAM;QAChD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,eAAe,CAAC;QACpB;QACA,IAAI;QACJ,IAAI,cAAc;QAClB,IAAI,eAAe,CAAC;QACpB,GAAG;YACC,IAAI,CAAC,YAAY,CAAC,KAAK,eAAe;YACtC,UAAU,IAAI,CAAC,uBAAuB,CAAC,KAAK,WAAW;YACvD,IAAI,YAAY,MAAM;gBAClB,eAAe,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC3E,OACK;gBACD,cAAc;YAClB;QACJ,QAAS,YAAa;QACtB,4GAA4G;QAC5G,iEAAiE;QACjE,IAAI,WAAW,IAAI,CAAC,mBAAmB,CAAC,KAAK,SAAS,cAAc;QACpE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAClB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,CAAC,UAAU,IAAI;YACtD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI;QACJ,IAAI;YACA,YAAY,IAAI,CAAC,mBAAmB,CAAC,KAAK,SAAS,cAAc;QACrE,EACA,OAAO,GAAG;YACN,YAAY;YACZ,QAAQ,GAAG,CAAC;QAChB;QACA,OAAO,IAAI,+LAAA,CAAA,UAAY,CAAC,UAAU,WAAW,SAAS;IAC1D;IACA,kBAAkB,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QACrD,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB,OAAO;QACX;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,aAAa,EAAE,YAAY;QACjF,IAAI,WAAW,IAAI,CAAC,uBAAuB;QAC3C,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI;QACJ,IAAI,gBAAgB,GAAG;YACnB,YAAY;QAChB,OACK,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB;YACtC,YAAY;QAChB,OACK;YACD,IAAI,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;YACtD,YAAY,SAAS,gBAAgB,GAAG,WAAW,EAAE,CAAC,EAAE;QAC5D;QACA,IAAI,oBAAoB,cAAc,MAAM,GAAG,MAAM;QACrD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,oBAAoB,CAAC;QACzB;QACA,IAAI,UAAU;QACd,MAAO,YAAY,MAAO;YACtB,UAAU,CAAC,IAAI,GAAG,CAAC;YACnB,IAAI,CAAC,SAAS;gBACV;YACJ;YACA;QACJ;QACA,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,GAAG;oBACvB,IAAI,mBAAmB;wBACnB,kBAAkB,eAAe,CAAC;oBACtC;oBACA,IAAI,kBAAkB,eAAe,CAAC,WAAW;wBAC7C,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;wBACnB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;wBACnB;oBACJ;oBACA,IAAI,mBAAmB;wBACnB,kBAAkB,eAAe,CAAC;oBACtC;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzB,QAAQ,CAAC,EAAE,GAAG;oBACd,QAAQ,CAAC,EAAE,GAAG;oBACd;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,kBAAkB,eAAe,GAAG,SAAU,QAAQ;QAClD,IAAI,SAAS,SAAS,MAAM;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,EAAE,EAAG;YACjC,IAAI,MAAM,QAAQ,CAAC,EAAE;YACrB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,SAAS,IAAI,EAAE;YACtC,QAAQ,CAAC,SAAS,IAAI,EAAE,GAAG;QAC/B;IACJ;IACA,kBAAkB,SAAS,CAAC,uBAAuB,GAAG,SAAU,GAAG,EAAE,SAAS,EAAE,UAAU;QACtF,kCAAkC;QAClC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,YAAY;YACZ,oFAAoF;YACpF,IAAI,oBAAoB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;YAC3C,mBAAmB;YACnB,MAAO,qBAAqB,KAAK,CAAC,IAAI,GAAG,CAAC,mBAAoB;gBAC1D;YACJ;YACA;YACA,eAAe,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;YAClC,QAAQ;YACR,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC1B,OACK;YACD,gHAAgH;YAChH,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YACxB,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;YAC1C,eAAe,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;QACzC;QACA,2BAA2B;QAC3B,IAAI,WAAW,IAAI,CAAC,uBAAuB;QAC3C,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,MAAM,GAAG;QAC7D,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI;QACJ,IAAI;YACA,QAAQ,IAAI,CAAC,gBAAgB,CAAC,UAAU,kBAAkB,eAAe;QAC7E,EACA,OAAO,GAAG;YACN,OAAO;QACX;QACA,qFAAqF;QACrF,OAAO,IAAI,oLAAA,CAAA,UAAa,CAAC,OAAO;YAAC;YAAO;SAAI,EAAE,OAAO,KAAK;IAC9D;IACA,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ;QAC5F,IAAI,WAAW,IAAI,CAAC,wBAAwB;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,QAAQ,CAAC,EAAE,GAAG;QAClB;QACA,IAAI,UAAU;YACV,kBAAkB,sBAAsB,CAAC,KAAK,QAAQ,WAAW,EAAE,CAAC,EAAE,EAAE;QAC5E,OACK;YACD,kBAAkB,aAAa,CAAC,KAAK,QAAQ,WAAW,EAAE,CAAC,EAAE,EAAE;YAC/D,aAAa;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,IAAK;gBACtD,IAAI,OAAO,QAAQ,CAAC,EAAE;gBACtB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzB,QAAQ,CAAC,EAAE,GAAG;YAClB;QACJ,EAAE,0CAA0C;QAC5C,IAAI,aAAa,IAAI,0DAA0D;QAC/E,IAAI,eAAe,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,aAAa;QAC7D,yEAAyE;QACzE,IAAI,uBAAuB,CAAC,QAAQ,WAAW,EAAE,CAAC,EAAE,GAAG,QAAQ,WAAW,EAAE,CAAC,EAAE,IAAI;QACnF,IAAI,KAAK,GAAG,CAAC,eAAe,wBAAwB,uBAChD,KAAK;YACL,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,aAAa,IAAI,CAAC,aAAa;QACnC,IAAI,oBAAoB,IAAI,CAAC,oBAAoB;QACjD,IAAI,qBAAqB,IAAI,CAAC,qBAAqB;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,UAAU,AAAC,MAAM,QAAQ,CAAC,EAAE,GAAI;YACpC,IAAI,QAAQ,UAAU,KAAK,QAAQ;YACnC,IAAI,QAAQ,GAAG;gBACX,IAAI,UAAU,KAAK;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,QAAQ;YACZ,OACK,IAAI,QAAQ,GAAG;gBAChB,IAAI,UAAU,KAAK;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,QAAQ;YACZ;YACA,IAAI,SAAS,IAAI;YACjB,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;gBAClB,SAAS,CAAC,OAAO,GAAG;gBACpB,iBAAiB,CAAC,OAAO,GAAG,UAAU;YAC1C,OACK;gBACD,UAAU,CAAC,OAAO,GAAG;gBACrB,kBAAkB,CAAC,OAAO,GAAG,UAAU;YAC3C;QACJ;QACA,IAAI,CAAC,mBAAmB,CAAC;QACzB,IAAI,kBAAkB,IAAI,QAAQ,QAAQ,KAAK,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI;QAC7F,IAAI,SAAS;QACb,IAAI,qBAAqB;QACzB,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC5C,IAAI,kBAAkB,WAAW,CAAC,SAAS,cAAc,WAAW;gBAChE,IAAI,SAAS,kBAAkB,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE;gBAC9D,sBAAsB,SAAS,CAAC,EAAE,GAAG;YACzC;YACA,UAAU,SAAS,CAAC,EAAE;QAC1B;QACA,IAAI,sBAAsB;QAC1B,mBAAmB;QACnB,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC7C,IAAI,kBAAkB,WAAW,CAAC,SAAS,cAAc,WAAW;gBAChE,IAAI,SAAS,kBAAkB,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,EAAE;gBAClE,uBAAuB,UAAU,CAAC,EAAE,GAAG;YAC3C;QACA,4BAA4B;QAChC;QACA,IAAI,kBAAkB,qBAAqB;QAC3C,IAAI,CAAC,SAAS,IAAI,MAAM,KAAK,SAAS,MAAM,SAAS,GAAG;YACpD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,QAAQ,CAAC,KAAK,MAAM,IAAI;QAC5B,IAAI,YAAY,kBAAkB,aAAa,CAAC,MAAM;QACtD,IAAI,aAAa,IAAI;QACrB,IAAI,OAAO,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,WAAW,WAAW;QACtD,IAAI,QAAQ,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,YAAY,YAAY;QACzD,IAAI,QAAQ,kBAAkB,iBAAiB,CAAC,MAAM;QACtD,IAAI,OAAO,kBAAkB,IAAI,CAAC,MAAM;QACxC,IAAI,QAAQ,OAAO,QAAQ,QAAQ;QACnC,OAAO,IAAI,oLAAA,CAAA,UAAa,CAAC,OAAO;IACpC;IACA,kBAAkB,WAAW,GAAG,SAAU,OAAO,EAAE,YAAY,EAAE,QAAQ;QACrE,+EAA+E;QAC/E,OAAO,CAAC,CAAC,QAAQ,QAAQ,OAAO,KAAK,gBAAgB,QAAQ;IACjE;IACA,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,UAAU;QAClE,IAAI,SAAS,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,CAAC,YAAY;QAC3D,IAAI,UAAU,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,CAAC,aAAa;QAC7D,IAAI,eAAe;QACnB,IAAI,eAAe;QACnB,IAAI,SAAS,IAAI;YACb,eAAe;QACnB,OACK,IAAI,SAAS,GAAG;YACjB,eAAe;QACnB;QACA,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QACpB,IAAI,UAAU,IAAI;YACd,gBAAgB;QACpB,OACK,IAAI,UAAU,GAAG;YAClB,gBAAgB;QACpB;QACA,IAAI,WAAW,SAAS,UAAU;QAClC,IAAI,eAAe,CAAC,SAAS,IAAI,MAAM;QACvC,IAAI,gBAAgB,CAAC,UAAU,IAAI,MAAM;QACzC,IAAI,aAAa,GAAG;YAChB,IAAI,cAAc;gBACd,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,eAAe;YACnB,OACK;gBACD,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,gBAAgB;YACpB;QACJ,OACK,IAAI,aAAa,CAAC,GAAG;YACtB,IAAI,cAAc;gBACd,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,eAAe;YACnB,OACK;gBACD,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,gBAAgB;YACpB;QACJ,OACK,IAAI,aAAa,GAAG;YACrB,IAAI,cAAc;gBACd,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,WAAW;gBACX,IAAI,SAAS,SAAS;oBAClB,eAAe;oBACf,gBAAgB;gBACpB,OACK;oBACD,eAAe;oBACf,gBAAgB;gBACpB;YACJ,OACK;gBACD,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;YACA,iBAAiB;YACrB;QACJ,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,cAAc;YACd,IAAI,cAAc;gBACd,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,kBAAkB,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;QAC9E;QACA,IAAI,cAAc;YACd,kBAAkB,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;QAC9E;QACA,IAAI,eAAe;YACf,IAAI,eAAe;gBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,kBAAkB,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,oBAAoB;QAC/E;QACA,IAAI,eAAe;YACf,kBAAkB,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,qBAAqB;QAChF;IACJ;IACA,kBAAkB,aAAa,GAAG;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE;IACjD,kBAAkB,iBAAiB,GAAG;QAAC;QAAG;QAAI;QAAI;QAAK;KAAI;IAC3D,kBAAkB,IAAI,GAAG;QAAC;QAAG;QAAK;QAAM;QAAM;KAAK;IACnD,kBAAkB,eAAe,GAAG;QAChC,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;KAC/B;IACD,kBAAkB,OAAO,GAAG;QACxB;YAAC;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;SAAG;QAC7B;YAAC;YAAI;YAAI;YAAK;YAAK;YAAK;YAAG;YAAI;SAAG;QAClC;YAAC;YAAK;YAAK;YAAI;YAAI;YAAK;YAAK;YAAK;SAAI;QACtC;YAAC;YAAK;YAAK;YAAI;YAAK;YAAI;YAAI;YAAK;SAAG;QACpC;YAAC;YAAI;YAAK;YAAK;YAAK;YAAK;YAAI;YAAI;SAAI;QACrC;YAAC;YAAK;YAAK;YAAK;YAAK;YAAG;YAAI;YAAI;SAAI;QACpC;YAAC;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAI;SAAG;QACnC;YAAC;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAI;SAAI;QACpC;YAAC;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACvC;YAAC;YAAI;YAAI;YAAI;YAAK;YAAI;YAAK;YAAK;SAAI;QACpC;YAAC;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACvC;YAAC;YAAI;YAAI;YAAK;YAAI;YAAI;YAAI;YAAI;SAAI;QAClC;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACxC;YAAC;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACvC;YAAC;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAK;SAAI;QACrC;YAAC;YAAK;YAAI;YAAI;YAAK;YAAK;YAAI;YAAI;SAAE;QAClC;YAAC;YAAG;YAAI;YAAI;YAAK;YAAI;YAAK;YAAK;SAAG;QAClC;YAAC;YAAK;YAAK;YAAI;YAAI;YAAI;YAAI;YAAK;SAAI;QACpC;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QACnC;YAAC;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;SAAI;QACrC;YAAC;YAAK;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;SAAI;QACrC;YAAC;YAAI;YAAK;YAAI;YAAG;YAAI;YAAI;YAAG;SAAG;QAC/B;YAAC;YAAI;YAAK;YAAK;YAAK;YAAI;YAAK;YAAK;SAAG;KACxC;IACD,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,YAAY,GAAG;IACjC,kBAAkB,wBAAwB,GAAG;QACzC;YAAC,kBAAkB,YAAY;YAAE,kBAAkB,YAAY;SAAC;QAChE;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;QACD;YACI,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;YAC9B,kBAAkB,YAAY;SACjC;KACJ;IACD,kBAAkB,SAAS,GAAG;IAC9B,OAAO;AACX,EAAE,wLAAA,CAAA,UAAiB;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/Pair.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DataCharacter from './DataCharacter';\nvar Pair = /** @class */ (function (_super) {\n    __extends(Pair, _super);\n    function Pair(value, checksumPortion, finderPattern) {\n        var _this = _super.call(this, value, checksumPortion) || this;\n        _this.count = 0;\n        _this.finderPattern = finderPattern;\n        return _this;\n    }\n    Pair.prototype.getFinderPattern = function () {\n        return this.finderPattern;\n    };\n    Pair.prototype.getCount = function () {\n        return this.count;\n    };\n    Pair.prototype.incrementCount = function () {\n        this.count++;\n    };\n    return Pair;\n}(DataCharacter));\nexport default Pair;\n"], "names": [], "mappings": ";;;AAaA;AAbA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,OAAsB,SAAU,MAAM;IACtC,UAAU,MAAM;IAChB,SAAS,KAAK,KAAK,EAAE,eAAe,EAAE,aAAa;QAC/C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,oBAAoB,IAAI;QAC7D,MAAM,KAAK,GAAG;QACd,MAAM,aAAa,GAAG;QACtB,OAAO;IACX;IACA,KAAK,SAAS,CAAC,gBAAgB,GAAG;QAC9B,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,KAAK,SAAS,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,KAAK,SAAS,CAAC,cAAc,GAAG;QAC5B,IAAI,CAAC,KAAK;IACd;IACA,OAAO;AACX,EAAE,oLAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/rss/RSS14Reader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AbstractRSSReader from './AbstractRSSReader';\nimport Pair from './Pair';\nimport Result from '../../Result';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport StringBuilder from '../../util/StringBuilder';\nimport BarcodeFormat from '../../BarcodeFormat';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport DataCharacter from './DataCharacter';\nimport MathUtils from '../../common/detector/MathUtils';\nimport RSSUtils from './RSSUtils';\nimport System from '../../util/System';\nimport OneDReader from '../OneDReader';\nvar RSS14Reader = /** @class */ (function (_super) {\n    __extends(RSS14Reader, _super);\n    function RSS14Reader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.possibleLeftPairs = [];\n        _this.possibleRightPairs = [];\n        return _this;\n    }\n    RSS14Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var leftPair = this.decodePair(row, false, rowNumber, hints);\n        RSS14Reader.addOrTally(this.possibleLeftPairs, leftPair);\n        row.reverse();\n        var rightPair = this.decodePair(row, true, rowNumber, hints);\n        RSS14Reader.addOrTally(this.possibleRightPairs, rightPair);\n        row.reverse();\n        try {\n            for (var _c = __values(this.possibleLeftPairs), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var left = _d.value;\n                if (left.getCount() > 1) {\n                    try {\n                        for (var _e = (e_2 = void 0, __values(this.possibleRightPairs)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                            var right = _f.value;\n                            if (right.getCount() > 1 && RSS14Reader.checkChecksum(left, right)) {\n                                return RSS14Reader.constructResult(left, right);\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        throw new NotFoundException();\n    };\n    RSS14Reader.addOrTally = function (possiblePairs, pair) {\n        var e_3, _a;\n        if (pair == null) {\n            return;\n        }\n        var found = false;\n        try {\n            for (var possiblePairs_1 = __values(possiblePairs), possiblePairs_1_1 = possiblePairs_1.next(); !possiblePairs_1_1.done; possiblePairs_1_1 = possiblePairs_1.next()) {\n                var other = possiblePairs_1_1.value;\n                if (other.getValue() === pair.getValue()) {\n                    other.incrementCount();\n                    found = true;\n                    break;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (possiblePairs_1_1 && !possiblePairs_1_1.done && (_a = possiblePairs_1.return)) _a.call(possiblePairs_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        if (!found) {\n            possiblePairs.push(pair);\n        }\n    };\n    RSS14Reader.prototype.reset = function () {\n        this.possibleLeftPairs.length = 0;\n        this.possibleRightPairs.length = 0;\n    };\n    RSS14Reader.constructResult = function (leftPair, rightPair) {\n        var symbolValue = 4537077 * leftPair.getValue() + rightPair.getValue();\n        var text = new String(symbolValue).toString();\n        var buffer = new StringBuilder();\n        for (var i = 13 - text.length; i > 0; i--) {\n            buffer.append('0');\n        }\n        buffer.append(text);\n        var checkDigit = 0;\n        for (var i = 0; i < 13; i++) {\n            var digit = buffer.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            checkDigit += ((i & 0x01) === 0) ? 3 * digit : digit;\n        }\n        checkDigit = 10 - (checkDigit % 10);\n        if (checkDigit === 10) {\n            checkDigit = 0;\n        }\n        buffer.append(checkDigit.toString());\n        var leftPoints = leftPair.getFinderPattern().getResultPoints();\n        var rightPoints = rightPair.getFinderPattern().getResultPoints();\n        return new Result(buffer.toString(), null, 0, [leftPoints[0], leftPoints[1], rightPoints[0], rightPoints[1]], BarcodeFormat.RSS_14, new Date().getTime());\n    };\n    RSS14Reader.checkChecksum = function (leftPair, rightPair) {\n        var checkValue = (leftPair.getChecksumPortion() + 16 * rightPair.getChecksumPortion()) % 79;\n        var targetCheckValue = 9 * leftPair.getFinderPattern().getValue() + rightPair.getFinderPattern().getValue();\n        if (targetCheckValue > 72) {\n            targetCheckValue--;\n        }\n        if (targetCheckValue > 8) {\n            targetCheckValue--;\n        }\n        return checkValue === targetCheckValue;\n    };\n    RSS14Reader.prototype.decodePair = function (row, right, rowNumber, hints) {\n        try {\n            var startEnd = this.findFinderPattern(row, right);\n            var pattern = this.parseFoundFinderPattern(row, rowNumber, right, startEnd);\n            var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n            if (resultPointCallback != null) {\n                var center = (startEnd[0] + startEnd[1]) / 2.0;\n                if (right) {\n                    // row is actually reversed\n                    center = row.getSize() - 1 - center;\n                }\n                resultPointCallback.foundPossibleResultPoint(new ResultPoint(center, rowNumber));\n            }\n            var outside = this.decodeDataCharacter(row, pattern, true);\n            var inside = this.decodeDataCharacter(row, pattern, false);\n            return new Pair(1597 * outside.getValue() + inside.getValue(), outside.getChecksumPortion() + 4 * inside.getChecksumPortion(), pattern);\n        }\n        catch (err) {\n            return null;\n        }\n    };\n    RSS14Reader.prototype.decodeDataCharacter = function (row, pattern, outsideChar) {\n        var counters = this.getDataCharacterCounters();\n        for (var x = 0; x < counters.length; x++) {\n            counters[x] = 0;\n        }\n        if (outsideChar) {\n            OneDReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n        }\n        else {\n            OneDReader.recordPattern(row, pattern.getStartEnd()[1] + 1, counters);\n            // reverse it\n            for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n                var temp = counters[i];\n                counters[i] = counters[j];\n                counters[j] = temp;\n            }\n        }\n        var numModules = outsideChar ? 16 : 15;\n        var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n        var oddCounts = this.getOddCounts();\n        var evenCounts = this.getEvenCounts();\n        var oddRoundingErrors = this.getOddRoundingErrors();\n        var evenRoundingErrors = this.getEvenRoundingErrors();\n        for (var i = 0; i < counters.length; i++) {\n            var value = counters[i] / elementWidth;\n            var count = Math.floor(value + 0.5);\n            if (count < 1) {\n                count = 1;\n            }\n            else if (count > 8) {\n                count = 8;\n            }\n            var offset = Math.floor(i / 2);\n            if ((i & 0x01) === 0) {\n                oddCounts[offset] = count;\n                oddRoundingErrors[offset] = value - count;\n            }\n            else {\n                evenCounts[offset] = count;\n                evenRoundingErrors[offset] = value - count;\n            }\n        }\n        this.adjustOddEvenCounts(outsideChar, numModules);\n        var oddSum = 0;\n        var oddChecksumPortion = 0;\n        for (var i = oddCounts.length - 1; i >= 0; i--) {\n            oddChecksumPortion *= 9;\n            oddChecksumPortion += oddCounts[i];\n            oddSum += oddCounts[i];\n        }\n        var evenChecksumPortion = 0;\n        var evenSum = 0;\n        for (var i = evenCounts.length - 1; i >= 0; i--) {\n            evenChecksumPortion *= 9;\n            evenChecksumPortion += evenCounts[i];\n            evenSum += evenCounts[i];\n        }\n        var checksumPortion = oddChecksumPortion + 3 * evenChecksumPortion;\n        if (outsideChar) {\n            if ((oddSum & 0x01) !== 0 || oddSum > 12 || oddSum < 4) {\n                throw new NotFoundException();\n            }\n            var group = (12 - oddSum) / 2;\n            var oddWidest = RSS14Reader.OUTSIDE_ODD_WIDEST[group];\n            var evenWidest = 9 - oddWidest;\n            var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, false);\n            var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, true);\n            var tEven = RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET[group];\n            var gSum = RSS14Reader.OUTSIDE_GSUM[group];\n            return new DataCharacter(vOdd * tEven + vEven + gSum, checksumPortion);\n        }\n        else {\n            if ((evenSum & 0x01) !== 0 || evenSum > 10 || evenSum < 4) {\n                throw new NotFoundException();\n            }\n            var group = (10 - evenSum) / 2;\n            var oddWidest = RSS14Reader.INSIDE_ODD_WIDEST[group];\n            var evenWidest = 9 - oddWidest;\n            var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n            var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n            var tOdd = RSS14Reader.INSIDE_ODD_TOTAL_SUBSET[group];\n            var gSum = RSS14Reader.INSIDE_GSUM[group];\n            return new DataCharacter(vEven * tOdd + vOdd + gSum, checksumPortion);\n        }\n    };\n    RSS14Reader.prototype.findFinderPattern = function (row, rightFinderPattern) {\n        var counters = this.getDecodeFinderCounters();\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var width = row.getSize();\n        var isWhite = false;\n        var rowOffset = 0;\n        while (rowOffset < width) {\n            isWhite = !row.get(rowOffset);\n            if (rightFinderPattern === isWhite) {\n                // Will encounter white first when searching for right finder pattern\n                break;\n            }\n            rowOffset++;\n        }\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === 3) {\n                    if (AbstractRSSReader.isFinderPattern(counters)) {\n                        return [patternStart, x];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters[0] = counters[2];\n                    counters[1] = counters[3];\n                    counters[2] = 0;\n                    counters[3] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    RSS14Reader.prototype.parseFoundFinderPattern = function (row, rowNumber, right, startEnd) {\n        // Actually we found elements 2-5\n        var firstIsBlack = row.get(startEnd[0]);\n        var firstElementStart = startEnd[0] - 1;\n        // Locate element 1\n        while (firstElementStart >= 0 && firstIsBlack !== row.get(firstElementStart)) {\n            firstElementStart--;\n        }\n        firstElementStart++;\n        var firstCounter = startEnd[0] - firstElementStart;\n        // Make 'counters' hold 1-4\n        var counters = this.getDecodeFinderCounters();\n        var copy = new Int32Array(counters.length);\n        System.arraycopy(counters, 0, copy, 1, counters.length - 1);\n        copy[0] = firstCounter;\n        var value = this.parseFinderValue(copy, RSS14Reader.FINDER_PATTERNS);\n        var start = firstElementStart;\n        var end = startEnd[1];\n        if (right) {\n            // row is actually reversed\n            start = row.getSize() - 1 - start;\n            end = row.getSize() - 1 - end;\n        }\n        return new FinderPattern(value, [firstElementStart, startEnd[1]], start, end, rowNumber);\n    };\n    RSS14Reader.prototype.adjustOddEvenCounts = function (outsideChar, numModules) {\n        var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n        var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n        var incrementOdd = false;\n        var decrementOdd = false;\n        var incrementEven = false;\n        var decrementEven = false;\n        if (outsideChar) {\n            if (oddSum > 12) {\n                decrementOdd = true;\n            }\n            else if (oddSum < 4) {\n                incrementOdd = true;\n            }\n            if (evenSum > 12) {\n                decrementEven = true;\n            }\n            else if (evenSum < 4) {\n                incrementEven = true;\n            }\n        }\n        else {\n            if (oddSum > 11) {\n                decrementOdd = true;\n            }\n            else if (oddSum < 5) {\n                incrementOdd = true;\n            }\n            if (evenSum > 10) {\n                decrementEven = true;\n            }\n            else if (evenSum < 4) {\n                incrementEven = true;\n            }\n        }\n        var mismatch = oddSum + evenSum - numModules;\n        var oddParityBad = (oddSum & 0x01) === (outsideChar ? 1 : 0);\n        var evenParityBad = (evenSum & 0x01) === 1;\n        if (mismatch === 1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementEven = true;\n            }\n        }\n        else if (mismatch === -1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementEven = true;\n            }\n        }\n        else if (mismatch === 0) {\n            if (oddParityBad) {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Both bad\n                if (oddSum < evenSum) {\n                    incrementOdd = true;\n                    decrementEven = true;\n                }\n                else {\n                    decrementOdd = true;\n                    incrementEven = true;\n                }\n            }\n            else {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Nothing to do!\n            }\n        }\n        else {\n            throw new NotFoundException();\n        }\n        if (incrementOdd) {\n            if (decrementOdd) {\n                throw new NotFoundException();\n            }\n            AbstractRSSReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementOdd) {\n            AbstractRSSReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (incrementEven) {\n            if (decrementEven) {\n                throw new NotFoundException();\n            }\n            AbstractRSSReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementEven) {\n            AbstractRSSReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n        }\n    };\n    RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET = [1, 10, 34, 70, 126];\n    RSS14Reader.INSIDE_ODD_TOTAL_SUBSET = [4, 20, 48, 81];\n    RSS14Reader.OUTSIDE_GSUM = [0, 161, 961, 2015, 2715];\n    RSS14Reader.INSIDE_GSUM = [0, 336, 1036, 1516];\n    RSS14Reader.OUTSIDE_ODD_WIDEST = [8, 6, 4, 3, 1];\n    RSS14Reader.INSIDE_ODD_WIDEST = [2, 4, 6, 8];\n    RSS14Reader.FINDER_PATTERNS = [\n        Int32Array.from([3, 8, 2, 1]),\n        Int32Array.from([3, 5, 5, 1]),\n        Int32Array.from([3, 3, 7, 1]),\n        Int32Array.from([3, 1, 9, 1]),\n        Int32Array.from([2, 7, 4, 1]),\n        Int32Array.from([2, 5, 6, 1]),\n        Int32Array.from([2, 3, 8, 1]),\n        Int32Array.from([1, 5, 7, 1]),\n        Int32Array.from([1, 3, 9, 1]),\n    ];\n    return RSS14Reader;\n}(AbstractRSSReader));\nexport default RSS14Reader;\n"], "names": [], "mappings": ";;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArCA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;;;;;;AAeA,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS;QACL,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,iBAAiB,GAAG,EAAE;QAC5B,MAAM,kBAAkB,GAAG,EAAE;QAC7B,OAAO;IACX;IACA,YAAY,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QAC7D,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,KAAK,OAAO,WAAW;QACtD,YAAY,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE;QAC/C,IAAI,OAAO;QACX,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,WAAW;QACtD,YAAY,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAChD,IAAI,OAAO;QACX,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,iBAAiB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACtF,IAAI,OAAO,GAAG,KAAK;gBACnB,IAAI,KAAK,QAAQ,KAAK,GAAG;oBACrB,IAAI;wBACA,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;4BACvG,IAAI,QAAQ,GAAG,KAAK;4BACpB,IAAI,MAAM,QAAQ,KAAK,KAAK,YAAY,aAAa,CAAC,MAAM,QAAQ;gCAChE,OAAO,YAAY,eAAe,CAAC,MAAM;4BAC7C;wBACJ;oBACJ,EACA,OAAO,OAAO;wBAAE,MAAM;4BAAE,OAAO;wBAAM;oBAAG,SAChC;wBACJ,IAAI;4BACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;wBACpD,SACQ;4BAAE,IAAI,KAAK,MAAM,IAAI,KAAK;wBAAE;oBACxC;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,YAAY,UAAU,GAAG,SAAU,aAAa,EAAE,IAAI;QAClD,IAAI,KAAK;QACT,IAAI,QAAQ,MAAM;YACd;QACJ;QACA,IAAI,QAAQ;QACZ,IAAI;YACA,IAAK,IAAI,kBAAkB,SAAS,gBAAgB,oBAAoB,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,IAAI,EAAE,oBAAoB,gBAAgB,IAAI,GAAI;gBACjK,IAAI,QAAQ,kBAAkB,KAAK;gBACnC,IAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI;oBACtC,MAAM,cAAc;oBACpB,QAAQ;oBACR;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,qBAAqB,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,gBAAgB,MAAM,GAAG,GAAG,IAAI,CAAC;YAC/F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,CAAC,OAAO;YACR,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,YAAY,SAAS,CAAC,KAAK,GAAG;QAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG;QAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG;IACrC;IACA,YAAY,eAAe,GAAG,SAAU,QAAQ,EAAE,SAAS;QACvD,IAAI,cAAc,UAAU,SAAS,QAAQ,KAAK,UAAU,QAAQ;QACpE,IAAI,OAAO,IAAI,OAAO,aAAa,QAAQ;QAC3C,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;QAC9B,IAAK,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE,IAAI,GAAG,IAAK;YACvC,OAAO,MAAM,CAAC;QAClB;QACA,OAAO,MAAM,CAAC;QACd,IAAI,aAAa;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YACzB,IAAI,QAAQ,OAAO,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;YAC5D,cAAc,AAAC,CAAC,IAAI,IAAI,MAAM,IAAK,IAAI,QAAQ;QACnD;QACA,aAAa,KAAM,aAAa;QAChC,IAAI,eAAe,IAAI;YACnB,aAAa;QACjB;QACA,OAAO,MAAM,CAAC,WAAW,QAAQ;QACjC,IAAI,aAAa,SAAS,gBAAgB,GAAG,eAAe;QAC5D,IAAI,cAAc,UAAU,gBAAgB,GAAG,eAAe;QAC9D,OAAO,IAAI,8JAAA,CAAA,UAAM,CAAC,OAAO,QAAQ,IAAI,MAAM,GAAG;YAAC,UAAU,CAAC,EAAE;YAAE,UAAU,CAAC,EAAE;YAAE,WAAW,CAAC,EAAE;YAAE,WAAW,CAAC,EAAE;SAAC,EAAE,qKAAA,CAAA,UAAa,CAAC,MAAM,EAAE,IAAI,OAAO,OAAO;IAC1J;IACA,YAAY,aAAa,GAAG,SAAU,QAAQ,EAAE,SAAS;QACrD,IAAI,aAAa,CAAC,SAAS,kBAAkB,KAAK,KAAK,UAAU,kBAAkB,EAAE,IAAI;QACzF,IAAI,mBAAmB,IAAI,SAAS,gBAAgB,GAAG,QAAQ,KAAK,UAAU,gBAAgB,GAAG,QAAQ;QACzG,IAAI,mBAAmB,IAAI;YACvB;QACJ;QACA,IAAI,mBAAmB,GAAG;YACtB;QACJ;QACA,OAAO,eAAe;IAC1B;IACA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK;QACrE,IAAI;YACA,IAAI,WAAW,IAAI,CAAC,iBAAiB,CAAC,KAAK;YAC3C,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC,KAAK,WAAW,OAAO;YAClE,IAAI,sBAAsB,SAAS,OAAO,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B;YACpG,IAAI,uBAAuB,MAAM;gBAC7B,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;gBAC3C,IAAI,OAAO;oBACP,2BAA2B;oBAC3B,SAAS,IAAI,OAAO,KAAK,IAAI;gBACjC;gBACA,oBAAoB,wBAAwB,CAAC,IAAI,mKAAA,CAAA,UAAW,CAAC,QAAQ;YACzE;YACA,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC,KAAK,SAAS;YACrD,IAAI,SAAS,IAAI,CAAC,mBAAmB,CAAC,KAAK,SAAS;YACpD,OAAO,IAAI,2KAAA,CAAA,UAAI,CAAC,OAAO,QAAQ,QAAQ,KAAK,OAAO,QAAQ,IAAI,QAAQ,kBAAkB,KAAK,IAAI,OAAO,kBAAkB,IAAI;QACnI,EACA,OAAO,KAAK;YACR,OAAO;QACX;IACJ;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,GAAG,EAAE,OAAO,EAAE,WAAW;QAC3E,IAAI,WAAW,IAAI,CAAC,wBAAwB;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,QAAQ,CAAC,EAAE,GAAG;QAClB;QACA,IAAI,aAAa;YACb,0KAAA,CAAA,UAAU,CAAC,sBAAsB,CAAC,KAAK,QAAQ,WAAW,EAAE,CAAC,EAAE,EAAE;QACrE,OACK;YACD,0KAAA,CAAA,UAAU,CAAC,aAAa,CAAC,KAAK,QAAQ,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG;YAC5D,aAAa;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,IAAK;gBACtD,IAAI,OAAO,QAAQ,CAAC,EAAE;gBACtB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzB,QAAQ,CAAC,EAAE,GAAG;YAClB;QACJ;QACA,IAAI,aAAa,cAAc,KAAK;QACpC,IAAI,eAAe,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,aAAa;QAC7D,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,aAAa,IAAI,CAAC,aAAa;QACnC,IAAI,oBAAoB,IAAI,CAAC,oBAAoB;QACjD,IAAI,qBAAqB,IAAI,CAAC,qBAAqB;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,QAAQ,QAAQ,CAAC,EAAE,GAAG;YAC1B,IAAI,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAC/B,IAAI,QAAQ,GAAG;gBACX,QAAQ;YACZ,OACK,IAAI,QAAQ,GAAG;gBAChB,QAAQ;YACZ;YACA,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI;YAC5B,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;gBAClB,SAAS,CAAC,OAAO,GAAG;gBACpB,iBAAiB,CAAC,OAAO,GAAG,QAAQ;YACxC,OACK;gBACD,UAAU,CAAC,OAAO,GAAG;gBACrB,kBAAkB,CAAC,OAAO,GAAG,QAAQ;YACzC;QACJ;QACA,IAAI,CAAC,mBAAmB,CAAC,aAAa;QACtC,IAAI,SAAS;QACb,IAAI,qBAAqB;QACzB,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC5C,sBAAsB;YACtB,sBAAsB,SAAS,CAAC,EAAE;YAClC,UAAU,SAAS,CAAC,EAAE;QAC1B;QACA,IAAI,sBAAsB;QAC1B,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC7C,uBAAuB;YACvB,uBAAuB,UAAU,CAAC,EAAE;YACpC,WAAW,UAAU,CAAC,EAAE;QAC5B;QACA,IAAI,kBAAkB,qBAAqB,IAAI;QAC/C,IAAI,aAAa;YACb,IAAI,CAAC,SAAS,IAAI,MAAM,KAAK,SAAS,MAAM,SAAS,GAAG;gBACpD,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,QAAQ,CAAC,KAAK,MAAM,IAAI;YAC5B,IAAI,YAAY,YAAY,kBAAkB,CAAC,MAAM;YACrD,IAAI,aAAa,IAAI;YACrB,IAAI,OAAO,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,WAAW,WAAW;YACtD,IAAI,QAAQ,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,YAAY,YAAY;YACzD,IAAI,QAAQ,YAAY,yBAAyB,CAAC,MAAM;YACxD,IAAI,OAAO,YAAY,YAAY,CAAC,MAAM;YAC1C,OAAO,IAAI,oLAAA,CAAA,UAAa,CAAC,OAAO,QAAQ,QAAQ,MAAM;QAC1D,OACK;YACD,IAAI,CAAC,UAAU,IAAI,MAAM,KAAK,UAAU,MAAM,UAAU,GAAG;gBACvD,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,QAAQ,CAAC,KAAK,OAAO,IAAI;YAC7B,IAAI,YAAY,YAAY,iBAAiB,CAAC,MAAM;YACpD,IAAI,aAAa,IAAI;YACrB,IAAI,OAAO,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,WAAW,WAAW;YACtD,IAAI,QAAQ,+KAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,YAAY,YAAY;YACzD,IAAI,OAAO,YAAY,uBAAuB,CAAC,MAAM;YACrD,IAAI,OAAO,YAAY,WAAW,CAAC,MAAM;YACzC,OAAO,IAAI,oLAAA,CAAA,UAAa,CAAC,QAAQ,OAAO,OAAO,MAAM;QACzD;IACJ;IACA,YAAY,SAAS,CAAC,iBAAiB,GAAG,SAAU,GAAG,EAAE,kBAAkB;QACvE,IAAI,WAAW,IAAI,CAAC,uBAAuB;QAC3C,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,QAAQ,CAAC,EAAE,GAAG;QACd,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,UAAU;QACd,IAAI,YAAY;QAChB,MAAO,YAAY,MAAO;YACtB,UAAU,CAAC,IAAI,GAAG,CAAC;YACnB,IAAI,uBAAuB,SAAS;gBAEhC;YACJ;YACA;QACJ;QACA,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,IAAK;YACpC,IAAI,IAAI,GAAG,CAAC,OAAO,SAAS;gBACxB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,GAAG;oBACvB,IAAI,wLAAA,CAAA,UAAiB,CAAC,eAAe,CAAC,WAAW;wBAC7C,OAAO;4BAAC;4BAAc;yBAAE;oBAC5B;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzB,QAAQ,CAAC,EAAE,GAAG;oBACd,QAAQ,CAAC,EAAE,GAAG;oBACd;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,YAAY,SAAS,CAAC,uBAAuB,GAAG,SAAU,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ;QACrF,iCAAiC;QACjC,IAAI,eAAe,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE;QACtC,IAAI,oBAAoB,QAAQ,CAAC,EAAE,GAAG;QACtC,mBAAmB;QACnB,MAAO,qBAAqB,KAAK,iBAAiB,IAAI,GAAG,CAAC,mBAAoB;YAC1E;QACJ;QACA;QACA,IAAI,eAAe,QAAQ,CAAC,EAAE,GAAG;QACjC,2BAA2B;QAC3B,IAAI,WAAW,IAAI,CAAC,uBAAuB;QAC3C,IAAI,OAAO,IAAI,WAAW,SAAS,MAAM;QACzC,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;QACzD,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,QAAQ,IAAI,CAAC,gBAAgB,CAAC,MAAM,YAAY,eAAe;QACnE,IAAI,QAAQ;QACZ,IAAI,MAAM,QAAQ,CAAC,EAAE;QACrB,IAAI,OAAO;YACP,2BAA2B;YAC3B,QAAQ,IAAI,OAAO,KAAK,IAAI;YAC5B,MAAM,IAAI,OAAO,KAAK,IAAI;QAC9B;QACA,OAAO,IAAI,oLAAA,CAAA,UAAa,CAAC,OAAO;YAAC;YAAmB,QAAQ,CAAC,EAAE;SAAC,EAAE,OAAO,KAAK;IAClF;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,WAAW,EAAE,UAAU;QACzE,IAAI,SAAS,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,CAAC,YAAY;QAC3D,IAAI,UAAU,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,CAAC,aAAa;QAC7D,IAAI,eAAe;QACnB,IAAI,eAAe;QACnB,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QACpB,IAAI,aAAa;YACb,IAAI,SAAS,IAAI;gBACb,eAAe;YACnB,OACK,IAAI,SAAS,GAAG;gBACjB,eAAe;YACnB;YACA,IAAI,UAAU,IAAI;gBACd,gBAAgB;YACpB,OACK,IAAI,UAAU,GAAG;gBAClB,gBAAgB;YACpB;QACJ,OACK;YACD,IAAI,SAAS,IAAI;gBACb,eAAe;YACnB,OACK,IAAI,SAAS,GAAG;gBACjB,eAAe;YACnB;YACA,IAAI,UAAU,IAAI;gBACd,gBAAgB;YACpB,OACK,IAAI,UAAU,GAAG;gBAClB,gBAAgB;YACpB;QACJ;QACA,IAAI,WAAW,SAAS,UAAU;QAClC,IAAI,eAAe,CAAC,SAAS,IAAI,MAAM,CAAC,cAAc,IAAI,CAAC;QAC3D,IAAI,gBAAgB,CAAC,UAAU,IAAI,MAAM;QACzC,IAAI,aAAa,GAAG;YAChB,IAAI,cAAc;gBACd,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,eAAe;YACnB,OACK;gBACD,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,gBAAgB;YACpB;QACJ,OACK,IAAI,aAAa,CAAC,GAAG;YACtB,IAAI,cAAc;gBACd,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,eAAe;YACnB,OACK;gBACD,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,gBAAgB;YACpB;QACJ,OACK,IAAI,aAAa,GAAG;YACrB,IAAI,cAAc;gBACd,IAAI,CAAC,eAAe;oBAChB,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;gBACA,WAAW;gBACX,IAAI,SAAS,SAAS;oBAClB,eAAe;oBACf,gBAAgB;gBACpB,OACK;oBACD,eAAe;oBACf,gBAAgB;gBACpB;YACJ,OACK;gBACD,IAAI,eAAe;oBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;gBAC/B;YACA,iBAAiB;YACrB;QACJ,OACK;YACD,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,cAAc;YACd,IAAI,cAAc;gBACd,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,wLAAA,CAAA,UAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;QAC9E;QACA,IAAI,cAAc;YACd,wLAAA,CAAA,UAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB;QAC9E;QACA,IAAI,eAAe;YACf,IAAI,eAAe;gBACf,MAAM,IAAI,yKAAA,CAAA,UAAiB;YAC/B;YACA,wLAAA,CAAA,UAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,oBAAoB;QAC/E;QACA,IAAI,eAAe;YACf,wLAAA,CAAA,UAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,qBAAqB;QAChF;IACJ;IACA,YAAY,yBAAyB,GAAG;QAAC;QAAG;QAAI;QAAI;QAAI;KAAI;IAC5D,YAAY,uBAAuB,GAAG;QAAC;QAAG;QAAI;QAAI;KAAG;IACrD,YAAY,YAAY,GAAG;QAAC;QAAG;QAAK;QAAK;QAAM;KAAK;IACpD,YAAY,WAAW,GAAG;QAAC;QAAG;QAAK;QAAM;KAAK;IAC9C,YAAY,kBAAkB,GAAG;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE;IAChD,YAAY,iBAAiB,GAAG;QAAC;QAAG;QAAG;QAAG;KAAE;IAC5C,YAAY,eAAe,GAAG;QAC1B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QAC5B,WAAW,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;KAC/B;IACD,OAAO;AACX,EAAE,wLAAA,CAAA,UAAiB;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/oned/MultiFormatOneDReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Code128Reader from './Code128Reader';\nimport Code39Reader from './Code39Reader';\nimport Code93Reader from './Code93Reader';\nimport ITFReader from './ITFReader';\nimport MultiFormatUPCEANReader from './MultiFormatUPCEANReader';\nimport OneDReader from './OneDReader';\nimport CodaBarReader from './CodaBarReader';\nimport RSSExpandedReader from './rss/expanded/RSSExpandedReader';\nimport RSS14Reader from './rss/RSS14Reader';\n/**\n * <AUTHOR> Switkin <<EMAIL>>\n * <AUTHOR> Owen\n */\nvar MultiFormatOneDReader = /** @class */ (function (_super) {\n    __extends(MultiFormatOneDReader, _super);\n    function MultiFormatOneDReader(hints) {\n        var _this = _super.call(this) || this;\n        _this.readers = [];\n        var possibleFormats = !hints ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n        var useCode39CheckDigit = hints && hints.get(DecodeHintType.ASSUME_CODE_39_CHECK_DIGIT) !== undefined;\n        var useCode39ExtendedMode = hints && hints.get(DecodeHintType.ENABLE_CODE_39_EXTENDED_MODE) !== undefined;\n        if (possibleFormats) {\n            if (possibleFormats.includes(BarcodeFormat.EAN_13) ||\n                possibleFormats.includes(BarcodeFormat.UPC_A) ||\n                possibleFormats.includes(BarcodeFormat.EAN_8) ||\n                possibleFormats.includes(BarcodeFormat.UPC_E)) {\n                _this.readers.push(new MultiFormatUPCEANReader(hints));\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_39)) {\n                _this.readers.push(new Code39Reader(useCode39CheckDigit, useCode39ExtendedMode));\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_93)) {\n                _this.readers.push(new Code93Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_128)) {\n                _this.readers.push(new Code128Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.ITF)) {\n                _this.readers.push(new ITFReader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODABAR)) {\n                _this.readers.push(new CodaBarReader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.RSS_14)) {\n                _this.readers.push(new RSS14Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.RSS_EXPANDED)) {\n                console.warn('RSS Expanded reader IS NOT ready for production yet! use at your own risk.');\n                _this.readers.push(new RSSExpandedReader());\n            }\n        }\n        if (_this.readers.length === 0) {\n            _this.readers.push(new MultiFormatUPCEANReader(hints));\n            _this.readers.push(new Code39Reader());\n            // this.readers.push(new CodaBarReader());\n            _this.readers.push(new Code93Reader());\n            _this.readers.push(new MultiFormatUPCEANReader(hints));\n            _this.readers.push(new Code128Reader());\n            _this.readers.push(new ITFReader());\n            _this.readers.push(new RSS14Reader());\n            // this.readers.push(new RSSExpandedReader());\n        }\n        return _this;\n    }\n    // @Override\n    MultiFormatOneDReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        for (var i = 0; i < this.readers.length; i++) {\n            try {\n                return this.readers[i].decodeRow(rowNumber, row, hints);\n            }\n            catch (re) {\n                // continue\n            }\n        }\n        throw new NotFoundException();\n    };\n    // @Override\n    MultiFormatOneDReader.prototype.reset = function () {\n        this.readers.forEach(function (reader) { return reader.reset(); });\n    };\n    return MultiFormatOneDReader;\n}(OneDReader));\nexport default MultiFormatOneDReader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,mCAAmC,GACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;;;;;;;;AAcA;;;CAGC,GACD,IAAI,wBAAuC,SAAU,MAAM;IACvD,UAAU,uBAAuB;IACjC,SAAS,sBAAsB,KAAK;QAChC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,OAAO,GAAG,EAAE;QAClB,IAAI,kBAAkB,CAAC,QAAQ,OAAO,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,gBAAgB;QAC/E,IAAI,sBAAsB,SAAS,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,0BAA0B,MAAM;QAC5F,IAAI,wBAAwB,SAAS,MAAM,GAAG,CAAC,sKAAA,CAAA,UAAc,CAAC,4BAA4B,MAAM;QAChG,IAAI,iBAAiB;YACjB,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,MAAM,KAC7C,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,KAC5C,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,KAC5C,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,KAAK,GAAG;gBAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,uLAAA,CAAA,UAAuB,CAAC;YACnD;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,OAAO,GAAG;gBACjD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,4KAAA,CAAA,UAAY,CAAC,qBAAqB;YAC7D;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,OAAO,GAAG;gBACjD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,4KAAA,CAAA,UAAY;YACvC;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,QAAQ,GAAG;gBAClD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,6KAAA,CAAA,UAAa;YACxC;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,GAAG,GAAG;gBAC7C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,yKAAA,CAAA,UAAS;YACpC;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,OAAO,GAAG;gBACjD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,6KAAA,CAAA,UAAa;YACxC;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,MAAM,GAAG;gBAChD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAW;YACtC;YACA,IAAI,gBAAgB,QAAQ,CAAC,qKAAA,CAAA,UAAa,CAAC,YAAY,GAAG;gBACtD,QAAQ,IAAI,CAAC;gBACb,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,oMAAA,CAAA,UAAiB;YAC5C;QACJ;QACA,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;YAC5B,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,uLAAA,CAAA,UAAuB,CAAC;YAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,4KAAA,CAAA,UAAY;YACnC,0CAA0C;YAC1C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,4KAAA,CAAA,UAAY;YACnC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,uLAAA,CAAA,UAAuB,CAAC;YAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,6KAAA,CAAA,UAAa;YACpC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,yKAAA,CAAA,UAAS;YAChC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,kLAAA,CAAA,UAAW;QAClC,8CAA8C;QAClD;QACA,OAAO;IACX;IACA,YAAY;IACZ,sBAAsB,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,KAAK;QACvE,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI;gBACA,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,KAAK;YACrD,EACA,OAAO,IAAI;YACP,WAAW;YACf;QACJ;QACA,MAAM,IAAI,yKAAA,CAAA,UAAiB;IAC/B;IACA,YAAY;IACZ,sBAAsB,SAAS,CAAC,KAAK,GAAG;QACpC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAU,MAAM;YAAI,OAAO,OAAO,KAAK;QAAI;IACpE;IACA,OAAO;AACX,EAAE,0KAAA,CAAA,UAAU;uCACG", "ignoreList": [0], "debugId": null}}]}