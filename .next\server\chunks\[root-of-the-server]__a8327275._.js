module.exports = {

"[project]/.next-internal/server/app/api/scanner/attendance/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/data/mock-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "findPeriodById": ()=>findPeriodById,
    "findStudentById": ()=>findStudentById,
    "findStudentByQRCode": ()=>findStudentByQRCode,
    "findSubjectById": ()=>findSubjectById,
    "getStudentAttendanceRecords": ()=>getStudentAttendanceRecords,
    "getTodayAttendanceRecord": ()=>getTodayAttendanceRecord,
    "mockAttendanceRecords": ()=>mockAttendanceRecords,
    "mockStudents": ()=>mockStudents,
    "mockSubjects": ()=>mockSubjects,
    "mockTimePeriods": ()=>mockTimePeriods
});
const mockStudents = [
    {
        id: "STU001",
        name: "John Doe",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "3rd Year",
        section: "IT-3A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU001_2025"
    },
    {
        id: "STU002",
        name: "Jane Smith",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "2nd Year",
        section: "CS-2B",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU002_2025"
    },
    {
        id: "STU003",
        name: "Mike Johnson",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "1st Year",
        section: "IT-1C",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU003_2025"
    },
    {
        id: "STU004",
        name: "Sarah Wilson",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "4th Year",
        section: "CS-4A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU004_2025"
    },
    {
        id: "STU005",
        name: "Alex Chen",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "2nd Year",
        section: "IT-2A",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU005_2025"
    },
    {
        id: "STU006",
        name: "Maria Garcia",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "3rd Year",
        section: "CS-3B",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU006_2025"
    },
    {
        id: "STU007",
        name: "David Brown",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "4th Year",
        section: "IT-4A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU007_2025"
    },
    {
        id: "STU008",
        name: "Lisa Anderson",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "1st Year",
        section: "CS-1A",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU008_2025"
    },
    {
        id: "STU009",
        name: "Robert Taylor",
        email: "<EMAIL>",
        course: "Information Technology",
        year: "3rd Year",
        section: "IT-3B",
        grade: "11",
        status: "Inactive",
        photo: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU009_2025"
    },
    {
        id: "STU010",
        name: "Emily Davis",
        email: "<EMAIL>",
        course: "Computer Science",
        year: "2nd Year",
        section: "CS-2A",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU010_2025"
    }
];
const mockSubjects = [
    {
        id: "SUBJ001",
        name: "Programming Fundamentals",
        code: "IT101",
        instructor: "Prof. Martinez",
        schedule: [
            {
                day: "Monday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Wednesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Friday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ002",
        name: "Database Management",
        code: "IT201",
        instructor: "Prof. Rodriguez",
        schedule: [
            {
                day: "Tuesday",
                startTime: "10:00",
                endTime: "12:00"
            },
            {
                day: "Thursday",
                startTime: "10:00",
                endTime: "12:00"
            }
        ]
    },
    {
        id: "SUBJ003",
        name: "Web Development",
        code: "IT301",
        instructor: "Prof. Santos",
        schedule: [
            {
                day: "Monday",
                startTime: "13:00",
                endTime: "15:00"
            },
            {
                day: "Wednesday",
                startTime: "13:00",
                endTime: "15:00"
            }
        ]
    },
    {
        id: "SUBJ004",
        name: "Data Structures",
        code: "CS201",
        instructor: "Prof. Reyes",
        schedule: [
            {
                day: "Tuesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Thursday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ005",
        name: "Software Engineering",
        code: "CS301",
        instructor: "Prof. Cruz",
        schedule: [
            {
                day: "Monday",
                startTime: "15:00",
                endTime: "17:00"
            },
            {
                day: "Friday",
                startTime: "15:00",
                endTime: "17:00"
            }
        ]
    }
];
const mockTimePeriods = [
    {
        id: "PERIOD001",
        name: "1st Period",
        startTime: "08:00",
        endTime: "10:00",
        type: "morning"
    },
    {
        id: "PERIOD002",
        name: "2nd Period",
        startTime: "10:00",
        endTime: "12:00",
        type: "morning"
    },
    {
        id: "PERIOD003",
        name: "3rd Period",
        startTime: "13:00",
        endTime: "15:00",
        type: "afternoon"
    },
    {
        id: "PERIOD004",
        name: "4th Period",
        startTime: "15:00",
        endTime: "17:00",
        type: "afternoon"
    },
    {
        id: "PERIOD005",
        name: "Evening Class",
        startTime: "18:00",
        endTime: "20:00",
        type: "evening"
    }
];
const mockAttendanceRecords = [
    {
        id: "ATT001",
        studentId: "STU001",
        studentName: "John Doe",
        course: "Information Technology",
        checkIn: "8:30 AM",
        checkOut: "5:00 PM",
        date: "2025-01-02",
        status: "Present",
        type: "gate",
        timestamp: new Date("2025-01-02T08:30:00")
    },
    {
        id: "ATT002",
        studentId: "STU002",
        studentName: "Jane Smith",
        course: "Computer Science",
        checkIn: "8:25 AM",
        checkOut: "4:55 PM",
        date: "2025-01-02",
        status: "Present",
        type: "gate",
        timestamp: new Date("2025-01-02T08:25:00")
    },
    {
        id: "ATT003",
        studentId: "STU003",
        studentName: "Mike Johnson",
        course: "Information Technology",
        checkIn: "8:45 AM",
        checkOut: "5:10 PM",
        date: "2025-01-02",
        status: "Late",
        type: "gate",
        timestamp: new Date("2025-01-02T08:45:00")
    },
    {
        id: "ATT004",
        studentId: "STU004",
        studentName: "Sarah Wilson",
        course: "Computer Science",
        date: "2025-01-02",
        status: "Absent",
        type: "subject",
        subject: "Programming Fundamentals",
        period: "1st Period",
        timestamp: new Date("2025-01-02T08:00:00")
    }
];
function findStudentById(id) {
    return mockStudents.find((student)=>student.id === id);
}
function findStudentByQRCode(qrCode) {
    return mockStudents.find((student)=>student.qrCode === qrCode);
}
function findSubjectById(id) {
    return mockSubjects.find((subject)=>subject.id === id);
}
function findPeriodById(id) {
    return mockTimePeriods.find((period)=>period.id === id);
}
function getStudentAttendanceRecords(studentId) {
    return mockAttendanceRecords.filter((record)=>record.studentId === studentId);
}
function getTodayAttendanceRecord(studentId) {
    const today = new Date().toISOString().split('T')[0];
    return mockAttendanceRecords.find((record)=>record.studentId === studentId && record.date === today);
}
}),
"[project]/app/api/scanner/attendance/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/mock-data.ts [app-route] (ecmascript)");
;
;
// In-memory storage for demo purposes
// In production, this would be stored in a database
let attendanceRecords = [];
async function POST(request) {
    try {
        const body = await request.json();
        const { studentId, action, subject, period, reason, offline = false, originalTimestamp } = body;
        if (!studentId || !action) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "studentId and action are required"
            }, {
                status: 400
            });
        }
        // Validate student exists
        const student = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findStudentById"])(studentId);
        if (!student) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "Student not found"
            }, {
                status: 404
            });
        }
        // Validate subject if provided
        if (subject) {
            const subjectData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findSubjectById"])(subject);
            if (!subjectData) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: "Subject not found"
                }, {
                    status: 404
                });
            }
        }
        // Validate period if provided
        if (period) {
            const periodData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findPeriodById"])(period);
            if (!periodData) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: "Period not found"
                }, {
                    status: 404
                });
            }
        }
        // Create attendance record
        const timestamp = originalTimestamp ? new Date(originalTimestamp) : new Date();
        const attendanceRecord = {
            id: `ATT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            studentId: student.id,
            studentName: student.name,
            course: student.course,
            date: timestamp.toISOString().split('T')[0],
            status: mapActionToStatus(action),
            type: determineType(action, subject),
            timestamp,
            subject,
            period
        };
        // Set check-in/check-out times for gate actions
        if (action === 'check-in') {
            attendanceRecord.checkIn = timestamp.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        } else if (action === 'check-out') {
            attendanceRecord.checkOut = timestamp.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }
        // Store the record
        attendanceRecords.push(attendanceRecord);
        // Log for debugging
        console.log(`Attendance recorded: ${student.name} - ${action}${offline ? ' (offline)' : ''}`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: attendanceRecord,
            message: `Attendance recorded successfully${offline ? ' (synced from offline)' : ''}`
        });
    } catch (error) {
        console.error("Attendance recording error:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: "Internal server error"
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const studentId = searchParams.get('studentId');
        const date = searchParams.get('date');
        let filteredRecords = attendanceRecords;
        if (studentId) {
            filteredRecords = filteredRecords.filter((record)=>record.studentId === studentId);
        }
        if (date) {
            filteredRecords = filteredRecords.filter((record)=>record.date === date);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: filteredRecords,
            message: "Attendance records retrieved successfully"
        });
    } catch (error) {
        console.error("Attendance retrieval error:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: "Internal server error"
        }, {
            status: 500
        });
    }
}
// Helper functions
function mapActionToStatus(action) {
    switch(action){
        case 'present':
        case 'check-in':
        case 'check-out':
            return 'Present';
        case 'late':
            return 'Late';
        case 'absent':
            return 'Absent';
        default:
            return 'Present';
    }
}
function determineType(action, subject) {
    if (action === 'check-in' || action === 'check-out') {
        return 'gate';
    }
    return subject ? 'subject' : 'gate';
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a8327275._.js.map