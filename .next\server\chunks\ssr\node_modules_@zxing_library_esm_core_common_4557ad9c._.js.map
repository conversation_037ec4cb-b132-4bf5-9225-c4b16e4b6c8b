{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/BitArray.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport Arrays from '../util/Arrays';\nimport Integer from '../util/Integer';\nimport System from '../util/System';\n/**\n * <p>A simple, fast array of bits, represented compactly by an array of ints internally.</p>\n *\n * <AUTHOR>\n */\nvar BitArray /*implements Cloneable*/ = /** @class */ (function () {\n    // public constructor() {\n    //   this.size = 0\n    //   this.bits = new Int32Array(1)\n    // }\n    // public constructor(size?: number /*int*/) {\n    //   if (undefined === size) {\n    //     this.size = 0\n    //   } else {\n    //     this.size = size\n    //   }\n    //   this.bits = this.makeArray(size)\n    // }\n    // For testing only\n    function BitArray(size /*int*/, bits) {\n        if (undefined === size) {\n            this.size = 0;\n            this.bits = new Int32Array(1);\n        }\n        else {\n            this.size = size;\n            if (undefined === bits || null === bits) {\n                this.bits = BitArray.makeArray(size);\n            }\n            else {\n                this.bits = bits;\n            }\n        }\n    }\n    BitArray.prototype.getSize = function () {\n        return this.size;\n    };\n    BitArray.prototype.getSizeInBytes = function () {\n        return Math.floor((this.size + 7) / 8);\n    };\n    BitArray.prototype.ensureCapacity = function (size /*int*/) {\n        if (size > this.bits.length * 32) {\n            var newBits = BitArray.makeArray(size);\n            System.arraycopy(this.bits, 0, newBits, 0, this.bits.length);\n            this.bits = newBits;\n        }\n    };\n    /**\n     * @param i bit to get\n     * @return true iff bit i is set\n     */\n    BitArray.prototype.get = function (i /*int*/) {\n        return (this.bits[Math.floor(i / 32)] & (1 << (i & 0x1F))) !== 0;\n    };\n    /**\n     * Sets bit i.\n     *\n     * @param i bit to set\n     */\n    BitArray.prototype.set = function (i /*int*/) {\n        this.bits[Math.floor(i / 32)] |= 1 << (i & 0x1F);\n    };\n    /**\n     * Flips bit i.\n     *\n     * @param i bit to set\n     */\n    BitArray.prototype.flip = function (i /*int*/) {\n        this.bits[Math.floor(i / 32)] ^= 1 << (i & 0x1F);\n    };\n    /**\n     * @param from first bit to check\n     * @return index of first bit that is set, starting from the given index, or size if none are set\n     *  at or beyond this given index\n     * @see #getNextUnset(int)\n     */\n    BitArray.prototype.getNextSet = function (from /*int*/) {\n        var size = this.size;\n        if (from >= size) {\n            return size;\n        }\n        var bits = this.bits;\n        var bitsOffset = Math.floor(from / 32);\n        var currentBits = bits[bitsOffset];\n        // mask off lesser bits first\n        currentBits &= ~((1 << (from & 0x1F)) - 1);\n        var length = bits.length;\n        while (currentBits === 0) {\n            if (++bitsOffset === length) {\n                return size;\n            }\n            currentBits = bits[bitsOffset];\n        }\n        var result = (bitsOffset * 32) + Integer.numberOfTrailingZeros(currentBits);\n        return result > size ? size : result;\n    };\n    /**\n     * @param from index to start looking for unset bit\n     * @return index of next unset bit, or {@code size} if none are unset until the end\n     * @see #getNextSet(int)\n     */\n    BitArray.prototype.getNextUnset = function (from /*int*/) {\n        var size = this.size;\n        if (from >= size) {\n            return size;\n        }\n        var bits = this.bits;\n        var bitsOffset = Math.floor(from / 32);\n        var currentBits = ~bits[bitsOffset];\n        // mask off lesser bits first\n        currentBits &= ~((1 << (from & 0x1F)) - 1);\n        var length = bits.length;\n        while (currentBits === 0) {\n            if (++bitsOffset === length) {\n                return size;\n            }\n            currentBits = ~bits[bitsOffset];\n        }\n        var result = (bitsOffset * 32) + Integer.numberOfTrailingZeros(currentBits);\n        return result > size ? size : result;\n    };\n    /**\n     * Sets a block of 32 bits, starting at bit i.\n     *\n     * @param i first bit to set\n     * @param newBits the new value of the next 32 bits. Note again that the least-significant bit\n     * corresponds to bit i, the next-least-significant to i+1, and so on.\n     */\n    BitArray.prototype.setBulk = function (i /*int*/, newBits /*int*/) {\n        this.bits[Math.floor(i / 32)] = newBits;\n    };\n    /**\n     * Sets a range of bits.\n     *\n     * @param start start of range, inclusive.\n     * @param end end of range, exclusive\n     */\n    BitArray.prototype.setRange = function (start /*int*/, end /*int*/) {\n        if (end < start || start < 0 || end > this.size) {\n            throw new IllegalArgumentException();\n        }\n        if (end === start) {\n            return;\n        }\n        end--; // will be easier to treat this as the last actually set bit -- inclusive\n        var firstInt = Math.floor(start / 32);\n        var lastInt = Math.floor(end / 32);\n        var bits = this.bits;\n        for (var i = firstInt; i <= lastInt; i++) {\n            var firstBit = i > firstInt ? 0 : start & 0x1F;\n            var lastBit = i < lastInt ? 31 : end & 0x1F;\n            // Ones from firstBit to lastBit, inclusive\n            var mask = (2 << lastBit) - (1 << firstBit);\n            bits[i] |= mask;\n        }\n    };\n    /**\n     * Clears all bits (sets to false).\n     */\n    BitArray.prototype.clear = function () {\n        var max = this.bits.length;\n        var bits = this.bits;\n        for (var i = 0; i < max; i++) {\n            bits[i] = 0;\n        }\n    };\n    /**\n     * Efficient method to check if a range of bits is set, or not set.\n     *\n     * @param start start of range, inclusive.\n     * @param end end of range, exclusive\n     * @param value if true, checks that bits in range are set, otherwise checks that they are not set\n     * @return true iff all bits are set or not set in range, according to value argument\n     * @throws IllegalArgumentException if end is less than start or the range is not contained in the array\n     */\n    BitArray.prototype.isRange = function (start /*int*/, end /*int*/, value) {\n        if (end < start || start < 0 || end > this.size) {\n            throw new IllegalArgumentException();\n        }\n        if (end === start) {\n            return true; // empty range matches\n        }\n        end--; // will be easier to treat this as the last actually set bit -- inclusive\n        var firstInt = Math.floor(start / 32);\n        var lastInt = Math.floor(end / 32);\n        var bits = this.bits;\n        for (var i = firstInt; i <= lastInt; i++) {\n            var firstBit = i > firstInt ? 0 : start & 0x1F;\n            var lastBit = i < lastInt ? 31 : end & 0x1F;\n            // Ones from firstBit to lastBit, inclusive\n            var mask = (2 << lastBit) - (1 << firstBit) & 0xFFFFFFFF;\n            // TYPESCRIPTPORT: & 0xFFFFFFFF added to discard anything after 32 bits, as ES has 53 bits\n            // Return false if we're looking for 1s and the masked bits[i] isn't all 1s (is: that,\n            // equals the mask, or we're looking for 0s and the masked portion is not all 0s\n            if ((bits[i] & mask) !== (value ? mask : 0)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    BitArray.prototype.appendBit = function (bit) {\n        this.ensureCapacity(this.size + 1);\n        if (bit) {\n            this.bits[Math.floor(this.size / 32)] |= 1 << (this.size & 0x1F);\n        }\n        this.size++;\n    };\n    /**\n     * Appends the least-significant bits, from value, in order from most-significant to\n     * least-significant. For example, appending 6 bits from 0x000001E will append the bits\n     * 0, 1, 1, 1, 1, 0 in that order.\n     *\n     * @param value {@code int} containing bits to append\n     * @param numBits bits from value to append\n     */\n    BitArray.prototype.appendBits = function (value /*int*/, numBits /*int*/) {\n        if (numBits < 0 || numBits > 32) {\n            throw new IllegalArgumentException('Num bits must be between 0 and 32');\n        }\n        this.ensureCapacity(this.size + numBits);\n        // const appendBit = this.appendBit;\n        for (var numBitsLeft = numBits; numBitsLeft > 0; numBitsLeft--) {\n            this.appendBit(((value >> (numBitsLeft - 1)) & 0x01) === 1);\n        }\n    };\n    BitArray.prototype.appendBitArray = function (other) {\n        var otherSize = other.size;\n        this.ensureCapacity(this.size + otherSize);\n        // const appendBit = this.appendBit;\n        for (var i = 0; i < otherSize; i++) {\n            this.appendBit(other.get(i));\n        }\n    };\n    BitArray.prototype.xor = function (other) {\n        if (this.size !== other.size) {\n            throw new IllegalArgumentException('Sizes don\\'t match');\n        }\n        var bits = this.bits;\n        for (var i = 0, length_1 = bits.length; i < length_1; i++) {\n            // The last int could be incomplete (i.e. not have 32 bits in\n            // it) but there is no problem since 0 XOR 0 == 0.\n            bits[i] ^= other.bits[i];\n        }\n    };\n    /**\n     *\n     * @param bitOffset first bit to start writing\n     * @param array array to write into. Bytes are written most-significant byte first. This is the opposite\n     *  of the internal representation, which is exposed by {@link #getBitArray()}\n     * @param offset position in array to start writing\n     * @param numBytes how many bytes to write\n     */\n    BitArray.prototype.toBytes = function (bitOffset /*int*/, array, offset /*int*/, numBytes /*int*/) {\n        for (var i = 0; i < numBytes; i++) {\n            var theByte = 0;\n            for (var j = 0; j < 8; j++) {\n                if (this.get(bitOffset)) {\n                    theByte |= 1 << (7 - j);\n                }\n                bitOffset++;\n            }\n            array[offset + i] = /*(byte)*/ theByte;\n        }\n    };\n    /**\n     * @return underlying array of ints. The first element holds the first 32 bits, and the least\n     *         significant bit is bit 0.\n     */\n    BitArray.prototype.getBitArray = function () {\n        return this.bits;\n    };\n    /**\n     * Reverses all bits in the array.\n     */\n    BitArray.prototype.reverse = function () {\n        var newBits = new Int32Array(this.bits.length);\n        // reverse all int's first\n        var len = Math.floor((this.size - 1) / 32);\n        var oldBitsLen = len + 1;\n        var bits = this.bits;\n        for (var i = 0; i < oldBitsLen; i++) {\n            var x = bits[i];\n            x = ((x >> 1) & 0x55555555) | ((x & 0x55555555) << 1);\n            x = ((x >> 2) & 0x33333333) | ((x & 0x33333333) << 2);\n            x = ((x >> 4) & 0x0f0f0f0f) | ((x & 0x0f0f0f0f) << 4);\n            x = ((x >> 8) & 0x00ff00ff) | ((x & 0x00ff00ff) << 8);\n            x = ((x >> 16) & 0x0000ffff) | ((x & 0x0000ffff) << 16);\n            newBits[len - i] = /*(int)*/ x;\n        }\n        // now correct the int's if the bit size isn't a multiple of 32\n        if (this.size !== oldBitsLen * 32) {\n            var leftOffset = oldBitsLen * 32 - this.size;\n            var currentInt = newBits[0] >>> leftOffset;\n            for (var i = 1; i < oldBitsLen; i++) {\n                var nextInt = newBits[i];\n                currentInt |= nextInt << (32 - leftOffset);\n                newBits[i - 1] = currentInt;\n                currentInt = nextInt >>> leftOffset;\n            }\n            newBits[oldBitsLen - 1] = currentInt;\n        }\n        this.bits = newBits;\n    };\n    BitArray.makeArray = function (size /*int*/) {\n        return new Int32Array(Math.floor((size + 31) / 32));\n    };\n    /*@Override*/\n    BitArray.prototype.equals = function (o) {\n        if (!(o instanceof BitArray)) {\n            return false;\n        }\n        var other = o;\n        return this.size === other.size && Arrays.equals(this.bits, other.bits);\n    };\n    /*@Override*/\n    BitArray.prototype.hashCode = function () {\n        return 31 * this.size + Arrays.hashCode(this.bits);\n    };\n    /*@Override*/\n    BitArray.prototype.toString = function () {\n        var result = '';\n        for (var i = 0, size = this.size; i < size; i++) {\n            if ((i & 0x07) === 0) {\n                result += ' ';\n            }\n            result += this.get(i) ? 'X' : '.';\n        }\n        return result;\n    };\n    /*@Override*/\n    BitArray.prototype.clone = function () {\n        return new BitArray(this.size, this.bits.slice());\n    };\n    /**\n     * converts to boolean array.\n     */\n    BitArray.prototype.toArray = function () {\n        var result = [];\n        for (var i = 0, size = this.size; i < size; i++) {\n            result.push(this.get(i));\n        }\n        return result;\n    };\n    return BitArray;\n}());\nexport default BitArray;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC,GACrC,0BAA0B;;;AAC1B;AACA;AACA;AACA;;;;;AACA;;;;CAIC,GACD,IAAI,SAAS,sBAAsB,MAAoB;IACnD,yBAAyB;IACzB,kBAAkB;IAClB,kCAAkC;IAClC,IAAI;IACJ,8CAA8C;IAC9C,8BAA8B;IAC9B,oBAAoB;IACpB,aAAa;IACb,uBAAuB;IACvB,MAAM;IACN,qCAAqC;IACrC,IAAI;IACJ,mBAAmB;IACnB,SAAS,SAAS,KAAK,KAAK,GAAN,EAAU,IAAI;QAChC,IAAI,cAAc,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW;QAC/B,OACK;YACD,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,cAAc,QAAQ,SAAS,MAAM;gBACrC,IAAI,CAAC,IAAI,GAAG,SAAS,SAAS,CAAC;YACnC,OACK;gBACD,IAAI,CAAC,IAAI,GAAG;YAChB;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,SAAS,SAAS,CAAC,cAAc,GAAG;QAChC,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI;IACxC;IACA,SAAS,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK,KAAK,GAAN;QAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;YAC9B,IAAI,UAAU,SAAS,SAAS,CAAC;YACjC,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;YAC3D,IAAI,CAAC,IAAI,GAAG;QAChB;IACJ;IACA;;;KAGC,GACD,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN;QAChC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,GAAI,KAAK,CAAC,IAAI,IAAI,CAAE,MAAM;IACnE;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN;QAChC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI;IACnD;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,EAAE,KAAK,GAAN;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI;IACnD;IACA;;;;;KAKC,GACD,SAAS,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,KAAK,GAAN;QAC1C,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,QAAQ,MAAM;YACd,OAAO;QACX;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,aAAa,KAAK,KAAK,CAAC,OAAO;QACnC,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,6BAA6B;QAC7B,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;QACzC,IAAI,SAAS,KAAK,MAAM;QACxB,MAAO,gBAAgB,EAAG;YACtB,IAAI,EAAE,eAAe,QAAQ;gBACzB,OAAO;YACX;YACA,cAAc,IAAI,CAAC,WAAW;QAClC;QACA,IAAI,SAAS,AAAC,aAAa,KAAM,oKAAA,CAAA,UAAO,CAAC,qBAAqB,CAAC;QAC/D,OAAO,SAAS,OAAO,OAAO;IAClC;IACA;;;;KAIC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK,KAAK,GAAN;QAC5C,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,QAAQ,MAAM;YACd,OAAO;QACX;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,aAAa,KAAK,KAAK,CAAC,OAAO;QACnC,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW;QACnC,6BAA6B;QAC7B,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;QACzC,IAAI,SAAS,KAAK,MAAM;QACxB,MAAO,gBAAgB,EAAG;YACtB,IAAI,EAAE,eAAe,QAAQ;gBACzB,OAAO;YACX;YACA,cAAc,CAAC,IAAI,CAAC,WAAW;QACnC;QACA,IAAI,SAAS,AAAC,aAAa,KAAM,oKAAA,CAAA,UAAO,CAAC,qBAAqB,CAAC;QAC/D,OAAO,SAAS,OAAO,OAAO;IAClC;IACA;;;;;;KAMC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN;QACrD,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,GAAG;IACpC;IACA;;;;;KAKC,GACD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN;QACtD,IAAI,MAAM,SAAS,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,EAAE;YAC7C,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,IAAI,QAAQ,OAAO;YACf;QACJ;QACA,OAAO,yEAAyE;QAChF,IAAI,WAAW,KAAK,KAAK,CAAC,QAAQ;QAClC,IAAI,UAAU,KAAK,KAAK,CAAC,MAAM;QAC/B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,UAAU,KAAK,SAAS,IAAK;YACtC,IAAI,WAAW,IAAI,WAAW,IAAI,QAAQ;YAC1C,IAAI,UAAU,IAAI,UAAU,KAAK,MAAM;YACvC,2CAA2C;YAC3C,IAAI,OAAO,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,QAAQ;YAC1C,IAAI,CAAC,EAAE,IAAI;QACf;IACJ;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA;;;;;;;;KAQC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,KAAK;QACpE,IAAI,MAAM,SAAS,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,EAAE;YAC7C,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,IAAI,QAAQ,OAAO;YACf,OAAO,MAAM,sBAAsB;QACvC;QACA,OAAO,yEAAyE;QAChF,IAAI,WAAW,KAAK,KAAK,CAAC,QAAQ;QAClC,IAAI,UAAU,KAAK,KAAK,CAAC,MAAM;QAC/B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,UAAU,KAAK,SAAS,IAAK;YACtC,IAAI,WAAW,IAAI,WAAW,IAAI,QAAQ;YAC1C,IAAI,UAAU,IAAI,UAAU,KAAK,MAAM;YACvC,2CAA2C;YAC3C,IAAI,OAAO,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,QAAQ,IAAI;YAC9C,0FAA0F;YAC1F,sFAAsF;YACtF,gFAAgF;YAChF,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,OAAO,CAAC,GAAG;gBACzC,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,SAAS,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG;QACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,GAAG;QAChC,IAAI,KAAK;YACL,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI;QACnE;QACA,IAAI,CAAC,IAAI;IACb;IACA;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,UAAU,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN;QAC5D,IAAI,UAAU,KAAK,UAAU,IAAI;YAC7B,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,GAAG;QAChC,oCAAoC;QACpC,IAAK,IAAI,cAAc,SAAS,cAAc,GAAG,cAAe;YAC5D,IAAI,CAAC,SAAS,CAAC,CAAC,AAAC,SAAU,cAAc,IAAM,IAAI,MAAM;QAC7D;IACJ;IACA,SAAS,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QAC/C,IAAI,YAAY,MAAM,IAAI;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,GAAG;QAChC,oCAAoC;QACpC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAChC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;QAC7B;IACJ;IACA,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK;QACpC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,EAAE;YAC1B,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,GAAG,WAAW,KAAK,MAAM,EAAE,IAAI,UAAU,IAAK;YACvD,6DAA6D;YAC7D,kDAAkD;YAClD,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE;QAC5B;IACJ;IACA;;;;;;;KAOC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,UAAU,KAAK,GAAN,EAAU,KAAK,EAAE,OAAO,KAAK,GAAN,EAAU,SAAS,KAAK,GAAN;QACrF,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YAC/B,IAAI,UAAU;YACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY;oBACrB,WAAW,KAAM,IAAI;gBACzB;gBACA;YACJ;YACA,KAAK,CAAC,SAAS,EAAE,GAAG,QAAQ,GAAG;QACnC;IACJ;IACA;;;KAGC,GACD,SAAS,SAAS,CAAC,WAAW,GAAG;QAC7B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,IAAI,UAAU,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM;QAC7C,0BAA0B;QAC1B,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI;QACvC,IAAI,aAAa,MAAM;QACvB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,IAAI,IAAI,IAAI,CAAC,EAAE;YACf,IAAI,AAAE,KAAK,IAAK,aAAe,CAAC,IAAI,UAAU,KAAK;YACnD,IAAI,AAAE,KAAK,IAAK,aAAe,CAAC,IAAI,UAAU,KAAK;YACnD,IAAI,AAAE,KAAK,IAAK,aAAe,CAAC,IAAI,UAAU,KAAK;YACnD,IAAI,AAAE,KAAK,IAAK,aAAe,CAAC,IAAI,UAAU,KAAK;YACnD,IAAI,AAAE,KAAK,KAAM,aAAe,CAAC,IAAI,UAAU,KAAK;YACpD,OAAO,CAAC,MAAM,EAAE,GAAG,OAAO,GAAG;QACjC;QACA,+DAA+D;QAC/D,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI;YAC/B,IAAI,aAAa,aAAa,KAAK,IAAI,CAAC,IAAI;YAC5C,IAAI,aAAa,OAAO,CAAC,EAAE,KAAK;YAChC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;gBACjC,IAAI,UAAU,OAAO,CAAC,EAAE;gBACxB,cAAc,WAAY,KAAK;gBAC/B,OAAO,CAAC,IAAI,EAAE,GAAG;gBACjB,aAAa,YAAY;YAC7B;YACA,OAAO,CAAC,aAAa,EAAE,GAAG;QAC9B;QACA,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,SAAS,SAAS,GAAG,SAAU,KAAK,KAAK,GAAN;QAC/B,OAAO,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI;IACnD;IACA,WAAW,GACX,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACnC,IAAI,CAAC,CAAC,aAAa,QAAQ,GAAG;YAC1B,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,mKAAA,CAAA,UAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI;IAC1E;IACA,WAAW,GACX,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,mKAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;IACrD;IACA,WAAW,GACX,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM,IAAK;YAC7C,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;gBAClB,UAAU;YACd;YACA,UAAU,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAClC;QACA,OAAO;IACX;IACA,WAAW,GACX,SAAS,SAAS,CAAC,KAAK,GAAG;QACvB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;IAClD;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM,IAAK;YAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACzB;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/CharacterSetECI.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.common {*/\nimport FormatException from '../FormatException';\n/*import java.util.HashMap;*/\n/*import java.util.Map;*/\nexport var CharacterSetValueIdentifiers;\n(function (CharacterSetValueIdentifiers) {\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp437\"] = 0] = \"Cp437\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_1\"] = 1] = \"ISO8859_1\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_2\"] = 2] = \"ISO8859_2\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_3\"] = 3] = \"ISO8859_3\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_4\"] = 4] = \"ISO8859_4\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_5\"] = 5] = \"ISO8859_5\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_6\"] = 6] = \"ISO8859_6\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_7\"] = 7] = \"ISO8859_7\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_8\"] = 8] = \"ISO8859_8\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_9\"] = 9] = \"ISO8859_9\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_10\"] = 10] = \"ISO8859_10\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_11\"] = 11] = \"ISO8859_11\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_13\"] = 12] = \"ISO8859_13\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_14\"] = 13] = \"ISO8859_14\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_15\"] = 14] = \"ISO8859_15\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_16\"] = 15] = \"ISO8859_16\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"SJIS\"] = 16] = \"SJIS\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1250\"] = 17] = \"Cp1250\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1251\"] = 18] = \"Cp1251\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1252\"] = 19] = \"Cp1252\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1256\"] = 20] = \"Cp1256\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UnicodeBigUnmarked\"] = 21] = \"UnicodeBigUnmarked\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UTF8\"] = 22] = \"UTF8\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ASCII\"] = 23] = \"ASCII\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Big5\"] = 24] = \"Big5\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"GB18030\"] = 25] = \"GB18030\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"EUC_KR\"] = 26] = \"EUC_KR\";\n})(CharacterSetValueIdentifiers || (CharacterSetValueIdentifiers = {}));\n/**\n * Encapsulates a Character Set ECI, according to \"Extended Channel Interpretations\" 5.3.1.1\n * of ISO 18004.\n *\n * <AUTHOR> Owen\n */\nvar CharacterSetECI = /** @class */ (function () {\n    function CharacterSetECI(valueIdentifier, valuesParam, name) {\n        var e_1, _a;\n        var otherEncodingNames = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            otherEncodingNames[_i - 3] = arguments[_i];\n        }\n        this.valueIdentifier = valueIdentifier;\n        this.name = name;\n        if (typeof valuesParam === 'number') {\n            this.values = Int32Array.from([valuesParam]);\n        }\n        else {\n            this.values = valuesParam;\n        }\n        this.otherEncodingNames = otherEncodingNames;\n        CharacterSetECI.VALUE_IDENTIFIER_TO_ECI.set(valueIdentifier, this);\n        CharacterSetECI.NAME_TO_ECI.set(name, this);\n        var values = this.values;\n        for (var i = 0, length_1 = values.length; i !== length_1; i++) {\n            var v = values[i];\n            CharacterSetECI.VALUES_TO_ECI.set(v, this);\n        }\n        try {\n            for (var otherEncodingNames_1 = __values(otherEncodingNames), otherEncodingNames_1_1 = otherEncodingNames_1.next(); !otherEncodingNames_1_1.done; otherEncodingNames_1_1 = otherEncodingNames_1.next()) {\n                var otherName = otherEncodingNames_1_1.value;\n                CharacterSetECI.NAME_TO_ECI.set(otherName, this);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (otherEncodingNames_1_1 && !otherEncodingNames_1_1.done && (_a = otherEncodingNames_1.return)) _a.call(otherEncodingNames_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    // CharacterSetECI(value: number /*int*/) {\n    //   this(new Int32Array {value})\n    // }\n    // CharacterSetECI(value: number /*int*/, String... otherEncodingNames) {\n    //   this.values = new Int32Array {value}\n    //   this.otherEncodingNames = otherEncodingNames\n    // }\n    // CharacterSetECI(values: Int32Array, String... otherEncodingNames) {\n    //   this.values = values\n    //   this.otherEncodingNames = otherEncodingNames\n    // }\n    CharacterSetECI.prototype.getValueIdentifier = function () {\n        return this.valueIdentifier;\n    };\n    CharacterSetECI.prototype.getName = function () {\n        return this.name;\n    };\n    CharacterSetECI.prototype.getValue = function () {\n        return this.values[0];\n    };\n    /**\n     * @param value character set ECI value\n     * @return {@code CharacterSetECI} representing ECI of given value, or null if it is legal but\n     *   unsupported\n     * @throws FormatException if ECI value is invalid\n     */\n    CharacterSetECI.getCharacterSetECIByValue = function (value /*int*/) {\n        if (value < 0 || value >= 900) {\n            throw new FormatException('incorect value');\n        }\n        var characterSet = CharacterSetECI.VALUES_TO_ECI.get(value);\n        if (undefined === characterSet) {\n            throw new FormatException('incorect value');\n        }\n        return characterSet;\n    };\n    /**\n     * @param name character set ECI encoding name\n     * @return CharacterSetECI representing ECI for character encoding, or null if it is legal\n     *   but unsupported\n     */\n    CharacterSetECI.getCharacterSetECIByName = function (name) {\n        var characterSet = CharacterSetECI.NAME_TO_ECI.get(name);\n        if (undefined === characterSet) {\n            throw new FormatException('incorect value');\n        }\n        return characterSet;\n    };\n    CharacterSetECI.prototype.equals = function (o) {\n        if (!(o instanceof CharacterSetECI)) {\n            return false;\n        }\n        var other = o;\n        return this.getName() === other.getName();\n    };\n    CharacterSetECI.VALUE_IDENTIFIER_TO_ECI = new Map();\n    CharacterSetECI.VALUES_TO_ECI = new Map();\n    CharacterSetECI.NAME_TO_ECI = new Map();\n    // Enum name is a Java encoding valid for java.lang and java.io\n    // TYPESCRIPTPORT: changed the main label for ISO as the TextEncoder did not recognized them in the form from java\n    // (eg ISO8859_1 must be ISO88591 or ISO8859-1 or ISO-8859-1)\n    // later on: well, except 16 wich does not work with ISO885916 so used ISO-8859-1 form for default\n    CharacterSetECI.Cp437 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp437, Int32Array.from([0, 2]), 'Cp437');\n    CharacterSetECI.ISO8859_1 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_1, Int32Array.from([1, 3]), 'ISO-8859-1', 'ISO88591', 'ISO8859_1');\n    CharacterSetECI.ISO8859_2 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_2, 4, 'ISO-8859-2', 'ISO88592', 'ISO8859_2');\n    CharacterSetECI.ISO8859_3 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_3, 5, 'ISO-8859-3', 'ISO88593', 'ISO8859_3');\n    CharacterSetECI.ISO8859_4 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_4, 6, 'ISO-8859-4', 'ISO88594', 'ISO8859_4');\n    CharacterSetECI.ISO8859_5 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_5, 7, 'ISO-8859-5', 'ISO88595', 'ISO8859_5');\n    CharacterSetECI.ISO8859_6 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_6, 8, 'ISO-8859-6', 'ISO88596', 'ISO8859_6');\n    CharacterSetECI.ISO8859_7 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_7, 9, 'ISO-8859-7', 'ISO88597', 'ISO8859_7');\n    CharacterSetECI.ISO8859_8 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_8, 10, 'ISO-8859-8', 'ISO88598', 'ISO8859_8');\n    CharacterSetECI.ISO8859_9 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_9, 11, 'ISO-8859-9', 'ISO88599', 'ISO8859_9');\n    CharacterSetECI.ISO8859_10 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_10, 12, 'ISO-8859-10', 'ISO885910', 'ISO8859_10');\n    CharacterSetECI.ISO8859_11 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_11, 13, 'ISO-8859-11', 'ISO885911', 'ISO8859_11');\n    CharacterSetECI.ISO8859_13 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_13, 15, 'ISO-8859-13', 'ISO885913', 'ISO8859_13');\n    CharacterSetECI.ISO8859_14 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_14, 16, 'ISO-8859-14', 'ISO885914', 'ISO8859_14');\n    CharacterSetECI.ISO8859_15 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_15, 17, 'ISO-8859-15', 'ISO885915', 'ISO8859_15');\n    CharacterSetECI.ISO8859_16 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_16, 18, 'ISO-8859-16', 'ISO885916', 'ISO8859_16');\n    CharacterSetECI.SJIS = new CharacterSetECI(CharacterSetValueIdentifiers.SJIS, 20, 'SJIS', 'Shift_JIS');\n    CharacterSetECI.Cp1250 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1250, 21, 'Cp1250', 'windows-1250');\n    CharacterSetECI.Cp1251 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1251, 22, 'Cp1251', 'windows-1251');\n    CharacterSetECI.Cp1252 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1252, 23, 'Cp1252', 'windows-1252');\n    CharacterSetECI.Cp1256 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1256, 24, 'Cp1256', 'windows-1256');\n    CharacterSetECI.UnicodeBigUnmarked = new CharacterSetECI(CharacterSetValueIdentifiers.UnicodeBigUnmarked, 25, 'UnicodeBigUnmarked', 'UTF-16BE', 'UnicodeBig');\n    CharacterSetECI.UTF8 = new CharacterSetECI(CharacterSetValueIdentifiers.UTF8, 26, 'UTF8', 'UTF-8');\n    CharacterSetECI.ASCII = new CharacterSetECI(CharacterSetValueIdentifiers.ASCII, Int32Array.from([27, 170]), 'ASCII', 'US-ASCII');\n    CharacterSetECI.Big5 = new CharacterSetECI(CharacterSetValueIdentifiers.Big5, 28, 'Big5');\n    CharacterSetECI.GB18030 = new CharacterSetECI(CharacterSetValueIdentifiers.GB18030, 29, 'GB18030', 'GB2312', 'EUC_CN', 'GBK');\n    CharacterSetECI.EUC_KR = new CharacterSetECI(CharacterSetValueIdentifiers.EUC_KR, 30, 'EUC_KR', 'EUC-KR');\n    return CharacterSetECI;\n}());\nexport default CharacterSetECI;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAYD,qCAAqC,GACrC;AAZA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;AAKO,IAAI;AACX,CAAC,SAAU,4BAA4B;IACnC,4BAA4B,CAAC,4BAA4B,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,YAAY,GAAG,EAAE,GAAG;IAC9E,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,aAAa,GAAG,GAAG,GAAG;IAChF,4BAA4B,CAAC,4BAA4B,CAAC,OAAO,GAAG,GAAG,GAAG;IAC1E,4BAA4B,CAAC,4BAA4B,CAAC,SAAS,GAAG,GAAG,GAAG;IAC5E,4BAA4B,CAAC,4BAA4B,CAAC,SAAS,GAAG,GAAG,GAAG;IAC5E,4BAA4B,CAAC,4BAA4B,CAAC,SAAS,GAAG,GAAG,GAAG;IAC5E,4BAA4B,CAAC,4BAA4B,CAAC,SAAS,GAAG,GAAG,GAAG;IAC5E,4BAA4B,CAAC,4BAA4B,CAAC,qBAAqB,GAAG,GAAG,GAAG;IACxF,4BAA4B,CAAC,4BAA4B,CAAC,OAAO,GAAG,GAAG,GAAG;IAC1E,4BAA4B,CAAC,4BAA4B,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC3E,4BAA4B,CAAC,4BAA4B,CAAC,OAAO,GAAG,GAAG,GAAG;IAC1E,4BAA4B,CAAC,4BAA4B,CAAC,UAAU,GAAG,GAAG,GAAG;IAC7E,4BAA4B,CAAC,4BAA4B,CAAC,SAAS,GAAG,GAAG,GAAG;AAChF,CAAC,EAAE,gCAAgC,CAAC,+BAA+B,CAAC,CAAC;AACrE;;;;;CAKC,GACD,IAAI,kBAAiC;IACjC,SAAS,gBAAgB,eAAe,EAAE,WAAW,EAAE,IAAI;QACvD,IAAI,KAAK;QACT,IAAI,qBAAqB,EAAE;QAC3B,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,kBAAkB,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QAC9C;QACA,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,OAAO,gBAAgB,UAAU;YACjC,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,CAAC;gBAAC;aAAY;QAC/C,OACK;YACD,IAAI,CAAC,MAAM,GAAG;QAClB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,gBAAgB,uBAAuB,CAAC,GAAG,CAAC,iBAAiB,IAAI;QACjE,gBAAgB,WAAW,CAAC,GAAG,CAAC,MAAM,IAAI;QAC1C,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAK,IAAI,IAAI,GAAG,WAAW,OAAO,MAAM,EAAE,MAAM,UAAU,IAAK;YAC3D,IAAI,IAAI,MAAM,CAAC,EAAE;YACjB,gBAAgB,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI;QAC7C;QACA,IAAI;YACA,IAAK,IAAI,uBAAuB,SAAS,qBAAqB,yBAAyB,qBAAqB,IAAI,IAAI,CAAC,uBAAuB,IAAI,EAAE,yBAAyB,qBAAqB,IAAI,GAAI;gBACpM,IAAI,YAAY,uBAAuB,KAAK;gBAC5C,gBAAgB,WAAW,CAAC,GAAG,CAAC,WAAW,IAAI;YACnD;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,0BAA0B,CAAC,uBAAuB,IAAI,IAAI,CAAC,KAAK,qBAAqB,MAAM,GAAG,GAAG,IAAI,CAAC;YAC9G,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA,2CAA2C;IAC3C,iCAAiC;IACjC,IAAI;IACJ,yEAAyE;IACzE,yCAAyC;IACzC,iDAAiD;IACjD,IAAI;IACJ,sEAAsE;IACtE,yBAAyB;IACzB,iDAAiD;IACjD,IAAI;IACJ,gBAAgB,SAAS,CAAC,kBAAkB,GAAG;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG;QAChC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,gBAAgB,SAAS,CAAC,QAAQ,GAAG;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;IACzB;IACA;;;;;KAKC,GACD,gBAAgB,yBAAyB,GAAG,SAAU,MAAM,KAAK,GAAN;QACvD,IAAI,QAAQ,KAAK,SAAS,KAAK;YAC3B,MAAM,IAAI,oKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,IAAI,eAAe,gBAAgB,aAAa,CAAC,GAAG,CAAC;QACrD,IAAI,cAAc,cAAc;YAC5B,MAAM,IAAI,oKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,gBAAgB,wBAAwB,GAAG,SAAU,IAAI;QACrD,IAAI,eAAe,gBAAgB,WAAW,CAAC,GAAG,CAAC;QACnD,IAAI,cAAc,cAAc;YAC5B,MAAM,IAAI,oKAAA,CAAA,UAAe,CAAC;QAC9B;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QAC1C,IAAI,CAAC,CAAC,aAAa,eAAe,GAAG;YACjC,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,OAAO,OAAO,MAAM,OAAO;IAC3C;IACA,gBAAgB,uBAAuB,GAAG,IAAI;IAC9C,gBAAgB,aAAa,GAAG,IAAI;IACpC,gBAAgB,WAAW,GAAG,IAAI;IAClC,+DAA+D;IAC/D,kHAAkH;IAClH,6DAA6D;IAC7D,kGAAkG;IAClG,gBAAgB,KAAK,GAAG,IAAI,gBAAgB,6BAA6B,KAAK,EAAE,WAAW,IAAI,CAAC;QAAC;QAAG;KAAE,GAAG;IACzG,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,WAAW,IAAI,CAAC;QAAC;QAAG;KAAE,GAAG,cAAc,YAAY;IAC3I,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,GAAG,cAAc,YAAY;IACrH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,IAAI,cAAc,YAAY;IACtH,gBAAgB,SAAS,GAAG,IAAI,gBAAgB,6BAA6B,SAAS,EAAE,IAAI,cAAc,YAAY;IACtH,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,UAAU,GAAG,IAAI,gBAAgB,6BAA6B,UAAU,EAAE,IAAI,eAAe,aAAa;IAC1H,gBAAgB,IAAI,GAAG,IAAI,gBAAgB,6BAA6B,IAAI,EAAE,IAAI,QAAQ;IAC1F,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,6BAA6B,MAAM,EAAE,IAAI,UAAU;IAChG,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,6BAA6B,MAAM,EAAE,IAAI,UAAU;IAChG,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,6BAA6B,MAAM,EAAE,IAAI,UAAU;IAChG,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,6BAA6B,MAAM,EAAE,IAAI,UAAU;IAChG,gBAAgB,kBAAkB,GAAG,IAAI,gBAAgB,6BAA6B,kBAAkB,EAAE,IAAI,sBAAsB,YAAY;IAChJ,gBAAgB,IAAI,GAAG,IAAI,gBAAgB,6BAA6B,IAAI,EAAE,IAAI,QAAQ;IAC1F,gBAAgB,KAAK,GAAG,IAAI,gBAAgB,6BAA6B,KAAK,EAAE,WAAW,IAAI,CAAC;QAAC;QAAI;KAAI,GAAG,SAAS;IACrH,gBAAgB,IAAI,GAAG,IAAI,gBAAgB,6BAA6B,IAAI,EAAE,IAAI;IAClF,gBAAgB,OAAO,GAAG,IAAI,gBAAgB,6BAA6B,OAAO,EAAE,IAAI,WAAW,UAAU,UAAU;IACvH,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,6BAA6B,MAAM,EAAE,IAAI,UAAU;IAChG,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/StringUtils.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.nio.charset.Charset;*/\n/*import java.util.Map;*/\nimport DecodeHintType from '../DecodeHintType';\nimport CharacterSetECI from './CharacterSetECI';\nimport StringEncoding from '../util/StringEncoding';\n/**\n * Common string-related functions.\n *\n * <AUTHOR>\n * <AUTHOR> <PERSON>re\n */\nvar StringUtils = /** @class */ (function () {\n    function StringUtils() {\n    }\n    // SHIFT_JIS.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING) ||\n    // EUC_JP.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING);\n    StringUtils.castAsNonUtf8Char = function (code, encoding) {\n        if (encoding === void 0) { encoding = null; }\n        // ISO 8859-1 is the Java default as UTF-8 is JavaScripts\n        // you can see this method as a Java version of String.fromCharCode\n        var e = encoding ? encoding.getName() : this.ISO88591;\n        // use passed format (fromCharCode will return UTF8 encoding)\n        return StringEncoding.decode(new Uint8Array([code]), e);\n    };\n    /**\n     * @param bytes bytes encoding a string, whose encoding should be guessed\n     * @param hints decode hints if applicable\n     * @return name of guessed encoding; at the moment will only guess one of:\n     *  {@link #SHIFT_JIS}, {@link #UTF8}, {@link #ISO88591}, or the platform\n     *  default encoding if none of these can possibly be correct\n     */\n    StringUtils.guessEncoding = function (bytes, hints) {\n        if (hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.CHARACTER_SET)) {\n            return hints.get(DecodeHintType.CHARACTER_SET).toString();\n        }\n        // For now, merely tries to distinguish ISO-8859-1, UTF-8 and Shift_JIS,\n        // which should be by far the most common encodings.\n        var length = bytes.length;\n        var canBeISO88591 = true;\n        var canBeShiftJIS = true;\n        var canBeUTF8 = true;\n        var utf8BytesLeft = 0;\n        // int utf8LowChars = 0\n        var utf2BytesChars = 0;\n        var utf3BytesChars = 0;\n        var utf4BytesChars = 0;\n        var sjisBytesLeft = 0;\n        // int sjisLowChars = 0\n        var sjisKatakanaChars = 0;\n        // int sjisDoubleBytesChars = 0\n        var sjisCurKatakanaWordLength = 0;\n        var sjisCurDoubleBytesWordLength = 0;\n        var sjisMaxKatakanaWordLength = 0;\n        var sjisMaxDoubleBytesWordLength = 0;\n        // int isoLowChars = 0\n        // int isoHighChars = 0\n        var isoHighOther = 0;\n        var utf8bom = bytes.length > 3 &&\n            bytes[0] === /*(byte) */ 0xEF &&\n            bytes[1] === /*(byte) */ 0xBB &&\n            bytes[2] === /*(byte) */ 0xBF;\n        for (var i = 0; i < length && (canBeISO88591 || canBeShiftJIS || canBeUTF8); i++) {\n            var value = bytes[i] & 0xFF;\n            // UTF-8 stuff\n            if (canBeUTF8) {\n                if (utf8BytesLeft > 0) {\n                    if ((value & 0x80) === 0) {\n                        canBeUTF8 = false;\n                    }\n                    else {\n                        utf8BytesLeft--;\n                    }\n                }\n                else if ((value & 0x80) !== 0) {\n                    if ((value & 0x40) === 0) {\n                        canBeUTF8 = false;\n                    }\n                    else {\n                        utf8BytesLeft++;\n                        if ((value & 0x20) === 0) {\n                            utf2BytesChars++;\n                        }\n                        else {\n                            utf8BytesLeft++;\n                            if ((value & 0x10) === 0) {\n                                utf3BytesChars++;\n                            }\n                            else {\n                                utf8BytesLeft++;\n                                if ((value & 0x08) === 0) {\n                                    utf4BytesChars++;\n                                }\n                                else {\n                                    canBeUTF8 = false;\n                                }\n                            }\n                        }\n                    }\n                } // else {\n                // utf8LowChars++\n                // }\n            }\n            // ISO-8859-1 stuff\n            if (canBeISO88591) {\n                if (value > 0x7F && value < 0xA0) {\n                    canBeISO88591 = false;\n                }\n                else if (value > 0x9F) {\n                    if (value < 0xC0 || value === 0xD7 || value === 0xF7) {\n                        isoHighOther++;\n                    } // else {\n                    // isoHighChars++\n                    // }\n                } // else {\n                // isoLowChars++\n                // }\n            }\n            // Shift_JIS stuff\n            if (canBeShiftJIS) {\n                if (sjisBytesLeft > 0) {\n                    if (value < 0x40 || value === 0x7F || value > 0xFC) {\n                        canBeShiftJIS = false;\n                    }\n                    else {\n                        sjisBytesLeft--;\n                    }\n                }\n                else if (value === 0x80 || value === 0xA0 || value > 0xEF) {\n                    canBeShiftJIS = false;\n                }\n                else if (value > 0xA0 && value < 0xE0) {\n                    sjisKatakanaChars++;\n                    sjisCurDoubleBytesWordLength = 0;\n                    sjisCurKatakanaWordLength++;\n                    if (sjisCurKatakanaWordLength > sjisMaxKatakanaWordLength) {\n                        sjisMaxKatakanaWordLength = sjisCurKatakanaWordLength;\n                    }\n                }\n                else if (value > 0x7F) {\n                    sjisBytesLeft++;\n                    // sjisDoubleBytesChars++\n                    sjisCurKatakanaWordLength = 0;\n                    sjisCurDoubleBytesWordLength++;\n                    if (sjisCurDoubleBytesWordLength > sjisMaxDoubleBytesWordLength) {\n                        sjisMaxDoubleBytesWordLength = sjisCurDoubleBytesWordLength;\n                    }\n                }\n                else {\n                    // sjisLowChars++\n                    sjisCurKatakanaWordLength = 0;\n                    sjisCurDoubleBytesWordLength = 0;\n                }\n            }\n        }\n        if (canBeUTF8 && utf8BytesLeft > 0) {\n            canBeUTF8 = false;\n        }\n        if (canBeShiftJIS && sjisBytesLeft > 0) {\n            canBeShiftJIS = false;\n        }\n        // Easy -- if there is BOM or at least 1 valid not-single byte character (and no evidence it can't be UTF-8), done\n        if (canBeUTF8 && (utf8bom || utf2BytesChars + utf3BytesChars + utf4BytesChars > 0)) {\n            return StringUtils.UTF8;\n        }\n        // Easy -- if assuming Shift_JIS or at least 3 valid consecutive not-ascii characters (and no evidence it can't be), done\n        if (canBeShiftJIS && (StringUtils.ASSUME_SHIFT_JIS || sjisMaxKatakanaWordLength >= 3 || sjisMaxDoubleBytesWordLength >= 3)) {\n            return StringUtils.SHIFT_JIS;\n        }\n        // Distinguishing Shift_JIS and ISO-8859-1 can be a little tough for short words. The crude heuristic is:\n        // - If we saw\n        //   - only two consecutive katakana chars in the whole text, or\n        //   - at least 10% of bytes that could be \"upper\" not-alphanumeric Latin1,\n        // - then we conclude Shift_JIS, else ISO-8859-1\n        if (canBeISO88591 && canBeShiftJIS) {\n            return (sjisMaxKatakanaWordLength === 2 && sjisKatakanaChars === 2) || isoHighOther * 10 >= length\n                ? StringUtils.SHIFT_JIS : StringUtils.ISO88591;\n        }\n        // Otherwise, try in order ISO-8859-1, Shift JIS, UTF-8 and fall back to default platform encoding\n        if (canBeISO88591) {\n            return StringUtils.ISO88591;\n        }\n        if (canBeShiftJIS) {\n            return StringUtils.SHIFT_JIS;\n        }\n        if (canBeUTF8) {\n            return StringUtils.UTF8;\n        }\n        // Otherwise, we take a wild guess with platform encoding\n        return StringUtils.PLATFORM_DEFAULT_ENCODING;\n    };\n    /**\n     *\n     * @see https://stackoverflow.com/a/13439711/4367683\n     *\n     * @param append The new string to append.\n     * @param args Argumets values to be formated.\n     */\n    StringUtils.format = function (append) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var i = -1;\n        function callback(exp, p0, p1, p2, p3, p4) {\n            if (exp === '%%')\n                return '%';\n            if (args[++i] === undefined)\n                return undefined;\n            exp = p2 ? parseInt(p2.substr(1)) : undefined;\n            var base = p3 ? parseInt(p3.substr(1)) : undefined;\n            var val;\n            switch (p4) {\n                case 's':\n                    val = args[i];\n                    break;\n                case 'c':\n                    val = args[i][0];\n                    break;\n                case 'f':\n                    val = parseFloat(args[i]).toFixed(exp);\n                    break;\n                case 'p':\n                    val = parseFloat(args[i]).toPrecision(exp);\n                    break;\n                case 'e':\n                    val = parseFloat(args[i]).toExponential(exp);\n                    break;\n                case 'x':\n                    val = parseInt(args[i]).toString(base ? base : 16);\n                    break;\n                case 'd':\n                    val = parseFloat(parseInt(args[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n                    break;\n            }\n            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n            var size = parseInt(p1); /* padding size */\n            var ch = p1 && (p1[0] + '') === '0' ? '0' : ' '; /* isnull? */\n            while (val.length < size)\n                val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n            return val;\n        }\n        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n        return append.replace(regex, callback);\n    };\n    /**\n     *\n     */\n    StringUtils.getBytes = function (str, encoding) {\n        return StringEncoding.encode(str, encoding);\n    };\n    /**\n     * Returns the charcode at the specified index or at index zero.\n     */\n    StringUtils.getCharCode = function (str, index) {\n        if (index === void 0) { index = 0; }\n        return str.charCodeAt(index);\n    };\n    /**\n     * Returns char for given charcode\n     */\n    StringUtils.getCharAt = function (charCode) {\n        return String.fromCharCode(charCode);\n    };\n    StringUtils.SHIFT_JIS = CharacterSetECI.SJIS.getName(); // \"SJIS\"\n    StringUtils.GB2312 = 'GB2312';\n    StringUtils.ISO88591 = CharacterSetECI.ISO8859_1.getName(); // \"ISO8859_1\"\n    StringUtils.EUC_JP = 'EUC_JP';\n    StringUtils.UTF8 = CharacterSetECI.UTF8.getName(); // \"UTF8\"\n    StringUtils.PLATFORM_DEFAULT_ENCODING = StringUtils.UTF8; // \"UTF8\"//Charset.defaultCharset().name()\n    StringUtils.ASSUME_SHIFT_JIS = false;\n    return StringUtils;\n}());\nexport default StringUtils;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC,GACrC,kCAAkC,GAClC,uBAAuB;;;AACvB;AACA;AACA;;;;AACA;;;;;CAKC,GACD,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA,2DAA2D;IAC3D,sDAAsD;IACtD,YAAY,iBAAiB,GAAG,SAAU,IAAI,EAAE,QAAQ;QACpD,IAAI,aAAa,KAAK,GAAG;YAAE,WAAW;QAAM;QAC5C,yDAAyD;QACzD,mEAAmE;QACnE,IAAI,IAAI,WAAW,SAAS,OAAO,KAAK,IAAI,CAAC,QAAQ;QACrD,6DAA6D;QAC7D,OAAO,2KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,IAAI,WAAW;YAAC;SAAK,GAAG;IACzD;IACA;;;;;;KAMC,GACD,YAAY,aAAa,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,UAAU,QAAQ,UAAU,aAAa,cAAc,MAAM,GAAG,CAAC,mKAAA,CAAA,UAAc,CAAC,aAAa,GAAG;YAChG,OAAO,MAAM,GAAG,CAAC,mKAAA,CAAA,UAAc,CAAC,aAAa,EAAE,QAAQ;QAC3D;QACA,wEAAwE;QACxE,oDAAoD;QACpD,IAAI,SAAS,MAAM,MAAM;QACzB,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QACpB,IAAI,YAAY;QAChB,IAAI,gBAAgB;QACpB,uBAAuB;QACvB,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,gBAAgB;QACpB,uBAAuB;QACvB,IAAI,oBAAoB;QACxB,+BAA+B;QAC/B,IAAI,4BAA4B;QAChC,IAAI,+BAA+B;QACnC,IAAI,4BAA4B;QAChC,IAAI,+BAA+B;QACnC,sBAAsB;QACtB,uBAAuB;QACvB,IAAI,eAAe;QACnB,IAAI,UAAU,MAAM,MAAM,GAAG,KACzB,KAAK,CAAC,EAAE,KAAK,SAAS,GAAG,QACzB,KAAK,CAAC,EAAE,KAAK,SAAS,GAAG,QACzB,KAAK,CAAC,EAAE,KAAK,SAAS,GAAG;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,iBAAiB,iBAAiB,SAAS,GAAG,IAAK;YAC9E,IAAI,QAAQ,KAAK,CAAC,EAAE,GAAG;YACvB,cAAc;YACd,IAAI,WAAW;gBACX,IAAI,gBAAgB,GAAG;oBACnB,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;wBACtB,YAAY;oBAChB,OACK;wBACD;oBACJ;gBACJ,OACK,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;oBAC3B,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;wBACtB,YAAY;oBAChB,OACK;wBACD;wBACA,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;4BACtB;wBACJ,OACK;4BACD;4BACA,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;gCACtB;4BACJ,OACK;gCACD;gCACA,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;oCACtB;gCACJ,OACK;oCACD,YAAY;gCAChB;4BACJ;wBACJ;oBACJ;gBACJ,EAAE,SAAS;YACX,iBAAiB;YACjB,IAAI;YACR;YACA,mBAAmB;YACnB,IAAI,eAAe;gBACf,IAAI,QAAQ,QAAQ,QAAQ,MAAM;oBAC9B,gBAAgB;gBACpB,OACK,IAAI,QAAQ,MAAM;oBACnB,IAAI,QAAQ,QAAQ,UAAU,QAAQ,UAAU,MAAM;wBAClD;oBACJ,EAAE,SAAS;gBACX,iBAAiB;gBACjB,IAAI;gBACR,EAAE,SAAS;YACX,gBAAgB;YAChB,IAAI;YACR;YACA,kBAAkB;YAClB,IAAI,eAAe;gBACf,IAAI,gBAAgB,GAAG;oBACnB,IAAI,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM;wBAChD,gBAAgB;oBACpB,OACK;wBACD;oBACJ;gBACJ,OACK,IAAI,UAAU,QAAQ,UAAU,QAAQ,QAAQ,MAAM;oBACvD,gBAAgB;gBACpB,OACK,IAAI,QAAQ,QAAQ,QAAQ,MAAM;oBACnC;oBACA,+BAA+B;oBAC/B;oBACA,IAAI,4BAA4B,2BAA2B;wBACvD,4BAA4B;oBAChC;gBACJ,OACK,IAAI,QAAQ,MAAM;oBACnB;oBACA,yBAAyB;oBACzB,4BAA4B;oBAC5B;oBACA,IAAI,+BAA+B,8BAA8B;wBAC7D,+BAA+B;oBACnC;gBACJ,OACK;oBACD,iBAAiB;oBACjB,4BAA4B;oBAC5B,+BAA+B;gBACnC;YACJ;QACJ;QACA,IAAI,aAAa,gBAAgB,GAAG;YAChC,YAAY;QAChB;QACA,IAAI,iBAAiB,gBAAgB,GAAG;YACpC,gBAAgB;QACpB;QACA,kHAAkH;QAClH,IAAI,aAAa,CAAC,WAAW,iBAAiB,iBAAiB,iBAAiB,CAAC,GAAG;YAChF,OAAO,YAAY,IAAI;QAC3B;QACA,yHAAyH;QACzH,IAAI,iBAAiB,CAAC,YAAY,gBAAgB,IAAI,6BAA6B,KAAK,gCAAgC,CAAC,GAAG;YACxH,OAAO,YAAY,SAAS;QAChC;QACA,yGAAyG;QACzG,cAAc;QACd,gEAAgE;QAChE,2EAA2E;QAC3E,gDAAgD;QAChD,IAAI,iBAAiB,eAAe;YAChC,OAAO,AAAC,8BAA8B,KAAK,sBAAsB,KAAM,eAAe,MAAM,SACtF,YAAY,SAAS,GAAG,YAAY,QAAQ;QACtD;QACA,kGAAkG;QAClG,IAAI,eAAe;YACf,OAAO,YAAY,QAAQ;QAC/B;QACA,IAAI,eAAe;YACf,OAAO,YAAY,SAAS;QAChC;QACA,IAAI,WAAW;YACX,OAAO,YAAY,IAAI;QAC3B;QACA,yDAAyD;QACzD,OAAO,YAAY,yBAAyB;IAChD;IACA;;;;;;KAMC,GACD,YAAY,MAAM,GAAG,SAAU,MAAM;QACjC,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QAChC;QACA,IAAI,IAAI,CAAC;QACT,SAAS,SAAS,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YACrC,IAAI,QAAQ,MACR,OAAO;YACX,IAAI,IAAI,CAAC,EAAE,EAAE,KAAK,WACd,OAAO;YACX,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM;YACpC,IAAI,OAAO,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM;YACzC,IAAI;YACJ,OAAQ;gBACJ,KAAK;oBACD,MAAM,IAAI,CAAC,EAAE;oBACb;gBACJ,KAAK;oBACD,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE;oBAChB;gBACJ,KAAK;oBACD,MAAM,WAAW,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC;oBAClC;gBACJ,KAAK;oBACD,MAAM,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC;oBACtC;gBACJ,KAAK;oBACD,MAAM,WAAW,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC;oBACxC;gBACJ,KAAK;oBACD,MAAM,SAAS,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,OAAO;oBAC/C;gBACJ,KAAK;oBACD,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,EAAE,OAAO,OAAO,IAAI,WAAW,CAAC,MAAM,OAAO,CAAC;oBAC/E;YACR;YACA,MAAM,OAAO,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC;YACtE,IAAI,OAAO,SAAS,KAAK,gBAAgB;YACzC,IAAI,KAAK,MAAM,AAAC,EAAE,CAAC,EAAE,GAAG,OAAQ,MAAM,MAAM,KAAK,WAAW;YAC5D,MAAO,IAAI,MAAM,GAAG,KAChB,MAAM,OAAO,YAAY,MAAM,KAAK,KAAK,KAAK,YAAY;YAC9D,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,OAAO,OAAO,CAAC,OAAO;IACjC;IACA;;KAEC,GACD,YAAY,QAAQ,GAAG,SAAU,GAAG,EAAE,QAAQ;QAC1C,OAAO,2KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,KAAK;IACtC;IACA;;KAEC,GACD,YAAY,WAAW,GAAG,SAAU,GAAG,EAAE,KAAK;QAC1C,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAG;QACnC,OAAO,IAAI,UAAU,CAAC;IAC1B;IACA;;KAEC,GACD,YAAY,SAAS,GAAG,SAAU,QAAQ;QACtC,OAAO,OAAO,YAAY,CAAC;IAC/B;IACA,YAAY,SAAS,GAAG,8KAAA,CAAA,UAAe,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;IACjE,YAAY,MAAM,GAAG;IACrB,YAAY,QAAQ,GAAG,8KAAA,CAAA,UAAe,CAAC,SAAS,CAAC,OAAO,IAAI,cAAc;IAC1E,YAAY,MAAM,GAAG;IACrB,YAAY,IAAI,GAAG,8KAAA,CAAA,UAAe,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;IAC5D,YAAY,yBAAyB,GAAG,YAAY,IAAI,EAAE,0CAA0C;IACpG,YAAY,gBAAgB,GAAG;IAC/B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/BitMatrix.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport BitArray from './BitArray';\nimport System from '../util/System';\nimport Arrays from '../util/Arrays';\nimport StringBuilder from '../util/StringBuilder';\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>Represents a 2D matrix of bits. In function arguments below, and throughout the common\n * module, x is the column position, and y is the row position. The ordering is always x, y.\n * The origin is at the top-left.</p>\n *\n * <p>Internally the bits are represented in a 1-D array of 32-bit ints. However, each row begins\n * with a new int. This is done intentionally so that we can copy out a row into a BitArray very\n * efficiently.</p>\n *\n * <p>The ordering of bits is row-major. Within each int, the least significant bits are used first,\n * meaning they represent lower x values. This is compatible with BitArray's implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar BitMatrix /*implements Cloneable*/ = /** @class */ (function () {\n    /**\n     * Creates an empty square {@link BitMatrix}.\n     *\n     * @param dimension height and width\n     */\n    // public constructor(dimension: number /*int*/) {\n    //   this(dimension, dimension)\n    // }\n    /**\n     * Creates an empty {@link BitMatrix}.\n     *\n     * @param width bit matrix width\n     * @param height bit matrix height\n     */\n    // public constructor(width: number /*int*/, height: number /*int*/) {\n    //   if (width < 1 || height < 1) {\n    //     throw new IllegalArgumentException(\"Both dimensions must be greater than 0\")\n    //   }\n    //   this.width = width\n    //   this.height = height\n    //   this.rowSize = (width + 31) / 32\n    //   bits = new int[rowSize * height];\n    // }\n    function BitMatrix(width /*int*/, height /*int*/, rowSize /*int*/, bits) {\n        this.width = width;\n        this.height = height;\n        this.rowSize = rowSize;\n        this.bits = bits;\n        if (undefined === height || null === height) {\n            height = width;\n        }\n        this.height = height;\n        if (width < 1 || height < 1) {\n            throw new IllegalArgumentException('Both dimensions must be greater than 0');\n        }\n        if (undefined === rowSize || null === rowSize) {\n            rowSize = Math.floor((width + 31) / 32);\n        }\n        this.rowSize = rowSize;\n        if (undefined === bits || null === bits) {\n            this.bits = new Int32Array(this.rowSize * this.height);\n        }\n    }\n    /**\n     * Interprets a 2D array of booleans as a {@link BitMatrix}, where \"true\" means an \"on\" bit.\n     *\n     * @function parse\n     * @param image bits of the image, as a row-major 2D array. Elements are arrays representing rows\n     * @return {@link BitMatrix} representation of image\n     */\n    BitMatrix.parseFromBooleanArray = function (image) {\n        var height = image.length;\n        var width = image[0].length;\n        var bits = new BitMatrix(width, height);\n        for (var i = 0; i < height; i++) {\n            var imageI = image[i];\n            for (var j = 0; j < width; j++) {\n                if (imageI[j]) {\n                    bits.set(j, i);\n                }\n            }\n        }\n        return bits;\n    };\n    /**\n     *\n     * @function parse\n     * @param stringRepresentation\n     * @param setString\n     * @param unsetString\n     */\n    BitMatrix.parseFromString = function (stringRepresentation, setString, unsetString) {\n        if (stringRepresentation === null) {\n            throw new IllegalArgumentException('stringRepresentation cannot be null');\n        }\n        var bits = new Array(stringRepresentation.length);\n        var bitsPos = 0;\n        var rowStartPos = 0;\n        var rowLength = -1;\n        var nRows = 0;\n        var pos = 0;\n        while (pos < stringRepresentation.length) {\n            if (stringRepresentation.charAt(pos) === '\\n' ||\n                stringRepresentation.charAt(pos) === '\\r') {\n                if (bitsPos > rowStartPos) {\n                    if (rowLength === -1) {\n                        rowLength = bitsPos - rowStartPos;\n                    }\n                    else if (bitsPos - rowStartPos !== rowLength) {\n                        throw new IllegalArgumentException('row lengths do not match');\n                    }\n                    rowStartPos = bitsPos;\n                    nRows++;\n                }\n                pos++;\n            }\n            else if (stringRepresentation.substring(pos, pos + setString.length) === setString) {\n                pos += setString.length;\n                bits[bitsPos] = true;\n                bitsPos++;\n            }\n            else if (stringRepresentation.substring(pos, pos + unsetString.length) === unsetString) {\n                pos += unsetString.length;\n                bits[bitsPos] = false;\n                bitsPos++;\n            }\n            else {\n                throw new IllegalArgumentException('illegal character encountered: ' + stringRepresentation.substring(pos));\n            }\n        }\n        // no EOL at end?\n        if (bitsPos > rowStartPos) {\n            if (rowLength === -1) {\n                rowLength = bitsPos - rowStartPos;\n            }\n            else if (bitsPos - rowStartPos !== rowLength) {\n                throw new IllegalArgumentException('row lengths do not match');\n            }\n            nRows++;\n        }\n        var matrix = new BitMatrix(rowLength, nRows);\n        for (var i = 0; i < bitsPos; i++) {\n            if (bits[i]) {\n                matrix.set(Math.floor(i % rowLength), Math.floor(i / rowLength));\n            }\n        }\n        return matrix;\n    };\n    /**\n     * <p>Gets the requested bit, where true means black.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     * @return value of given bit in matrix\n     */\n    BitMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        return ((this.bits[offset] >>> (x & 0x1f)) & 1) !== 0;\n    };\n    /**\n     * <p>Sets the given bit to true.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     */\n    BitMatrix.prototype.set = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] |= (1 << (x & 0x1f)) & 0xFFFFFFFF;\n    };\n    BitMatrix.prototype.unset = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] &= ~((1 << (x & 0x1f)) & 0xFFFFFFFF);\n    };\n    /**\n     * <p>Flips the given bit.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     */\n    BitMatrix.prototype.flip = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] ^= ((1 << (x & 0x1f)) & 0xFFFFFFFF);\n    };\n    /**\n     * Exclusive-or (XOR): Flip the bit in this {@code BitMatrix} if the corresponding\n     * mask bit is set.\n     *\n     * @param mask XOR mask\n     */\n    BitMatrix.prototype.xor = function (mask) {\n        if (this.width !== mask.getWidth() || this.height !== mask.getHeight()\n            || this.rowSize !== mask.getRowSize()) {\n            throw new IllegalArgumentException('input matrix dimensions do not match');\n        }\n        var rowArray = new BitArray(Math.floor(this.width / 32) + 1);\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        for (var y = 0, height = this.height; y < height; y++) {\n            var offset = y * rowSize;\n            var row = mask.getRow(y, rowArray).getBitArray();\n            for (var x = 0; x < rowSize; x++) {\n                bits[offset + x] ^= row[x];\n            }\n        }\n    };\n    /**\n     * Clears all bits (sets to false).\n     */\n    BitMatrix.prototype.clear = function () {\n        var bits = this.bits;\n        var max = bits.length;\n        for (var i = 0; i < max; i++) {\n            bits[i] = 0;\n        }\n    };\n    /**\n     * <p>Sets a square region of the bit matrix to true.</p>\n     *\n     * @param left The horizontal position to begin at (inclusive)\n     * @param top The vertical position to begin at (inclusive)\n     * @param width The width of the region\n     * @param height The height of the region\n     */\n    BitMatrix.prototype.setRegion = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        if (top < 0 || left < 0) {\n            throw new IllegalArgumentException('Left and top must be nonnegative');\n        }\n        if (height < 1 || width < 1) {\n            throw new IllegalArgumentException('Height and width must be at least 1');\n        }\n        var right = left + width;\n        var bottom = top + height;\n        if (bottom > this.height || right > this.width) {\n            throw new IllegalArgumentException('The region must fit inside the matrix');\n        }\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        for (var y = top; y < bottom; y++) {\n            var offset = y * rowSize;\n            for (var x = left; x < right; x++) {\n                bits[offset + Math.floor(x / 32)] |= ((1 << (x & 0x1f)) & 0xFFFFFFFF);\n            }\n        }\n    };\n    /**\n     * A fast method to retrieve one row of data from the matrix as a BitArray.\n     *\n     * @param y The row to retrieve\n     * @param row An optional caller-allocated BitArray, will be allocated if null or too small\n     * @return The resulting BitArray - this reference should always be used even when passing\n     *         your own row\n     */\n    BitMatrix.prototype.getRow = function (y /*int*/, row) {\n        if (row === null || row === undefined || row.getSize() < this.width) {\n            row = new BitArray(this.width);\n        }\n        else {\n            row.clear();\n        }\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var offset = y * rowSize;\n        for (var x = 0; x < rowSize; x++) {\n            row.setBulk(x * 32, bits[offset + x]);\n        }\n        return row;\n    };\n    /**\n     * @param y row to set\n     * @param row {@link BitArray} to copy from\n     */\n    BitMatrix.prototype.setRow = function (y /*int*/, row) {\n        System.arraycopy(row.getBitArray(), 0, this.bits, y * this.rowSize, this.rowSize);\n    };\n    /**\n     * Modifies this {@code BitMatrix} to represent the same but rotated 180 degrees\n     */\n    BitMatrix.prototype.rotate180 = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        var topRow = new BitArray(width);\n        var bottomRow = new BitArray(width);\n        for (var i = 0, length_1 = Math.floor((height + 1) / 2); i < length_1; i++) {\n            topRow = this.getRow(i, topRow);\n            bottomRow = this.getRow(height - 1 - i, bottomRow);\n            topRow.reverse();\n            bottomRow.reverse();\n            this.setRow(i, bottomRow);\n            this.setRow(height - 1 - i, topRow);\n        }\n    };\n    /**\n     * This is useful in detecting the enclosing rectangle of a 'pure' barcode.\n     *\n     * @return {@code left,top,width,height} enclosing rectangle of all 1 bits, or null if it is all white\n     */\n    BitMatrix.prototype.getEnclosingRectangle = function () {\n        var width = this.width;\n        var height = this.height;\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var left = width;\n        var top = height;\n        var right = -1;\n        var bottom = -1;\n        for (var y = 0; y < height; y++) {\n            for (var x32 = 0; x32 < rowSize; x32++) {\n                var theBits = bits[y * rowSize + x32];\n                if (theBits !== 0) {\n                    if (y < top) {\n                        top = y;\n                    }\n                    if (y > bottom) {\n                        bottom = y;\n                    }\n                    if (x32 * 32 < left) {\n                        var bit = 0;\n                        while (((theBits << (31 - bit)) & 0xFFFFFFFF) === 0) {\n                            bit++;\n                        }\n                        if ((x32 * 32 + bit) < left) {\n                            left = x32 * 32 + bit;\n                        }\n                    }\n                    if (x32 * 32 + 31 > right) {\n                        var bit = 31;\n                        while ((theBits >>> bit) === 0) {\n                            bit--;\n                        }\n                        if ((x32 * 32 + bit) > right) {\n                            right = x32 * 32 + bit;\n                        }\n                    }\n                }\n            }\n        }\n        if (right < left || bottom < top) {\n            return null;\n        }\n        return Int32Array.from([left, top, right - left + 1, bottom - top + 1]);\n    };\n    /**\n     * This is useful in detecting a corner of a 'pure' barcode.\n     *\n     * @return {@code x,y} coordinate of top-left-most 1 bit, or null if it is all white\n     */\n    BitMatrix.prototype.getTopLeftOnBit = function () {\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var bitsOffset = 0;\n        while (bitsOffset < bits.length && bits[bitsOffset] === 0) {\n            bitsOffset++;\n        }\n        if (bitsOffset === bits.length) {\n            return null;\n        }\n        var y = bitsOffset / rowSize;\n        var x = (bitsOffset % rowSize) * 32;\n        var theBits = bits[bitsOffset];\n        var bit = 0;\n        while (((theBits << (31 - bit)) & 0xFFFFFFFF) === 0) {\n            bit++;\n        }\n        x += bit;\n        return Int32Array.from([x, y]);\n    };\n    BitMatrix.prototype.getBottomRightOnBit = function () {\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var bitsOffset = bits.length - 1;\n        while (bitsOffset >= 0 && bits[bitsOffset] === 0) {\n            bitsOffset--;\n        }\n        if (bitsOffset < 0) {\n            return null;\n        }\n        var y = Math.floor(bitsOffset / rowSize);\n        var x = Math.floor(bitsOffset % rowSize) * 32;\n        var theBits = bits[bitsOffset];\n        var bit = 31;\n        while ((theBits >>> bit) === 0) {\n            bit--;\n        }\n        x += bit;\n        return Int32Array.from([x, y]);\n    };\n    /**\n     * @return The width of the matrix\n     */\n    BitMatrix.prototype.getWidth = function () {\n        return this.width;\n    };\n    /**\n     * @return The height of the matrix\n     */\n    BitMatrix.prototype.getHeight = function () {\n        return this.height;\n    };\n    /**\n     * @return The row size of the matrix\n     */\n    BitMatrix.prototype.getRowSize = function () {\n        return this.rowSize;\n    };\n    /*@Override*/\n    BitMatrix.prototype.equals = function (o) {\n        if (!(o instanceof BitMatrix)) {\n            return false;\n        }\n        var other = o;\n        return this.width === other.width && this.height === other.height && this.rowSize === other.rowSize &&\n            Arrays.equals(this.bits, other.bits);\n    };\n    /*@Override*/\n    BitMatrix.prototype.hashCode = function () {\n        var hash = this.width;\n        hash = 31 * hash + this.width;\n        hash = 31 * hash + this.height;\n        hash = 31 * hash + this.rowSize;\n        hash = 31 * hash + Arrays.hashCode(this.bits);\n        return hash;\n    };\n    /**\n     * @return string representation using \"X\" for set and \" \" for unset bits\n     */\n    /*@Override*/\n    // public toString(): string {\n    //   return toString(\": \"X, \"  \")\n    // }\n    /**\n     * @param setString representation of a set bit\n     * @param unsetString representation of an unset bit\n     * @return string representation of entire matrix utilizing given strings\n     */\n    // public toString(setString: string = \"X \", unsetString: string = \"  \"): string {\n    //   return this.buildToString(setString, unsetString, \"\\n\")\n    // }\n    /**\n     * @param setString representation of a set bit\n     * @param unsetString representation of an unset bit\n     * @param lineSeparator newline character in string representation\n     * @return string representation of entire matrix utilizing given strings and line separator\n     * @deprecated call {@link #toString(String,String)} only, which uses \\n line separator always\n     */\n    // @Deprecated\n    BitMatrix.prototype.toString = function (setString, unsetString, lineSeparator) {\n        if (setString === void 0) { setString = 'X '; }\n        if (unsetString === void 0) { unsetString = '  '; }\n        if (lineSeparator === void 0) { lineSeparator = '\\n'; }\n        return this.buildToString(setString, unsetString, lineSeparator);\n    };\n    BitMatrix.prototype.buildToString = function (setString, unsetString, lineSeparator) {\n        var result = new StringBuilder();\n        // result.append(lineSeparator);\n        for (var y = 0, height = this.height; y < height; y++) {\n            for (var x = 0, width = this.width; x < width; x++) {\n                result.append(this.get(x, y) ? setString : unsetString);\n            }\n            result.append(lineSeparator);\n        }\n        return result.toString();\n    };\n    /*@Override*/\n    BitMatrix.prototype.clone = function () {\n        return new BitMatrix(this.width, this.height, this.rowSize, this.bits.slice());\n    };\n    return BitMatrix;\n}());\nexport default BitMatrix;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC,GACrC,0BAA0B;;;AAC1B;AACA;AACA;AACA;AACA;;;;;;AACA;;;;;;;;;;;;;;CAcC,GACD,IAAI,UAAU,sBAAsB,MAAoB;IACpD;;;;KAIC,GACD,kDAAkD;IAClD,+BAA+B;IAC/B,IAAI;IACJ;;;;;KAKC,GACD,sEAAsE;IACtE,mCAAmC;IACnC,mFAAmF;IACnF,MAAM;IACN,uBAAuB;IACvB,yBAAyB;IACzB,qCAAqC;IACrC,sCAAsC;IACtC,IAAI;IACJ,SAAS,UAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,IAAI;QACnE,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,cAAc,UAAU,SAAS,QAAQ;YACzC,SAAS;QACb;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,QAAQ,KAAK,SAAS,GAAG;YACzB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,cAAc,WAAW,SAAS,SAAS;YAC3C,UAAU,KAAK,KAAK,CAAC,CAAC,QAAQ,EAAE,IAAI;QACxC;QACA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,cAAc,QAAQ,SAAS,MAAM;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;QACzD;IACJ;IACA;;;;;;KAMC,GACD,UAAU,qBAAqB,GAAG,SAAU,KAAK;QAC7C,IAAI,SAAS,MAAM,MAAM;QACzB,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;QAC3B,IAAI,OAAO,IAAI,UAAU,OAAO;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,SAAS,KAAK,CAAC,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,MAAM,CAAC,EAAE,EAAE;oBACX,KAAK,GAAG,CAAC,GAAG;gBAChB;YACJ;QACJ;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,UAAU,eAAe,GAAG,SAAU,oBAAoB,EAAE,SAAS,EAAE,WAAW;QAC9E,IAAI,yBAAyB,MAAM;YAC/B,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,OAAO,IAAI,MAAM,qBAAqB,MAAM;QAChD,IAAI,UAAU;QACd,IAAI,cAAc;QAClB,IAAI,YAAY,CAAC;QACjB,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,MAAO,MAAM,qBAAqB,MAAM,CAAE;YACtC,IAAI,qBAAqB,MAAM,CAAC,SAAS,QACrC,qBAAqB,MAAM,CAAC,SAAS,MAAM;gBAC3C,IAAI,UAAU,aAAa;oBACvB,IAAI,cAAc,CAAC,GAAG;wBAClB,YAAY,UAAU;oBAC1B,OACK,IAAI,UAAU,gBAAgB,WAAW;wBAC1C,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;oBACvC;oBACA,cAAc;oBACd;gBACJ;gBACA;YACJ,OACK,IAAI,qBAAqB,SAAS,CAAC,KAAK,MAAM,UAAU,MAAM,MAAM,WAAW;gBAChF,OAAO,UAAU,MAAM;gBACvB,IAAI,CAAC,QAAQ,GAAG;gBAChB;YACJ,OACK,IAAI,qBAAqB,SAAS,CAAC,KAAK,MAAM,YAAY,MAAM,MAAM,aAAa;gBACpF,OAAO,YAAY,MAAM;gBACzB,IAAI,CAAC,QAAQ,GAAG;gBAChB;YACJ,OACK;gBACD,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,oCAAoC,qBAAqB,SAAS,CAAC;YAC1G;QACJ;QACA,iBAAiB;QACjB,IAAI,UAAU,aAAa;YACvB,IAAI,cAAc,CAAC,GAAG;gBAClB,YAAY,UAAU;YAC1B,OACK,IAAI,UAAU,gBAAgB,WAAW;gBAC1C,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;YACvC;YACA;QACJ;QACA,IAAI,SAAS,IAAI,UAAU,WAAW;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAC9B,IAAI,IAAI,CAAC,EAAE,EAAE;gBACT,OAAO,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;YACzD;QACJ;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QAC5C,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI;QAC/C,OAAO,CAAC,AAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,IAAK,CAAC,MAAM;IACxD;IACA;;;;;KAKC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QAC5C,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,AAAC,KAAK,CAAC,IAAI,IAAI,IAAK;IAC7C;IACA,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QAC9C,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,IAAI,IAAI,IAAK,UAAU;IACzD;IACA;;;;;KAKC,GACD,UAAU,SAAS,CAAC,IAAI,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QAC7C,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAK,AAAC,KAAK,CAAC,IAAI,IAAI,IAAK;IAC9C;IACA;;;;;KAKC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QACpC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,QAAQ,MAAM,IAAI,CAAC,MAAM,KAAK,KAAK,SAAS,MAC7D,IAAI,CAAC,OAAO,KAAK,KAAK,UAAU,IAAI;YACvC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,WAAW,IAAI,uKAAA,CAAA,UAAQ,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM;QAC1D,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,IAAK;YACnD,IAAI,SAAS,IAAI;YACjB,IAAI,MAAM,KAAK,MAAM,CAAC,GAAG,UAAU,WAAW;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAC9B,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,CAAC,EAAE;YAC9B;QACJ;IACJ;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,KAAK,GAAG;QACxB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,MAAM,KAAK,MAAM;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA;;;;;;;KAOC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACtF,IAAI,MAAM,KAAK,OAAO,GAAG;YACrB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,SAAS,KAAK,QAAQ,GAAG;YACzB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,QAAQ,OAAO;QACnB,IAAI,SAAS,MAAM;QACnB,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,IAAK;YAC/B,IAAI,SAAS,IAAI;YACjB,IAAK,IAAI,IAAI,MAAM,IAAI,OAAO,IAAK;gBAC/B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,IAAI,IAAK,AAAC,KAAK,CAAC,IAAI,IAAI,IAAK;YAC9D;QACJ;IACJ;IACA;;;;;;;KAOC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE;YACjE,MAAM,IAAI,uKAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,KAAK;QACjC,OACK;YACD,IAAI,KAAK;QACb;QACA,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,SAAS,IAAI;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAC9B,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;QACxC;QACA,OAAO;IACX;IACA;;;KAGC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QACjD,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,IAAI,WAAW,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO;IACpF;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,SAAS,IAAI,CAAC,SAAS;QAC3B,IAAI,SAAS,IAAI,uKAAA,CAAA,UAAQ,CAAC;QAC1B,IAAI,YAAY,IAAI,uKAAA,CAAA,UAAQ,CAAC;QAC7B,IAAK,IAAI,IAAI,GAAG,WAAW,KAAK,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,UAAU,IAAK;YACxE,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG;YACxB,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG;YACxC,OAAO,OAAO;YACd,UAAU,OAAO;YACjB,IAAI,CAAC,MAAM,CAAC,GAAG;YACf,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG;QAChC;IACJ;IACA;;;;KAIC,GACD,UAAU,SAAS,CAAC,qBAAqB,GAAG;QACxC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,OAAO;QACX,IAAI,MAAM;QACV,IAAI,QAAQ,CAAC;QACb,IAAI,SAAS,CAAC;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAK,IAAI,MAAM,GAAG,MAAM,SAAS,MAAO;gBACpC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI;gBACrC,IAAI,YAAY,GAAG;oBACf,IAAI,IAAI,KAAK;wBACT,MAAM;oBACV;oBACA,IAAI,IAAI,QAAQ;wBACZ,SAAS;oBACb;oBACA,IAAI,MAAM,KAAK,MAAM;wBACjB,IAAI,MAAM;wBACV,MAAO,CAAC,AAAC,WAAY,KAAK,MAAQ,UAAU,MAAM,EAAG;4BACjD;wBACJ;wBACA,IAAI,AAAC,MAAM,KAAK,MAAO,MAAM;4BACzB,OAAO,MAAM,KAAK;wBACtB;oBACJ;oBACA,IAAI,MAAM,KAAK,KAAK,OAAO;wBACvB,IAAI,MAAM;wBACV,MAAO,AAAC,YAAY,QAAS,EAAG;4BAC5B;wBACJ;wBACA,IAAI,AAAC,MAAM,KAAK,MAAO,OAAO;4BAC1B,QAAQ,MAAM,KAAK;wBACvB;oBACJ;gBACJ;YACJ;QACJ;QACA,IAAI,QAAQ,QAAQ,SAAS,KAAK;YAC9B,OAAO;QACX;QACA,OAAO,WAAW,IAAI,CAAC;YAAC;YAAM;YAAK,QAAQ,OAAO;YAAG,SAAS,MAAM;SAAE;IAC1E;IACA;;;;KAIC,GACD,UAAU,SAAS,CAAC,eAAe,GAAG;QAClC,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,aAAa;QACjB,MAAO,aAAa,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,EAAG;YACvD;QACJ;QACA,IAAI,eAAe,KAAK,MAAM,EAAE;YAC5B,OAAO;QACX;QACA,IAAI,IAAI,aAAa;QACrB,IAAI,IAAI,AAAC,aAAa,UAAW;QACjC,IAAI,UAAU,IAAI,CAAC,WAAW;QAC9B,IAAI,MAAM;QACV,MAAO,CAAC,AAAC,WAAY,KAAK,MAAQ,UAAU,MAAM,EAAG;YACjD;QACJ;QACA,KAAK;QACL,OAAO,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;IACjC;IACA,UAAU,SAAS,CAAC,mBAAmB,GAAG;QACtC,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,aAAa,KAAK,MAAM,GAAG;QAC/B,MAAO,cAAc,KAAK,IAAI,CAAC,WAAW,KAAK,EAAG;YAC9C;QACJ;QACA,IAAI,aAAa,GAAG;YAChB,OAAO;QACX;QACA,IAAI,IAAI,KAAK,KAAK,CAAC,aAAa;QAChC,IAAI,IAAI,KAAK,KAAK,CAAC,aAAa,WAAW;QAC3C,IAAI,UAAU,IAAI,CAAC,WAAW;QAC9B,IAAI,MAAM;QACV,MAAO,AAAC,YAAY,QAAS,EAAG;YAC5B;QACJ;QACA,KAAK;QACL,OAAO,WAAW,IAAI,CAAC;YAAC;YAAG;SAAE;IACjC;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG;QAC7B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,WAAW,GACX,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACpC,IAAI,CAAC,CAAC,aAAa,SAAS,GAAG;YAC3B,OAAO;QACX;QACA,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,OAAO,IAC/F,mKAAA,CAAA,UAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI;IAC3C;IACA,WAAW,GACX,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC3B,IAAI,OAAO,IAAI,CAAC,KAAK;QACrB,OAAO,KAAK,OAAO,IAAI,CAAC,KAAK;QAC7B,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM;QAC9B,OAAO,KAAK,OAAO,IAAI,CAAC,OAAO;QAC/B,OAAO,KAAK,OAAO,mKAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QAC5C,OAAO;IACX;IACA;;KAEC,GACD,WAAW,GACX,8BAA8B;IAC9B,iCAAiC;IACjC,IAAI;IACJ;;;;KAIC,GACD,kFAAkF;IAClF,4DAA4D;IAC5D,IAAI;IACJ;;;;;;KAMC,GACD,cAAc;IACd,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS,EAAE,WAAW,EAAE,aAAa;QAC1E,IAAI,cAAc,KAAK,GAAG;YAAE,YAAY;QAAM;QAC9C,IAAI,gBAAgB,KAAK,GAAG;YAAE,cAAc;QAAM;QAClD,IAAI,kBAAkB,KAAK,GAAG;YAAE,gBAAgB;QAAM;QACtD,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,aAAa;IACtD;IACA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,WAAW,EAAE,aAAa;QAC/E,IAAI,SAAS,IAAI,0KAAA,CAAA,UAAa;QAC9B,gCAAgC;QAChC,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,IAAK;YACnD,IAAK,IAAI,IAAI,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,OAAO,IAAK;gBAChD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,YAAY;YAC/C;YACA,OAAO,MAAM,CAAC;QAClB;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA,WAAW,GACX,UAAU,SAAS,CAAC,KAAK,GAAG;QACxB,OAAO,IAAI,UAAU,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;IAC/E;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/GlobalHistogramBinarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common {*/\nimport Binarizer from '../Binarizer';\nimport BitArray from './BitArray';\nimport BitMatrix from './BitMatrix';\nimport NotFoundException from '../NotFoundException';\n/**\n * This Binarizer implementation uses the old ZXing global histogram approach. It is suitable\n * for low-end mobile devices which don't have enough CPU or memory to use a local thresholding\n * algorithm. However, because it picks a global black point, it cannot handle difficult shadows\n * and gradients.\n *\n * Faster mobile devices and all desktop applications should probably use HybridBinarizer instead.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n */\nvar GlobalHistogramBinarizer = /** @class */ (function (_super) {\n    __extends(GlobalHistogramBinarizer, _super);\n    function GlobalHistogramBinarizer(source) {\n        var _this = _super.call(this, source) || this;\n        _this.luminances = GlobalHistogramBinarizer.EMPTY;\n        _this.buckets = new Int32Array(GlobalHistogramBinarizer.LUMINANCE_BUCKETS);\n        return _this;\n    }\n    // Applies simple sharpening to the row data to improve performance of the 1D Readers.\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.getBlackRow = function (y /*int*/, row) {\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        if (row === undefined || row === null || row.getSize() < width) {\n            row = new BitArray(width);\n        }\n        else {\n            row.clear();\n        }\n        this.initArrays(width);\n        var localLuminances = source.getRow(y, this.luminances);\n        var localBuckets = this.buckets;\n        for (var x = 0; x < width; x++) {\n            localBuckets[(localLuminances[x] & 0xff) >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n        }\n        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n        if (width < 3) {\n            // Special case for very small images\n            for (var x = 0; x < width; x++) {\n                if ((localLuminances[x] & 0xff) < blackPoint) {\n                    row.set(x);\n                }\n            }\n        }\n        else {\n            var left = localLuminances[0] & 0xff;\n            var center = localLuminances[1] & 0xff;\n            for (var x = 1; x < width - 1; x++) {\n                var right = localLuminances[x + 1] & 0xff;\n                // A simple -1 4 -1 box filter with a weight of 2.\n                if (((center * 4) - left - right) / 2 < blackPoint) {\n                    row.set(x);\n                }\n                left = center;\n                center = right;\n            }\n        }\n        return row;\n    };\n    // Does not sharpen the data, as this call is intended to only be used by 2D Readers.\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.getBlackMatrix = function () {\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        var height = source.getHeight();\n        var matrix = new BitMatrix(width, height);\n        // Quickly calculates the histogram by sampling four rows from the image. This proved to be\n        // more robust on the blackbox tests than sampling a diagonal as we used to do.\n        this.initArrays(width);\n        var localBuckets = this.buckets;\n        for (var y = 1; y < 5; y++) {\n            var row = Math.floor((height * y) / 5);\n            var localLuminances_1 = source.getRow(row, this.luminances);\n            var right = Math.floor((width * 4) / 5);\n            for (var x = Math.floor(width / 5); x < right; x++) {\n                var pixel = localLuminances_1[x] & 0xff;\n                localBuckets[pixel >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n            }\n        }\n        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n        // We delay reading the entire image luminance until the black point estimation succeeds.\n        // Although we end up reading four rows twice, it is consistent with our motto of\n        // \"fail quickly\" which is necessary for continuous scanning.\n        var localLuminances = source.getMatrix();\n        for (var y = 0; y < height; y++) {\n            var offset = y * width;\n            for (var x = 0; x < width; x++) {\n                var pixel = localLuminances[offset + x] & 0xff;\n                if (pixel < blackPoint) {\n                    matrix.set(x, y);\n                }\n            }\n        }\n        return matrix;\n    };\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.createBinarizer = function (source) {\n        return new GlobalHistogramBinarizer(source);\n    };\n    GlobalHistogramBinarizer.prototype.initArrays = function (luminanceSize /*int*/) {\n        if (this.luminances.length < luminanceSize) {\n            this.luminances = new Uint8ClampedArray(luminanceSize);\n        }\n        var buckets = this.buckets;\n        for (var x = 0; x < GlobalHistogramBinarizer.LUMINANCE_BUCKETS; x++) {\n            buckets[x] = 0;\n        }\n    };\n    GlobalHistogramBinarizer.estimateBlackPoint = function (buckets) {\n        // Find the tallest peak in the histogram.\n        var numBuckets = buckets.length;\n        var maxBucketCount = 0;\n        var firstPeak = 0;\n        var firstPeakSize = 0;\n        for (var x = 0; x < numBuckets; x++) {\n            if (buckets[x] > firstPeakSize) {\n                firstPeak = x;\n                firstPeakSize = buckets[x];\n            }\n            if (buckets[x] > maxBucketCount) {\n                maxBucketCount = buckets[x];\n            }\n        }\n        // Find the second-tallest peak which is somewhat far from the tallest peak.\n        var secondPeak = 0;\n        var secondPeakScore = 0;\n        for (var x = 0; x < numBuckets; x++) {\n            var distanceToBiggest = x - firstPeak;\n            // Encourage more distant second peaks by multiplying by square of distance.\n            var score = buckets[x] * distanceToBiggest * distanceToBiggest;\n            if (score > secondPeakScore) {\n                secondPeak = x;\n                secondPeakScore = score;\n            }\n        }\n        // Make sure firstPeak corresponds to the black peak.\n        if (firstPeak > secondPeak) {\n            var temp = firstPeak;\n            firstPeak = secondPeak;\n            secondPeak = temp;\n        }\n        // If there is too little contrast in the image to pick a meaningful black point, throw rather\n        // than waste time trying to decode the image, and risk false positives.\n        if (secondPeak - firstPeak <= numBuckets / 16) {\n            throw new NotFoundException();\n        }\n        // Find a valley between them that is low and closer to the white peak.\n        var bestValley = secondPeak - 1;\n        var bestValleyScore = -1;\n        for (var x = secondPeak - 1; x > firstPeak; x--) {\n            var fromFirst = x - firstPeak;\n            var score = fromFirst * fromFirst * (secondPeak - x) * (maxBucketCount - buckets[x]);\n            if (score > bestValleyScore) {\n                bestValley = x;\n                bestValleyScore = score;\n            }\n        }\n        return bestValley << GlobalHistogramBinarizer.LUMINANCE_SHIFT;\n    };\n    GlobalHistogramBinarizer.LUMINANCE_BITS = 5;\n    GlobalHistogramBinarizer.LUMINANCE_SHIFT = 8 - GlobalHistogramBinarizer.LUMINANCE_BITS;\n    GlobalHistogramBinarizer.LUMINANCE_BUCKETS = 1 << GlobalHistogramBinarizer.LUMINANCE_BITS;\n    GlobalHistogramBinarizer.EMPTY = Uint8ClampedArray.from([0]);\n    return GlobalHistogramBinarizer;\n}(Binarizer));\nexport default GlobalHistogramBinarizer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,qCAAqC,GACrC;AACA;AACA;AACA;AAjBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;AAMA;;;;;;;;;;CAUC,GACD,IAAI,2BAA0C,SAAU,MAAM;IAC1D,UAAU,0BAA0B;IACpC,SAAS,yBAAyB,MAAM;QACpC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,WAAW,IAAI;QAC7C,MAAM,UAAU,GAAG,yBAAyB,KAAK;QACjD,MAAM,OAAO,GAAG,IAAI,WAAW,yBAAyB,iBAAiB;QACzE,OAAO;IACX;IACA,sFAAsF;IACtF,WAAW,GACX,yBAAyB,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QACrE,IAAI,SAAS,IAAI,CAAC,kBAAkB;QACpC,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,QAAQ,aAAa,QAAQ,QAAQ,IAAI,OAAO,KAAK,OAAO;YAC5D,MAAM,IAAI,uKAAA,CAAA,UAAQ,CAAC;QACvB,OACK;YACD,IAAI,KAAK;QACb;QACA,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,kBAAkB,OAAO,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU;QACtD,IAAI,eAAe,IAAI,CAAC,OAAO;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,KAAK,yBAAyB,eAAe,CAAC;QACzF;QACA,IAAI,aAAa,yBAAyB,kBAAkB,CAAC;QAC7D,IAAI,QAAQ,GAAG;YACX,qCAAqC;YACrC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,IAAI,YAAY;oBAC1C,IAAI,GAAG,CAAC;gBACZ;YACJ;QACJ,OACK;YACD,IAAI,OAAO,eAAe,CAAC,EAAE,GAAG;YAChC,IAAI,SAAS,eAAe,CAAC,EAAE,GAAG;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;gBAChC,IAAI,QAAQ,eAAe,CAAC,IAAI,EAAE,GAAG;gBACrC,kDAAkD;gBAClD,IAAI,CAAC,AAAC,SAAS,IAAK,OAAO,KAAK,IAAI,IAAI,YAAY;oBAChD,IAAI,GAAG,CAAC;gBACZ;gBACA,OAAO;gBACP,SAAS;YACb;QACJ;QACA,OAAO;IACX;IACA,qFAAqF;IACrF,WAAW,GACX,yBAAyB,SAAS,CAAC,cAAc,GAAG;QAChD,IAAI,SAAS,IAAI,CAAC,kBAAkB;QACpC,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAI,SAAS,IAAI,wKAAA,CAAA,UAAS,CAAC,OAAO;QAClC,2FAA2F;QAC3F,+EAA+E;QAC/E,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,eAAe,IAAI,CAAC,OAAO;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,MAAM,KAAK,KAAK,CAAC,AAAC,SAAS,IAAK;YACpC,IAAI,oBAAoB,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,UAAU;YAC1D,IAAI,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,IAAK;YACrC,IAAK,IAAI,IAAI,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,OAAO,IAAK;gBAChD,IAAI,QAAQ,iBAAiB,CAAC,EAAE,GAAG;gBACnC,YAAY,CAAC,SAAS,yBAAyB,eAAe,CAAC;YACnE;QACJ;QACA,IAAI,aAAa,yBAAyB,kBAAkB,CAAC;QAC7D,yFAAyF;QACzF,iFAAiF;QACjF,6DAA6D;QAC7D,IAAI,kBAAkB,OAAO,SAAS;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,IAAI,SAAS,IAAI;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,IAAI,QAAQ,eAAe,CAAC,SAAS,EAAE,GAAG;gBAC1C,IAAI,QAAQ,YAAY;oBACpB,OAAO,GAAG,CAAC,GAAG;gBAClB;YACJ;QACJ;QACA,OAAO;IACX;IACA,WAAW,GACX,yBAAyB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM;QACjE,OAAO,IAAI,yBAAyB;IACxC;IACA,yBAAyB,SAAS,CAAC,UAAU,GAAG,SAAU,cAAc,KAAK,GAAN;QACnE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,eAAe;YACxC,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB;QAC5C;QACA,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,yBAAyB,iBAAiB,EAAE,IAAK;YACjE,OAAO,CAAC,EAAE,GAAG;QACjB;IACJ;IACA,yBAAyB,kBAAkB,GAAG,SAAU,OAAO;QAC3D,0CAA0C;QAC1C,IAAI,aAAa,QAAQ,MAAM;QAC/B,IAAI,iBAAiB;QACrB,IAAI,YAAY;QAChB,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,IAAI,OAAO,CAAC,EAAE,GAAG,eAAe;gBAC5B,YAAY;gBACZ,gBAAgB,OAAO,CAAC,EAAE;YAC9B;YACA,IAAI,OAAO,CAAC,EAAE,GAAG,gBAAgB;gBAC7B,iBAAiB,OAAO,CAAC,EAAE;YAC/B;QACJ;QACA,4EAA4E;QAC5E,IAAI,aAAa;QACjB,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,IAAI,oBAAoB,IAAI;YAC5B,4EAA4E;YAC5E,IAAI,QAAQ,OAAO,CAAC,EAAE,GAAG,oBAAoB;YAC7C,IAAI,QAAQ,iBAAiB;gBACzB,aAAa;gBACb,kBAAkB;YACtB;QACJ;QACA,qDAAqD;QACrD,IAAI,YAAY,YAAY;YACxB,IAAI,OAAO;YACX,YAAY;YACZ,aAAa;QACjB;QACA,8FAA8F;QAC9F,wEAAwE;QACxE,IAAI,aAAa,aAAa,aAAa,IAAI;YAC3C,MAAM,IAAI,sKAAA,CAAA,UAAiB;QAC/B;QACA,uEAAuE;QACvE,IAAI,aAAa,aAAa;QAC9B,IAAI,kBAAkB,CAAC;QACvB,IAAK,IAAI,IAAI,aAAa,GAAG,IAAI,WAAW,IAAK;YAC7C,IAAI,YAAY,IAAI;YACpB,IAAI,QAAQ,YAAY,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,EAAE;YACnF,IAAI,QAAQ,iBAAiB;gBACzB,aAAa;gBACb,kBAAkB;YACtB;QACJ;QACA,OAAO,cAAc,yBAAyB,eAAe;IACjE;IACA,yBAAyB,cAAc,GAAG;IAC1C,yBAAyB,eAAe,GAAG,IAAI,yBAAyB,cAAc;IACtF,yBAAyB,iBAAiB,GAAG,KAAK,yBAAyB,cAAc;IACzF,yBAAyB,KAAK,GAAG,kBAAkB,IAAI,CAAC;QAAC;KAAE;IAC3D,OAAO;AACX,EAAE,8JAAA,CAAA,UAAS;uCACI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/HybridBinarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport GlobalHistogramBinarizer from './GlobalHistogramBinarizer';\nimport BitMatrix from './BitMatrix';\n/**\n * This class implements a local thresholding algorithm, which while slower than the\n * GlobalHistogramBinarizer, is fairly efficient for what it does. It is designed for\n * high frequency images of barcodes with black data on white backgrounds. For this application,\n * it does a much better job than a global blackpoint with severe shadows and gradients.\n * However it tends to produce artifacts on lower frequency images and is therefore not\n * a good general purpose binarizer for uses outside ZXing.\n *\n * This class extends GlobalHistogramBinarizer, using the older histogram approach for 1D readers,\n * and the newer local approach for 2D readers. 1D decoding using a per-row histogram is already\n * inherently local, and only fails for horizontal gradients. We can revisit that problem later,\n * but for now it was not a win to use local blocks for 1D.\n *\n * This Binarizer is the default for the unit tests and the recommended class for library users.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar HybridBinarizer = /** @class */ (function (_super) {\n    __extends(HybridBinarizer, _super);\n    function HybridBinarizer(source) {\n        var _this = _super.call(this, source) || this;\n        _this.matrix = null;\n        return _this;\n    }\n    /**\n     * Calculates the final BitMatrix once for all requests. This could be called once from the\n     * constructor instead, but there are some advantages to doing it lazily, such as making\n     * profiling easier, and not doing heavy lifting when callers don't expect it.\n     */\n    /*@Override*/\n    HybridBinarizer.prototype.getBlackMatrix = function () {\n        if (this.matrix !== null) {\n            return this.matrix;\n        }\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        var height = source.getHeight();\n        if (width >= HybridBinarizer.MINIMUM_DIMENSION && height >= HybridBinarizer.MINIMUM_DIMENSION) {\n            var luminances = source.getMatrix();\n            var subWidth = width >> HybridBinarizer.BLOCK_SIZE_POWER;\n            if ((width & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n                subWidth++;\n            }\n            var subHeight = height >> HybridBinarizer.BLOCK_SIZE_POWER;\n            if ((height & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n                subHeight++;\n            }\n            var blackPoints = HybridBinarizer.calculateBlackPoints(luminances, subWidth, subHeight, width, height);\n            var newMatrix = new BitMatrix(width, height);\n            HybridBinarizer.calculateThresholdForBlock(luminances, subWidth, subHeight, width, height, blackPoints, newMatrix);\n            this.matrix = newMatrix;\n        }\n        else {\n            // If the image is too small, fall back to the global histogram approach.\n            this.matrix = _super.prototype.getBlackMatrix.call(this);\n        }\n        return this.matrix;\n    };\n    /*@Override*/\n    HybridBinarizer.prototype.createBinarizer = function (source) {\n        return new HybridBinarizer(source);\n    };\n    /**\n     * For each block in the image, calculate the average black point using a 5x5 grid\n     * of the blocks around it. Also handles the corner cases (fractional blocks are computed based\n     * on the last pixels in the row/column which are also used in the previous block).\n     */\n    HybridBinarizer.calculateThresholdForBlock = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/, blackPoints, matrix) {\n        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n        for (var y = 0; y < subHeight; y++) {\n            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n            if (yoffset > maxYOffset) {\n                yoffset = maxYOffset;\n            }\n            var top_1 = HybridBinarizer.cap(y, 2, subHeight - 3);\n            for (var x = 0; x < subWidth; x++) {\n                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n                if (xoffset > maxXOffset) {\n                    xoffset = maxXOffset;\n                }\n                var left = HybridBinarizer.cap(x, 2, subWidth - 3);\n                var sum = 0;\n                for (var z = -2; z <= 2; z++) {\n                    var blackRow = blackPoints[top_1 + z];\n                    sum += blackRow[left - 2] + blackRow[left - 1] + blackRow[left] + blackRow[left + 1] + blackRow[left + 2];\n                }\n                var average = sum / 25;\n                HybridBinarizer.thresholdBlock(luminances, xoffset, yoffset, average, width, matrix);\n            }\n        }\n    };\n    HybridBinarizer.cap = function (value /*int*/, min /*int*/, max /*int*/) {\n        return value < min ? min : value > max ? max : value;\n    };\n    /**\n     * Applies a single threshold to a block of pixels.\n     */\n    HybridBinarizer.thresholdBlock = function (luminances, xoffset /*int*/, yoffset /*int*/, threshold /*int*/, stride /*int*/, matrix) {\n        for (var y = 0, offset = yoffset * stride + xoffset; y < HybridBinarizer.BLOCK_SIZE; y++, offset += stride) {\n            for (var x = 0; x < HybridBinarizer.BLOCK_SIZE; x++) {\n                // Comparison needs to be <= so that black == 0 pixels are black even if the threshold is 0.\n                if ((luminances[offset + x] & 0xFF) <= threshold) {\n                    matrix.set(xoffset + x, yoffset + y);\n                }\n            }\n        }\n    };\n    /**\n     * Calculates a single black point for each block of pixels and saves it away.\n     * See the following thread for a discussion of this algorithm:\n     *  http://groups.google.com/group/zxing/browse_thread/thread/d06efa2c35a7ddc0\n     */\n    HybridBinarizer.calculateBlackPoints = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/) {\n        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n        // tslint:disable-next-line:whitespace\n        var blackPoints = new Array(subHeight); // subWidth\n        for (var y = 0; y < subHeight; y++) {\n            blackPoints[y] = new Int32Array(subWidth);\n            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n            if (yoffset > maxYOffset) {\n                yoffset = maxYOffset;\n            }\n            for (var x = 0; x < subWidth; x++) {\n                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n                if (xoffset > maxXOffset) {\n                    xoffset = maxXOffset;\n                }\n                var sum = 0;\n                var min = 0xFF;\n                var max = 0;\n                for (var yy = 0, offset = yoffset * width + xoffset; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n                    for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n                        var pixel = luminances[offset + xx] & 0xFF;\n                        sum += pixel;\n                        // still looking for good contrast\n                        if (pixel < min) {\n                            min = pixel;\n                        }\n                        if (pixel > max) {\n                            max = pixel;\n                        }\n                    }\n                    // short-circuit min/max tests once dynamic range is met\n                    if (max - min > HybridBinarizer.MIN_DYNAMIC_RANGE) {\n                        // finish the rest of the rows quickly\n                        for (yy++, offset += width; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n                            for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n                                sum += luminances[offset + xx] & 0xFF;\n                            }\n                        }\n                    }\n                }\n                // The default estimate is the average of the values in the block.\n                var average = sum >> (HybridBinarizer.BLOCK_SIZE_POWER * 2);\n                if (max - min <= HybridBinarizer.MIN_DYNAMIC_RANGE) {\n                    // If variation within the block is low, assume this is a block with only light or only\n                    // dark pixels. In that case we do not want to use the average, as it would divide this\n                    // low contrast area into black and white pixels, essentially creating data out of noise.\n                    //\n                    // The default assumption is that the block is light/background. Since no estimate for\n                    // the level of dark pixels exists locally, use half the min for the block.\n                    average = min / 2;\n                    if (y > 0 && x > 0) {\n                        // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n                        // the pixels in this block to the previously calculated black points. This is based on\n                        // the fact that dark barcode symbology is always surrounded by some amount of light\n                        // background for which reasonable black point estimates were made. The bp estimated at\n                        // the boundaries is used for the interior.\n                        // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n                        var averageNeighborBlackPoint = (blackPoints[y - 1][x] + (2 * blackPoints[y][x - 1]) + blackPoints[y - 1][x - 1]) / 4;\n                        if (min < averageNeighborBlackPoint) {\n                            average = averageNeighborBlackPoint;\n                        }\n                    }\n                }\n                blackPoints[y][x] = average;\n            }\n        }\n        return blackPoints;\n    };\n    // This class uses 5x5 blocks to compute local luminance, where each block is 8x8 pixels.\n    // So this is the smallest dimension in each axis we can accept.\n    HybridBinarizer.BLOCK_SIZE_POWER = 3;\n    HybridBinarizer.BLOCK_SIZE = 1 << HybridBinarizer.BLOCK_SIZE_POWER; // ...0100...00\n    HybridBinarizer.BLOCK_SIZE_MASK = HybridBinarizer.BLOCK_SIZE - 1; // ...0011...11\n    HybridBinarizer.MINIMUM_DIMENSION = HybridBinarizer.BLOCK_SIZE * 5;\n    HybridBinarizer.MIN_DYNAMIC_RANGE = 24;\n    return HybridBinarizer;\n}(GlobalHistogramBinarizer));\nexport default HybridBinarizer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;;;;;;;;;;;;;CAgBC,GACD,IAAI,kBAAiC,SAAU,MAAM;IACjD,UAAU,iBAAiB;IAC3B,SAAS,gBAAgB,MAAM;QAC3B,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,WAAW,IAAI;QAC7C,MAAM,MAAM,GAAG;QACf,OAAO;IACX;IACA;;;;KAIC,GACD,WAAW,GACX,gBAAgB,SAAS,CAAC,cAAc,GAAG;QACvC,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM;YACtB,OAAO,IAAI,CAAC,MAAM;QACtB;QACA,IAAI,SAAS,IAAI,CAAC,kBAAkB;QACpC,IAAI,QAAQ,OAAO,QAAQ;QAC3B,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAI,SAAS,gBAAgB,iBAAiB,IAAI,UAAU,gBAAgB,iBAAiB,EAAE;YAC3F,IAAI,aAAa,OAAO,SAAS;YACjC,IAAI,WAAW,SAAS,gBAAgB,gBAAgB;YACxD,IAAI,CAAC,QAAQ,gBAAgB,eAAe,MAAM,GAAG;gBACjD;YACJ;YACA,IAAI,YAAY,UAAU,gBAAgB,gBAAgB;YAC1D,IAAI,CAAC,SAAS,gBAAgB,eAAe,MAAM,GAAG;gBAClD;YACJ;YACA,IAAI,cAAc,gBAAgB,oBAAoB,CAAC,YAAY,UAAU,WAAW,OAAO;YAC/F,IAAI,YAAY,IAAI,wKAAA,CAAA,UAAS,CAAC,OAAO;YACrC,gBAAgB,0BAA0B,CAAC,YAAY,UAAU,WAAW,OAAO,QAAQ,aAAa;YACxG,IAAI,CAAC,MAAM,GAAG;QAClB,OACK;YACD,yEAAyE;YACzE,IAAI,CAAC,MAAM,GAAG,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;QAC3D;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,WAAW,GACX,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM;QACxD,OAAO,IAAI,gBAAgB;IAC/B;IACA;;;;KAIC,GACD,gBAAgB,0BAA0B,GAAG,SAAU,UAAU,EAAE,SAAS,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,WAAW,EAAE,MAAM;QACtJ,IAAI,aAAa,SAAS,gBAAgB,UAAU;QACpD,IAAI,aAAa,QAAQ,gBAAgB,UAAU;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAChC,IAAI,UAAU,KAAK,gBAAgB,gBAAgB;YACnD,IAAI,UAAU,YAAY;gBACtB,UAAU;YACd;YACA,IAAI,QAAQ,gBAAgB,GAAG,CAAC,GAAG,GAAG,YAAY;YAClD,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;gBAC/B,IAAI,UAAU,KAAK,gBAAgB,gBAAgB;gBACnD,IAAI,UAAU,YAAY;oBACtB,UAAU;gBACd;gBACA,IAAI,OAAO,gBAAgB,GAAG,CAAC,GAAG,GAAG,WAAW;gBAChD,IAAI,MAAM;gBACV,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;oBAC1B,IAAI,WAAW,WAAW,CAAC,QAAQ,EAAE;oBACrC,OAAO,QAAQ,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE;gBAC7G;gBACA,IAAI,UAAU,MAAM;gBACpB,gBAAgB,cAAc,CAAC,YAAY,SAAS,SAAS,SAAS,OAAO;YACjF;QACJ;IACJ;IACA,gBAAgB,GAAG,GAAG,SAAU,MAAM,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN;QAC3D,OAAO,QAAQ,MAAM,MAAM,QAAQ,MAAM,MAAM;IACnD;IACA;;KAEC,GACD,gBAAgB,cAAc,GAAG,SAAU,UAAU,EAAE,QAAQ,KAAK,GAAN,EAAU,QAAQ,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,MAAM;QAC9H,IAAK,IAAI,IAAI,GAAG,SAAS,UAAU,SAAS,SAAS,IAAI,gBAAgB,UAAU,EAAE,KAAK,UAAU,OAAQ;YACxG,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,UAAU,EAAE,IAAK;gBACjD,4FAA4F;gBAC5F,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,IAAI,KAAK,WAAW;oBAC9C,OAAO,GAAG,CAAC,UAAU,GAAG,UAAU;gBACtC;YACJ;QACJ;IACJ;IACA;;;;KAIC,GACD,gBAAgB,oBAAoB,GAAG,SAAU,UAAU,EAAE,SAAS,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACnH,IAAI,aAAa,SAAS,gBAAgB,UAAU;QACpD,IAAI,aAAa,QAAQ,gBAAgB,UAAU;QACnD,sCAAsC;QACtC,IAAI,cAAc,IAAI,MAAM,YAAY,WAAW;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAChC,WAAW,CAAC,EAAE,GAAG,IAAI,WAAW;YAChC,IAAI,UAAU,KAAK,gBAAgB,gBAAgB;YACnD,IAAI,UAAU,YAAY;gBACtB,UAAU;YACd;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;gBAC/B,IAAI,UAAU,KAAK,gBAAgB,gBAAgB;gBACnD,IAAI,UAAU,YAAY;oBACtB,UAAU;gBACd;gBACA,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAK,IAAI,KAAK,GAAG,SAAS,UAAU,QAAQ,SAAS,KAAK,gBAAgB,UAAU,EAAE,MAAM,UAAU,MAAO;oBACzG,IAAK,IAAI,KAAK,GAAG,KAAK,gBAAgB,UAAU,EAAE,KAAM;wBACpD,IAAI,QAAQ,UAAU,CAAC,SAAS,GAAG,GAAG;wBACtC,OAAO;wBACP,kCAAkC;wBAClC,IAAI,QAAQ,KAAK;4BACb,MAAM;wBACV;wBACA,IAAI,QAAQ,KAAK;4BACb,MAAM;wBACV;oBACJ;oBACA,wDAAwD;oBACxD,IAAI,MAAM,MAAM,gBAAgB,iBAAiB,EAAE;wBAC/C,sCAAsC;wBACtC,IAAK,MAAM,UAAU,OAAO,KAAK,gBAAgB,UAAU,EAAE,MAAM,UAAU,MAAO;4BAChF,IAAK,IAAI,KAAK,GAAG,KAAK,gBAAgB,UAAU,EAAE,KAAM;gCACpD,OAAO,UAAU,CAAC,SAAS,GAAG,GAAG;4BACrC;wBACJ;oBACJ;gBACJ;gBACA,kEAAkE;gBAClE,IAAI,UAAU,OAAQ,gBAAgB,gBAAgB,GAAG;gBACzD,IAAI,MAAM,OAAO,gBAAgB,iBAAiB,EAAE;oBAChD,uFAAuF;oBACvF,uFAAuF;oBACvF,yFAAyF;oBACzF,EAAE;oBACF,sFAAsF;oBACtF,2EAA2E;oBAC3E,UAAU,MAAM;oBAChB,IAAI,IAAI,KAAK,IAAI,GAAG;wBAChB,wFAAwF;wBACxF,uFAAuF;wBACvF,oFAAoF;wBACpF,uFAAuF;wBACvF,2CAA2C;wBAC3C,sFAAsF;wBACtF,IAAI,4BAA4B,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,GAAI,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,GAAI,WAAW,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI;wBACpH,IAAI,MAAM,2BAA2B;4BACjC,UAAU;wBACd;oBACJ;gBACJ;gBACA,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG;YACxB;QACJ;QACA,OAAO;IACX;IACA,yFAAyF;IACzF,gEAAgE;IAChE,gBAAgB,gBAAgB,GAAG;IACnC,gBAAgB,UAAU,GAAG,KAAK,gBAAgB,gBAAgB,EAAE,eAAe;IACnF,gBAAgB,eAAe,GAAG,gBAAgB,UAAU,GAAG,GAAG,eAAe;IACjF,gBAAgB,iBAAiB,GAAG,gBAAgB,UAAU,GAAG;IACjE,gBAAgB,iBAAiB,GAAG;IACpC,OAAO;AACX,EAAE,uLAAA,CAAA,UAAwB;uCACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/DecoderResult.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.List;*/\n/**\n * <p>Encapsulates the result of decoding a matrix of bits. This typically\n * applies to 2D barcode formats. For now it contains the raw bytes obtained,\n * as well as a String interpretation of those bytes, if applicable.</p>\n *\n * <AUTHOR>\n */\nvar DecoderResult = /** @class */ (function () {\n    // public constructor(rawBytes: Uint8Array,\n    //                      text: string,\n    //                      List<Uint8Array> byteSegments,\n    //                      String ecLevel) {\n    //   this(rawBytes, text, byteSegments, ecLevel, -1, -1)\n    // }\n    function DecoderResult(rawBytes, text, byteSegments, ecLevel, structuredAppendSequenceNumber, structuredAppendParity) {\n        if (structuredAppendSequenceNumber === void 0) { structuredAppendSequenceNumber = -1; }\n        if (structuredAppendParity === void 0) { structuredAppendParity = -1; }\n        this.rawBytes = rawBytes;\n        this.text = text;\n        this.byteSegments = byteSegments;\n        this.ecLevel = ecLevel;\n        this.structuredAppendSequenceNumber = structuredAppendSequenceNumber;\n        this.structuredAppendParity = structuredAppendParity;\n        this.numBits = (rawBytes === undefined || rawBytes === null) ? 0 : 8 * rawBytes.length;\n    }\n    /**\n     * @return raw bytes representing the result, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getRawBytes = function () {\n        return this.rawBytes;\n    };\n    /**\n     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n     * @since 3.3.0\n     */\n    DecoderResult.prototype.getNumBits = function () {\n        return this.numBits;\n    };\n    /**\n     * @param numBits overrides the number of bits that are valid in {@link #getRawBytes()}\n     * @since 3.3.0\n     */\n    DecoderResult.prototype.setNumBits = function (numBits /*int*/) {\n        this.numBits = numBits;\n    };\n    /**\n     * @return text representation of the result\n     */\n    DecoderResult.prototype.getText = function () {\n        return this.text;\n    };\n    /**\n     * @return list of byte segments in the result, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getByteSegments = function () {\n        return this.byteSegments;\n    };\n    /**\n     * @return name of error correction level used, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getECLevel = function () {\n        return this.ecLevel;\n    };\n    /**\n     * @return number of errors corrected, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getErrorsCorrected = function () {\n        return this.errorsCorrected;\n    };\n    DecoderResult.prototype.setErrorsCorrected = function (errorsCorrected /*Integer*/) {\n        this.errorsCorrected = errorsCorrected;\n    };\n    /**\n     * @return number of erasures corrected, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getErasures = function () {\n        return this.erasures;\n    };\n    DecoderResult.prototype.setErasures = function (erasures /*Integer*/) {\n        this.erasures = erasures;\n    };\n    /**\n     * @return arbitrary additional metadata\n     */\n    DecoderResult.prototype.getOther = function () {\n        return this.other;\n    };\n    DecoderResult.prototype.setOther = function (other) {\n        this.other = other;\n    };\n    DecoderResult.prototype.hasStructuredAppend = function () {\n        return this.structuredAppendParity >= 0 && this.structuredAppendSequenceNumber >= 0;\n    };\n    DecoderResult.prototype.getStructuredAppendParity = function () {\n        return this.structuredAppendParity;\n    };\n    DecoderResult.prototype.getStructuredAppendSequenceNumber = function () {\n        return this.structuredAppendSequenceNumber;\n    };\n    return DecoderResult;\n}());\nexport default DecoderResult;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC,GACrC,wBAAwB,GACxB;;;;;;CAMC;;;AACD,IAAI,gBAA+B;IAC/B,2CAA2C;IAC3C,qCAAqC;IACrC,sDAAsD;IACtD,yCAAyC;IACzC,wDAAwD;IACxD,IAAI;IACJ,SAAS,cAAc,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,8BAA8B,EAAE,sBAAsB;QAChH,IAAI,mCAAmC,KAAK,GAAG;YAAE,iCAAiC,CAAC;QAAG;QACtF,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB,CAAC;QAAG;QACtE,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,8BAA8B,GAAG;QACtC,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,OAAO,GAAG,AAAC,aAAa,aAAa,aAAa,OAAQ,IAAI,IAAI,SAAS,MAAM;IAC1F;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,WAAW,GAAG;QAClC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;;;KAGC,GACD,cAAc,SAAS,CAAC,UAAU,GAAG;QACjC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;KAGC,GACD,cAAc,SAAS,CAAC,UAAU,GAAG,SAAU,QAAQ,KAAK,GAAN;QAClD,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,OAAO,GAAG;QAC9B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,eAAe,GAAG;QACtC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,UAAU,GAAG;QACjC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,kBAAkB,GAAG;QACzC,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,gBAAgB,SAAS,GAAV;QAClE,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,WAAW,GAAG;QAClC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,SAAS,SAAS,GAAV;QACpD,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC9C,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,cAAc,SAAS,CAAC,mBAAmB,GAAG;QAC1C,OAAO,IAAI,CAAC,sBAAsB,IAAI,KAAK,IAAI,CAAC,8BAA8B,IAAI;IACtF;IACA,cAAc,SAAS,CAAC,yBAAyB,GAAG;QAChD,OAAO,IAAI,CAAC,sBAAsB;IACtC;IACA,cAAc,SAAS,CAAC,iCAAiC,GAAG;QACxD,OAAO,IAAI,CAAC,8BAA8B;IAC9C;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/reedsolomon/AbstractGenericGF.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar AbstractGenericGF = /** @class */ (function () {\n    function AbstractGenericGF() {\n    }\n    /**\n     * @return 2 to the power of a in GF(size)\n     */\n    AbstractGenericGF.prototype.exp = function (a) {\n        return this.expTable[a];\n    };\n    /**\n     * @return base 2 log of a in GF(size)\n     */\n    AbstractGenericGF.prototype.log = function (a /*int*/) {\n        if (a === 0) {\n            throw new IllegalArgumentException();\n        }\n        return this.logTable[a];\n    };\n    /**\n     * Implements both addition and subtraction -- they are the same in GF(size).\n     *\n     * @return sum/difference of a and b\n     */\n    AbstractGenericGF.addOrSubtract = function (a /*int*/, b /*int*/) {\n        return a ^ b;\n    };\n    return AbstractGenericGF;\n}());\nexport default AbstractGenericGF;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA;;;;;;;;;;CAUC,GACD,IAAI,oBAAmC;IACnC,SAAS,qBACT;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC3B;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,KAAK,GAAN;QACzC,IAAI,MAAM,GAAG;YACT,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC3B;IACA;;;;KAIC,GACD,kBAAkB,aAAa,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACpD,OAAO,IAAI;IACf;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1939, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport AbstractGenericGF from './AbstractGenericGF';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Represents a polynomial whose coefficients are elements of a GF.\n * Instances of this class are immutable.</p>\n *\n * <p>Much credit is due to <PERSON> since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR>\n */\nvar GenericGFPoly = /** @class */ (function () {\n    /**\n     * @param field the {@link GenericGF} instance representing the field to use\n     * to perform computations\n     * @param coefficients coefficients as ints representing elements of GF(size), arranged\n     * from most significant (highest-power term) coefficient to least significant\n     * @throws IllegalArgumentException if argument is null or empty,\n     * or if leading coefficient is 0 and this is not a\n     * constant polynomial (that is, it is not the monomial \"0\")\n     */\n    function GenericGFPoly(field, coefficients) {\n        if (coefficients.length === 0) {\n            throw new IllegalArgumentException();\n        }\n        this.field = field;\n        var coefficientsLength = coefficients.length;\n        if (coefficientsLength > 1 && coefficients[0] === 0) {\n            // Leading term must be non-zero for anything except the constant polynomial \"0\"\n            var firstNonZero = 1;\n            while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n                firstNonZero++;\n            }\n            if (firstNonZero === coefficientsLength) {\n                this.coefficients = Int32Array.from([0]);\n            }\n            else {\n                this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n                System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n            }\n        }\n        else {\n            this.coefficients = coefficients;\n        }\n    }\n    GenericGFPoly.prototype.getCoefficients = function () {\n        return this.coefficients;\n    };\n    /**\n     * @return degree of this polynomial\n     */\n    GenericGFPoly.prototype.getDegree = function () {\n        return this.coefficients.length - 1;\n    };\n    /**\n     * @return true iff this polynomial is the monomial \"0\"\n     */\n    GenericGFPoly.prototype.isZero = function () {\n        return this.coefficients[0] === 0;\n    };\n    /**\n     * @return coefficient of x^degree term in this polynomial\n     */\n    GenericGFPoly.prototype.getCoefficient = function (degree /*int*/) {\n        return this.coefficients[this.coefficients.length - 1 - degree];\n    };\n    /**\n     * @return evaluation of this polynomial at a given point\n     */\n    GenericGFPoly.prototype.evaluateAt = function (a /*int*/) {\n        if (a === 0) {\n            // Just return the x^0 coefficient\n            return this.getCoefficient(0);\n        }\n        var coefficients = this.coefficients;\n        var result;\n        if (a === 1) {\n            // Just the sum of the coefficients\n            result = 0;\n            for (var i = 0, length_1 = coefficients.length; i !== length_1; i++) {\n                var coefficient = coefficients[i];\n                result = AbstractGenericGF.addOrSubtract(result, coefficient);\n            }\n            return result;\n        }\n        result = coefficients[0];\n        var size = coefficients.length;\n        var field = this.field;\n        for (var i = 1; i < size; i++) {\n            result = AbstractGenericGF.addOrSubtract(field.multiply(a, result), coefficients[i]);\n        }\n        return result;\n    };\n    GenericGFPoly.prototype.addOrSubtract = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (this.isZero()) {\n            return other;\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        var smallerCoefficients = this.coefficients;\n        var largerCoefficients = other.coefficients;\n        if (smallerCoefficients.length > largerCoefficients.length) {\n            var temp = smallerCoefficients;\n            smallerCoefficients = largerCoefficients;\n            largerCoefficients = temp;\n        }\n        var sumDiff = new Int32Array(largerCoefficients.length);\n        var lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n        // Copy high-order terms only found in higher-degree polynomial's coefficients\n        System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n        for (var i = lengthDiff; i < largerCoefficients.length; i++) {\n            sumDiff[i] = AbstractGenericGF.addOrSubtract(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n        }\n        return new GenericGFPoly(this.field, sumDiff);\n    };\n    GenericGFPoly.prototype.multiply = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (this.isZero() || other.isZero()) {\n            return this.field.getZero();\n        }\n        var aCoefficients = this.coefficients;\n        var aLength = aCoefficients.length;\n        var bCoefficients = other.coefficients;\n        var bLength = bCoefficients.length;\n        var product = new Int32Array(aLength + bLength - 1);\n        var field = this.field;\n        for (var i = 0; i < aLength; i++) {\n            var aCoeff = aCoefficients[i];\n            for (var j = 0; j < bLength; j++) {\n                product[i + j] = AbstractGenericGF.addOrSubtract(product[i + j], field.multiply(aCoeff, bCoefficients[j]));\n            }\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.multiplyScalar = function (scalar /*int*/) {\n        if (scalar === 0) {\n            return this.field.getZero();\n        }\n        if (scalar === 1) {\n            return this;\n        }\n        var size = this.coefficients.length;\n        var field = this.field;\n        var product = new Int32Array(size);\n        var coefficients = this.coefficients;\n        for (var i = 0; i < size; i++) {\n            product[i] = field.multiply(coefficients[i], scalar);\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.multiplyByMonomial = function (degree /*int*/, coefficient /*int*/) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.field.getZero();\n        }\n        var coefficients = this.coefficients;\n        var size = coefficients.length;\n        var product = new Int32Array(size + degree);\n        var field = this.field;\n        for (var i = 0; i < size; i++) {\n            product[i] = field.multiply(coefficients[i], coefficient);\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.divide = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (other.isZero()) {\n            throw new IllegalArgumentException('Divide by 0');\n        }\n        var field = this.field;\n        var quotient = field.getZero();\n        var remainder = this;\n        var denominatorLeadingTerm = other.getCoefficient(other.getDegree());\n        var inverseDenominatorLeadingTerm = field.inverse(denominatorLeadingTerm);\n        while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n            var degreeDifference = remainder.getDegree() - other.getDegree();\n            var scale = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n            var term = other.multiplyByMonomial(degreeDifference, scale);\n            var iterationQuotient = field.buildMonomial(degreeDifference, scale);\n            quotient = quotient.addOrSubtract(iterationQuotient);\n            remainder = remainder.addOrSubtract(term);\n        }\n        return [quotient, remainder];\n    };\n    /*@Override*/\n    GenericGFPoly.prototype.toString = function () {\n        var result = '';\n        for (var degree = this.getDegree(); degree >= 0; degree--) {\n            var coefficient = this.getCoefficient(degree);\n            if (coefficient !== 0) {\n                if (coefficient < 0) {\n                    result += ' - ';\n                    coefficient = -coefficient;\n                }\n                else {\n                    if (result.length > 0) {\n                        result += ' + ';\n                    }\n                }\n                if (degree === 0 || coefficient !== 1) {\n                    var alphaPower = this.field.log(coefficient);\n                    if (alphaPower === 0) {\n                        result += '1';\n                    }\n                    else if (alphaPower === 1) {\n                        result += 'a';\n                    }\n                    else {\n                        result += 'a^';\n                        result += alphaPower;\n                    }\n                }\n                if (degree !== 0) {\n                    if (degree === 1) {\n                        result += 'x';\n                    }\n                    else {\n                        result += 'x^';\n                        result += degree;\n                    }\n                }\n            }\n        }\n        return result;\n    };\n    return GenericGFPoly;\n}());\nexport default GenericGFPoly;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,iDAAiD;;;AACjD;AACA;AACA;;;;AACA;;;;;;;;CAQC,GACD,IAAI,gBAA+B;IAC/B;;;;;;;;KAQC,GACD,SAAS,cAAc,KAAK,EAAE,YAAY;QACtC,IAAI,aAAa,MAAM,KAAK,GAAG;YAC3B,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,qBAAqB,aAAa,MAAM;QAC5C,IAAI,qBAAqB,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;YACjD,gFAAgF;YAChF,IAAI,eAAe;YACnB,MAAO,eAAe,sBAAsB,YAAY,CAAC,aAAa,KAAK,EAAG;gBAC1E;YACJ;YACA,IAAI,iBAAiB,oBAAoB;gBACrC,IAAI,CAAC,YAAY,GAAG,WAAW,IAAI,CAAC;oBAAC;iBAAE;YAC3C,OACK;gBACD,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,qBAAqB;gBACxD,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,cAAc,cAAc,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;YAC/F;QACJ,OACK;YACD,IAAI,CAAC,YAAY,GAAG;QACxB;IACJ;IACA,cAAc,SAAS,CAAC,eAAe,GAAG;QACtC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,SAAS,GAAG;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IACtC;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,MAAM,GAAG;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK;IACpC;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO,KAAK,GAAN;QACrD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,OAAO;IACnE;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,UAAU,GAAG,SAAU,EAAE,KAAK,GAAN;QAC5C,IAAI,MAAM,GAAG;YACT,kCAAkC;YAClC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B;QACA,IAAI,eAAe,IAAI,CAAC,YAAY;QACpC,IAAI;QACJ,IAAI,MAAM,GAAG;YACT,mCAAmC;YACnC,SAAS;YACT,IAAK,IAAI,IAAI,GAAG,WAAW,aAAa,MAAM,EAAE,MAAM,UAAU,IAAK;gBACjE,IAAI,cAAc,YAAY,CAAC,EAAE;gBACjC,SAAS,+LAAA,CAAA,UAAiB,CAAC,aAAa,CAAC,QAAQ;YACrD;YACA,OAAO;QACX;QACA,SAAS,YAAY,CAAC,EAAE;QACxB,IAAI,OAAO,aAAa,MAAM;QAC9B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,SAAS,+LAAA,CAAA,UAAiB,CAAC,aAAa,CAAC,MAAM,QAAQ,CAAC,GAAG,SAAS,YAAY,CAAC,EAAE;QACvF;QACA,OAAO;IACX;IACA,cAAc,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACnD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,IAAI,CAAC,MAAM,IAAI;YACf,OAAO;QACX;QACA,IAAI,MAAM,MAAM,IAAI;YAChB,OAAO,IAAI;QACf;QACA,IAAI,sBAAsB,IAAI,CAAC,YAAY;QAC3C,IAAI,qBAAqB,MAAM,YAAY;QAC3C,IAAI,oBAAoB,MAAM,GAAG,mBAAmB,MAAM,EAAE;YACxD,IAAI,OAAO;YACX,sBAAsB;YACtB,qBAAqB;QACzB;QACA,IAAI,UAAU,IAAI,WAAW,mBAAmB,MAAM;QACtD,IAAI,aAAa,mBAAmB,MAAM,GAAG,oBAAoB,MAAM;QACvE,8EAA8E;QAC9E,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAS,GAAG;QACpD,IAAK,IAAI,IAAI,YAAY,IAAI,mBAAmB,MAAM,EAAE,IAAK;YACzD,OAAO,CAAC,EAAE,GAAG,+LAAA,CAAA,UAAiB,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,WAAW,EAAE,kBAAkB,CAAC,EAAE;QAC3G;QACA,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;IACzC;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI;YACjC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;QAC7B;QACA,IAAI,gBAAgB,IAAI,CAAC,YAAY;QACrC,IAAI,UAAU,cAAc,MAAM;QAClC,IAAI,gBAAgB,MAAM,YAAY;QACtC,IAAI,UAAU,cAAc,MAAM;QAClC,IAAI,UAAU,IAAI,WAAW,UAAU,UAAU;QACjD,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAC9B,IAAI,SAAS,aAAa,CAAC,EAAE;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAC9B,OAAO,CAAC,IAAI,EAAE,GAAG,+LAAA,CAAA,UAAiB,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,MAAM,QAAQ,CAAC,QAAQ,aAAa,CAAC,EAAE;YAC5G;QACJ;QACA,OAAO,IAAI,cAAc,OAAO;IACpC;IACA,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO,KAAK,GAAN;QACrD,IAAI,WAAW,GAAG;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;QAC7B;QACA,IAAI,WAAW,GAAG;YACd,OAAO,IAAI;QACf;QACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACnC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,UAAU,IAAI,WAAW;QAC7B,IAAI,eAAe,IAAI,CAAC,YAAY;QACpC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,OAAO,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE;QACjD;QACA,OAAO,IAAI,cAAc,OAAO;IACpC;IACA,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,YAAY,KAAK,GAAN;QAC9E,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,IAAI,gBAAgB,GAAG;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;QAC7B;QACA,IAAI,eAAe,IAAI,CAAC,YAAY;QACpC,IAAI,OAAO,aAAa,MAAM;QAC9B,IAAI,UAAU,IAAI,WAAW,OAAO;QACpC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,OAAO,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE;QACjD;QACA,OAAO,IAAI,cAAc,OAAO;IACpC;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,MAAM,MAAM,IAAI;YAChB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,WAAW,MAAM,OAAO;QAC5B,IAAI,YAAY,IAAI;QACpB,IAAI,yBAAyB,MAAM,cAAc,CAAC,MAAM,SAAS;QACjE,IAAI,gCAAgC,MAAM,OAAO,CAAC;QAClD,MAAO,UAAU,SAAS,MAAM,MAAM,SAAS,MAAM,CAAC,UAAU,MAAM,GAAI;YACtE,IAAI,mBAAmB,UAAU,SAAS,KAAK,MAAM,SAAS;YAC9D,IAAI,QAAQ,MAAM,QAAQ,CAAC,UAAU,cAAc,CAAC,UAAU,SAAS,KAAK;YAC5E,IAAI,OAAO,MAAM,kBAAkB,CAAC,kBAAkB;YACtD,IAAI,oBAAoB,MAAM,aAAa,CAAC,kBAAkB;YAC9D,WAAW,SAAS,aAAa,CAAC;YAClC,YAAY,UAAU,aAAa,CAAC;QACxC;QACA,OAAO;YAAC;YAAU;SAAU;IAChC;IACA,WAAW,GACX,cAAc,SAAS,CAAC,QAAQ,GAAG;QAC/B,IAAI,SAAS;QACb,IAAK,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,UAAU,GAAG,SAAU;YACvD,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC;YACtC,IAAI,gBAAgB,GAAG;gBACnB,IAAI,cAAc,GAAG;oBACjB,UAAU;oBACV,cAAc,CAAC;gBACnB,OACK;oBACD,IAAI,OAAO,MAAM,GAAG,GAAG;wBACnB,UAAU;oBACd;gBACJ;gBACA,IAAI,WAAW,KAAK,gBAAgB,GAAG;oBACnC,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAChC,IAAI,eAAe,GAAG;wBAClB,UAAU;oBACd,OACK,IAAI,eAAe,GAAG;wBACvB,UAAU;oBACd,OACK;wBACD,UAAU;wBACV,UAAU;oBACd;gBACJ;gBACA,IAAI,WAAW,GAAG;oBACd,IAAI,WAAW,GAAG;wBACd,UAAU;oBACd,OACK;wBACD,UAAU;wBACV,UAAU;oBACd;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/reedsolomon/GenericGF.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGFPoly from './GenericGFPoly';\nimport AbstractGenericGF from './AbstractGenericGF';\nimport Integer from '../../util/Integer';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport ArithmeticException from '../../ArithmeticException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Olivier\n */\nvar GenericGF = /** @class */ (function (_super) {\n    __extends(GenericGF, _super);\n    /**\n     * Create a representation of GF(size) using the given primitive polynomial.\n     *\n     * @param primitive irreducible polynomial whose coefficients are represented by\n     *  the bits of an int, where the least-significant bit represents the constant\n     *  coefficient\n     * @param size the size of the field\n     * @param b the factor b in the generator polynomial can be 0- or 1-based\n     *  (g(x) = (x+a^b)(x+a^(b+1))...(x+a^(b+2t-1))).\n     *  In most cases it should be 1, but for QR code it is 0.\n     */\n    function GenericGF(primitive /*int*/, size /*int*/, generatorBase /*int*/) {\n        var _this = _super.call(this) || this;\n        _this.primitive = primitive;\n        _this.size = size;\n        _this.generatorBase = generatorBase;\n        var expTable = new Int32Array(size);\n        var x = 1;\n        for (var i = 0; i < size; i++) {\n            expTable[i] = x;\n            x *= 2; // we're assuming the generator alpha is 2\n            if (x >= size) {\n                x ^= primitive;\n                x &= size - 1;\n            }\n        }\n        _this.expTable = expTable;\n        var logTable = new Int32Array(size);\n        for (var i = 0; i < size - 1; i++) {\n            logTable[expTable[i]] = i;\n        }\n        _this.logTable = logTable;\n        // logTable[0] == 0 but this should never be used\n        _this.zero = new GenericGFPoly(_this, Int32Array.from([0]));\n        _this.one = new GenericGFPoly(_this, Int32Array.from([1]));\n        return _this;\n    }\n    GenericGF.prototype.getZero = function () {\n        return this.zero;\n    };\n    GenericGF.prototype.getOne = function () {\n        return this.one;\n    };\n    /**\n     * @return the monomial representing coefficient * x^degree\n     */\n    GenericGF.prototype.buildMonomial = function (degree /*int*/, coefficient /*int*/) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.zero;\n        }\n        var coefficients = new Int32Array(degree + 1);\n        coefficients[0] = coefficient;\n        return new GenericGFPoly(this, coefficients);\n    };\n    /**\n     * @return multiplicative inverse of a\n     */\n    GenericGF.prototype.inverse = function (a /*int*/) {\n        if (a === 0) {\n            throw new ArithmeticException();\n        }\n        return this.expTable[this.size - this.logTable[a] - 1];\n    };\n    /**\n     * @return product of a and b in GF(size)\n     */\n    GenericGF.prototype.multiply = function (a /*int*/, b /*int*/) {\n        if (a === 0 || b === 0) {\n            return 0;\n        }\n        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n    };\n    GenericGF.prototype.getSize = function () {\n        return this.size;\n    };\n    GenericGF.prototype.getGeneratorBase = function () {\n        return this.generatorBase;\n    };\n    /*@Override*/\n    GenericGF.prototype.toString = function () {\n        return ('GF(0x' + Integer.toHexString(this.primitive) + ',' + this.size + ')');\n    };\n    GenericGF.prototype.equals = function (o) {\n        return o === this;\n    };\n    GenericGF.AZTEC_DATA_12 = new GenericGF(0x1069, 4096, 1); // x^12 + x^6 + x^5 + x^3 + 1\n    GenericGF.AZTEC_DATA_10 = new GenericGF(0x409, 1024, 1); // x^10 + x^3 + 1\n    GenericGF.AZTEC_DATA_6 = new GenericGF(0x43, 64, 1); // x^6 + x + 1\n    GenericGF.AZTEC_PARAM = new GenericGF(0x13, 16, 1); // x^4 + x + 1\n    GenericGF.QR_CODE_FIELD_256 = new GenericGF(0x011d, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n    GenericGF.DATA_MATRIX_FIELD_256 = new GenericGF(0x012d, 256, 1); // x^8 + x^5 + x^3 + x^2 + 1\n    GenericGF.AZTEC_DATA_8 = GenericGF.DATA_MATRIX_FIELD_256;\n    GenericGF.MAXICODE_FIELD_64 = GenericGF.AZTEC_DATA_6;\n    return GenericGF;\n}(AbstractGenericGF));\nexport default GenericGF;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,iDAAiD,GACjD;AACA;AACA;AACA;AACA;AAlBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;AAOA;;;;;;;;;;CAUC,GACD,IAAI,YAA2B,SAAU,MAAM;IAC3C,UAAU,WAAW;IACrB;;;;;;;;;;KAUC,GACD,SAAS,UAAU,UAAU,KAAK,GAAN,EAAU,KAAK,KAAK,GAAN,EAAU,cAAc,KAAK,GAAN;QAC7D,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,SAAS,GAAG;QAClB,MAAM,IAAI,GAAG;QACb,MAAM,aAAa,GAAG;QACtB,IAAI,WAAW,IAAI,WAAW;QAC9B,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,QAAQ,CAAC,EAAE,GAAG;YACd,KAAK,GAAG,0CAA0C;YAClD,IAAI,KAAK,MAAM;gBACX,KAAK;gBACL,KAAK,OAAO;YAChB;QACJ;QACA,MAAM,QAAQ,GAAG;QACjB,IAAI,WAAW,IAAI,WAAW;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,GAAG,IAAK;YAC/B,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG;QAC5B;QACA,MAAM,QAAQ,GAAG;QACjB,iDAAiD;QACjD,MAAM,IAAI,GAAG,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO,WAAW,IAAI,CAAC;YAAC;SAAE;QACzD,MAAM,GAAG,GAAG,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO,WAAW,IAAI,CAAC;YAAC;SAAE;QACxD,OAAO;IACX;IACA,UAAU,SAAS,CAAC,OAAO,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,UAAU,SAAS,CAAC,MAAM,GAAG;QACzB,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,OAAO,KAAK,GAAN,EAAU,YAAY,KAAK,GAAN;QACrE,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,6KAAA,CAAA,UAAwB;QACtC;QACA,IAAI,gBAAgB,GAAG;YACnB,OAAO,IAAI,CAAC,IAAI;QACpB;QACA,IAAI,eAAe,IAAI,WAAW,SAAS;QAC3C,YAAY,CAAC,EAAE,GAAG;QAClB,OAAO,IAAI,2LAAA,CAAA,UAAa,CAAC,IAAI,EAAE;IACnC;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,KAAK,GAAN;QACrC,IAAI,MAAM,GAAG;YACT,MAAM,IAAI,wKAAA,CAAA,UAAmB;QACjC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE;IAC1D;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACjD,IAAI,MAAM,KAAK,MAAM,GAAG;YACpB,OAAO;QACX;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;IACjF;IACA,UAAU,SAAS,CAAC,OAAO,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,UAAU,SAAS,CAAC,gBAAgB,GAAG;QACnC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,WAAW,GACX,UAAU,SAAS,CAAC,QAAQ,GAAG;QAC3B,OAAQ,UAAU,oKAAA,CAAA,UAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG;IAC9E;IACA,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACpC,OAAO,MAAM,IAAI;IACrB;IACA,UAAU,aAAa,GAAG,IAAI,UAAU,QAAQ,MAAM,IAAI,6BAA6B;IACvF,UAAU,aAAa,GAAG,IAAI,UAAU,OAAO,MAAM,IAAI,iBAAiB;IAC1E,UAAU,YAAY,GAAG,IAAI,UAAU,MAAM,IAAI,IAAI,cAAc;IACnE,UAAU,WAAW,GAAG,IAAI,UAAU,MAAM,IAAI,IAAI,cAAc;IAClE,UAAU,iBAAiB,GAAG,IAAI,UAAU,QAAQ,KAAK,IAAI,4BAA4B;IACzF,UAAU,qBAAqB,GAAG,IAAI,UAAU,QAAQ,KAAK,IAAI,4BAA4B;IAC7F,UAAU,YAAY,GAAG,UAAU,qBAAqB;IACxD,UAAU,iBAAiB,GAAG,UAAU,YAAY;IACpD,OAAO;AACX,EAAE,+LAAA,CAAA,UAAiB;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGF from './GenericGF';\nimport GenericGFPoly from './GenericGFPoly';\nimport ReedSolomonException from '../../ReedSolomonException';\nimport IllegalStateException from '../../IllegalStateException';\n/**\n * <p>Implements Reed-Solomon decoding, as the name implies.</p>\n *\n * <p>The algorithm will not be explained here, but the following references were helpful\n * in creating this implementation:</p>\n *\n * <ul>\n * <li><PERSON>.\n * <a href=\"http://www.cs.cmu.edu/afs/cs.cmu.edu/project/pscico-guyb/realworld/www/rs_decode.ps\">\n * \"Decoding Reed-Solomon Codes\"</a> (see discussion of Forney's Formula)</li>\n * <li>J.I. Hall. <a href=\"www.mth.msu.edu/~jhall/classes/codenotes/GRS.pdf\">\n * \"Chapter 5. Generalized Reed-Solomon Codes\"</a>\n * (see discussion of Euclidean algorithm)</li>\n * </ul>\n *\n * <p>Much credit is due to William Rucklidge since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Rucklidge\n * <AUTHOR>\n */\nvar ReedSolomonDecoder = /** @class */ (function () {\n    function ReedSolomonDecoder(field) {\n        this.field = field;\n    }\n    /**\n     * <p>Decodes given set of received codewords, which include both data and error-correction\n     * codewords. Really, this means it uses Reed-Solomon to detect and correct errors, in-place,\n     * in the input.</p>\n     *\n     * @param received data and error-correction codewords\n     * @param twoS number of error-correction codewords available\n     * @throws ReedSolomonException if decoding fails for any reason\n     */\n    ReedSolomonDecoder.prototype.decode = function (received, twoS /*int*/) {\n        var field = this.field;\n        var poly = new GenericGFPoly(field, received);\n        var syndromeCoefficients = new Int32Array(twoS);\n        var noError = true;\n        for (var i = 0; i < twoS; i++) {\n            var evalResult = poly.evaluateAt(field.exp(i + field.getGeneratorBase()));\n            syndromeCoefficients[syndromeCoefficients.length - 1 - i] = evalResult;\n            if (evalResult !== 0) {\n                noError = false;\n            }\n        }\n        if (noError) {\n            return;\n        }\n        var syndrome = new GenericGFPoly(field, syndromeCoefficients);\n        var sigmaOmega = this.runEuclideanAlgorithm(field.buildMonomial(twoS, 1), syndrome, twoS);\n        var sigma = sigmaOmega[0];\n        var omega = sigmaOmega[1];\n        var errorLocations = this.findErrorLocations(sigma);\n        var errorMagnitudes = this.findErrorMagnitudes(omega, errorLocations);\n        for (var i = 0; i < errorLocations.length; i++) {\n            var position = received.length - 1 - field.log(errorLocations[i]);\n            if (position < 0) {\n                throw new ReedSolomonException('Bad error location');\n            }\n            received[position] = GenericGF.addOrSubtract(received[position], errorMagnitudes[i]);\n        }\n    };\n    ReedSolomonDecoder.prototype.runEuclideanAlgorithm = function (a, b, R /*int*/) {\n        // Assume a's degree is >= b's\n        if (a.getDegree() < b.getDegree()) {\n            var temp = a;\n            a = b;\n            b = temp;\n        }\n        var field = this.field;\n        var rLast = a;\n        var r = b;\n        var tLast = field.getZero();\n        var t = field.getOne();\n        // Run Euclidean algorithm until r's degree is less than R/2\n        while (r.getDegree() >= (R / 2 | 0)) {\n            var rLastLast = rLast;\n            var tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            // Divide rLastLast by rLast, with quotient in q and remainder in r\n            if (rLast.isZero()) {\n                // Oops, Euclidean algorithm already terminated?\n                throw new ReedSolomonException('r_{i-1} was zero');\n            }\n            r = rLastLast;\n            var q = field.getZero();\n            var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n            var dltInverse = field.inverse(denominatorLeadingTerm);\n            while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n                var degreeDiff = r.getDegree() - rLast.getDegree();\n                var scale = field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n                q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n                r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n            }\n            t = q.multiply(tLast).addOrSubtract(tLastLast);\n            if (r.getDegree() >= rLast.getDegree()) {\n                throw new IllegalStateException('Division algorithm failed to reduce polynomial?');\n            }\n        }\n        var sigmaTildeAtZero = t.getCoefficient(0);\n        if (sigmaTildeAtZero === 0) {\n            throw new ReedSolomonException('sigmaTilde(0) was zero');\n        }\n        var inverse = field.inverse(sigmaTildeAtZero);\n        var sigma = t.multiplyScalar(inverse);\n        var omega = r.multiplyScalar(inverse);\n        return [sigma, omega];\n    };\n    ReedSolomonDecoder.prototype.findErrorLocations = function (errorLocator) {\n        // This is a direct application of Chien's search\n        var numErrors = errorLocator.getDegree();\n        if (numErrors === 1) { // shortcut\n            return Int32Array.from([errorLocator.getCoefficient(1)]);\n        }\n        var result = new Int32Array(numErrors);\n        var e = 0;\n        var field = this.field;\n        for (var i = 1; i < field.getSize() && e < numErrors; i++) {\n            if (errorLocator.evaluateAt(i) === 0) {\n                result[e] = field.inverse(i);\n                e++;\n            }\n        }\n        if (e !== numErrors) {\n            throw new ReedSolomonException('Error locator degree does not match number of roots');\n        }\n        return result;\n    };\n    ReedSolomonDecoder.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocations) {\n        // This is directly applying Forney's Formula\n        var s = errorLocations.length;\n        var result = new Int32Array(s);\n        var field = this.field;\n        for (var i = 0; i < s; i++) {\n            var xiInverse = field.inverse(errorLocations[i]);\n            var denominator = 1;\n            for (var j = 0; j < s; j++) {\n                if (i !== j) {\n                    // denominator = field.multiply(denominator,\n                    //    GenericGF.addOrSubtract(1, field.multiply(errorLocations[j], xiInverse)))\n                    // Above should work but fails on some Apple and Linux JDKs due to a Hotspot bug.\n                    // Below is a funny-looking workaround from Steven Parkes\n                    var term = field.multiply(errorLocations[j], xiInverse);\n                    var termPlus1 = (term & 0x1) === 0 ? term | 1 : term & ~1;\n                    denominator = field.multiply(denominator, termPlus1);\n                }\n            }\n            result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n            if (field.getGeneratorBase() !== 0) {\n                result[i] = field.multiply(result[i], xiInverse);\n            }\n        }\n        return result;\n    };\n    return ReedSolomonDecoder;\n}());\nexport default ReedSolomonDecoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,iDAAiD;;;AACjD;AACA;AACA;AACA;;;;;AACA;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,IAAI,qBAAoC;IACpC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;;;;;;KAQC,GACD,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,KAAK,KAAK,GAAN;QAC1D,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO;QACpC,IAAI,uBAAuB,IAAI,WAAW;QAC1C,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,IAAI,aAAa,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,gBAAgB;YACrE,oBAAoB,CAAC,qBAAqB,MAAM,GAAG,IAAI,EAAE,GAAG;YAC5D,IAAI,eAAe,GAAG;gBAClB,UAAU;YACd;QACJ;QACA,IAAI,SAAS;YACT;QACJ;QACA,IAAI,WAAW,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO;QACxC,IAAI,aAAa,IAAI,CAAC,qBAAqB,CAAC,MAAM,aAAa,CAAC,MAAM,IAAI,UAAU;QACpF,IAAI,QAAQ,UAAU,CAAC,EAAE;QACzB,IAAI,QAAQ,UAAU,CAAC,EAAE;QACzB,IAAI,iBAAiB,IAAI,CAAC,kBAAkB,CAAC;QAC7C,IAAI,kBAAkB,IAAI,CAAC,mBAAmB,CAAC,OAAO;QACtD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,WAAW,SAAS,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE;YAChE,IAAI,WAAW,GAAG;gBACd,MAAM,IAAI,yKAAA,CAAA,UAAoB,CAAC;YACnC;YACA,QAAQ,CAAC,SAAS,GAAG,uLAAA,CAAA,UAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;QACvF;IACJ;IACA,mBAAmB,SAAS,CAAC,qBAAqB,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,GAAN;QAClE,8BAA8B;QAC9B,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI;YAC/B,IAAI,OAAO;YACX,IAAI;YACJ,IAAI;QACR;QACA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,QAAQ;QACZ,IAAI,IAAI;QACR,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,IAAI,MAAM,MAAM;QACpB,4DAA4D;QAC5D,MAAO,EAAE,SAAS,MAAM,CAAC,IAAI,IAAI,CAAC,EAAG;YACjC,IAAI,YAAY;YAChB,IAAI,YAAY;YAChB,QAAQ;YACR,QAAQ;YACR,mEAAmE;YACnE,IAAI,MAAM,MAAM,IAAI;gBAChB,gDAAgD;gBAChD,MAAM,IAAI,yKAAA,CAAA,UAAoB,CAAC;YACnC;YACA,IAAI;YACJ,IAAI,IAAI,MAAM,OAAO;YACrB,IAAI,yBAAyB,MAAM,cAAc,CAAC,MAAM,SAAS;YACjE,IAAI,aAAa,MAAM,OAAO,CAAC;YAC/B,MAAO,EAAE,SAAS,MAAM,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,GAAI;gBACtD,IAAI,aAAa,EAAE,SAAS,KAAK,MAAM,SAAS;gBAChD,IAAI,QAAQ,MAAM,QAAQ,CAAC,EAAE,cAAc,CAAC,EAAE,SAAS,KAAK;gBAC5D,IAAI,EAAE,aAAa,CAAC,MAAM,aAAa,CAAC,YAAY;gBACpD,IAAI,EAAE,aAAa,CAAC,MAAM,kBAAkB,CAAC,YAAY;YAC7D;YACA,IAAI,EAAE,QAAQ,CAAC,OAAO,aAAa,CAAC;YACpC,IAAI,EAAE,SAAS,MAAM,MAAM,SAAS,IAAI;gBACpC,MAAM,IAAI,0KAAA,CAAA,UAAqB,CAAC;YACpC;QACJ;QACA,IAAI,mBAAmB,EAAE,cAAc,CAAC;QACxC,IAAI,qBAAqB,GAAG;YACxB,MAAM,IAAI,yKAAA,CAAA,UAAoB,CAAC;QACnC;QACA,IAAI,UAAU,MAAM,OAAO,CAAC;QAC5B,IAAI,QAAQ,EAAE,cAAc,CAAC;QAC7B,IAAI,QAAQ,EAAE,cAAc,CAAC;QAC7B,OAAO;YAAC;YAAO;SAAM;IACzB;IACA,mBAAmB,SAAS,CAAC,kBAAkB,GAAG,SAAU,YAAY;QACpE,iDAAiD;QACjD,IAAI,YAAY,aAAa,SAAS;QACtC,IAAI,cAAc,GAAG;YACjB,OAAO,WAAW,IAAI,CAAC;gBAAC,aAAa,cAAc,CAAC;aAAG;QAC3D;QACA,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAI,IAAI;QACR,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,OAAO,MAAM,IAAI,WAAW,IAAK;YACvD,IAAI,aAAa,UAAU,CAAC,OAAO,GAAG;gBAClC,MAAM,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC;gBAC1B;YACJ;QACJ;QACA,IAAI,MAAM,WAAW;YACjB,MAAM,IAAI,yKAAA,CAAA,UAAoB,CAAC;QACnC;QACA,OAAO;IACX;IACA,mBAAmB,SAAS,CAAC,mBAAmB,GAAG,SAAU,cAAc,EAAE,cAAc;QACvF,6CAA6C;QAC7C,IAAI,IAAI,eAAe,MAAM;QAC7B,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,YAAY,MAAM,OAAO,CAAC,cAAc,CAAC,EAAE;YAC/C,IAAI,cAAc;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,MAAM,GAAG;oBACT,4CAA4C;oBAC5C,+EAA+E;oBAC/E,iFAAiF;oBACjF,yDAAyD;oBACzD,IAAI,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE;oBAC7C,IAAI,YAAY,CAAC,OAAO,GAAG,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC;oBACxD,cAAc,MAAM,QAAQ,CAAC,aAAa;gBAC9C;YACJ;YACA,MAAM,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,eAAe,UAAU,CAAC,YAAY,MAAM,OAAO,CAAC;YAC/E,IAAI,MAAM,gBAAgB,OAAO,GAAG;gBAChC,MAAM,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE;YAC1C;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/detector/MathUtils.js"], "sourcesContent": ["/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\n/**\n * General math-related and numeric utility functions.\n */\nvar MathUtils = /** @class */ (function () {\n    function MathUtils() {\n    }\n    /**\n     * Ends up being a bit faster than {@link Math#round(float)}. This merely rounds its\n     * argument to the nearest int, where x.5 rounds up to x+1. Semantics of this shortcut\n     * differ slightly from {@link Math#round(float)} in that half rounds down for negative\n     * values. -2.5 rounds to -3, not -2. For purposes here it makes no difference.\n     *\n     * @param d real value to round\n     * @return nearest {@code int}\n     */\n    MathUtils.round = function (d /*float*/) {\n        if (isNaN(d))\n            return 0;\n        if (d <= Number.MIN_SAFE_INTEGER)\n            return Number.MIN_SAFE_INTEGER;\n        if (d >= Number.MAX_SAFE_INTEGER)\n            return Number.MAX_SAFE_INTEGER;\n        return /*(int) */ (d + (d < 0.0 ? -0.5 : 0.5)) | 0;\n    };\n    // TYPESCRIPTPORT: maybe remove round method and call directly Math.round, it looks like it doesn't make sense for js\n    /**\n     * @param aX point A x coordinate\n     * @param aY point A y coordinate\n     * @param bX point B x coordinate\n     * @param bY point B y coordinate\n     * @return Euclidean distance between points A and B\n     */\n    MathUtils.distance = function (aX /*float|int*/, aY /*float|int*/, bX /*float|int*/, bY /*float|int*/) {\n        var xDiff = aX - bX;\n        var yDiff = aY - bY;\n        return /*(float) */ Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n    };\n    /**\n     * @param aX point A x coordinate\n     * @param aY point A y coordinate\n     * @param bX point B x coordinate\n     * @param bY point B y coordinate\n     * @return Euclidean distance between points A and B\n     */\n    // public static distance(aX: number /*int*/, aY: number /*int*/, bX: number /*int*/, bY: number /*int*/): float {\n    //   const xDiff = aX - bX\n    //   const yDiff = aY - bY\n    //   return (float) Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n    // }\n    /**\n     * @param array values to sum\n     * @return sum of values in array\n     */\n    MathUtils.sum = function (array) {\n        var count = 0;\n        for (var i = 0, length_1 = array.length; i !== length_1; i++) {\n            var a = array[i];\n            count += a;\n        }\n        return count;\n    };\n    return MathUtils;\n}());\nexport default MathUtils;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8CAA8C,GAC9C;;CAEC;;;AACD,IAAI,YAA2B;IAC3B,SAAS,aACT;IACA;;;;;;;;KAQC,GACD,UAAU,KAAK,GAAG,SAAU,EAAE,OAAO,GAAR;QACzB,IAAI,MAAM,IACN,OAAO;QACX,IAAI,KAAK,OAAO,gBAAgB,EAC5B,OAAO,OAAO,gBAAgB;QAClC,IAAI,KAAK,OAAO,gBAAgB,EAC5B,OAAO,OAAO,gBAAgB;QAClC,OAAO,QAAQ,GAAG,AAAC,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,IAAK;IACrD;IACA,qHAAqH;IACrH;;;;;;KAMC,GACD,UAAU,QAAQ,GAAG,SAAU,GAAG,WAAW,GAAZ,EAAgB,GAAG,WAAW,GAAZ,EAAgB,GAAG,WAAW,GAAZ,EAAgB,GAAG,WAAW,GAAZ;QACnF,IAAI,QAAQ,KAAK;QACjB,IAAI,QAAQ,KAAK;QACjB,OAAO,UAAU,GAAG,KAAK,IAAI,CAAC,QAAQ,QAAQ,QAAQ;IAC1D;IACA;;;;;;KAMC,GACD,kHAAkH;IAClH,0BAA0B;IAC1B,0BAA0B;IAC1B,6DAA6D;IAC7D,IAAI;IACJ;;;KAGC,GACD,UAAU,GAAG,GAAG,SAAU,KAAK;QAC3B,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,WAAW,MAAM,MAAM,EAAE,MAAM,UAAU,IAAK;YAC1D,IAAI,IAAI,KAAK,CAAC,EAAE;YAChB,SAAS;QACb;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/DetectorResult.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates the result of detecting a barcode in an image. This includes the raw\n * matrix of black/white pixels corresponding to the barcode, and possibly points of interest\n * in the image, like the location of finder patterns or corners of the barcode in the image.</p>\n *\n * <AUTHOR>\n */\nvar DetectorResult = /** @class */ (function () {\n    function DetectorResult(bits, points) {\n        this.bits = bits;\n        this.points = points;\n    }\n    DetectorResult.prototype.getBits = function () {\n        return this.bits;\n    };\n    DetectorResult.prototype.getPoints = function () {\n        return this.points;\n    };\n    return DetectorResult;\n}());\nexport default DetectorResult;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD;;;;;;CAMC;;;AACD,IAAI,iBAAgC;IAChC,SAAS,eAAe,IAAI,EAAE,MAAM;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,eAAe,SAAS,CAAC,OAAO,GAAG;QAC/B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,eAAe,SAAS,CAAC,SAAS,GAAG;QACjC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/detector/WhiteRectangleDetector.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\nimport ResultPoint from '../../ResultPoint';\nimport MathUtils from './MathUtils';\nimport NotFoundException from '../../NotFoundException';\n/**\n * <p>\n * Detects a candidate barcode-like rectangular region within an image. It\n * starts around the center of the image, increases the size of the candidate\n * region until it finds a white rectangular region. By keeping track of the\n * last black points it encountered, it determines the corners of the barcode.\n * </p>\n *\n * <AUTHOR>\n */\nvar WhiteRectangleDetector = /** @class */ (function () {\n    // public constructor(private image: BitMatrix) /*throws NotFoundException*/ {\n    //   this(image, INIT_SIZE, image.getWidth() / 2, image.getHeight() / 2)\n    // }\n    /**\n     * @param image barcode image to find a rectangle in\n     * @param initSize initial size of search area around center\n     * @param x x position of search center\n     * @param y y position of search center\n     * @throws NotFoundException if image is too small to accommodate {@code initSize}\n     */\n    function WhiteRectangleDetector(image, initSize /*int*/, x /*int*/, y /*int*/) {\n        this.image = image;\n        this.height = image.getHeight();\n        this.width = image.getWidth();\n        if (undefined === initSize || null === initSize) {\n            initSize = WhiteRectangleDetector.INIT_SIZE;\n        }\n        if (undefined === x || null === x) {\n            x = image.getWidth() / 2 | 0;\n        }\n        if (undefined === y || null === y) {\n            y = image.getHeight() / 2 | 0;\n        }\n        var halfsize = initSize / 2 | 0;\n        this.leftInit = x - halfsize;\n        this.rightInit = x + halfsize;\n        this.upInit = y - halfsize;\n        this.downInit = y + halfsize;\n        if (this.upInit < 0 || this.leftInit < 0 || this.downInit >= this.height || this.rightInit >= this.width) {\n            throw new NotFoundException();\n        }\n    }\n    /**\n     * <p>\n     * Detects a candidate barcode-like rectangular region within an image. It\n     * starts around the center of the image, increases the size of the candidate\n     * region until it finds a white rectangular region.\n     * </p>\n     *\n     * @return {@link ResultPoint}[] describing the corners of the rectangular\n     *         region. The first and last points are opposed on the diagonal, as\n     *         are the second and third. The first point will be the topmost\n     *         point and the last, the bottommost. The second point will be\n     *         leftmost and the third, the rightmost\n     * @throws NotFoundException if no Data Matrix Code can be found\n     */\n    WhiteRectangleDetector.prototype.detect = function () {\n        var left = this.leftInit;\n        var right = this.rightInit;\n        var up = this.upInit;\n        var down = this.downInit;\n        var sizeExceeded = false;\n        var aBlackPointFoundOnBorder = true;\n        var atLeastOneBlackPointFoundOnBorder = false;\n        var atLeastOneBlackPointFoundOnRight = false;\n        var atLeastOneBlackPointFoundOnBottom = false;\n        var atLeastOneBlackPointFoundOnLeft = false;\n        var atLeastOneBlackPointFoundOnTop = false;\n        var width = this.width;\n        var height = this.height;\n        while (aBlackPointFoundOnBorder) {\n            aBlackPointFoundOnBorder = false;\n            // .....\n            // .   |\n            // .....\n            var rightBorderNotWhite = true;\n            while ((rightBorderNotWhite || !atLeastOneBlackPointFoundOnRight) && right < width) {\n                rightBorderNotWhite = this.containsBlackPoint(up, down, right, false);\n                if (rightBorderNotWhite) {\n                    right++;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnRight = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnRight) {\n                    right++;\n                }\n            }\n            if (right >= width) {\n                sizeExceeded = true;\n                break;\n            }\n            // .....\n            // .   .\n            // .___.\n            var bottomBorderNotWhite = true;\n            while ((bottomBorderNotWhite || !atLeastOneBlackPointFoundOnBottom) && down < height) {\n                bottomBorderNotWhite = this.containsBlackPoint(left, right, down, true);\n                if (bottomBorderNotWhite) {\n                    down++;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnBottom = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnBottom) {\n                    down++;\n                }\n            }\n            if (down >= height) {\n                sizeExceeded = true;\n                break;\n            }\n            // .....\n            // |   .\n            // .....\n            var leftBorderNotWhite = true;\n            while ((leftBorderNotWhite || !atLeastOneBlackPointFoundOnLeft) && left >= 0) {\n                leftBorderNotWhite = this.containsBlackPoint(up, down, left, false);\n                if (leftBorderNotWhite) {\n                    left--;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnLeft = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnLeft) {\n                    left--;\n                }\n            }\n            if (left < 0) {\n                sizeExceeded = true;\n                break;\n            }\n            // .___.\n            // .   .\n            // .....\n            var topBorderNotWhite = true;\n            while ((topBorderNotWhite || !atLeastOneBlackPointFoundOnTop) && up >= 0) {\n                topBorderNotWhite = this.containsBlackPoint(left, right, up, true);\n                if (topBorderNotWhite) {\n                    up--;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnTop = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnTop) {\n                    up--;\n                }\n            }\n            if (up < 0) {\n                sizeExceeded = true;\n                break;\n            }\n            if (aBlackPointFoundOnBorder) {\n                atLeastOneBlackPointFoundOnBorder = true;\n            }\n        }\n        if (!sizeExceeded && atLeastOneBlackPointFoundOnBorder) {\n            var maxSize = right - left;\n            var z = null;\n            for (var i = 1; z === null && i < maxSize; i++) {\n                z = this.getBlackPointOnSegment(left, down - i, left + i, down);\n            }\n            if (z == null) {\n                throw new NotFoundException();\n            }\n            var t = null;\n            // go down right\n            for (var i = 1; t === null && i < maxSize; i++) {\n                t = this.getBlackPointOnSegment(left, up + i, left + i, up);\n            }\n            if (t == null) {\n                throw new NotFoundException();\n            }\n            var x = null;\n            // go down left\n            for (var i = 1; x === null && i < maxSize; i++) {\n                x = this.getBlackPointOnSegment(right, up + i, right - i, up);\n            }\n            if (x == null) {\n                throw new NotFoundException();\n            }\n            var y = null;\n            // go up left\n            for (var i = 1; y === null && i < maxSize; i++) {\n                y = this.getBlackPointOnSegment(right, down - i, right - i, down);\n            }\n            if (y == null) {\n                throw new NotFoundException();\n            }\n            return this.centerEdges(y, z, x, t);\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    WhiteRectangleDetector.prototype.getBlackPointOnSegment = function (aX /*float*/, aY /*float*/, bX /*float*/, bY /*float*/) {\n        var dist = MathUtils.round(MathUtils.distance(aX, aY, bX, bY));\n        var xStep = (bX - aX) / dist;\n        var yStep = (bY - aY) / dist;\n        var image = this.image;\n        for (var i = 0; i < dist; i++) {\n            var x = MathUtils.round(aX + i * xStep);\n            var y = MathUtils.round(aY + i * yStep);\n            if (image.get(x, y)) {\n                return new ResultPoint(x, y);\n            }\n        }\n        return null;\n    };\n    /**\n     * recenters the points of a constant distance towards the center\n     *\n     * @param y bottom most point\n     * @param z left most point\n     * @param x right most point\n     * @param t top most point\n     * @return {@link ResultPoint}[] describing the corners of the rectangular\n     *         region. The first and last points are opposed on the diagonal, as\n     *         are the second and third. The first point will be the topmost\n     *         point and the last, the bottommost. The second point will be\n     *         leftmost and the third, the rightmost\n     */\n    WhiteRectangleDetector.prototype.centerEdges = function (y, z, x, t) {\n        //\n        //       t            t\n        //  z                      x\n        //        x    OR    z\n        //   y                    y\n        //\n        var yi = y.getX();\n        var yj = y.getY();\n        var zi = z.getX();\n        var zj = z.getY();\n        var xi = x.getX();\n        var xj = x.getY();\n        var ti = t.getX();\n        var tj = t.getY();\n        var CORR = WhiteRectangleDetector.CORR;\n        if (yi < this.width / 2.0) {\n            return [\n                new ResultPoint(ti - CORR, tj + CORR),\n                new ResultPoint(zi + CORR, zj + CORR),\n                new ResultPoint(xi - CORR, xj - CORR),\n                new ResultPoint(yi + CORR, yj - CORR)\n            ];\n        }\n        else {\n            return [\n                new ResultPoint(ti + CORR, tj + CORR),\n                new ResultPoint(zi + CORR, zj - CORR),\n                new ResultPoint(xi - CORR, xj + CORR),\n                new ResultPoint(yi - CORR, yj - CORR)\n            ];\n        }\n    };\n    /**\n     * Determines whether a segment contains a black point\n     *\n     * @param a          min value of the scanned coordinate\n     * @param b          max value of the scanned coordinate\n     * @param fixed      value of fixed coordinate\n     * @param horizontal set to true if scan must be horizontal, false if vertical\n     * @return true if a black point has been found, else false.\n     */\n    WhiteRectangleDetector.prototype.containsBlackPoint = function (a /*int*/, b /*int*/, fixed /*int*/, horizontal) {\n        var image = this.image;\n        if (horizontal) {\n            for (var x = a; x <= b; x++) {\n                if (image.get(x, fixed)) {\n                    return true;\n                }\n            }\n        }\n        else {\n            for (var y = a; y <= b; y++) {\n                if (image.get(fixed, y)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n    WhiteRectangleDetector.INIT_SIZE = 10;\n    WhiteRectangleDetector.CORR = 1;\n    return WhiteRectangleDetector;\n}());\nexport default WhiteRectangleDetector;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,8CAA8C;;;AAC9C;AACA;AACA;;;;AACA;;;;;;;;;CASC,GACD,IAAI,yBAAwC;IACxC,8EAA8E;IAC9E,wEAAwE;IACxE,IAAI;IACJ;;;;;;KAMC,GACD,SAAS,uBAAuB,KAAK,EAAE,SAAS,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN;QACjE,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS;QAC7B,IAAI,CAAC,KAAK,GAAG,MAAM,QAAQ;QAC3B,IAAI,cAAc,YAAY,SAAS,UAAU;YAC7C,WAAW,uBAAuB,SAAS;QAC/C;QACA,IAAI,cAAc,KAAK,SAAS,GAAG;YAC/B,IAAI,MAAM,QAAQ,KAAK,IAAI;QAC/B;QACA,IAAI,cAAc,KAAK,SAAS,GAAG;YAC/B,IAAI,MAAM,SAAS,KAAK,IAAI;QAChC;QACA,IAAI,WAAW,WAAW,IAAI;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;YACtG,MAAM,IAAI,sKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA;;;;;;;;;;;;;KAaC,GACD,uBAAuB,SAAS,CAAC,MAAM,GAAG;QACtC,IAAI,OAAO,IAAI,CAAC,QAAQ;QACxB,IAAI,QAAQ,IAAI,CAAC,SAAS;QAC1B,IAAI,KAAK,IAAI,CAAC,MAAM;QACpB,IAAI,OAAO,IAAI,CAAC,QAAQ;QACxB,IAAI,eAAe;QACnB,IAAI,2BAA2B;QAC/B,IAAI,oCAAoC;QACxC,IAAI,mCAAmC;QACvC,IAAI,oCAAoC;QACxC,IAAI,kCAAkC;QACtC,IAAI,iCAAiC;QACrC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,MAAO,yBAA0B;YAC7B,2BAA2B;YAC3B,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,IAAI,sBAAsB;YAC1B,MAAO,CAAC,uBAAuB,CAAC,gCAAgC,KAAK,QAAQ,MAAO;gBAChF,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,IAAI,MAAM,OAAO;gBAC/D,IAAI,qBAAqB;oBACrB;oBACA,2BAA2B;oBAC3B,mCAAmC;gBACvC,OACK,IAAI,CAAC,kCAAkC;oBACxC;gBACJ;YACJ;YACA,IAAI,SAAS,OAAO;gBAChB,eAAe;gBACf;YACJ;YACA,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,IAAI,uBAAuB;YAC3B,MAAO,CAAC,wBAAwB,CAAC,iCAAiC,KAAK,OAAO,OAAQ;gBAClF,uBAAuB,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,MAAM;gBAClE,IAAI,sBAAsB;oBACtB;oBACA,2BAA2B;oBAC3B,oCAAoC;gBACxC,OACK,IAAI,CAAC,mCAAmC;oBACzC;gBACJ;YACJ;YACA,IAAI,QAAQ,QAAQ;gBAChB,eAAe;gBACf;YACJ;YACA,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,IAAI,qBAAqB;YACzB,MAAO,CAAC,sBAAsB,CAAC,+BAA+B,KAAK,QAAQ,EAAG;gBAC1E,qBAAqB,IAAI,CAAC,kBAAkB,CAAC,IAAI,MAAM,MAAM;gBAC7D,IAAI,oBAAoB;oBACpB;oBACA,2BAA2B;oBAC3B,kCAAkC;gBACtC,OACK,IAAI,CAAC,iCAAiC;oBACvC;gBACJ;YACJ;YACA,IAAI,OAAO,GAAG;gBACV,eAAe;gBACf;YACJ;YACA,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,IAAI,oBAAoB;YACxB,MAAO,CAAC,qBAAqB,CAAC,8BAA8B,KAAK,MAAM,EAAG;gBACtE,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,IAAI;gBAC7D,IAAI,mBAAmB;oBACnB;oBACA,2BAA2B;oBAC3B,iCAAiC;gBACrC,OACK,IAAI,CAAC,gCAAgC;oBACtC;gBACJ;YACJ;YACA,IAAI,KAAK,GAAG;gBACR,eAAe;gBACf;YACJ;YACA,IAAI,0BAA0B;gBAC1B,oCAAoC;YACxC;QACJ;QACA,IAAI,CAAC,gBAAgB,mCAAmC;YACpD,IAAI,UAAU,QAAQ;YACtB,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAI,SAAS,IAAK;gBAC5C,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO,GAAG,OAAO,GAAG;YAC9D;YACA,IAAI,KAAK,MAAM;gBACX,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,IAAI;YACR,gBAAgB;YAChB,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAI,SAAS,IAAK;gBAC5C,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,KAAK,GAAG,OAAO,GAAG;YAC5D;YACA,IAAI,KAAK,MAAM;gBACX,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,IAAI;YACR,eAAe;YACf,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAI,SAAS,IAAK;gBAC5C,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,KAAK,GAAG,QAAQ,GAAG;YAC9D;YACA,IAAI,KAAK,MAAM;gBACX,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,IAAI,IAAI;YACR,aAAa;YACb,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAI,SAAS,IAAK;gBAC5C,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,OAAO,GAAG,QAAQ,GAAG;YAChE;YACA,IAAI,KAAK,MAAM;gBACX,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG;QACrC,OACK;YACD,MAAM,IAAI,sKAAA,CAAA,UAAiB;QAC/B;IACJ;IACA,uBAAuB,SAAS,CAAC,sBAAsB,GAAG,SAAU,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR;QAC5G,IAAI,OAAO,oLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,oLAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI;QAC1D,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI;QACxB,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI;QACxB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,IAAI,IAAI,oLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,KAAK,IAAI;YACjC,IAAI,IAAI,oLAAA,CAAA,UAAS,CAAC,KAAK,CAAC,KAAK,IAAI;YACjC,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI;gBACjB,OAAO,IAAI,gKAAA,CAAA,UAAW,CAAC,GAAG;YAC9B;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;KAYC,GACD,uBAAuB,SAAS,CAAC,WAAW,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/D,EAAE;QACF,uBAAuB;QACvB,4BAA4B;QAC5B,sBAAsB;QACtB,2BAA2B;QAC3B,EAAE;QACF,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,KAAK,EAAE,IAAI;QACf,IAAI,OAAO,uBAAuB,IAAI;QACtC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK;YACvB,OAAO;gBACH,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;aACnC;QACL,OACK;YACD,OAAO;gBACH,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;gBAChC,IAAI,gKAAA,CAAA,UAAW,CAAC,KAAK,MAAM,KAAK;aACnC;QACL;IACJ;IACA;;;;;;;;KAQC,GACD,uBAAuB,SAAS,CAAC,kBAAkB,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,EAAE,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,UAAU;QAC3G,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,YAAY;YACZ,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;gBACzB,IAAI,MAAM,GAAG,CAAC,GAAG,QAAQ;oBACrB,OAAO;gBACX;YACJ;QACJ,OACK;YACD,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;gBACzB,IAAI,MAAM,GAAG,CAAC,OAAO,IAAI;oBACrB,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,uBAAuB,SAAS,GAAG;IACnC,uBAAuB,IAAI,GAAG;IAC9B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/GridSampler.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport NotFoundException from '../NotFoundException';\n/**\n * Implementations of this class can, given locations of finder patterns for a QR code in an\n * image, sample the right points in the image to reconstruct the QR code, accounting for\n * perspective distortion. It is abstracted since it is relatively expensive and should be allowed\n * to take advantage of platform-specific optimized implementations, like Sun's Java Advanced\n * Imaging library, but which may not be available in other environments such as J2ME, and vice\n * versa.\n *\n * The implementation used can be controlled by calling {@link #setGridSampler(GridSampler)}\n * with an instance of a class which implements this interface.\n *\n * <AUTHOR>\n */\nvar GridSampler = /** @class */ (function () {\n    function GridSampler() {\n    }\n    /**\n     * <p>Checks a set of points that have been transformed to sample points on an image against\n     * the image's dimensions to see if the point are even within the image.</p>\n     *\n     * <p>This method will actually \"nudge\" the endpoints back onto the image if they are found to be\n     * barely (less than 1 pixel) off the image. This accounts for imperfect detection of finder\n     * patterns in an image where the QR Code runs all the way to the image border.</p>\n     *\n     * <p>For efficiency, the method will check points from either end of the line until one is found\n     * to be within the image. Because the set of points are assumed to be linear, this is valid.</p>\n     *\n     * @param image image into which the points should map\n     * @param points actual points in x1,y1,...,xn,yn form\n     * @throws NotFoundException if an endpoint is lies outside the image boundaries\n     */\n    GridSampler.checkAndNudgePoints = function (image, points) {\n        var width = image.getWidth();\n        var height = image.getHeight();\n        // Check and nudge points from start until we see some that are OK:\n        var nudged = true;\n        for (var offset = 0; offset < points.length && nudged; offset += 2) {\n            var x = Math.floor(points[offset]);\n            var y = Math.floor(points[offset + 1]);\n            if (x < -1 || x > width || y < -1 || y > height) {\n                throw new NotFoundException();\n            }\n            nudged = false;\n            if (x === -1) {\n                points[offset] = 0.0;\n                nudged = true;\n            }\n            else if (x === width) {\n                points[offset] = width - 1;\n                nudged = true;\n            }\n            if (y === -1) {\n                points[offset + 1] = 0.0;\n                nudged = true;\n            }\n            else if (y === height) {\n                points[offset + 1] = height - 1;\n                nudged = true;\n            }\n        }\n        // Check and nudge points from end:\n        nudged = true;\n        for (var offset = points.length - 2; offset >= 0 && nudged; offset -= 2) {\n            var x = Math.floor(points[offset]);\n            var y = Math.floor(points[offset + 1]);\n            if (x < -1 || x > width || y < -1 || y > height) {\n                throw new NotFoundException();\n            }\n            nudged = false;\n            if (x === -1) {\n                points[offset] = 0.0;\n                nudged = true;\n            }\n            else if (x === width) {\n                points[offset] = width - 1;\n                nudged = true;\n            }\n            if (y === -1) {\n                points[offset + 1] = 0.0;\n                nudged = true;\n            }\n            else if (y === height) {\n                points[offset + 1] = height - 1;\n                nudged = true;\n            }\n        }\n    };\n    return GridSampler;\n}());\nexport default GridSampler;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;;AACA;;;;;;;;;;;;CAYC,GACD,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA;;;;;;;;;;;;;;KAcC,GACD,YAAY,mBAAmB,GAAG,SAAU,KAAK,EAAE,MAAM;QACrD,IAAI,QAAQ,MAAM,QAAQ;QAC1B,IAAI,SAAS,MAAM,SAAS;QAC5B,mEAAmE;QACnE,IAAI,SAAS;QACb,IAAK,IAAI,SAAS,GAAG,SAAS,OAAO,MAAM,IAAI,QAAQ,UAAU,EAAG;YAChE,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO;YACjC,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE;YACrC,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,IAAI,QAAQ;gBAC7C,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,SAAS;YACT,IAAI,MAAM,CAAC,GAAG;gBACV,MAAM,CAAC,OAAO,GAAG;gBACjB,SAAS;YACb,OACK,IAAI,MAAM,OAAO;gBAClB,MAAM,CAAC,OAAO,GAAG,QAAQ;gBACzB,SAAS;YACb;YACA,IAAI,MAAM,CAAC,GAAG;gBACV,MAAM,CAAC,SAAS,EAAE,GAAG;gBACrB,SAAS;YACb,OACK,IAAI,MAAM,QAAQ;gBACnB,MAAM,CAAC,SAAS,EAAE,GAAG,SAAS;gBAC9B,SAAS;YACb;QACJ;QACA,mCAAmC;QACnC,SAAS;QACT,IAAK,IAAI,SAAS,OAAO,MAAM,GAAG,GAAG,UAAU,KAAK,QAAQ,UAAU,EAAG;YACrE,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO;YACjC,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE;YACrC,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,IAAI,QAAQ;gBAC7C,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;YACA,SAAS;YACT,IAAI,MAAM,CAAC,GAAG;gBACV,MAAM,CAAC,OAAO,GAAG;gBACjB,SAAS;YACb,OACK,IAAI,MAAM,OAAO;gBAClB,MAAM,CAAC,OAAO,GAAG,QAAQ;gBACzB,SAAS;YACb;YACA,IAAI,MAAM,CAAC,GAAG;gBACV,MAAM,CAAC,SAAS,EAAE,GAAG;gBACrB,SAAS;YACb,OACK,IAAI,MAAM,QAAQ;gBACnB,MAAM,CAAC,SAAS,EAAE,GAAG,SAAS;gBAC9B,SAAS;YACb;QACJ;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/PerspectiveTransform.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/**\n * <p>This class implements a perspective transform in two dimensions. Given four source and four\n * destination points, it will compute the transformation implied between them. The code is based\n * directly upon section 3.4.2 of <PERSON>'s \"Digital Image Warping\"; see pages 54-56.</p>\n *\n * <AUTHOR>\n */\nvar PerspectiveTransform = /** @class */ (function () {\n    function PerspectiveTransform(a11 /*float*/, a21 /*float*/, a31 /*float*/, a12 /*float*/, a22 /*float*/, a32 /*float*/, a13 /*float*/, a23 /*float*/, a33 /*float*/) {\n        this.a11 = a11;\n        this.a21 = a21;\n        this.a31 = a31;\n        this.a12 = a12;\n        this.a22 = a22;\n        this.a32 = a32;\n        this.a13 = a13;\n        this.a23 = a23;\n        this.a33 = a33;\n    }\n    PerspectiveTransform.quadrilateralToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/, x0p /*float*/, y0p /*float*/, x1p /*float*/, y1p /*float*/, x2p /*float*/, y2p /*float*/, x3p /*float*/, y3p /*float*/) {\n        var qToS = PerspectiveTransform.quadrilateralToSquare(x0, y0, x1, y1, x2, y2, x3, y3);\n        var sToQ = PerspectiveTransform.squareToQuadrilateral(x0p, y0p, x1p, y1p, x2p, y2p, x3p, y3p);\n        return sToQ.times(qToS);\n    };\n    PerspectiveTransform.prototype.transformPoints = function (points) {\n        var max = points.length;\n        var a11 = this.a11;\n        var a12 = this.a12;\n        var a13 = this.a13;\n        var a21 = this.a21;\n        var a22 = this.a22;\n        var a23 = this.a23;\n        var a31 = this.a31;\n        var a32 = this.a32;\n        var a33 = this.a33;\n        for (var i = 0; i < max; i += 2) {\n            var x = points[i];\n            var y = points[i + 1];\n            var denominator = a13 * x + a23 * y + a33;\n            points[i] = (a11 * x + a21 * y + a31) / denominator;\n            points[i + 1] = (a12 * x + a22 * y + a32) / denominator;\n        }\n    };\n    PerspectiveTransform.prototype.transformPointsWithValues = function (xValues, yValues) {\n        var a11 = this.a11;\n        var a12 = this.a12;\n        var a13 = this.a13;\n        var a21 = this.a21;\n        var a22 = this.a22;\n        var a23 = this.a23;\n        var a31 = this.a31;\n        var a32 = this.a32;\n        var a33 = this.a33;\n        var n = xValues.length;\n        for (var i = 0; i < n; i++) {\n            var x = xValues[i];\n            var y = yValues[i];\n            var denominator = a13 * x + a23 * y + a33;\n            xValues[i] = (a11 * x + a21 * y + a31) / denominator;\n            yValues[i] = (a12 * x + a22 * y + a32) / denominator;\n        }\n    };\n    PerspectiveTransform.squareToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n        var dx3 = x0 - x1 + x2 - x3;\n        var dy3 = y0 - y1 + y2 - y3;\n        if (dx3 === 0.0 && dy3 === 0.0) {\n            // Affine\n            return new PerspectiveTransform(x1 - x0, x2 - x1, x0, y1 - y0, y2 - y1, y0, 0.0, 0.0, 1.0);\n        }\n        else {\n            var dx1 = x1 - x2;\n            var dx2 = x3 - x2;\n            var dy1 = y1 - y2;\n            var dy2 = y3 - y2;\n            var denominator = dx1 * dy2 - dx2 * dy1;\n            var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n            var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n            return new PerspectiveTransform(x1 - x0 + a13 * x1, x3 - x0 + a23 * x3, x0, y1 - y0 + a13 * y1, y3 - y0 + a23 * y3, y0, a13, a23, 1.0);\n        }\n    };\n    PerspectiveTransform.quadrilateralToSquare = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n        // Here, the adjoint serves as the inverse:\n        return PerspectiveTransform.squareToQuadrilateral(x0, y0, x1, y1, x2, y2, x3, y3).buildAdjoint();\n    };\n    PerspectiveTransform.prototype.buildAdjoint = function () {\n        // Adjoint is the transpose of the cofactor matrix:\n        return new PerspectiveTransform(this.a22 * this.a33 - this.a23 * this.a32, this.a23 * this.a31 - this.a21 * this.a33, this.a21 * this.a32 - this.a22 * this.a31, this.a13 * this.a32 - this.a12 * this.a33, this.a11 * this.a33 - this.a13 * this.a31, this.a12 * this.a31 - this.a11 * this.a32, this.a12 * this.a23 - this.a13 * this.a22, this.a13 * this.a21 - this.a11 * this.a23, this.a11 * this.a22 - this.a12 * this.a21);\n    };\n    PerspectiveTransform.prototype.times = function (other) {\n        return new PerspectiveTransform(this.a11 * other.a11 + this.a21 * other.a12 + this.a31 * other.a13, this.a11 * other.a21 + this.a21 * other.a22 + this.a31 * other.a23, this.a11 * other.a31 + this.a21 * other.a32 + this.a31 * other.a33, this.a12 * other.a11 + this.a22 * other.a12 + this.a32 * other.a13, this.a12 * other.a21 + this.a22 * other.a22 + this.a32 * other.a23, this.a12 * other.a31 + this.a22 * other.a32 + this.a32 * other.a33, this.a13 * other.a11 + this.a23 * other.a12 + this.a33 * other.a13, this.a13 * other.a21 + this.a23 * other.a22 + this.a33 * other.a23, this.a13 * other.a31 + this.a23 * other.a32 + this.a33 * other.a33);\n    };\n    return PerspectiveTransform;\n}());\nexport default PerspectiveTransform;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC,GACrC;;;;;;CAMC;;;AACD,IAAI,uBAAsC;IACtC,SAAS,qBAAqB,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR;QACrJ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;IACf;IACA,qBAAqB,4BAA4B,GAAG,SAAU,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR,EAAY,IAAI,OAAO,GAAR;QACtR,IAAI,OAAO,qBAAqB,qBAAqB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAClF,IAAI,OAAO,qBAAqB,qBAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;QACzF,OAAO,KAAK,KAAK,CAAC;IACtB;IACA,qBAAqB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM;QAC7D,IAAI,MAAM,OAAO,MAAM;QACvB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;YAC7B,IAAI,IAAI,MAAM,CAAC,EAAE;YACjB,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE;YACrB,IAAI,cAAc,MAAM,IAAI,MAAM,IAAI;YACtC,MAAM,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;YACxC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;QAChD;IACJ;IACA,qBAAqB,SAAS,CAAC,yBAAyB,GAAG,SAAU,OAAO,EAAE,OAAO;QACjF,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,IAAI,QAAQ,MAAM;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,IAAI,OAAO,CAAC,EAAE;YAClB,IAAI,IAAI,OAAO,CAAC,EAAE;YAClB,IAAI,cAAc,MAAM,IAAI,MAAM,IAAI;YACtC,OAAO,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;YACzC,OAAO,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;QAC7C;IACJ;IACA,qBAAqB,qBAAqB,GAAG,SAAU,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR;QACvJ,IAAI,MAAM,KAAK,KAAK,KAAK;QACzB,IAAI,MAAM,KAAK,KAAK,KAAK;QACzB,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAC5B,SAAS;YACT,OAAO,IAAI,qBAAqB,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK;QAC1F,OACK;YACD,IAAI,MAAM,KAAK;YACf,IAAI,MAAM,KAAK;YACf,IAAI,MAAM,KAAK;YACf,IAAI,MAAM,KAAK;YACf,IAAI,cAAc,MAAM,MAAM,MAAM;YACpC,IAAI,MAAM,CAAC,MAAM,MAAM,MAAM,GAAG,IAAI;YACpC,IAAI,MAAM,CAAC,MAAM,MAAM,MAAM,GAAG,IAAI;YACpC,OAAO,IAAI,qBAAqB,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK;QACtI;IACJ;IACA,qBAAqB,qBAAqB,GAAG,SAAU,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR,EAAY,GAAG,OAAO,GAAR;QACvJ,2CAA2C;QAC3C,OAAO,qBAAqB,qBAAqB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,YAAY;IAClG;IACA,qBAAqB,SAAS,CAAC,YAAY,GAAG;QAC1C,mDAAmD;QACnD,OAAO,IAAI,qBAAqB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;IACra;IACA,qBAAqB,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK;QAClD,OAAO,IAAI,qBAAqB,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG;IACtoB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/DefaultGridSampler.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common {*/\nimport GridSampler from './GridSampler';\nimport BitMatrix from './BitMatrix';\nimport PerspectiveTransform from './PerspectiveTransform';\nimport NotFoundException from '../NotFoundException';\n/**\n * <AUTHOR> Owen\n */\nvar DefaultGridSampler = /** @class */ (function (_super) {\n    __extends(DefaultGridSampler, _super);\n    function DefaultGridSampler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /*@Override*/\n    DefaultGridSampler.prototype.sampleGrid = function (image, dimensionX /*int*/, dimensionY /*int*/, p1ToX /*float*/, p1ToY /*float*/, p2ToX /*float*/, p2ToY /*float*/, p3ToX /*float*/, p3ToY /*float*/, p4ToX /*float*/, p4ToY /*float*/, p1FromX /*float*/, p1FromY /*float*/, p2FromX /*float*/, p2FromY /*float*/, p3FromX /*float*/, p3FromY /*float*/, p4FromX /*float*/, p4FromY /*float*/) {\n        var transform = PerspectiveTransform.quadrilateralToQuadrilateral(p1ToX, p1ToY, p2ToX, p2ToY, p3ToX, p3ToY, p4ToX, p4ToY, p1FromX, p1FromY, p2FromX, p2FromY, p3FromX, p3FromY, p4FromX, p4FromY);\n        return this.sampleGridWithTransform(image, dimensionX, dimensionY, transform);\n    };\n    /*@Override*/\n    DefaultGridSampler.prototype.sampleGridWithTransform = function (image, dimensionX /*int*/, dimensionY /*int*/, transform) {\n        if (dimensionX <= 0 || dimensionY <= 0) {\n            throw new NotFoundException();\n        }\n        var bits = new BitMatrix(dimensionX, dimensionY);\n        var points = new Float32Array(2 * dimensionX);\n        for (var y = 0; y < dimensionY; y++) {\n            var max = points.length;\n            var iValue = y + 0.5;\n            for (var x = 0; x < max; x += 2) {\n                points[x] = (x / 2) + 0.5;\n                points[x + 1] = iValue;\n            }\n            transform.transformPoints(points);\n            // Quick check to see if points transformed to something inside the image\n            // sufficient to check the endpoints\n            GridSampler.checkAndNudgePoints(image, points);\n            try {\n                for (var x = 0; x < max; x += 2) {\n                    if (image.get(Math.floor(points[x]), Math.floor(points[x + 1]))) {\n                        // Black(-ish) pixel\n                        bits.set(x / 2, y);\n                    }\n                }\n            }\n            catch (aioobe /*: ArrayIndexOutOfBoundsException*/) {\n                // This feels wrong, but, sometimes if the finder patterns are misidentified, the resulting\n                // transform gets \"twisted\" such that it maps a straight line of points to a set of points\n                // whose endpoints are in bounds, but others are not. There is probably some mathematical\n                // way to detect this about the transformation that I don't know yet.\n                // This results in an ugly runtime exception despite our clever checks above -- can't have\n                // that. We could check each point's coordinates but that feels duplicative. We settle for\n                // catching and wrapping ArrayIndexOutOfBoundsException.\n                throw new NotFoundException();\n            }\n        }\n        return bits;\n    };\n    return DefaultGridSampler;\n}(GridSampler));\nexport default DefaultGridSampler;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,qCAAqC,GACrC;AACA;AACA;AACA;AAjBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;AAMA;;CAEC,GACD,IAAI,qBAAoC,SAAU,MAAM;IACpD,UAAU,oBAAoB;IAC9B,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,WAAW,KAAK,GAAN,EAAU,WAAW,KAAK,GAAN,EAAU,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,MAAM,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR,EAAY,QAAQ,OAAO,GAAR;QACnX,IAAI,YAAY,mLAAA,CAAA,UAAoB,CAAC,4BAA4B,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS;QACzL,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,YAAY,YAAY;IACvE;IACA,WAAW,GACX,mBAAmB,SAAS,CAAC,uBAAuB,GAAG,SAAU,KAAK,EAAE,WAAW,KAAK,GAAN,EAAU,WAAW,KAAK,GAAN,EAAU,SAAS;QACrH,IAAI,cAAc,KAAK,cAAc,GAAG;YACpC,MAAM,IAAI,sKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,OAAO,IAAI,wKAAA,CAAA,UAAS,CAAC,YAAY;QACrC,IAAI,SAAS,IAAI,aAAa,IAAI;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,IAAI,MAAM,OAAO,MAAM;YACvB,IAAI,SAAS,IAAI;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;gBAC7B,MAAM,CAAC,EAAE,GAAG,AAAC,IAAI,IAAK;gBACtB,MAAM,CAAC,IAAI,EAAE,GAAG;YACpB;YACA,UAAU,eAAe,CAAC;YAC1B,yEAAyE;YACzE,oCAAoC;YACpC,0KAAA,CAAA,UAAW,CAAC,mBAAmB,CAAC,OAAO;YACvC,IAAI;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;oBAC7B,IAAI,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI;wBAC7D,oBAAoB;wBACpB,KAAK,GAAG,CAAC,IAAI,GAAG;oBACpB;gBACJ;YACJ,EACA,OAAO,OAAO,kCAAkC,KAAI;gBAChD,2FAA2F;gBAC3F,0FAA0F;gBAC1F,yFAAyF;gBACzF,qEAAqE;gBACrE,0FAA0F;gBAC1F,0FAA0F;gBAC1F,wDAAwD;gBACxD,MAAM,IAAI,sKAAA,CAAA,UAAiB;YAC/B;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX,EAAE,0KAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/GridSamplerInstance.js"], "sourcesContent": ["import DefaultGridSampler from './DefaultGridSampler';\nvar GridSamplerInstance = /** @class */ (function () {\n    function GridSamplerInstance() {\n    }\n    /**\n     * Sets the implementation of GridSampler used by the library. One global\n     * instance is stored, which may sound problematic. But, the implementation provided\n     * ought to be appropriate for the entire platform, and all uses of this library\n     * in the whole lifetime of the JVM. For instance, an Android activity can swap in\n     * an implementation that takes advantage of native platform libraries.\n     *\n     * @param newGridSampler The platform-specific object to install.\n     */\n    GridSamplerInstance.setGridSampler = function (newGridSampler) {\n        GridSamplerInstance.gridSampler = newGridSampler;\n    };\n    /**\n     * @return the current implementation of GridSampler\n     */\n    GridSamplerInstance.getInstance = function () {\n        return GridSamplerInstance.gridSampler;\n    };\n    GridSamplerInstance.gridSampler = new DefaultGridSampler();\n    return GridSamplerInstance;\n}());\nexport default GridSamplerInstance;\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,sBAAqC;IACrC,SAAS,uBACT;IACA;;;;;;;;KAQC,GACD,oBAAoB,cAAc,GAAG,SAAU,cAAc;QACzD,oBAAoB,WAAW,GAAG;IACtC;IACA;;KAEC,GACD,oBAAoB,WAAW,GAAG;QAC9B,OAAO,oBAAoB,WAAW;IAC1C;IACA,oBAAoB,WAAW,GAAG,IAAI,iLAAA,CAAA,UAAkB;IACxD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/BitSource.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>This provides an easy abstraction to read bits at a time from a sequence of bytes, where the\n * number of bits read is not often a multiple of 8.</p>\n *\n * <p>This class is thread-safe but not reentrant -- unless the caller modifies the bytes array\n * it passed in, in which case all bets are off.</p>\n *\n * <AUTHOR>\n */\nvar BitSource = /** @class */ (function () {\n    /**\n     * @param bytes bytes from which this will read bits. Bits will be read from the first byte first.\n     * Bits are read within a byte from most-significant to least-significant bit.\n     */\n    function BitSource(bytes) {\n        this.bytes = bytes;\n        this.byteOffset = 0;\n        this.bitOffset = 0;\n    }\n    /**\n     * @return index of next bit in current byte which would be read by the next call to {@link #readBits(int)}.\n     */\n    BitSource.prototype.getBitOffset = function () {\n        return this.bitOffset;\n    };\n    /**\n     * @return index of next byte in input byte array which would be read by the next call to {@link #readBits(int)}.\n     */\n    BitSource.prototype.getByteOffset = function () {\n        return this.byteOffset;\n    };\n    /**\n     * @param numBits number of bits to read\n     * @return int representing the bits read. The bits will appear as the least-significant\n     *         bits of the int\n     * @throws IllegalArgumentException if numBits isn't in [1,32] or more than is available\n     */\n    BitSource.prototype.readBits = function (numBits /*int*/) {\n        if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n            throw new IllegalArgumentException('' + numBits);\n        }\n        var result = 0;\n        var bitOffset = this.bitOffset;\n        var byteOffset = this.byteOffset;\n        var bytes = this.bytes;\n        // First, read remainder from current byte\n        if (bitOffset > 0) {\n            var bitsLeft = 8 - bitOffset;\n            var toRead = numBits < bitsLeft ? numBits : bitsLeft;\n            var bitsToNotRead = bitsLeft - toRead;\n            var mask = (0xFF >> (8 - toRead)) << bitsToNotRead;\n            result = (bytes[byteOffset] & mask) >> bitsToNotRead;\n            numBits -= toRead;\n            bitOffset += toRead;\n            if (bitOffset === 8) {\n                bitOffset = 0;\n                byteOffset++;\n            }\n        }\n        // Next read whole bytes\n        if (numBits > 0) {\n            while (numBits >= 8) {\n                result = (result << 8) | (bytes[byteOffset] & 0xFF);\n                byteOffset++;\n                numBits -= 8;\n            }\n            // Finally read a partial byte\n            if (numBits > 0) {\n                var bitsToNotRead = 8 - numBits;\n                var mask = (0xFF >> bitsToNotRead) << bitsToNotRead;\n                result = (result << numBits) | ((bytes[byteOffset] & mask) >> bitsToNotRead);\n                bitOffset += numBits;\n            }\n        }\n        this.bitOffset = bitOffset;\n        this.byteOffset = byteOffset;\n        return result;\n    };\n    /**\n     * @return number of bits that can be read successfully\n     */\n    BitSource.prototype.available = function () {\n        return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n    };\n    return BitSource;\n}());\nexport default BitSource;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,qCAAqC;;;AACrC;;AACA;;;;;;;;CAQC,GACD,IAAI,YAA2B;IAC3B;;;KAGC,GACD,SAAS,UAAU,KAAK;QACpB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,YAAY,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,aAAa,GAAG;QAChC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;;KAKC,GACD,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,QAAQ,KAAK,GAAN;QAC5C,IAAI,UAAU,KAAK,UAAU,MAAM,UAAU,IAAI,CAAC,SAAS,IAAI;YAC3D,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC,KAAK;QAC5C;QACA,IAAI,SAAS;QACb,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACf,IAAI,WAAW,IAAI;YACnB,IAAI,SAAS,UAAU,WAAW,UAAU;YAC5C,IAAI,gBAAgB,WAAW;YAC/B,IAAI,OAAO,AAAC,QAAS,IAAI,UAAY;YACrC,SAAS,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,KAAK;YACvC,WAAW;YACX,aAAa;YACb,IAAI,cAAc,GAAG;gBACjB,YAAY;gBACZ;YACJ;QACJ;QACA,wBAAwB;QACxB,IAAI,UAAU,GAAG;YACb,MAAO,WAAW,EAAG;gBACjB,SAAS,AAAC,UAAU,IAAM,KAAK,CAAC,WAAW,GAAG;gBAC9C;gBACA,WAAW;YACf;YACA,8BAA8B;YAC9B,IAAI,UAAU,GAAG;gBACb,IAAI,gBAAgB,IAAI;gBACxB,IAAI,OAAO,AAAC,QAAQ,iBAAkB;gBACtC,SAAS,AAAC,UAAU,UAAY,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,KAAK;gBAC9D,aAAa;YACjB;QACJ;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,OAAO;IACX;IACA;;KAEC,GACD,UAAU,SAAS,CAAC,SAAS,GAAG;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS;IACrE;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport GenericGFPoly from './GenericGFPoly';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Implements Reed-Solomon encoding, as the name implies.</p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar ReedSolomonEncoder = /** @class */ (function () {\n    /**\n     * A reed solomon error-correcting encoding constructor is created by\n     * passing as Galois Field with of size equal to the number of code\n     * words (symbols) in the alphabet (the number of values in each\n     * element of arrays that are encoded/decoded).\n     * @param field A galois field with a number of elements equal to the size\n     * of the alphabet of symbols to encode.\n     */\n    function ReedSolomonEncoder(field) {\n        this.field = field;\n        this.cachedGenerators = [];\n        this.cachedGenerators.push(new GenericGFPoly(field, Int32Array.from([1])));\n    }\n    ReedSolomonEncoder.prototype.buildGenerator = function (degree /*int*/) {\n        var cachedGenerators = this.cachedGenerators;\n        if (degree >= cachedGenerators.length) {\n            var lastGenerator = cachedGenerators[cachedGenerators.length - 1];\n            var field = this.field;\n            for (var d = cachedGenerators.length; d <= degree; d++) {\n                var nextGenerator = lastGenerator.multiply(new GenericGFPoly(field, Int32Array.from([1, field.exp(d - 1 + field.getGeneratorBase())])));\n                cachedGenerators.push(nextGenerator);\n                lastGenerator = nextGenerator;\n            }\n        }\n        return cachedGenerators[degree];\n    };\n    /**\n     * <p>Encode a sequence of code words (symbols) using Reed-Solomon to allow decoders\n     * to detect and correct errors that may have been introduced when the resulting\n     * data is stored or transmitted.</p>\n     *\n     * @param toEncode array used for both and output. Caller initializes the array with\n     * the code words (symbols) to be encoded followed by empty elements allocated to make\n     * space for error-correction code words in the encoded output. The array contains\n     * the encdoded output when encode returns. Code words are encoded as numbers from\n     * 0 to n-1, where n is the number of possible code words (symbols), as determined\n     * by the size of the Galois Field passed in the constructor of this object.\n     * @param ecBytes the number of elements reserved in the array (first parameter)\n     * to store error-correction code words. Thus, the number of code words (symbols)\n     * to encode in the first parameter is thus toEncode.length - ecBytes.\n     * Note, the use of \"bytes\" in the name of this parameter is misleading, as there may\n     * be more or fewer than 256 symbols being encoded, as determined by the number of\n     * elements in the Galois Field passed as a constructor to this object.\n     * @throws IllegalArgumentException thrown in response to validation errros.\n     */\n    ReedSolomonEncoder.prototype.encode = function (toEncode, ecBytes /*int*/) {\n        if (ecBytes === 0) {\n            throw new IllegalArgumentException('No error correction bytes');\n        }\n        var dataBytes = toEncode.length - ecBytes;\n        if (dataBytes <= 0) {\n            throw new IllegalArgumentException('No data bytes provided');\n        }\n        var generator = this.buildGenerator(ecBytes);\n        var infoCoefficients = new Int32Array(dataBytes);\n        System.arraycopy(toEncode, 0, infoCoefficients, 0, dataBytes);\n        var info = new GenericGFPoly(this.field, infoCoefficients);\n        info = info.multiplyByMonomial(ecBytes, 1);\n        var remainder = info.divide(generator)[1];\n        var coefficients = remainder.getCoefficients();\n        var numZeroCoefficients = ecBytes - coefficients.length;\n        for (var i = 0; i < numZeroCoefficients; i++) {\n            toEncode[dataBytes + i] = 0;\n        }\n        System.arraycopy(coefficients, 0, toEncode, dataBytes + numZeroCoefficients, coefficients.length);\n    };\n    return ReedSolomonEncoder;\n}());\nexport default ReedSolomonEncoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AACD;AACA;AACA;;;;AACA;;;;;CAKC,GACD,IAAI,qBAAoC;IACpC;;;;;;;KAOC,GACD,SAAS,mBAAmB,KAAK;QAC7B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO,WAAW,IAAI,CAAC;YAAC;SAAE;IAC3E;IACA,mBAAmB,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO,KAAK,GAAN;QAC1D,IAAI,mBAAmB,IAAI,CAAC,gBAAgB;QAC5C,IAAI,UAAU,iBAAiB,MAAM,EAAE;YACnC,IAAI,gBAAgB,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE;YACjE,IAAI,QAAQ,IAAI,CAAC,KAAK;YACtB,IAAK,IAAI,IAAI,iBAAiB,MAAM,EAAE,KAAK,QAAQ,IAAK;gBACpD,IAAI,gBAAgB,cAAc,QAAQ,CAAC,IAAI,2LAAA,CAAA,UAAa,CAAC,OAAO,WAAW,IAAI,CAAC;oBAAC;oBAAG,MAAM,GAAG,CAAC,IAAI,IAAI,MAAM,gBAAgB;iBAAI;gBACpI,iBAAiB,IAAI,CAAC;gBACtB,gBAAgB;YACpB;QACJ;QACA,OAAO,gBAAgB,CAAC,OAAO;IACnC;IACA;;;;;;;;;;;;;;;;;;KAkBC,GACD,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,QAAQ,KAAK,GAAN;QAC7D,IAAI,YAAY,GAAG;YACf,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,YAAY,SAAS,MAAM,GAAG;QAClC,IAAI,aAAa,GAAG;YAChB,MAAM,IAAI,6KAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,YAAY,IAAI,CAAC,cAAc,CAAC;QACpC,IAAI,mBAAmB,IAAI,WAAW;QACtC,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,GAAG,kBAAkB,GAAG;QACnD,IAAI,OAAO,IAAI,2LAAA,CAAA,UAAa,CAAC,IAAI,CAAC,KAAK,EAAE;QACzC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACxC,IAAI,YAAY,KAAK,MAAM,CAAC,UAAU,CAAC,EAAE;QACzC,IAAI,eAAe,UAAU,eAAe;QAC5C,IAAI,sBAAsB,UAAU,aAAa,MAAM;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,qBAAqB,IAAK;YAC1C,QAAQ,CAAC,YAAY,EAAE,GAAG;QAC9B;QACA,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,YAAY,qBAAqB,aAAa,MAAM;IACpG;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/ECIEncoderSet.js"], "sourcesContent": ["/**\n * Set of CharsetEncoders for a given input string\n *\n * Invariants:\n * - The list contains only encoders from CharacterSetECI (list is shorter then the list of encoders available on\n *   the platform for which ECI values are defined).\n * - The list contains encoders at least one encoder for every character in the input.\n * - The first encoder in the list is always the ISO-8859-1 encoder even of no character in the input can be encoded\n *       by it.\n * - If the input contains a character that is not in ISO-8859-1 then the last two entries in the list will be the\n *   UTF-8 encoder and the UTF-16BE encoder.\n *\n * <AUTHOR>\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Charset from '../util/Charset';\nimport StandardCharsets from '../util/StandardCharsets';\nimport StringEncoding from '../util/StringEncoding';\nimport StringUtils from './StringUtils';\nvar CharsetEncoder = /** @class */ (function () {\n    function CharsetEncoder(charset) {\n        this.charset = charset;\n        this.name = charset.name;\n    }\n    CharsetEncoder.prototype.canEncode = function (c) {\n        try {\n            return StringEncoding.encode(c, this.charset) != null;\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    return CharsetEncoder;\n}());\nvar ECIEncoderSet = /** @class */ (function () {\n    /**\n     * Constructs an encoder set\n     *\n     * @param stringToEncode the string that needs to be encoded\n     * @param priorityCharset The preferred {@link Charset} or null.\n     * @param fnc1 fnc1 denotes the character in the input that represents the FNC1 character or -1 for a non-GS1 bar\n     * code. When specified, it is considered an error to pass it as argument to the methods canEncode() or encode().\n     */\n    function ECIEncoderSet(stringToEncode, priorityCharset, fnc1) {\n        var e_1, _a, e_2, _b, e_3, _c;\n        this.ENCODERS = [\n            'IBM437',\n            'ISO-8859-2',\n            'ISO-8859-3',\n            'ISO-8859-4',\n            'ISO-8859-5',\n            'ISO-8859-6',\n            'ISO-8859-7',\n            'ISO-8859-8',\n            'ISO-8859-9',\n            'ISO-8859-10',\n            'ISO-8859-11',\n            'ISO-8859-13',\n            'ISO-8859-14',\n            'ISO-8859-15',\n            'ISO-8859-16',\n            'windows-1250',\n            'windows-1251',\n            'windows-1252',\n            'windows-1256',\n            'Shift_JIS',\n        ].map(function (name) { return new CharsetEncoder(Charset.forName(name)); });\n        this.encoders = [];\n        var neededEncoders = [];\n        // we always need the ISO-8859-1 encoder. It is the default encoding\n        neededEncoders.push(new CharsetEncoder(StandardCharsets.ISO_8859_1));\n        var needUnicodeEncoder = priorityCharset != null && priorityCharset.name.startsWith('UTF');\n        // Walk over the input string and see if all characters can be encoded with the list of encoders\n        for (var i = 0; i < stringToEncode.length; i++) {\n            var canEncode = false;\n            try {\n                for (var neededEncoders_1 = (e_1 = void 0, __values(neededEncoders)), neededEncoders_1_1 = neededEncoders_1.next(); !neededEncoders_1_1.done; neededEncoders_1_1 = neededEncoders_1.next()) {\n                    var encoder = neededEncoders_1_1.value;\n                    var singleCharacter = stringToEncode.charAt(i);\n                    var c = singleCharacter.charCodeAt(0);\n                    if (c === fnc1 || encoder.canEncode(singleCharacter)) {\n                        canEncode = true;\n                        break;\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (neededEncoders_1_1 && !neededEncoders_1_1.done && (_a = neededEncoders_1.return)) _a.call(neededEncoders_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (!canEncode) {\n                try {\n                    // for the character at position i we don't yet have an encoder in the list\n                    for (var _d = (e_2 = void 0, __values(this.ENCODERS)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var encoder = _e.value;\n                        if (encoder.canEncode(stringToEncode.charAt(i))) {\n                            // Good, we found an encoder that can encode the character. We add him to the list and continue scanning\n                            // the input\n                            neededEncoders.push(encoder);\n                            canEncode = true;\n                            break;\n                        }\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n            }\n            if (!canEncode) {\n                // The character is not encodeable by any of the single byte encoders so we remember that we will need a\n                // Unicode encoder.\n                needUnicodeEncoder = true;\n            }\n        }\n        if (neededEncoders.length === 1 && !needUnicodeEncoder) {\n            // the entire input can be encoded by the ISO-8859-1 encoder\n            this.encoders = [neededEncoders[0]];\n        }\n        else {\n            // we need more than one single byte encoder or we need a Unicode encoder.\n            // In this case we append a UTF-8 and UTF-16 encoder to the list\n            this.encoders = [];\n            var index = 0;\n            try {\n                for (var neededEncoders_2 = __values(neededEncoders), neededEncoders_2_1 = neededEncoders_2.next(); !neededEncoders_2_1.done; neededEncoders_2_1 = neededEncoders_2.next()) {\n                    var encoder = neededEncoders_2_1.value;\n                    this.encoders[index++] = encoder;\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (neededEncoders_2_1 && !neededEncoders_2_1.done && (_c = neededEncoders_2.return)) _c.call(neededEncoders_2);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            // this.encoders[index] = new CharsetEncoder(StandardCharsets.UTF_8);\n            // this.encoders[index + 1] = new CharsetEncoder(StandardCharsets.UTF_16BE);\n        }\n        // Compute priorityEncoderIndex by looking up priorityCharset in encoders\n        var priorityEncoderIndexValue = -1;\n        if (priorityCharset != null) {\n            for (var i = 0; i < this.encoders.length; i++) {\n                if (this.encoders[i] != null &&\n                    priorityCharset.name === this.encoders[i].name) {\n                    priorityEncoderIndexValue = i;\n                    break;\n                }\n            }\n        }\n        this.priorityEncoderIndex = priorityEncoderIndexValue;\n        // invariants\n        // if(this?.encoders?.[0].name !== StandardCharsets.ISO_8859_1)){\n        // throw new Error(\"ISO-8859-1 must be the first encoder\");\n        // }\n    }\n    ECIEncoderSet.prototype.length = function () {\n        return this.encoders.length;\n    };\n    ECIEncoderSet.prototype.getCharsetName = function (index) {\n        if (!(index < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return this.encoders[index].name;\n    };\n    ECIEncoderSet.prototype.getCharset = function (index) {\n        if (!(index < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return this.encoders[index].charset;\n    };\n    ECIEncoderSet.prototype.getECIValue = function (encoderIndex) {\n        return this.encoders[encoderIndex].charset.getValueIdentifier();\n    };\n    /*\n     *  returns -1 if no priority charset was defined\n     */\n    ECIEncoderSet.prototype.getPriorityEncoderIndex = function () {\n        return this.priorityEncoderIndex;\n    };\n    ECIEncoderSet.prototype.canEncode = function (c, encoderIndex) {\n        if (!(encoderIndex < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return true;\n    };\n    ECIEncoderSet.prototype.encode = function (c, encoderIndex) {\n        if (!(encoderIndex < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return StringEncoding.encode(StringUtils.getCharAt(c), this.encoders[encoderIndex].name);\n    };\n    return ECIEncoderSet;\n}());\nexport { ECIEncoderSet };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC;;;AAYD;AACA;AACA;AACA;AAdA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;AAKA,IAAI,iBAAgC;IAChC,SAAS,eAAe,OAAO;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;IAC5B;IACA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAU,CAAC;QAC5C,IAAI;YACA,OAAO,2KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,KAAK;QACrD,EACA,OAAO,IAAI;YACP,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,IAAI,gBAA+B;IAC/B;;;;;;;KAOC,GACD,SAAS,cAAc,cAAc,EAAE,eAAe,EAAE,IAAI;QACxD,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QAC3B,IAAI,CAAC,QAAQ,GAAG;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,CAAC,GAAG,CAAC,SAAU,IAAI;YAAI,OAAO,IAAI,eAAe,oKAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAAQ;QAC1E,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,iBAAiB,EAAE;QACvB,oEAAoE;QACpE,eAAe,IAAI,CAAC,IAAI,eAAe,6KAAA,CAAA,UAAgB,CAAC,UAAU;QAClE,IAAI,qBAAqB,mBAAmB,QAAQ,gBAAgB,IAAI,CAAC,UAAU,CAAC;QACpF,gGAAgG;QAChG,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,YAAY;YAChB,IAAI;gBACA,IAAK,IAAI,mBAAmB,CAAC,MAAM,KAAK,GAAG,SAAS,eAAe,GAAG,qBAAqB,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,EAAE,qBAAqB,iBAAiB,IAAI,GAAI;oBACxL,IAAI,UAAU,mBAAmB,KAAK;oBACtC,IAAI,kBAAkB,eAAe,MAAM,CAAC;oBAC5C,IAAI,IAAI,gBAAgB,UAAU,CAAC;oBACnC,IAAI,MAAM,QAAQ,QAAQ,SAAS,CAAC,kBAAkB;wBAClD,YAAY;wBACZ;oBACJ;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,sBAAsB,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,iBAAiB,MAAM,GAAG,GAAG,IAAI,CAAC;gBAClG,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,CAAC,WAAW;gBACZ,IAAI;oBACA,2EAA2E;oBAC3E,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;wBAC7F,IAAI,UAAU,GAAG,KAAK;wBACtB,IAAI,QAAQ,SAAS,CAAC,eAAe,MAAM,CAAC,KAAK;4BAC7C,wGAAwG;4BACxG,YAAY;4BACZ,eAAe,IAAI,CAAC;4BACpB,YAAY;4BACZ;wBACJ;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;oBACpD,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;YACJ;YACA,IAAI,CAAC,WAAW;gBACZ,wGAAwG;gBACxG,mBAAmB;gBACnB,qBAAqB;YACzB;QACJ;QACA,IAAI,eAAe,MAAM,KAAK,KAAK,CAAC,oBAAoB;YACpD,4DAA4D;YAC5D,IAAI,CAAC,QAAQ,GAAG;gBAAC,cAAc,CAAC,EAAE;aAAC;QACvC,OACK;YACD,0EAA0E;YAC1E,gEAAgE;YAChE,IAAI,CAAC,QAAQ,GAAG,EAAE;YAClB,IAAI,QAAQ;YACZ,IAAI;gBACA,IAAK,IAAI,mBAAmB,SAAS,iBAAiB,qBAAqB,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,EAAE,qBAAqB,iBAAiB,IAAI,GAAI;oBACxK,IAAI,UAAU,mBAAmB,KAAK;oBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;gBAC7B;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,sBAAsB,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,iBAAiB,MAAM,GAAG,GAAG,IAAI,CAAC;gBAClG,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACA,qEAAqE;QACrE,4EAA4E;QAChF;QACA,yEAAyE;QACzE,IAAI,4BAA4B,CAAC;QACjC,IAAI,mBAAmB,MAAM;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;gBAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,QACpB,gBAAgB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE;oBAChD,4BAA4B;oBAC5B;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,oBAAoB,GAAG;IAC5B,aAAa;IACb,iEAAiE;IACjE,2DAA2D;IAC3D,IAAI;IACR;IACA,cAAc,SAAS,CAAC,MAAM,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC/B;IACA,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QACpD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG;YAC1B,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;IACpC;IACA,cAAc,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK;QAChD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG;YAC1B,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;IACvC;IACA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,YAAY;QACxD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,kBAAkB;IACjE;IACA;;KAEC,GACD,cAAc,SAAS,CAAC,uBAAuB,GAAG;QAC9C,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,CAAC,EAAE,YAAY;QACzD,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE,GAAG;YACjC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC,EAAE,YAAY;QACtD,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE,GAAG;YACjC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,2KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,0KAAA,CAAA,UAAW,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI;IAC3F;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/common/MinimalECIInput.js"], "sourcesContent": ["import { ECIEncoderSet } from './ECIEncoderSet';\nimport Integer from '../util/Integer';\nimport StringBuilder from '../util/StringBuilder';\nvar COST_PER_ECI = 3; // approximated (latch + 2 codewords)\nvar MinimalECIInput = /** @class */ (function () {\n    /**\n     * Constructs a minimal input\n     *\n     * @param stringToEncode the character string to encode\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not GS1\n     *   input.\n     */\n    function MinimalECIInput(stringToEncode, priorityCharset, fnc1) {\n        this.fnc1 = fnc1;\n        var encoderSet = new ECIEncoderSet(stringToEncode, priorityCharset, fnc1);\n        if (encoderSet.length() === 1) {\n            // optimization for the case when all can be encoded without ECI in ISO-8859-1\n            for (var i = 0; i < this.bytes.length; i++) {\n                var c = stringToEncode.charAt(i).charCodeAt(0);\n                this.bytes[i] = c === fnc1 ? 1000 : c;\n            }\n        }\n        else {\n            this.bytes = this.encodeMinimally(stringToEncode, encoderSet, fnc1);\n        }\n    }\n    MinimalECIInput.prototype.getFNC1Character = function () {\n        return this.fnc1;\n    };\n    /**\n     * Returns the length of this input.  The length is the number\n     * of {@code byte}s, FNC1 characters or ECIs in the sequence.\n     *\n     * @return  the number of {@code char}s in this sequence\n     */\n    MinimalECIInput.prototype.length = function () {\n        return this.bytes.length;\n    };\n    MinimalECIInput.prototype.haveNCharacters = function (index, n) {\n        if (index + n - 1 >= this.bytes.length) {\n            return false;\n        }\n        for (var i = 0; i < n; i++) {\n            if (this.isECI(index + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * Returns the {@code byte} value at the specified index.  An index ranges from zero\n     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n     * index zero, the next at index one, and so on, as for array\n     * indexing.\n     *\n     * @param   index the index of the {@code byte} value to be returned\n     *\n     * @return  the specified {@code byte} value as character or the FNC1 character\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     * @throws  IllegalArgumentException\n     *          if the value at the {@code index} argument is an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.charAt = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        if (this.isECI(index)) {\n            throw new Error('value at ' + index + ' is not a character but an ECI');\n        }\n        return this.isFNC1(index) ? this.fnc1 : this.bytes[index];\n    };\n    /**\n     * Returns a {@code CharSequence} that is a subsequence of this sequence.\n     * The subsequence starts with the {@code char} value at the specified index and\n     * ends with the {@code char} value at index {@code end - 1}.  The length\n     * (in {@code char}s) of the\n     * returned sequence is {@code end - start}, so if {@code start == end}\n     * then an empty sequence is returned.\n     *\n     * @param   start   the start index, inclusive\n     * @param   end     the end index, exclusive\n     *\n     * @return  the specified subsequence\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if {@code start} or {@code end} are negative,\n     *          if {@code end} is greater than {@code length()},\n     *          or if {@code start} is greater than {@code end}\n     * @throws  IllegalArgumentException\n     *          if a value in the range {@code start}-{@code end} is an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.subSequence = function (start, end) {\n        if (start < 0 || start > end || end > this.length()) {\n            throw new Error('' + start);\n        }\n        var result = new StringBuilder();\n        for (var i = start; i < end; i++) {\n            if (this.isECI(i)) {\n                throw new Error('value at ' + i + ' is not a character but an ECI');\n            }\n            result.append(this.charAt(i));\n        }\n        return result.toString();\n    };\n    /**\n     * Determines if a value is an ECI\n     *\n     * @param   index the index of the value\n     *\n     * @return  true if the value at position {@code index} is an ECI\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     */\n    MinimalECIInput.prototype.isECI = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        return this.bytes[index] > 255 && this.bytes[index] <= 999;\n    };\n    /**\n     * Determines if a value is the FNC1 character\n     *\n     * @param   index the index of the value\n     *\n     * @return  true if the value at position {@code index} is the FNC1 character\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     */\n    MinimalECIInput.prototype.isFNC1 = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        return this.bytes[index] === 1000;\n    };\n    /**\n     * Returns the {@code int} ECI value at the specified index.  An index ranges from zero\n     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n     * index zero, the next at index one, and so on, as for array\n     * indexing.\n     *\n     * @param   index the index of the {@code int} value to be returned\n     *\n     * @return  the specified {@code int} ECI value.\n     *          The ECI specified the encoding of all bytes with a higher index until the\n     *          next ECI or until the end of the input if no other ECI follows.\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     * @throws  IllegalArgumentException\n     *          if the value at the {@code index} argument is not an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.getECIValue = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        if (!this.isECI(index)) {\n            throw new Error('value at ' + index + ' is not an ECI but a character');\n        }\n        return this.bytes[index] - 256;\n    };\n    MinimalECIInput.prototype.addEdge = function (edges, to, edge) {\n        if (edges[to][edge.encoderIndex] == null ||\n            edges[to][edge.encoderIndex].cachedTotalSize > edge.cachedTotalSize) {\n            edges[to][edge.encoderIndex] = edge;\n        }\n    };\n    MinimalECIInput.prototype.addEdges = function (stringToEncode, encoderSet, edges, from, previous, fnc1) {\n        var ch = stringToEncode.charAt(from).charCodeAt(0);\n        var start = 0;\n        var end = encoderSet.length();\n        if (encoderSet.getPriorityEncoderIndex() >= 0 &&\n            (ch === fnc1 ||\n                encoderSet.canEncode(ch, encoderSet.getPriorityEncoderIndex()))) {\n            start = encoderSet.getPriorityEncoderIndex();\n            end = start + 1;\n        }\n        for (var i = start; i < end; i++) {\n            if (ch === fnc1 || encoderSet.canEncode(ch, i)) {\n                this.addEdge(edges, from + 1, new InputEdge(ch, encoderSet, i, previous, fnc1));\n            }\n        }\n    };\n    MinimalECIInput.prototype.encodeMinimally = function (stringToEncode, encoderSet, fnc1) {\n        var inputLength = stringToEncode.length;\n        // Array that represents vertices. There is a vertex for every character and encoding.\n        var edges = new InputEdge[inputLength + 1][encoderSet.length()]();\n        this.addEdges(stringToEncode, encoderSet, edges, 0, null, fnc1);\n        for (var i = 1; i <= inputLength; i++) {\n            for (var j = 0; j < encoderSet.length(); j++) {\n                if (edges[i][j] != null && i < inputLength) {\n                    this.addEdges(stringToEncode, encoderSet, edges, i, edges[i][j], fnc1);\n                }\n            }\n            // optimize memory by removing edges that have been passed.\n            for (var j = 0; j < encoderSet.length(); j++) {\n                edges[i - 1][j] = null;\n            }\n        }\n        var minimalJ = -1;\n        var minimalSize = Integer.MAX_VALUE;\n        for (var j = 0; j < encoderSet.length(); j++) {\n            if (edges[inputLength][j] != null) {\n                var edge = edges[inputLength][j];\n                if (edge.cachedTotalSize < minimalSize) {\n                    minimalSize = edge.cachedTotalSize;\n                    minimalJ = j;\n                }\n            }\n        }\n        if (minimalJ < 0) {\n            throw new Error('Failed to encode \"' + stringToEncode + '\"');\n        }\n        var intsAL = [];\n        var current = edges[inputLength][minimalJ];\n        while (current != null) {\n            if (current.isFNC1()) {\n                intsAL.unshift(1000);\n            }\n            else {\n                var bytes = encoderSet.encode(current.c, current.encoderIndex);\n                for (var i = bytes.length - 1; i >= 0; i--) {\n                    intsAL.unshift(bytes[i] & 0xff);\n                }\n            }\n            var previousEncoderIndex = current.previous === null ? 0 : current.previous.encoderIndex;\n            if (previousEncoderIndex !== current.encoderIndex) {\n                intsAL.unshift(256 + encoderSet.getECIValue(current.encoderIndex));\n            }\n            current = current.previous;\n        }\n        var ints = [];\n        for (var i = 0; i < ints.length; i++) {\n            ints[i] = intsAL[i];\n        }\n        return ints;\n    };\n    return MinimalECIInput;\n}());\nexport { MinimalECIInput };\nvar InputEdge = /** @class */ (function () {\n    function InputEdge(c, encoderSet, encoderIndex, previous, fnc1) {\n        this.c = c;\n        this.encoderSet = encoderSet;\n        this.encoderIndex = encoderIndex;\n        this.previous = previous;\n        this.fnc1 = fnc1;\n        this.c = c === fnc1 ? 1000 : c;\n        var size = this.isFNC1() ? 1 : encoderSet.encode(c, encoderIndex).length;\n        var previousEncoderIndex = previous === null ? 0 : previous.encoderIndex;\n        if (previousEncoderIndex !== encoderIndex) {\n            size += COST_PER_ECI;\n        }\n        if (previous != null) {\n            size += previous.cachedTotalSize;\n        }\n        this.cachedTotalSize = size;\n    }\n    InputEdge.prototype.isFNC1 = function () {\n        return this.c === 1000;\n    };\n    return InputEdge;\n}());\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,eAAe,GAAG,qCAAqC;AAC3D,IAAI,kBAAiC;IACjC;;;;;;;;;;KAUC,GACD,SAAS,gBAAgB,cAAc,EAAE,eAAe,EAAE,IAAI;QAC1D,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,aAAa,IAAI,4KAAA,CAAA,gBAAa,CAAC,gBAAgB,iBAAiB;QACpE,IAAI,WAAW,MAAM,OAAO,GAAG;YAC3B,8EAA8E;YAC9E,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;gBACxC,IAAI,IAAI,eAAe,MAAM,CAAC,GAAG,UAAU,CAAC;gBAC5C,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,OAAO;YACxC;QACJ,OACK;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,YAAY;QAClE;IACJ;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QACzC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;;;;KAKC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC5B;IACA,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,CAAC;QAC1D,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACpC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;gBACvB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;;;;KAeC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QAC9C,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI;YACrC,MAAM,IAAI,MAAM,KAAK;QACzB;QACA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ;YACnB,MAAM,IAAI,MAAM,cAAc,QAAQ;QAC1C;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;IAC7D;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK,EAAE,GAAG;QACxD,IAAI,QAAQ,KAAK,QAAQ,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;YACjD,MAAM,IAAI,MAAM,KAAK;QACzB;QACA,IAAI,SAAS,IAAI,0KAAA,CAAA,UAAa;QAC9B,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,IAAK;YAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;gBACf,MAAM,IAAI,MAAM,cAAc,IAAI;YACtC;YACA,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QAC9B;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA;;;;;;;;;;KAUC,GACD,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK;QAC7C,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI;YACrC,MAAM,IAAI,MAAM,KAAK;QACzB;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI;IAC3D;IACA;;;;;;;;;;KAUC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QAC9C,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI;YACrC,MAAM,IAAI,MAAM,KAAK;QACzB;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK;IACjC;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QACnD,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI;YACrC,MAAM,IAAI,MAAM,KAAK;QACzB;QACA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YACpB,MAAM,IAAI,MAAM,cAAc,QAAQ;QAC1C;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,EAAE,EAAE,IAAI;QACzD,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,IAAI,QAChC,KAAK,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,CAAC,eAAe,GAAG,KAAK,eAAe,EAAE;YACrE,KAAK,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,GAAG;QACnC;IACJ;IACA,gBAAgB,SAAS,CAAC,QAAQ,GAAG,SAAU,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;QAClG,IAAI,KAAK,eAAe,MAAM,CAAC,MAAM,UAAU,CAAC;QAChD,IAAI,QAAQ;QACZ,IAAI,MAAM,WAAW,MAAM;QAC3B,IAAI,WAAW,uBAAuB,MAAM,KACxC,CAAC,OAAO,QACJ,WAAW,SAAS,CAAC,IAAI,WAAW,uBAAuB,GAAG,GAAG;YACrE,QAAQ,WAAW,uBAAuB;YAC1C,MAAM,QAAQ;QAClB;QACA,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,IAAK;YAC9B,IAAI,OAAO,QAAQ,WAAW,SAAS,CAAC,IAAI,IAAI;gBAC5C,IAAI,CAAC,OAAO,CAAC,OAAO,OAAO,GAAG,IAAI,UAAU,IAAI,YAAY,GAAG,UAAU;YAC7E;QACJ;IACJ;IACA,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAU,cAAc,EAAE,UAAU,EAAE,IAAI;QAClF,IAAI,cAAc,eAAe,MAAM;QACvC,sFAAsF;QACtF,IAAI,QAAQ,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC,WAAW,MAAM,GAAG;QAC/D,IAAI,CAAC,QAAQ,CAAC,gBAAgB,YAAY,OAAO,GAAG,MAAM;QAC1D,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,IAAK;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,IAAI,IAAK;gBAC1C,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,QAAQ,IAAI,aAAa;oBACxC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,YAAY,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE;gBACrE;YACJ;YACA,2DAA2D;YAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,IAAI,IAAK;gBAC1C,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YACtB;QACJ;QACA,IAAI,WAAW,CAAC;QAChB,IAAI,cAAc,oKAAA,CAAA,UAAO,CAAC,SAAS;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,IAAI,IAAK;YAC1C,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,MAAM;gBAC/B,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,EAAE;gBAChC,IAAI,KAAK,eAAe,GAAG,aAAa;oBACpC,cAAc,KAAK,eAAe;oBAClC,WAAW;gBACf;YACJ;QACJ;QACA,IAAI,WAAW,GAAG;YACd,MAAM,IAAI,MAAM,uBAAuB,iBAAiB;QAC5D;QACA,IAAI,SAAS,EAAE;QACf,IAAI,UAAU,KAAK,CAAC,YAAY,CAAC,SAAS;QAC1C,MAAO,WAAW,KAAM;YACpB,IAAI,QAAQ,MAAM,IAAI;gBAClB,OAAO,OAAO,CAAC;YACnB,OACK;gBACD,IAAI,QAAQ,WAAW,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,YAAY;gBAC7D,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;oBACxC,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG;gBAC9B;YACJ;YACA,IAAI,uBAAuB,QAAQ,QAAQ,KAAK,OAAO,IAAI,QAAQ,QAAQ,CAAC,YAAY;YACxF,IAAI,yBAAyB,QAAQ,YAAY,EAAE;gBAC/C,OAAO,OAAO,CAAC,MAAM,WAAW,WAAW,CAAC,QAAQ,YAAY;YACpE;YACA,UAAU,QAAQ,QAAQ;QAC9B;QACA,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;QACvB;QACA,OAAO;IACX;IACA,OAAO;AACX;;AAEA,IAAI,YAA2B;IAC3B,SAAS,UAAU,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI;QAC1D,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,CAAC,GAAG,MAAM,OAAO,OAAO;QAC7B,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,WAAW,MAAM,CAAC,GAAG,cAAc,MAAM;QACxE,IAAI,uBAAuB,aAAa,OAAO,IAAI,SAAS,YAAY;QACxE,IAAI,yBAAyB,cAAc;YACvC,QAAQ;QACZ;QACA,IAAI,YAAY,MAAM;YAClB,QAAQ,SAAS,eAAe;QACpC;QACA,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,UAAU,SAAS,CAAC,MAAM,GAAG;QACzB,OAAO,IAAI,CAAC,CAAC,KAAK;IACtB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}