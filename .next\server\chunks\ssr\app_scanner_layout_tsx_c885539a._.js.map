{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/scanner/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\n\nexport const metadata: Metadata = {\n  title: \"QR Scanner - QRSAMS\",\n  description: \"QR Code Scanner for Student Attendance\",\n}\n\nexport default function ScannerLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}]}