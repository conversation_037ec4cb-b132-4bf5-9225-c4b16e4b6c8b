(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode.decoder {*/ __turbopack_context__.s({
    "ErrorCorrectionLevelValues": ()=>ErrorCorrectionLevelValues,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
;
var ErrorCorrectionLevelValues;
(function(ErrorCorrectionLevelValues) {
    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues["L"] = 0] = "L";
    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues["M"] = 1] = "M";
    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues["Q"] = 2] = "Q";
    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues["H"] = 3] = "H";
})(ErrorCorrectionLevelValues || (ErrorCorrectionLevelValues = {}));
/**
 * <p>See ISO 18004:2006, 6.5.1. This enum encapsulates the four error correction levels
 * defined by the QR code standard.</p>
 *
 * <AUTHOR> Owen
 */ var ErrorCorrectionLevel = function() {
    function ErrorCorrectionLevel(value, stringValue, bits /*int*/ ) {
        this.value = value;
        this.stringValue = stringValue;
        this.bits = bits;
        ErrorCorrectionLevel.FOR_BITS.set(bits, this);
        ErrorCorrectionLevel.FOR_VALUE.set(value, this);
    }
    ErrorCorrectionLevel.prototype.getValue = function() {
        return this.value;
    };
    ErrorCorrectionLevel.prototype.getBits = function() {
        return this.bits;
    };
    ErrorCorrectionLevel.fromString = function(s) {
        switch(s){
            case 'L':
                return ErrorCorrectionLevel.L;
            case 'M':
                return ErrorCorrectionLevel.M;
            case 'Q':
                return ErrorCorrectionLevel.Q;
            case 'H':
                return ErrorCorrectionLevel.H;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](s + 'not available');
        }
    };
    ErrorCorrectionLevel.prototype.toString = function() {
        return this.stringValue;
    };
    ErrorCorrectionLevel.prototype.equals = function(o) {
        if (!(o instanceof ErrorCorrectionLevel)) {
            return false;
        }
        var other = o;
        return this.value === other.value;
    };
    /**
     * @param bits int containing the two bits encoding a QR Code's error correction level
     * @return ErrorCorrectionLevel representing the encoded error correction level
     */ ErrorCorrectionLevel.forBits = function(bits /*int*/ ) {
        if (bits < 0 || bits >= ErrorCorrectionLevel.FOR_BITS.size) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return ErrorCorrectionLevel.FOR_BITS.get(bits);
    };
    ErrorCorrectionLevel.FOR_BITS = new Map();
    ErrorCorrectionLevel.FOR_VALUE = new Map();
    /** L = ~7% correction */ ErrorCorrectionLevel.L = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.L, 'L', 0x01);
    /** M = ~15% correction */ ErrorCorrectionLevel.M = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.M, 'M', 0x00);
    /** Q = ~25% correction */ ErrorCorrectionLevel.Q = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.Q, 'Q', 0x03);
    /** H = ~30% correction */ ErrorCorrectionLevel.H = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.H, 'H', 0x02);
    return ErrorCorrectionLevel;
}();
const __TURBOPACK__default__export__ = ErrorCorrectionLevel;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/FormatInformation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.decoder {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
/**
 * <p>Encapsulates a QR Code's format information, including the data mask used and
 * error correction level.</p>
 *
 * <AUTHOR> Owen
 * @see DataMask
 * @see ErrorCorrectionLevel
 */ var FormatInformation = function() {
    function FormatInformation(formatInfo /*int*/ ) {
        // Bits 3,4
        this.errorCorrectionLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forBits(formatInfo >> 3 & 0x03);
        // Bottom 3 bits
        this.dataMask = formatInfo & 0x07;
    }
    FormatInformation.numBitsDiffering = function(a /*int*/ , b /*int*/ ) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bitCount(a ^ b);
    };
    /**
     * @param maskedFormatInfo1 format info indicator, with mask still applied
     * @param maskedFormatInfo2 second copy of same info; both are checked at the same time
     *  to establish best match
     * @return information about the format it specifies, or {@code null}
     *  if doesn't seem to match any known pattern
     */ FormatInformation.decodeFormatInformation = function(maskedFormatInfo1 /*int*/ , maskedFormatInfo2 /*int*/ ) {
        var formatInfo = FormatInformation.doDecodeFormatInformation(maskedFormatInfo1, maskedFormatInfo2);
        if (formatInfo !== null) {
            return formatInfo;
        }
        // Should return null, but, some QR codes apparently
        // do not mask this info. Try again by actually masking the pattern
        // first
        return FormatInformation.doDecodeFormatInformation(maskedFormatInfo1 ^ FormatInformation.FORMAT_INFO_MASK_QR, maskedFormatInfo2 ^ FormatInformation.FORMAT_INFO_MASK_QR);
    };
    FormatInformation.doDecodeFormatInformation = function(maskedFormatInfo1 /*int*/ , maskedFormatInfo2 /*int*/ ) {
        var e_1, _a;
        // Find the int in FORMAT_INFO_DECODE_LOOKUP with fewest bits differing
        var bestDifference = Number.MAX_SAFE_INTEGER;
        var bestFormatInfo = 0;
        try {
            for(var _b = __values(FormatInformation.FORMAT_INFO_DECODE_LOOKUP), _c = _b.next(); !_c.done; _c = _b.next()){
                var decodeInfo = _c.value;
                var targetInfo = decodeInfo[0];
                if (targetInfo === maskedFormatInfo1 || targetInfo === maskedFormatInfo2) {
                    // Found an exact match
                    return new FormatInformation(decodeInfo[1]);
                }
                var bitsDifference = FormatInformation.numBitsDiffering(maskedFormatInfo1, targetInfo);
                if (bitsDifference < bestDifference) {
                    bestFormatInfo = decodeInfo[1];
                    bestDifference = bitsDifference;
                }
                if (maskedFormatInfo1 !== maskedFormatInfo2) {
                    // also try the other option
                    bitsDifference = FormatInformation.numBitsDiffering(maskedFormatInfo2, targetInfo);
                    if (bitsDifference < bestDifference) {
                        bestFormatInfo = decodeInfo[1];
                        bestDifference = bitsDifference;
                    }
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits
        // differing means we found a match
        if (bestDifference <= 3) {
            return new FormatInformation(bestFormatInfo);
        }
        return null;
    };
    FormatInformation.prototype.getErrorCorrectionLevel = function() {
        return this.errorCorrectionLevel;
    };
    FormatInformation.prototype.getDataMask = function() {
        return this.dataMask;
    };
    /*@Override*/ FormatInformation.prototype.hashCode = function() {
        return this.errorCorrectionLevel.getBits() << 3 | this.dataMask;
    };
    /*@Override*/ FormatInformation.prototype.equals = function(o) {
        if (!(o instanceof FormatInformation)) {
            return false;
        }
        var other = o;
        return this.errorCorrectionLevel === other.errorCorrectionLevel && this.dataMask === other.dataMask;
    };
    FormatInformation.FORMAT_INFO_MASK_QR = 0x5412;
    /**
     * See ISO 18004:2006, Annex C, Table C.1
     */ FormatInformation.FORMAT_INFO_DECODE_LOOKUP = [
        Int32Array.from([
            0x5412,
            0x00
        ]),
        Int32Array.from([
            0x5125,
            0x01
        ]),
        Int32Array.from([
            0x5E7C,
            0x02
        ]),
        Int32Array.from([
            0x5B4B,
            0x03
        ]),
        Int32Array.from([
            0x45F9,
            0x04
        ]),
        Int32Array.from([
            0x40CE,
            0x05
        ]),
        Int32Array.from([
            0x4F97,
            0x06
        ]),
        Int32Array.from([
            0x4AA0,
            0x07
        ]),
        Int32Array.from([
            0x77C4,
            0x08
        ]),
        Int32Array.from([
            0x72F3,
            0x09
        ]),
        Int32Array.from([
            0x7DAA,
            0x0A
        ]),
        Int32Array.from([
            0x789D,
            0x0B
        ]),
        Int32Array.from([
            0x662F,
            0x0C
        ]),
        Int32Array.from([
            0x6318,
            0x0D
        ]),
        Int32Array.from([
            0x6C41,
            0x0E
        ]),
        Int32Array.from([
            0x6976,
            0x0F
        ]),
        Int32Array.from([
            0x1689,
            0x10
        ]),
        Int32Array.from([
            0x13BE,
            0x11
        ]),
        Int32Array.from([
            0x1CE7,
            0x12
        ]),
        Int32Array.from([
            0x19D0,
            0x13
        ]),
        Int32Array.from([
            0x0762,
            0x14
        ]),
        Int32Array.from([
            0x0255,
            0x15
        ]),
        Int32Array.from([
            0x0D0C,
            0x16
        ]),
        Int32Array.from([
            0x083B,
            0x17
        ]),
        Int32Array.from([
            0x355F,
            0x18
        ]),
        Int32Array.from([
            0x3068,
            0x19
        ]),
        Int32Array.from([
            0x3F31,
            0x1A
        ]),
        Int32Array.from([
            0x3A06,
            0x1B
        ]),
        Int32Array.from([
            0x24B4,
            0x1C
        ]),
        Int32Array.from([
            0x2183,
            0x1D
        ]),
        Int32Array.from([
            0x2EDA,
            0x1E
        ]),
        Int32Array.from([
            0x2BED,
            0x1F
        ])
    ];
    return FormatInformation;
}();
const __TURBOPACK__default__export__ = FormatInformation;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ECBlocks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
/**
 * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will
 * use blocks of differing sizes within one version, so, this encapsulates the parameters for
 * each set of blocks. It also holds the number of error-correction codewords per block since it
 * will be the same across all blocks within one version.</p>
 */ var ECBlocks = function() {
    function ECBlocks(ecCodewordsPerBlock /*int*/ ) {
        var ecBlocks = [];
        for(var _i = 1; _i < arguments.length; _i++){
            ecBlocks[_i - 1] = arguments[_i];
        }
        this.ecCodewordsPerBlock = ecCodewordsPerBlock;
        this.ecBlocks = ecBlocks;
    }
    ECBlocks.prototype.getECCodewordsPerBlock = function() {
        return this.ecCodewordsPerBlock;
    };
    ECBlocks.prototype.getNumBlocks = function() {
        var e_1, _a;
        var total = 0;
        var ecBlocks = this.ecBlocks;
        try {
            for(var ecBlocks_1 = __values(ecBlocks), ecBlocks_1_1 = ecBlocks_1.next(); !ecBlocks_1_1.done; ecBlocks_1_1 = ecBlocks_1.next()){
                var ecBlock = ecBlocks_1_1.value;
                total += ecBlock.getCount();
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (ecBlocks_1_1 && !ecBlocks_1_1.done && (_a = ecBlocks_1.return)) _a.call(ecBlocks_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return total;
    };
    ECBlocks.prototype.getTotalECCodewords = function() {
        return this.ecCodewordsPerBlock * this.getNumBlocks();
    };
    ECBlocks.prototype.getECBlocks = function() {
        return this.ecBlocks;
    };
    return ECBlocks;
}();
const __TURBOPACK__default__export__ = ECBlocks;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ECB.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * <p>Encapsulates the parameters for one error-correction block in one symbol version.
 * This includes the number of data codewords, and the number of times a block with these
 * parameters is used consecutively in the QR code version's format.</p>
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var ECB = function() {
    function ECB(count /*int*/ , dataCodewords /*int*/ ) {
        this.count = count;
        this.dataCodewords = dataCodewords;
    }
    ECB.prototype.getCount = function() {
        return this.count;
    };
    ECB.prototype.getDataCodewords = function() {
        return this.dataCodewords;
    };
    return ECB;
}();
const __TURBOPACK__default__export__ = ECB;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.decoder {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/FormatInformation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ECBlocks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ECB.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/**
 * See ISO 18004:2006 Annex D
 *
 * <AUTHOR> Owen
 */ var Version = function() {
    function Version(versionNumber /*int*/ , alignmentPatternCenters) {
        var e_1, _a;
        var ecBlocks = [];
        for(var _i = 2; _i < arguments.length; _i++){
            ecBlocks[_i - 2] = arguments[_i];
        }
        this.versionNumber = versionNumber;
        this.alignmentPatternCenters = alignmentPatternCenters;
        this.ecBlocks = ecBlocks;
        var total = 0;
        var ecCodewords = ecBlocks[0].getECCodewordsPerBlock();
        var ecbArray = ecBlocks[0].getECBlocks();
        try {
            for(var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()){
                var ecBlock = ecbArray_1_1.value;
                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        this.totalCodewords = total;
    }
    Version.prototype.getVersionNumber = function() {
        return this.versionNumber;
    };
    Version.prototype.getAlignmentPatternCenters = function() {
        return this.alignmentPatternCenters;
    };
    Version.prototype.getTotalCodewords = function() {
        return this.totalCodewords;
    };
    Version.prototype.getDimensionForVersion = function() {
        return 17 + 4 * this.versionNumber;
    };
    Version.prototype.getECBlocksForLevel = function(ecLevel) {
        return this.ecBlocks[ecLevel.getValue()];
    // TYPESCRIPTPORT: original was using ordinal, and using the order of levels as defined in ErrorCorrectionLevel enum (LMQH)
    // I will use the direct value from ErrorCorrectionLevelValues enum which in typescript goes to a number
    };
    /**
     * <p>Deduces version information purely from QR Code dimensions.</p>
     *
     * @param dimension dimension in modules
     * @return Version for a QR Code of that dimension
     * @throws FormatException if dimension is not 1 mod 4
     */ Version.getProvisionalVersionForDimension = function(dimension /*int*/ ) {
        if (dimension % 4 !== 1) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        try {
            return this.getVersionForNumber((dimension - 17) / 4);
        } catch (ignored /*: IllegalArgumentException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    Version.getVersionForNumber = function(versionNumber /*int*/ ) {
        if (versionNumber < 1 || versionNumber > 40) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return Version.VERSIONS[versionNumber - 1];
    };
    Version.decodeVersionInformation = function(versionBits /*int*/ ) {
        var bestDifference = Number.MAX_SAFE_INTEGER;
        var bestVersion = 0;
        for(var i = 0; i < Version.VERSION_DECODE_INFO.length; i++){
            var targetVersion = Version.VERSION_DECODE_INFO[i];
            // Do the version info bits match exactly? done.
            if (targetVersion === versionBits) {
                return Version.getVersionForNumber(i + 7);
            }
            // Otherwise see if this is the closest to a real version info bit string
            // we have seen so far
            var bitsDifference = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].numBitsDiffering(versionBits, targetVersion);
            if (bitsDifference < bestDifference) {
                bestVersion = i + 7;
                bestDifference = bitsDifference;
            }
        }
        // We can tolerate up to 3 bits of error since no two version info codewords will
        // differ in less than 8 bits.
        if (bestDifference <= 3) {
            return Version.getVersionForNumber(bestVersion);
        }
        // If we didn't find a close enough match, fail
        return null;
    };
    /**
     * See ISO 18004:2006 Annex E
     */ Version.prototype.buildFunctionPattern = function() {
        var dimension = this.getDimensionForVersion();
        var bitMatrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](dimension);
        // Top left finder pattern + separator + format
        bitMatrix.setRegion(0, 0, 9, 9);
        // Top right finder pattern + separator + format
        bitMatrix.setRegion(dimension - 8, 0, 8, 9);
        // Bottom left finder pattern + separator + format
        bitMatrix.setRegion(0, dimension - 8, 9, 8);
        // Alignment patterns
        var max = this.alignmentPatternCenters.length;
        for(var x = 0; x < max; x++){
            var i = this.alignmentPatternCenters[x] - 2;
            for(var y = 0; y < max; y++){
                if (x === 0 && (y === 0 || y === max - 1) || x === max - 1 && y === 0) {
                    continue;
                }
                bitMatrix.setRegion(this.alignmentPatternCenters[y] - 2, i, 5, 5);
            }
        }
        // Vertical timing pattern
        bitMatrix.setRegion(6, 9, 1, dimension - 17);
        // Horizontal timing pattern
        bitMatrix.setRegion(9, 6, dimension - 17, 1);
        if (this.versionNumber > 6) {
            // Version info, top right
            bitMatrix.setRegion(dimension - 11, 0, 3, 6);
            // Version info, bottom left
            bitMatrix.setRegion(0, dimension - 11, 6, 3);
        }
        return bitMatrix;
    };
    /*@Override*/ Version.prototype.toString = function() {
        return '' + this.versionNumber;
    };
    /**
       * See ISO 18004:2006 Annex D.
       * Element i represents the raw version bits that specify version i + 7
       */ Version.VERSION_DECODE_INFO = Int32Array.from([
        0x07C94,
        0x085BC,
        0x09A99,
        0x0A4D3,
        0x0BBF6,
        0x0C762,
        0x0D847,
        0x0E60D,
        0x0F928,
        0x10B78,
        0x1145D,
        0x12A17,
        0x13532,
        0x149A6,
        0x15683,
        0x168C9,
        0x177EC,
        0x18EC4,
        0x191E1,
        0x1AFAB,
        0x1B08E,
        0x1CC1A,
        0x1D33F,
        0x1ED75,
        0x1F250,
        0x209D5,
        0x216F0,
        0x228BA,
        0x2379F,
        0x24B0B,
        0x2542E,
        0x26A64,
        0x27541,
        0x28C69
    ]);
    /**
       * See ISO 18004:2006 6.5.1 Table 9
       */ Version.VERSIONS = [
        new Version(1, new Int32Array(0), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 19)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 16)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 13)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 9))),
        new Version(2, Int32Array.from([
            6,
            18
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 34)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 28)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 22)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 16))),
        new Version(3, Int32Array.from([
            6,
            22
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 55)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 44)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 17)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 13))),
        new Version(4, Int32Array.from([
            6,
            26
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 80)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 32)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 24)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 9))),
        new Version(5, Int32Array.from([
            6,
            30
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 108)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 43)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 16)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 11), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 12))),
        new Version(6, Int32Array.from([
            6,
            34
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 68)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 27)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 19)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 15))),
        new Version(7, Int32Array.from([
            6,
            22,
            38
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 78)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 31)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 14), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 15)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 13), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 14))),
        new Version(8, Int32Array.from([
            6,
            24,
            42
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 97)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 38), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 39)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 18), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 19)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 14), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 15))),
        new Version(9, Int32Array.from([
            6,
            26,
            46
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 36), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 37)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 17)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 12), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 13))),
        new Version(10, Int32Array.from([
            6,
            28,
            50
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 68), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 69)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 43), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 44)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 19), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 20)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 16))),
        new Version(11, Int32Array.from([
            6,
            30,
            54
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 81)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 50), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 51)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 22), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 23)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 12), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 13))),
        new Version(12, Int32Array.from([
            6,
            32,
            58
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 92), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 93)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 36), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 37)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 20), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 21)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 14), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 15))),
        new Version(13, Int32Array.from([
            6,
            34,
            62
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 107)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 37), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 38)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 20), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 21)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](12, 11), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 12))),
        new Version(14, Int32Array.from([
            6,
            26,
            46,
            66
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 115), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 40), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 41)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 17)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 12), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 13))),
        new Version(15, Int32Array.from([
            6,
            26,
            48,
            70
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 87), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 88)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 41), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 42)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 12), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 13))),
        new Version(16, Int32Array.from([
            6,
            26,
            50,
            74
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 98), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 99)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 45), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, 19), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 20)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 16))),
        new Version(17, Int32Array.from([
            6,
            30,
            54,
            78
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 107), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 108)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 22), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, 23)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 14), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 15))),
        new Version(18, Int32Array.from([
            6,
            30,
            56,
            82
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 120), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 121)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](9, 43), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 44)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 22), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 23)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 14), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 15))),
        new Version(19, Int32Array.from([
            6,
            30,
            58,
            86
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 113), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 114)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 44), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 45)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 21), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 22)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](9, 13), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, 14))),
        new Version(20, Int32Array.from([
            6,
            34,
            62,
            90
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 107), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 108)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 41), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 42)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 16))),
        new Version(21, Int32Array.from([
            6,
            28,
            50,
            72,
            94
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 116), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 117)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 42)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 22), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 23)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 17))),
        new Version(22, Int32Array.from([
            6,
            26,
            50,
            74,
            98
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 111), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 112)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](34, 13))),
        new Version(23, Int32Array.from([
            6,
            30,
            54,
            78,
            102
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 121), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 122)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 16))),
        new Version(24, Int32Array.from([
            6,
            28,
            54,
            80,
            106
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 117), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 118)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 45), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](16, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 17))),
        new Version(25, Int32Array.from([
            6,
            32,
            58,
            84,
            110
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 106), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 107)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 16))),
        new Version(26, Int32Array.from([
            6,
            30,
            58,
            86,
            114
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 114), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 115)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, 22), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 23)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](33, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 17))),
        new Version(27, Int32Array.from([
            6,
            34,
            62,
            90,
            118
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 122), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 123)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, 45), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](8, 23), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, 24)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](12, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, 16))),
        new Version(28, Int32Array.from([
            6,
            26,
            50,
            74,
            98,
            122
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 117), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 118)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 45), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](23, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](31, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](31, 16))),
        new Version(29, Int32Array.from([
            6,
            30,
            54,
            78,
            102,
            126
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 116), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 117)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](21, 45), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 46)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 23), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](37, 24)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, 16))),
        new Version(30, Int32Array.from([
            6,
            26,
            52,
            78,
            104,
            130
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](5, 115), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](15, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](25, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](23, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](25, 16))),
        new Version(31, Int32Array.from([
            6,
            30,
            56,
            82,
            108,
            134
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 115), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](3, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](29, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](42, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](23, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, 16))),
        new Version(32, Int32Array.from([
            6,
            34,
            60,
            86,
            112,
            138
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 115)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](23, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](35, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](35, 16))),
        new Version(33, Int32Array.from([
            6,
            30,
            58,
            86,
            114,
            142
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 115), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](21, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](29, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](11, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](46, 16))),
        new Version(34, Int32Array.from([
            6,
            34,
            62,
            90,
            118,
            146
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 115), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 116)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](23, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](44, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](59, 16), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](1, 17))),
        new Version(35, Int32Array.from([
            6,
            30,
            54,
            78,
            102,
            126,
            150
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](12, 121), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 122)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](12, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](26, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](39, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](41, 16))),
        new Version(36, Int32Array.from([
            6,
            24,
            50,
            76,
            102,
            128,
            154
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 121), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 122)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](34, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](46, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](2, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](64, 16))),
        new Version(37, Int32Array.from([
            6,
            28,
            54,
            80,
            106,
            132,
            158
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](17, 122), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 123)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](29, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](49, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](24, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](46, 16))),
        new Version(38, Int32Array.from([
            6,
            32,
            58,
            84,
            110,
            136,
            162
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 122), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, 123)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](13, 46), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](32, 47)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](48, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](14, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](42, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](32, 16))),
        new Version(39, Int32Array.from([
            6,
            26,
            54,
            82,
            110,
            138,
            166
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, 117), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](4, 118)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](40, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](7, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](43, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](22, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](10, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](67, 16))),
        new Version(40, Int32Array.from([
            6,
            30,
            58,
            86,
            114,
            142,
            170
        ]), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](19, 118), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](6, 119)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](28, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](18, 47), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](31, 48)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](34, 24), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](34, 25)), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECBlocks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](30, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](20, 15), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ECB$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](61, 16)))
    ];
    return Version;
}();
const __TURBOPACK__default__export__ = Version;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataMask.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "DataMaskValues": ()=>DataMaskValues,
    "default": ()=>__TURBOPACK__default__export__
});
var DataMaskValues;
(function(DataMaskValues) {
    DataMaskValues[DataMaskValues["DATA_MASK_000"] = 0] = "DATA_MASK_000";
    DataMaskValues[DataMaskValues["DATA_MASK_001"] = 1] = "DATA_MASK_001";
    DataMaskValues[DataMaskValues["DATA_MASK_010"] = 2] = "DATA_MASK_010";
    DataMaskValues[DataMaskValues["DATA_MASK_011"] = 3] = "DATA_MASK_011";
    DataMaskValues[DataMaskValues["DATA_MASK_100"] = 4] = "DATA_MASK_100";
    DataMaskValues[DataMaskValues["DATA_MASK_101"] = 5] = "DATA_MASK_101";
    DataMaskValues[DataMaskValues["DATA_MASK_110"] = 6] = "DATA_MASK_110";
    DataMaskValues[DataMaskValues["DATA_MASK_111"] = 7] = "DATA_MASK_111";
})(DataMaskValues || (DataMaskValues = {}));
/**
 * <p>Encapsulates data masks for the data bits in a QR code, per ISO 18004:2006 6.8. Implementations
 * of this class can un-mask a raw BitMatrix. For simplicity, they will unmask the entire BitMatrix,
 * including areas used for finder patterns, timing patterns, etc. These areas should be unused
 * after the point they are unmasked anyway.</p>
 *
 * <p>Note that the diagram in section 6.8.1 is misleading since it indicates that i is column position
 * and j is row position. In fact, as the text says, i is row position and j is column position.</p>
 *
 * <AUTHOR> Owen
 */ var DataMask = function() {
    // See ISO 18004:2006 6.8.1
    function DataMask(value, isMasked) {
        this.value = value;
        this.isMasked = isMasked;
    }
    // End of enum constants.
    /**
     * <p>Implementations of this method reverse the data masking process applied to a QR Code and
     * make its bits ready to read.</p>
     *
     * @param bits representation of QR Code bits
     * @param dimension dimension of QR Code, represented by bits, being unmasked
     */ DataMask.prototype.unmaskBitMatrix = function(bits, dimension /*int*/ ) {
        for(var i = 0; i < dimension; i++){
            for(var j = 0; j < dimension; j++){
                if (this.isMasked(i, j)) {
                    bits.flip(j, i);
                }
            }
        }
    };
    DataMask.values = new Map([
        /**
         * 000: mask bits for which (x + y) mod 2 == 0
         */ [
            DataMaskValues.DATA_MASK_000,
            new DataMask(DataMaskValues.DATA_MASK_000, function(i /*int*/ , j /*int*/ ) {
                return (i + j & 0x01) === 0;
            })
        ],
        /**
         * 001: mask bits for which x mod 2 == 0
         */ [
            DataMaskValues.DATA_MASK_001,
            new DataMask(DataMaskValues.DATA_MASK_001, function(i /*int*/ , j /*int*/ ) {
                return (i & 0x01) === 0;
            })
        ],
        /**
         * 010: mask bits for which y mod 3 == 0
         */ [
            DataMaskValues.DATA_MASK_010,
            new DataMask(DataMaskValues.DATA_MASK_010, function(i /*int*/ , j /*int*/ ) {
                return j % 3 === 0;
            })
        ],
        /**
         * 011: mask bits for which (x + y) mod 3 == 0
         */ [
            DataMaskValues.DATA_MASK_011,
            new DataMask(DataMaskValues.DATA_MASK_011, function(i /*int*/ , j /*int*/ ) {
                return (i + j) % 3 === 0;
            })
        ],
        /**
         * 100: mask bits for which (x/2 + y/3) mod 2 == 0
         */ [
            DataMaskValues.DATA_MASK_100,
            new DataMask(DataMaskValues.DATA_MASK_100, function(i /*int*/ , j /*int*/ ) {
                return (Math.floor(i / 2) + Math.floor(j / 3) & 0x01) === 0;
            })
        ],
        /**
         * 101: mask bits for which xy mod 2 + xy mod 3 == 0
         * equivalently, such that xy mod 6 == 0
         */ [
            DataMaskValues.DATA_MASK_101,
            new DataMask(DataMaskValues.DATA_MASK_101, function(i /*int*/ , j /*int*/ ) {
                return i * j % 6 === 0;
            })
        ],
        /**
         * 110: mask bits for which (xy mod 2 + xy mod 3) mod 2 == 0
         * equivalently, such that xy mod 6 < 3
         */ [
            DataMaskValues.DATA_MASK_110,
            new DataMask(DataMaskValues.DATA_MASK_110, function(i /*int*/ , j /*int*/ ) {
                return i * j % 6 < 3;
            })
        ],
        /**
         * 111: mask bits for which ((x+y)mod 2 + xy mod 3) mod 2 == 0
         * equivalently, such that (x + y + xy mod 3) mod 2 == 0
         */ [
            DataMaskValues.DATA_MASK_111,
            new DataMask(DataMaskValues.DATA_MASK_111, function(i /*int*/ , j /*int*/ ) {
                return (i + j + i * j % 3 & 0x01) === 0;
            })
        ]
    ]);
    return DataMask;
}();
const __TURBOPACK__default__export__ = DataMask;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/BitMatrixParser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/FormatInformation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataMask$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataMask.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-client] (ecmascript)");
;
;
;
;
/**
 * <AUTHOR> Owen
 */ var BitMatrixParser = function() {
    /**
     * @param bitMatrix {@link BitMatrix} to parse
     * @throws FormatException if dimension is not >= 21 and 1 mod 4
     */ function BitMatrixParser(bitMatrix) {
        var dimension = bitMatrix.getHeight();
        if (dimension < 21 || (dimension & 0x03) !== 1) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        this.bitMatrix = bitMatrix;
    }
    /**
     * <p>Reads format information from one of its two locations within the QR Code.</p>
     *
     * @return {@link FormatInformation} encapsulating the QR Code's format info
     * @throws FormatException if both format information locations cannot be parsed as
     * the valid encoding of format information
     */ BitMatrixParser.prototype.readFormatInformation = function() {
        if (this.parsedFormatInfo !== null && this.parsedFormatInfo !== undefined) {
            return this.parsedFormatInfo;
        }
        // Read top-left format info bits
        var formatInfoBits1 = 0;
        for(var i = 0; i < 6; i++){
            formatInfoBits1 = this.copyBit(i, 8, formatInfoBits1);
        }
        // .. and skip a bit in the timing pattern ...
        formatInfoBits1 = this.copyBit(7, 8, formatInfoBits1);
        formatInfoBits1 = this.copyBit(8, 8, formatInfoBits1);
        formatInfoBits1 = this.copyBit(8, 7, formatInfoBits1);
        // .. and skip a bit in the timing pattern ...
        for(var j = 5; j >= 0; j--){
            formatInfoBits1 = this.copyBit(8, j, formatInfoBits1);
        }
        // Read the top-right/bottom-left pattern too
        var dimension = this.bitMatrix.getHeight();
        var formatInfoBits2 = 0;
        var jMin = dimension - 7;
        for(var j = dimension - 1; j >= jMin; j--){
            formatInfoBits2 = this.copyBit(8, j, formatInfoBits2);
        }
        for(var i = dimension - 8; i < dimension; i++){
            formatInfoBits2 = this.copyBit(i, 8, formatInfoBits2);
        }
        this.parsedFormatInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decodeFormatInformation(formatInfoBits1, formatInfoBits2);
        if (this.parsedFormatInfo !== null) {
            return this.parsedFormatInfo;
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * <p>Reads version information from one of its two locations within the QR Code.</p>
     *
     * @return {@link Version} encapsulating the QR Code's version
     * @throws FormatException if both version information locations cannot be parsed as
     * the valid encoding of version information
     */ BitMatrixParser.prototype.readVersion = function() {
        if (this.parsedVersion !== null && this.parsedVersion !== undefined) {
            return this.parsedVersion;
        }
        var dimension = this.bitMatrix.getHeight();
        var provisionalVersion = Math.floor((dimension - 17) / 4);
        if (provisionalVersion <= 6) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getVersionForNumber(provisionalVersion);
        }
        // Read top-right version info: 3 wide by 6 tall
        var versionBits = 0;
        var ijMin = dimension - 11;
        for(var j = 5; j >= 0; j--){
            for(var i = dimension - 9; i >= ijMin; i--){
                versionBits = this.copyBit(i, j, versionBits);
            }
        }
        var theParsedVersion = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decodeVersionInformation(versionBits);
        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {
            this.parsedVersion = theParsedVersion;
            return theParsedVersion;
        }
        // Hmm, failed. Try bottom left: 6 wide by 3 tall
        versionBits = 0;
        for(var i = 5; i >= 0; i--){
            for(var j = dimension - 9; j >= ijMin; j--){
                versionBits = this.copyBit(i, j, versionBits);
            }
        }
        theParsedVersion = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decodeVersionInformation(versionBits);
        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {
            this.parsedVersion = theParsedVersion;
            return theParsedVersion;
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    };
    BitMatrixParser.prototype.copyBit = function(i /*int*/ , j /*int*/ , versionBits /*int*/ ) {
        var bit = this.isMirror ? this.bitMatrix.get(j, i) : this.bitMatrix.get(i, j);
        return bit ? versionBits << 1 | 0x1 : versionBits << 1;
    };
    /**
     * <p>Reads the bits in the {@link BitMatrix} representing the finder pattern in the
     * correct order in order to reconstruct the codewords bytes contained within the
     * QR Code.</p>
     *
     * @return bytes encoded within the QR Code
     * @throws FormatException if the exact number of bytes expected is not read
     */ BitMatrixParser.prototype.readCodewords = function() {
        var formatInfo = this.readFormatInformation();
        var version = this.readVersion();
        // Get the data mask for the format used in this QR Code. This will exclude
        // some bits from reading as we wind through the bit matrix.
        var dataMask = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataMask$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].values.get(formatInfo.getDataMask());
        var dimension = this.bitMatrix.getHeight();
        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);
        var functionPattern = version.buildFunctionPattern();
        var readingUp = true;
        var result = new Uint8Array(version.getTotalCodewords());
        var resultOffset = 0;
        var currentByte = 0;
        var bitsRead = 0;
        // Read columns in pairs, from right to left
        for(var j = dimension - 1; j > 0; j -= 2){
            if (j === 6) {
                // Skip whole column with vertical alignment pattern
                // saves time and makes the other code proceed more cleanly
                j--;
            }
            // Read alternatingly from bottom to top then top to bottom
            for(var count = 0; count < dimension; count++){
                var i = readingUp ? dimension - 1 - count : count;
                for(var col = 0; col < 2; col++){
                    // Ignore bits covered by the function pattern
                    if (!functionPattern.get(j - col, i)) {
                        // Read a bit
                        bitsRead++;
                        currentByte <<= 1;
                        if (this.bitMatrix.get(j - col, i)) {
                            currentByte |= 1;
                        }
                        // If we've made a whole byte, save it off
                        if (bitsRead === 8) {
                            result[resultOffset++] = /*(byte) */ currentByte;
                            bitsRead = 0;
                            currentByte = 0;
                        }
                    }
                }
            }
            readingUp = !readingUp; // readingUp ^= true; // readingUp = !readingUp; // switch directions
        }
        if (resultOffset !== version.getTotalCodewords()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return result;
    };
    /**
     * Revert the mask removal done while reading the code words. The bit matrix should revert to its original state.
     */ BitMatrixParser.prototype.remask = function() {
        if (this.parsedFormatInfo === null) {
            return; // We have no format information, and have no data mask
        }
        var dataMask = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataMask$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].values.get(this.parsedFormatInfo.getDataMask());
        var dimension = this.bitMatrix.getHeight();
        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);
    };
    /**
     * Prepare the parser for a mirrored operation.
     * This flag has effect only on the {@link #readFormatInformation()} and the
     * {@link #readVersion()}. Before proceeding with {@link #readCodewords()} the
     * {@link #mirror()} method should be called.
     *
     * @param mirror Whether to read version and format information mirrored.
     */ BitMatrixParser.prototype.setMirror = function(isMirror) {
        this.parsedVersion = null;
        this.parsedFormatInfo = null;
        this.isMirror = isMirror;
    };
    /** Mirror the bit matrix in order to attempt a second reading. */ BitMatrixParser.prototype.mirror = function() {
        var bitMatrix = this.bitMatrix;
        for(var x = 0, width = bitMatrix.getWidth(); x < width; x++){
            for(var y = x + 1, height = bitMatrix.getHeight(); y < height; y++){
                if (bitMatrix.get(x, y) !== bitMatrix.get(y, x)) {
                    bitMatrix.flip(y, x);
                    bitMatrix.flip(x, y);
                }
            }
        }
    };
    return BitMatrixParser;
}();
const __TURBOPACK__default__export__ = BitMatrixParser;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
/**
 * <p>Encapsulates a block of data within a QR Code. QR Codes may split their data into
 * multiple blocks, each of which is a unit of data and error-correction codewords. Each
 * is represented by an instance of this class.</p>
 *
 * <AUTHOR> Owen
 */ var DataBlock = function() {
    function DataBlock(numDataCodewords /*int*/ , codewords) {
        this.numDataCodewords = numDataCodewords;
        this.codewords = codewords;
    }
    /**
     * <p>When QR Codes use multiple data blocks, they are actually interleaved.
     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This
     * method will separate the data into original blocks.</p>
     *
     * @param rawCodewords bytes as read directly from the QR Code
     * @param version version of the QR Code
     * @param ecLevel error-correction level of the QR Code
     * @return DataBlocks containing original bytes, "de-interleaved" from representation in the
     *         QR Code
     */ DataBlock.getDataBlocks = function(rawCodewords, version, ecLevel) {
        var e_1, _a, e_2, _b;
        if (rawCodewords.length !== version.getTotalCodewords()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Figure out the number and size of data blocks used by this version and
        // error correction level
        var ecBlocks = version.getECBlocksForLevel(ecLevel);
        // First count the total number of data blocks
        var totalBlocks = 0;
        var ecBlockArray = ecBlocks.getECBlocks();
        try {
            for(var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()){
                var ecBlock = ecBlockArray_1_1.value;
                totalBlocks += ecBlock.getCount();
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        // Now establish DataBlocks of the appropriate size and number of data codewords
        var result = new Array(totalBlocks);
        var numResultBlocks = 0;
        try {
            for(var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()){
                var ecBlock = ecBlockArray_2_1.value;
                for(var i = 0; i < ecBlock.getCount(); i++){
                    var numDataCodewords = ecBlock.getDataCodewords();
                    var numBlockCodewords = ecBlocks.getECCodewordsPerBlock() + numDataCodewords;
                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        // All blocks have the same amount of data, except that the last n
        // (where n may be 0) have 1 more byte. Figure out where these start.
        var shorterBlocksTotalCodewords = result[0].codewords.length;
        var longerBlocksStartAt = result.length - 1;
        // TYPESCRIPTPORT: check length is correct here
        while(longerBlocksStartAt >= 0){
            var numCodewords = result[longerBlocksStartAt].codewords.length;
            if (numCodewords === shorterBlocksTotalCodewords) {
                break;
            }
            longerBlocksStartAt--;
        }
        longerBlocksStartAt++;
        var shorterBlocksNumDataCodewords = shorterBlocksTotalCodewords - ecBlocks.getECCodewordsPerBlock();
        // The last elements of result may be 1 element longer
        // first fill out as many elements as all of them have
        var rawCodewordsOffset = 0;
        for(var i = 0; i < shorterBlocksNumDataCodewords; i++){
            for(var j = 0; j < numResultBlocks; j++){
                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];
            }
        }
        // Fill out the last data block in the longer ones
        for(var j = longerBlocksStartAt; j < numResultBlocks; j++){
            result[j].codewords[shorterBlocksNumDataCodewords] = rawCodewords[rawCodewordsOffset++];
        }
        // Now add in error correction blocks
        var max = result[0].codewords.length;
        for(var i = shorterBlocksNumDataCodewords; i < max; i++){
            for(var j = 0; j < numResultBlocks; j++){
                var iOffset = j < longerBlocksStartAt ? i : i + 1;
                result[j].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];
            }
        }
        return result;
    };
    DataBlock.prototype.getNumDataCodewords = function() {
        return this.numDataCodewords;
    };
    DataBlock.prototype.getCodewords = function() {
        return this.codewords;
    };
    return DataBlock;
}();
const __TURBOPACK__default__export__ = DataBlock;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ModeValues": ()=>ModeValues,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
var ModeValues;
(function(ModeValues) {
    ModeValues[ModeValues["TERMINATOR"] = 0] = "TERMINATOR";
    ModeValues[ModeValues["NUMERIC"] = 1] = "NUMERIC";
    ModeValues[ModeValues["ALPHANUMERIC"] = 2] = "ALPHANUMERIC";
    ModeValues[ModeValues["STRUCTURED_APPEND"] = 3] = "STRUCTURED_APPEND";
    ModeValues[ModeValues["BYTE"] = 4] = "BYTE";
    ModeValues[ModeValues["ECI"] = 5] = "ECI";
    ModeValues[ModeValues["KANJI"] = 6] = "KANJI";
    ModeValues[ModeValues["FNC1_FIRST_POSITION"] = 7] = "FNC1_FIRST_POSITION";
    ModeValues[ModeValues["FNC1_SECOND_POSITION"] = 8] = "FNC1_SECOND_POSITION";
    /** See GBT 18284-2000; "Hanzi" is a transliteration of this mode name. */ ModeValues[ModeValues["HANZI"] = 9] = "HANZI";
})(ModeValues || (ModeValues = {}));
/**
 * <p>See ISO 18004:2006, 6.4.1, Tables 2 and 3. This enum encapsulates the various modes in which
 * data can be encoded to bits in the QR code standard.</p>
 *
 * <AUTHOR> Owen
 */ var Mode = function() {
    function Mode(value, stringValue, characterCountBitsForVersions, bits /*int*/ ) {
        this.value = value;
        this.stringValue = stringValue;
        this.characterCountBitsForVersions = characterCountBitsForVersions;
        this.bits = bits;
        Mode.FOR_BITS.set(bits, this);
        Mode.FOR_VALUE.set(value, this);
    }
    /**
     * @param bits four bits encoding a QR Code data mode
     * @return Mode encoded by these bits
     * @throws IllegalArgumentException if bits do not correspond to a known mode
     */ Mode.forBits = function(bits /*int*/ ) {
        var mode = Mode.FOR_BITS.get(bits);
        if (undefined === mode) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return mode;
    };
    /**
     * @param version version in question
     * @return number of bits used, in this QR Code symbol {@link Version}, to encode the
     *         count of characters that will follow encoded in this Mode
     */ Mode.prototype.getCharacterCountBits = function(version) {
        var versionNumber = version.getVersionNumber();
        var offset;
        if (versionNumber <= 9) {
            offset = 0;
        } else if (versionNumber <= 26) {
            offset = 1;
        } else {
            offset = 2;
        }
        return this.characterCountBitsForVersions[offset];
    };
    Mode.prototype.getValue = function() {
        return this.value;
    };
    Mode.prototype.getBits = function() {
        return this.bits;
    };
    Mode.prototype.equals = function(o) {
        if (!(o instanceof Mode)) {
            return false;
        }
        var other = o;
        return this.value === other.value;
    };
    Mode.prototype.toString = function() {
        return this.stringValue;
    };
    Mode.FOR_BITS = new Map();
    Mode.FOR_VALUE = new Map();
    Mode.TERMINATOR = new Mode(ModeValues.TERMINATOR, 'TERMINATOR', Int32Array.from([
        0,
        0,
        0
    ]), 0x00); // Not really a mode...
    Mode.NUMERIC = new Mode(ModeValues.NUMERIC, 'NUMERIC', Int32Array.from([
        10,
        12,
        14
    ]), 0x01);
    Mode.ALPHANUMERIC = new Mode(ModeValues.ALPHANUMERIC, 'ALPHANUMERIC', Int32Array.from([
        9,
        11,
        13
    ]), 0x02);
    Mode.STRUCTURED_APPEND = new Mode(ModeValues.STRUCTURED_APPEND, 'STRUCTURED_APPEND', Int32Array.from([
        0,
        0,
        0
    ]), 0x03); // Not supported
    Mode.BYTE = new Mode(ModeValues.BYTE, 'BYTE', Int32Array.from([
        8,
        16,
        16
    ]), 0x04);
    Mode.ECI = new Mode(ModeValues.ECI, 'ECI', Int32Array.from([
        0,
        0,
        0
    ]), 0x07); // character counts don't apply
    Mode.KANJI = new Mode(ModeValues.KANJI, 'KANJI', Int32Array.from([
        8,
        10,
        12
    ]), 0x08);
    Mode.FNC1_FIRST_POSITION = new Mode(ModeValues.FNC1_FIRST_POSITION, 'FNC1_FIRST_POSITION', Int32Array.from([
        0,
        0,
        0
    ]), 0x05);
    Mode.FNC1_SECOND_POSITION = new Mode(ModeValues.FNC1_SECOND_POSITION, 'FNC1_SECOND_POSITION', Int32Array.from([
        0,
        0,
        0
    ]), 0x09);
    /** See GBT 18284-2000; "Hanzi" is a transliteration of this mode name. */ Mode.HANZI = new Mode(ModeValues.HANZI, 'HANZI', Int32Array.from([
        8,
        10,
        12
    ]), 0x0D);
    return Mode;
}();
const __TURBOPACK__default__export__ = Mode;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode.decoder {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitSource.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
/*import java.io.UnsupportedEncodingException;*/ /*import java.util.ArrayList;*/ /*import java.util.Collection;*/ /*import java.util.List;*/ /*import java.util.Map;*/ /**
 * <p>QR Codes can encode text as bits in one of several modes, and can use multiple modes
 * in one QR Code. This class decodes the bits back into text.</p>
 *
 * <p>See ISO 18004:2006, 6.4.3 - 6.4.7</p>
 *
 * <AUTHOR> Owen
 */ var DecodedBitStreamParser = function() {
    function DecodedBitStreamParser() {}
    DecodedBitStreamParser.decode = function(bytes, version, ecLevel, hints) {
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](bytes);
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        var byteSegments = new Array(); // 1
        // TYPESCRIPTPORT: I do not use constructor with size 1 as in original Java means capacity and the array length is checked below
        var symbolSequence = -1;
        var parityData = -1;
        try {
            var currentCharacterSetECI = null;
            var fc1InEffect = false;
            var mode = void 0;
            do {
                // While still another segment to read...
                if (bits.available() < 4) {
                    // OK, assume we're done. Really, a TERMINATOR mode should have been recorded here
                    mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].TERMINATOR;
                } else {
                    var modeBits = bits.readBits(4);
                    mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forBits(modeBits); // mode is encoded by 4 bits
                }
                switch(mode){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].TERMINATOR:
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FNC1_FIRST_POSITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FNC1_SECOND_POSITION:
                        // We do little with FNC1 except alter the parsed result a bit according to the spec
                        fc1InEffect = true;
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].STRUCTURED_APPEND:
                        if (bits.available() < 16) {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
                        }
                        // sequence number and parity is added later to the result metadata
                        // Read next 8 bits (symbol sequence #) and 8 bits (data: parity), then continue
                        symbolSequence = bits.readBits(8);
                        parityData = bits.readBits(8);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ECI:
                        // Count doesn't apply to ECI
                        var value = DecodedBitStreamParser.parseECIValue(bits);
                        currentCharacterSetECI = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharacterSetECIByValue(value);
                        if (currentCharacterSetECI === null) {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].HANZI:
                        // First handle Hanzi mode which does not start with character count
                        // Chinese mode contains a sub set indicator right after mode indicator
                        var subset = bits.readBits(4);
                        var countHanzi = bits.readBits(mode.getCharacterCountBits(version));
                        if (subset === DecodedBitStreamParser.GB2312_SUBSET) {
                            DecodedBitStreamParser.decodeHanziSegment(bits, result, countHanzi);
                        }
                        break;
                    default:
                        // "Normal" QR code modes:
                        // How many characters will follow, encoded in this mode?
                        var count = bits.readBits(mode.getCharacterCountBits(version));
                        switch(mode){
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NUMERIC:
                                DecodedBitStreamParser.decodeNumericSegment(bits, result, count);
                                break;
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ALPHANUMERIC:
                                DecodedBitStreamParser.decodeAlphanumericSegment(bits, result, count, fc1InEffect);
                                break;
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE:
                                DecodedBitStreamParser.decodeByteSegment(bits, result, count, currentCharacterSetECI, byteSegments, hints);
                                break;
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].KANJI:
                                DecodedBitStreamParser.decodeKanjiSegment(bits, result, count);
                                break;
                            default:
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                }
            }while (mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].TERMINATOR)
        } catch (iae /*: IllegalArgumentException*/ ) {
            // from readBits() calls
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, ecLevel === null ? null : ecLevel.toString(), symbolSequence, parityData);
    };
    /**
     * See specification GBT 18284-2000
     */ DecodedBitStreamParser.decodeHanziSegment = function(bits, result, count /*int*/ ) {
        // Don't crash trying to read more bits than we have available.
        if (count * 13 > bits.available()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Each character will require 2 bytes. Read the characters as 2-byte pairs
        // and decode as GB2312 afterwards
        var buffer = new Uint8Array(2 * count);
        var offset = 0;
        while(count > 0){
            // Each 13 bits encodes a 2-byte character
            var twoBytes = bits.readBits(13);
            var assembledTwoBytes = twoBytes / 0x060 << 8 & 0xFFFFFFFF | twoBytes % 0x060;
            if (assembledTwoBytes < 0x003BF) {
                // In the 0xA1A1 to 0xAAFE range
                assembledTwoBytes += 0x0A1A1;
            } else {
                // In the 0xB0A1 to 0xFAFE range
                assembledTwoBytes += 0x0A6A1;
            }
            buffer[offset] = assembledTwoBytes >> 8 & 0xFF;
            buffer[offset + 1] = assembledTwoBytes & 0xFF;
            offset += 2;
            count--;
        }
        try {
            result.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(buffer, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].GB2312));
        // TYPESCRIPTPORT: TODO: implement GB2312 decode. StringView from MDN could be a starting point
        } catch (ignored /*: UnsupportedEncodingException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ignored);
        }
    };
    DecodedBitStreamParser.decodeKanjiSegment = function(bits, result, count /*int*/ ) {
        // Don't crash trying to read more bits than we have available.
        if (count * 13 > bits.available()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Each character will require 2 bytes. Read the characters as 2-byte pairs
        // and decode as Shift_JIS afterwards
        var buffer = new Uint8Array(2 * count);
        var offset = 0;
        while(count > 0){
            // Each 13 bits encodes a 2-byte character
            var twoBytes = bits.readBits(13);
            var assembledTwoBytes = twoBytes / 0x0C0 << 8 & 0xFFFFFFFF | twoBytes % 0x0C0;
            if (assembledTwoBytes < 0x01F00) {
                // In the 0x8140 to 0x9FFC range
                assembledTwoBytes += 0x08140;
            } else {
                // In the 0xE040 to 0xEBBF range
                assembledTwoBytes += 0x0C140;
            }
            buffer[offset] = assembledTwoBytes >> 8;
            buffer[offset + 1] = /*(byte) */ assembledTwoBytes;
            offset += 2;
            count--;
        }
        // Shift_JIS may not be supported in some environments:
        try {
            result.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(buffer, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SHIFT_JIS));
        // TYPESCRIPTPORT: TODO: implement SHIFT_JIS decode. StringView from MDN could be a starting point
        } catch (ignored /*: UnsupportedEncodingException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ignored);
        }
    };
    DecodedBitStreamParser.decodeByteSegment = function(bits, result, count /*int*/ , currentCharacterSetECI, byteSegments, hints) {
        // Don't crash trying to read more bits than we have available.
        if (8 * count > bits.available()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var readBytes = new Uint8Array(count);
        for(var i = 0; i < count; i++){
            readBytes[i] = /*(byte) */ bits.readBits(8);
        }
        var encoding;
        if (currentCharacterSetECI === null) {
            // The spec isn't clear on this mode; see
            // section 6.4.5: t does not say which encoding to assuming
            // upon decoding. I have seen ISO-8859-1 used as well as
            // Shift_JIS -- without anything like an ECI designator to
            // give a hint.
            encoding = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].guessEncoding(readBytes, hints);
        } else {
            encoding = currentCharacterSetECI.getName();
        }
        try {
            result.append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(readBytes, encoding));
        } catch (ignored /*: UnsupportedEncodingException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ignored);
        }
        byteSegments.push(readBytes);
    };
    DecodedBitStreamParser.toAlphaNumericChar = function(value /*int*/ ) {
        if (value >= DecodedBitStreamParser.ALPHANUMERIC_CHARS.length) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return DecodedBitStreamParser.ALPHANUMERIC_CHARS[value];
    };
    DecodedBitStreamParser.decodeAlphanumericSegment = function(bits, result, count /*int*/ , fc1InEffect) {
        // Read two characters at a time
        var start = result.length();
        while(count > 1){
            if (bits.available() < 11) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var nextTwoCharsBits = bits.readBits(11);
            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(nextTwoCharsBits / 45)));
            result.append(DecodedBitStreamParser.toAlphaNumericChar(nextTwoCharsBits % 45));
            count -= 2;
        }
        if (count === 1) {
            // special case: one character left
            if (bits.available() < 6) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            result.append(DecodedBitStreamParser.toAlphaNumericChar(bits.readBits(6)));
        }
        // See section *******, *******
        if (fc1InEffect) {
            // We need to massage the result a bit if in an FNC1 mode:
            for(var i = start; i < result.length(); i++){
                if (result.charAt(i) === '%') {
                    if (i < result.length() - 1 && result.charAt(i + 1) === '%') {
                        // %% is rendered as %
                        result.deleteCharAt(i + 1);
                    } else {
                        // In alpha mode, % should be converted to FNC1 separator 0x1D
                        result.setCharAt(i, String.fromCharCode(0x1D));
                    }
                }
            }
        }
    };
    DecodedBitStreamParser.decodeNumericSegment = function(bits, result, count /*int*/ ) {
        // Read three digits at a time
        while(count >= 3){
            // Each 10 bits encodes three digits
            if (bits.available() < 10) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var threeDigitsBits = bits.readBits(10);
            if (threeDigitsBits >= 1000) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 100)));
            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 10) % 10));
            result.append(DecodedBitStreamParser.toAlphaNumericChar(threeDigitsBits % 10));
            count -= 3;
        }
        if (count === 2) {
            // Two digits left over to read, encoded in 7 bits
            if (bits.available() < 7) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var twoDigitsBits = bits.readBits(7);
            if (twoDigitsBits >= 100) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(twoDigitsBits / 10)));
            result.append(DecodedBitStreamParser.toAlphaNumericChar(twoDigitsBits % 10));
        } else if (count === 1) {
            // One digit left over to read
            if (bits.available() < 4) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var digitBits = bits.readBits(4);
            if (digitBits >= 10) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            result.append(DecodedBitStreamParser.toAlphaNumericChar(digitBits));
        }
    };
    DecodedBitStreamParser.parseECIValue = function(bits) {
        var firstByte = bits.readBits(8);
        if ((firstByte & 0x80) === 0) {
            // just one byte
            return firstByte & 0x7F;
        }
        if ((firstByte & 0xC0) === 0x80) {
            // two bytes
            var secondByte = bits.readBits(8);
            return (firstByte & 0x3F) << 8 & 0xFFFFFFFF | secondByte;
        }
        if ((firstByte & 0xE0) === 0xC0) {
            // three bytes
            var secondThirdBytes = bits.readBits(16);
            return (firstByte & 0x1F) << 16 & 0xFFFFFFFF | secondThirdBytes;
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * See ISO 18004:2006, 6.4.4 Table 5
     */ DecodedBitStreamParser.ALPHANUMERIC_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';
    DecodedBitStreamParser.GB2312_SUBSET = 1;
    return DecodedBitStreamParser;
}();
const __TURBOPACK__default__export__ = DecodedBitStreamParser;
 // function Uint8ArrayToString(a: Uint8Array): string {
 //     const CHUNK_SZ = 0x8000;
 //     const c = new StringBuilder();
 //     for (let i = 0, length = a.length; i < length; i += CHUNK_SZ) {
 //         c.append(String.fromCharCode.apply(null, a.subarray(i, i + CHUNK_SZ)));
 //     }
 //     return c.toString();
 // }
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/QRCodeDecoderMetaData.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2013 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Meta-data container for QR Code decoding. Instances of this class may be used to convey information back to the
 * decoding caller. Callers are expected to process this.
 *
 * @see com.google.zxing.common.DecoderResult#getOther()
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var QRCodeDecoderMetaData = function() {
    function QRCodeDecoderMetaData(mirrored) {
        this.mirrored = mirrored;
    }
    /**
     * @return true if the QR Code was mirrored.
     */ QRCodeDecoderMetaData.prototype.isMirrored = function() {
        return this.mirrored;
    };
    /**
     * Apply the result points' order correction due to mirroring.
     *
     * @param points Array of points to apply mirror correction to.
     */ QRCodeDecoderMetaData.prototype.applyMirroredCorrection = function(points) {
        if (!this.mirrored || points === null || points.length < 3) {
            return;
        }
        var bottomLeft = points[0];
        points[0] = points[2];
        points[2] = bottomLeft;
    // No need to 'fix' top-left and alignment pattern.
    };
    return QRCodeDecoderMetaData;
}();
const __TURBOPACK__default__export__ = QRCodeDecoderMetaData;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Decoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.decoder {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ChecksumException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$BitMatrixParser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/BitMatrixParser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$QRCodeDecoderMetaData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/QRCodeDecoderMetaData.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
/*import java.util.Map;*/ /**
 * <p>The main class which implements QR Code decoding -- as opposed to locating and extracting
 * the QR Code from an image.</p>
 *
 * <AUTHOR> Owen
 */ var Decoder = function() {
    function Decoder() {
        this.rsDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_CODE_FIELD_256);
    }
    // public decode(image: boolean[][]): DecoderResult /*throws ChecksumException, FormatException*/ {
    //   return decode(image, null)
    // }
    /**
     * <p>Convenience method that can decode a QR Code represented as a 2D array of booleans.
     * "true" is taken to mean a black module.</p>
     *
     * @param image booleans representing white/black QR Code modules
     * @param hints decoding hints that should be used to influence decoding
     * @return text and bytes encoded within the QR Code
     * @throws FormatException if the QR Code cannot be decoded
     * @throws ChecksumException if error correction fails
     */ Decoder.prototype.decodeBooleanArray = function(image, hints) {
        return this.decodeBitMatrix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].parseFromBooleanArray(image), hints);
    };
    // public decodeBitMatrix(bits: BitMatrix): DecoderResult /*throws ChecksumException, FormatException*/ {
    //   return decode(bits, null)
    // }
    /**
     * <p>Decodes a QR Code represented as a {@link BitMatrix}. A 1 or "true" is taken to mean a black module.</p>
     *
     * @param bits booleans representing white/black QR Code modules
     * @param hints decoding hints that should be used to influence decoding
     * @return text and bytes encoded within the QR Code
     * @throws FormatException if the QR Code cannot be decoded
     * @throws ChecksumException if error correction fails
     */ Decoder.prototype.decodeBitMatrix = function(bits, hints) {
        // Construct a parser and read version, error-correction level
        var parser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$BitMatrixParser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](bits);
        var ex = null;
        try {
            return this.decodeBitMatrixParser(parser, hints);
        } catch (e /*: FormatException, ChecksumException*/ ) {
            ex = e;
        }
        try {
            // Revert the bit matrix
            parser.remask();
            // Will be attempting a mirrored reading of the version and format info.
            parser.setMirror(true);
            // Preemptively read the version.
            parser.readVersion();
            // Preemptively read the format information.
            parser.readFormatInformation();
            /*
             * Since we're here, this means we have successfully detected some kind
             * of version and format information when mirrored. This is a good sign,
             * that the QR code may be mirrored, and we should try once more with a
             * mirrored content.
             */ // Prepare for a mirrored reading.
            parser.mirror();
            var result = this.decodeBitMatrixParser(parser, hints);
            // Success! Notify the caller that the code was mirrored.
            result.setOther(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$QRCodeDecoderMetaData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](true));
            return result;
        } catch (e /*FormatException | ChecksumException*/ ) {
            // Throw the exception from the original reading
            if (ex !== null) {
                throw ex;
            }
            throw e;
        }
    };
    Decoder.prototype.decodeBitMatrixParser = function(parser, hints) {
        var e_1, _a, e_2, _b;
        var version = parser.readVersion();
        var ecLevel = parser.readFormatInformation().getErrorCorrectionLevel();
        // Read codewords
        var codewords = parser.readCodewords();
        // Separate into data blocks
        var dataBlocks = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDataBlocks(codewords, version, ecLevel);
        // Count total number of data bytes
        var totalBytes = 0;
        try {
            for(var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()){
                var dataBlock = dataBlocks_1_1.value;
                totalBytes += dataBlock.getNumDataCodewords();
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        var resultBytes = new Uint8Array(totalBytes);
        var resultOffset = 0;
        try {
            // Error-correct and copy data blocks together into a stream of bytes
            for(var dataBlocks_2 = __values(dataBlocks), dataBlocks_2_1 = dataBlocks_2.next(); !dataBlocks_2_1.done; dataBlocks_2_1 = dataBlocks_2.next()){
                var dataBlock = dataBlocks_2_1.value;
                var codewordBytes = dataBlock.getCodewords();
                var numDataCodewords = dataBlock.getNumDataCodewords();
                this.correctErrors(codewordBytes, numDataCodewords);
                for(var i = 0; i < numDataCodewords; i++){
                    resultBytes[resultOffset++] = codewordBytes[i];
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (dataBlocks_2_1 && !dataBlocks_2_1.done && (_b = dataBlocks_2.return)) _b.call(dataBlocks_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        // Decode the contents of that stream of bytes
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(resultBytes, version, ecLevel, hints);
    };
    /**
     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to
     * correct the errors in-place using Reed-Solomon error correction.</p>
     *
     * @param codewordBytes data and error correction codewords
     * @param numDataCodewords number of codewords that are data bytes
     * @throws ChecksumException if error correction fails
     */ Decoder.prototype.correctErrors = function(codewordBytes, numDataCodewords /*int*/ ) {
        // const numCodewords = codewordBytes.length;
        // First read into an array of ints
        var codewordsInts = new Int32Array(codewordBytes);
        // TYPESCRIPTPORT: not realy necessary to transform to ints? could redesign everything to work with unsigned bytes?
        // const codewordsInts = new Int32Array(numCodewords)
        // for (let i = 0; i < numCodewords; i++) {
        //   codewordsInts[i] = codewordBytes[i] & 0xFF
        // }
        try {
            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);
        } catch (ignored /*: ReedSolomonException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Copy back into array of bytes -- only need to worry about the bytes that were data
        // We don't care about errors in the error-correction codewords
        for(var i = 0; i < numDataCodewords; i++){
            codewordBytes[i] = /*(byte) */ codewordsInts[i];
        }
    };
    return Decoder;
}();
const __TURBOPACK__default__export__ = Decoder;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPattern.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.detector {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * <p>Encapsulates an alignment pattern, which are the smaller square patterns found in
 * all but the simplest QR Codes.</p>
 *
 * <AUTHOR> Owen
 */ var AlignmentPattern = function(_super) {
    __extends(AlignmentPattern, _super);
    function AlignmentPattern(posX /*float*/ , posY /*float*/ , estimatedModuleSize /*float*/ ) {
        var _this = _super.call(this, posX, posY) || this;
        _this.estimatedModuleSize = estimatedModuleSize;
        return _this;
    }
    /**
     * <p>Determines if this alignment pattern "about equals" an alignment pattern at the stated
     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>
     */ AlignmentPattern.prototype.aboutEquals = function(moduleSize /*float*/ , i /*float*/ , j /*float*/ ) {
        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {
            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);
            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;
        }
        return false;
    };
    /**
     * Combines this object's current estimate of a finder pattern position and module size
     * with a new estimate. It returns a new {@code FinderPattern} containing an average of the two.
     */ AlignmentPattern.prototype.combineEstimate = function(i /*float*/ , j /*float*/ , newModuleSize /*float*/ ) {
        var combinedX = (this.getX() + j) / 2.0;
        var combinedY = (this.getY() + i) / 2.0;
        var combinedModuleSize = (this.estimatedModuleSize + newModuleSize) / 2.0;
        return new AlignmentPattern(combinedX, combinedY, combinedModuleSize);
    };
    return AlignmentPattern;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AlignmentPattern;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPatternFinder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$AlignmentPattern$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPattern.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
/*import java.util.ArrayList;*/ /*import java.util.List;*/ /**
 * <p>This class attempts to find alignment patterns in a QR Code. Alignment patterns look like finder
 * patterns but are smaller and appear at regular intervals throughout the image.</p>
 *
 * <p>At the moment this only looks for the bottom-right alignment pattern.</p>
 *
 * <p>This is mostly a simplified copy of {@link FinderPatternFinder}. It is copied,
 * pasted and stripped down here for maximum performance but does unfortunately duplicate
 * some code.</p>
 *
 * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.</p>
 *
 * <AUTHOR> Owen
 */ var AlignmentPatternFinder = function() {
    /**
     * <p>Creates a finder that will look in a portion of the whole image.</p>
     *
     * @param image image to search
     * @param startX left column from which to start searching
     * @param startY top row from which to start searching
     * @param width width of region to search
     * @param height height of region to search
     * @param moduleSize estimated module size so far
     */ function AlignmentPatternFinder(image, startX /*int*/ , startY /*int*/ , width /*int*/ , height /*int*/ , moduleSize /*float*/ , resultPointCallback) {
        this.image = image;
        this.startX = startX;
        this.startY = startY;
        this.width = width;
        this.height = height;
        this.moduleSize = moduleSize;
        this.resultPointCallback = resultPointCallback;
        this.possibleCenters = []; // new Array<any>(5))
        // TYPESCRIPTPORT: array initialization without size as the length is checked below
        this.crossCheckStateCount = new Int32Array(3);
    }
    /**
     * <p>This method attempts to find the bottom-right alignment pattern in the image. It is a bit messy since
     * it's pretty performance-critical and so is written to be fast foremost.</p>
     *
     * @return {@link AlignmentPattern} if found
     * @throws NotFoundException if not found
     */ AlignmentPatternFinder.prototype.find = function() {
        var startX = this.startX;
        var height = this.height;
        var width = this.width;
        var maxJ = startX + width;
        var middleI = this.startY + height / 2;
        // We are looking for black/white/black modules in 1:1:1 ratio
        // this tracks the number of black/white/black modules seen so far
        var stateCount = new Int32Array(3);
        var image = this.image;
        for(var iGen = 0; iGen < height; iGen++){
            // Search from middle outwards
            var i = middleI + ((iGen & 0x01) === 0 ? Math.floor((iGen + 1) / 2) : -Math.floor((iGen + 1) / 2));
            stateCount[0] = 0;
            stateCount[1] = 0;
            stateCount[2] = 0;
            var j = startX;
            // Burn off leading white pixels before anything else; if we start in the middle of
            // a white run, it doesn't make sense to count its length, since we don't know if the
            // white run continued to the left of the start point
            while(j < maxJ && !image.get(j, i)){
                j++;
            }
            var currentState = 0;
            while(j < maxJ){
                if (image.get(j, i)) {
                    // Black pixel
                    if (currentState === 1) {
                        stateCount[1]++;
                    } else {
                        if (currentState === 2) {
                            if (this.foundPatternCross(stateCount)) {
                                var confirmed = this.handlePossibleCenter(stateCount, i, j);
                                if (confirmed !== null) {
                                    return confirmed;
                                }
                            }
                            stateCount[0] = stateCount[2];
                            stateCount[1] = 1;
                            stateCount[2] = 0;
                            currentState = 1;
                        } else {
                            stateCount[++currentState]++;
                        }
                    }
                } else {
                    if (currentState === 1) {
                        currentState++;
                    }
                    stateCount[currentState]++;
                }
                j++;
            }
            if (this.foundPatternCross(stateCount)) {
                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);
                if (confirmed !== null) {
                    return confirmed;
                }
            }
        }
        // Hmm, nothing we saw was observed and confirmed twice. If we had
        // any guess at all, return it.
        if (this.possibleCenters.length !== 0) {
            return this.possibleCenters[0];
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * Given a count of black/white/black pixels just seen and an end position,
     * figures the location of the center of this black/white/black run.
     */ AlignmentPatternFinder.centerFromEnd = function(stateCount, end /*int*/ ) {
        return end - stateCount[2] - stateCount[1] / 2.0;
    };
    /**
     * @param stateCount count of black/white/black pixels just read
     * @return true iff the proportions of the counts is close enough to the 1/1/1 ratios
     *         used by alignment patterns to be considered a match
     */ AlignmentPatternFinder.prototype.foundPatternCross = function(stateCount) {
        var moduleSize = this.moduleSize;
        var maxVariance = moduleSize / 2.0;
        for(var i = 0; i < 3; i++){
            if (Math.abs(moduleSize - stateCount[i]) >= maxVariance) {
                return false;
            }
        }
        return true;
    };
    /**
     * <p>After a horizontal scan finds a potential alignment pattern, this method
     * "cross-checks" by scanning down vertically through the center of the possible
     * alignment pattern to see if the same proportion is detected.</p>
     *
     * @param startI row where an alignment pattern was detected
     * @param centerJ center of the section that appears to cross an alignment pattern
     * @param maxCount maximum reasonable number of modules that should be
     * observed in any reading state, based on the results of the horizontal scan
     * @return vertical center of alignment pattern, or {@link Float#NaN} if not found
     */ AlignmentPatternFinder.prototype.crossCheckVertical = function(startI /*int*/ , centerJ /*int*/ , maxCount /*int*/ , originalStateCountTotal /*int*/ ) {
        var image = this.image;
        var maxI = image.getHeight();
        var stateCount = this.crossCheckStateCount;
        stateCount[0] = 0;
        stateCount[1] = 0;
        stateCount[2] = 0;
        // Start counting up from center
        var i = startI;
        while(i >= 0 && image.get(centerJ, i) && stateCount[1] <= maxCount){
            stateCount[1]++;
            i--;
        }
        // If already too many modules in this state or ran off the edge:
        if (i < 0 || stateCount[1] > maxCount) {
            return NaN;
        }
        while(i >= 0 && !image.get(centerJ, i) && stateCount[0] <= maxCount){
            stateCount[0]++;
            i--;
        }
        if (stateCount[0] > maxCount) {
            return NaN;
        }
        // Now also count down from center
        i = startI + 1;
        while(i < maxI && image.get(centerJ, i) && stateCount[1] <= maxCount){
            stateCount[1]++;
            i++;
        }
        if (i === maxI || stateCount[1] > maxCount) {
            return NaN;
        }
        while(i < maxI && !image.get(centerJ, i) && stateCount[2] <= maxCount){
            stateCount[2]++;
            i++;
        }
        if (stateCount[2] > maxCount) {
            return NaN;
        }
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];
        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {
            return NaN;
        }
        return this.foundPatternCross(stateCount) ? AlignmentPatternFinder.centerFromEnd(stateCount, i) : NaN;
    };
    /**
     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will
     * cross check with a vertical scan, and if successful, will see if this pattern had been
     * found on a previous horizontal scan. If so, we consider it confirmed and conclude we have
     * found the alignment pattern.</p>
     *
     * @param stateCount reading state module counts from horizontal scan
     * @param i row where alignment pattern may be found
     * @param j end of possible alignment pattern in row
     * @return {@link AlignmentPattern} if we have found the same pattern twice, or null if not
     */ AlignmentPatternFinder.prototype.handlePossibleCenter = function(stateCount, i /*int*/ , j /*int*/ ) {
        var e_1, _a;
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];
        var centerJ = AlignmentPatternFinder.centerFromEnd(stateCount, j);
        var centerI = this.crossCheckVertical(i, /*(int) */ centerJ, 2 * stateCount[1], stateCountTotal);
        if (!isNaN(centerI)) {
            var estimatedModuleSize = (stateCount[0] + stateCount[1] + stateCount[2]) / 3.0;
            try {
                for(var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()){
                    var center = _c.value;
                    // Look for about the same center and module size:
                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {
                        return center.combineEstimate(centerI, centerJ, estimatedModuleSize);
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            // Hadn't found this before; save it
            var point = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$AlignmentPattern$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerJ, centerI, estimatedModuleSize);
            this.possibleCenters.push(point);
            if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {
                this.resultPointCallback.foundPossibleResultPoint(point);
            }
        }
        return null;
    };
    return AlignmentPatternFinder;
}();
const __TURBOPACK__default__export__ = AlignmentPatternFinder;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPattern.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.detector {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * <p>Encapsulates a finder pattern, which are the three square patterns found in
 * the corners of QR Codes. It also encapsulates a count of similar finder patterns,
 * as a convenience to the finder's bookkeeping.</p>
 *
 * <AUTHOR> Owen
 */ var FinderPattern = function(_super) {
    __extends(FinderPattern, _super);
    // FinderPattern(posX: number/*float*/, posY: number/*float*/, estimatedModuleSize: number/*float*/) {
    //   this(posX, posY, estimatedModuleSize, 1)
    // }
    function FinderPattern(posX /*float*/ , posY /*float*/ , estimatedModuleSize /*float*/ , count /*int*/ ) {
        var _this = _super.call(this, posX, posY) || this;
        _this.estimatedModuleSize = estimatedModuleSize;
        _this.count = count;
        if (undefined === count) {
            _this.count = 1;
        }
        return _this;
    }
    FinderPattern.prototype.getEstimatedModuleSize = function() {
        return this.estimatedModuleSize;
    };
    FinderPattern.prototype.getCount = function() {
        return this.count;
    };
    /*
    void incrementCount() {
      this.count++
    }
     */ /**
     * <p>Determines if this finder pattern "about equals" a finder pattern at the stated
     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>
     */ FinderPattern.prototype.aboutEquals = function(moduleSize /*float*/ , i /*float*/ , j /*float*/ ) {
        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {
            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);
            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;
        }
        return false;
    };
    /**
     * Combines this object's current estimate of a finder pattern position and module size
     * with a new estimate. It returns a new {@code FinderPattern} containing a weighted average
     * based on count.
     */ FinderPattern.prototype.combineEstimate = function(i /*float*/ , j /*float*/ , newModuleSize /*float*/ ) {
        var combinedCount = this.count + 1;
        var combinedX = (this.count * this.getX() + j) / combinedCount;
        var combinedY = (this.count * this.getY() + i) / combinedCount;
        var combinedModuleSize = (this.count * this.estimatedModuleSize + newModuleSize) / combinedCount;
        return new FinderPattern(combinedX, combinedY, combinedModuleSize, combinedCount);
    };
    return FinderPattern;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = FinderPattern;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternInfo.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>Encapsulates information about finder patterns in an image, including the location of
 * the three finder patterns, and their estimated module size.</p>
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var FinderPatternInfo = function() {
    function FinderPatternInfo(patternCenters) {
        this.bottomLeft = patternCenters[0];
        this.topLeft = patternCenters[1];
        this.topRight = patternCenters[2];
    }
    FinderPatternInfo.prototype.getBottomLeft = function() {
        return this.bottomLeft;
    };
    FinderPatternInfo.prototype.getTopLeft = function() {
        return this.topLeft;
    };
    FinderPatternInfo.prototype.getTopRight = function() {
        return this.topRight;
    };
    return FinderPatternInfo;
}();
const __TURBOPACK__default__export__ = FinderPatternInfo;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternFinder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.detector {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPattern$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPattern.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPatternInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternInfo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
/*import java.io.Serializable;*/ /*import java.util.ArrayList;*/ /*import java.util.Collections;*/ /*import java.util.Comparator;*/ /*import java.util.List;*/ /*import java.util.Map;*/ /**
 * <p>This class attempts to find finder patterns in a QR Code. Finder patterns are the square
 * markers at three corners of a QR Code.</p>
 *
 * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.
 *
 * <AUTHOR> Owen
 */ var FinderPatternFinder = function() {
    /**
     * <p>Creates a finder that will search the image for three finder patterns.</p>
     *
     * @param image image to search
     */ // public constructor(image: BitMatrix) {
    //   this(image, null)
    // }
    function FinderPatternFinder(image, resultPointCallback) {
        this.image = image;
        this.resultPointCallback = resultPointCallback;
        this.possibleCenters = [];
        this.crossCheckStateCount = new Int32Array(5);
        this.resultPointCallback = resultPointCallback;
    }
    FinderPatternFinder.prototype.getImage = function() {
        return this.image;
    };
    FinderPatternFinder.prototype.getPossibleCenters = function() {
        return this.possibleCenters;
    };
    FinderPatternFinder.prototype.find = function(hints) {
        var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].TRY_HARDER);
        var pureBarcode = hints !== null && hints !== undefined && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].PURE_BARCODE);
        var image = this.image;
        var maxI = image.getHeight();
        var maxJ = image.getWidth();
        // We are looking for black/white/black/white/black modules in
        // 1:1:3:1:1 ratio; this tracks the number of such modules seen so far
        // Let's assume that the maximum version QR Code we support takes up 1/4 the height of the
        // image, and then account for the center being 3 modules in size. This gives the smallest
        // number of pixels the center could be, so skip this often. When trying harder, look for all
        // QR versions regardless of how dense they are.
        var iSkip = Math.floor(3 * maxI / (4 * FinderPatternFinder.MAX_MODULES));
        if (iSkip < FinderPatternFinder.MIN_SKIP || tryHarder) {
            iSkip = FinderPatternFinder.MIN_SKIP;
        }
        var done = false;
        var stateCount = new Int32Array(5);
        for(var i = iSkip - 1; i < maxI && !done; i += iSkip){
            // Get a row of black/white values
            stateCount[0] = 0;
            stateCount[1] = 0;
            stateCount[2] = 0;
            stateCount[3] = 0;
            stateCount[4] = 0;
            var currentState = 0;
            for(var j = 0; j < maxJ; j++){
                if (image.get(j, i)) {
                    // Black pixel
                    if ((currentState & 1) === 1) {
                        currentState++;
                    }
                    stateCount[currentState]++;
                } else {
                    if ((currentState & 1) === 0) {
                        if (currentState === 4) {
                            if (FinderPatternFinder.foundPatternCross(stateCount)) {
                                var confirmed = this.handlePossibleCenter(stateCount, i, j, pureBarcode);
                                if (confirmed === true) {
                                    // Start examining every other line. Checking each line turned out to be too
                                    // expensive and didn't improve performance.
                                    iSkip = 2;
                                    if (this.hasSkipped === true) {
                                        done = this.haveMultiplyConfirmedCenters();
                                    } else {
                                        var rowSkip = this.findRowSkip();
                                        if (rowSkip > stateCount[2]) {
                                            // Skip rows between row of lower confirmed center
                                            // and top of presumed third confirmed center
                                            // but back up a bit to get a full chance of detecting
                                            // it, entire width of center of finder pattern
                                            // Skip by rowSkip, but back off by stateCount[2] (size of last center
                                            // of pattern we saw) to be conservative, and also back off by iSkip which
                                            // is about to be re-added
                                            i += rowSkip - stateCount[2] - iSkip;
                                            j = maxJ - 1;
                                        }
                                    }
                                } else {
                                    stateCount[0] = stateCount[2];
                                    stateCount[1] = stateCount[3];
                                    stateCount[2] = stateCount[4];
                                    stateCount[3] = 1;
                                    stateCount[4] = 0;
                                    currentState = 3;
                                    continue;
                                }
                                // Clear state to start looking again
                                currentState = 0;
                                stateCount[0] = 0;
                                stateCount[1] = 0;
                                stateCount[2] = 0;
                                stateCount[3] = 0;
                                stateCount[4] = 0;
                            } else {
                                stateCount[0] = stateCount[2];
                                stateCount[1] = stateCount[3];
                                stateCount[2] = stateCount[4];
                                stateCount[3] = 1;
                                stateCount[4] = 0;
                                currentState = 3;
                            }
                        } else {
                            stateCount[++currentState]++;
                        }
                    } else {
                        stateCount[currentState]++;
                    }
                }
            }
            if (FinderPatternFinder.foundPatternCross(stateCount)) {
                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ, pureBarcode);
                if (confirmed === true) {
                    iSkip = stateCount[0];
                    if (this.hasSkipped) {
                        // Found a third one
                        done = this.haveMultiplyConfirmedCenters();
                    }
                }
            }
        }
        var patternInfo = this.selectBestPatterns();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].orderBestPatterns(patternInfo);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPatternInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](patternInfo);
    };
    /**
     * Given a count of black/white/black/white/black pixels just seen and an end position,
     * figures the location of the center of this run.
     */ FinderPatternFinder.centerFromEnd = function(stateCount, end /*int*/ ) {
        return end - stateCount[4] - stateCount[3] - stateCount[2] / 2.0;
    };
    /**
     * @param stateCount count of black/white/black/white/black pixels just read
     * @return true iff the proportions of the counts is close enough to the 1/1/3/1/1 ratios
     *         used by finder patterns to be considered a match
     */ FinderPatternFinder.foundPatternCross = function(stateCount) {
        var totalModuleSize = 0;
        for(var i = 0; i < 5; i++){
            var count = stateCount[i];
            if (count === 0) {
                return false;
            }
            totalModuleSize += count;
        }
        if (totalModuleSize < 7) {
            return false;
        }
        var moduleSize = totalModuleSize / 7.0;
        var maxVariance = moduleSize / 2.0;
        // Allow less than 50% variance from 1-1-3-1-1 proportions
        return Math.abs(moduleSize - stateCount[0]) < maxVariance && Math.abs(moduleSize - stateCount[1]) < maxVariance && Math.abs(3.0 * moduleSize - stateCount[2]) < 3 * maxVariance && Math.abs(moduleSize - stateCount[3]) < maxVariance && Math.abs(moduleSize - stateCount[4]) < maxVariance;
    };
    FinderPatternFinder.prototype.getCrossCheckStateCount = function() {
        var crossCheckStateCount = this.crossCheckStateCount;
        crossCheckStateCount[0] = 0;
        crossCheckStateCount[1] = 0;
        crossCheckStateCount[2] = 0;
        crossCheckStateCount[3] = 0;
        crossCheckStateCount[4] = 0;
        return crossCheckStateCount;
    };
    /**
     * After a vertical and horizontal scan finds a potential finder pattern, this method
     * "cross-cross-cross-checks" by scanning down diagonally through the center of the possible
     * finder pattern to see if the same proportion is detected.
     *
     * @param startI row where a finder pattern was detected
     * @param centerJ center of the section that appears to cross a finder pattern
     * @param maxCount maximum reasonable number of modules that should be
     *  observed in any reading state, based on the results of the horizontal scan
     * @param originalStateCountTotal The original state count total.
     * @return true if proportions are withing expected limits
     */ FinderPatternFinder.prototype.crossCheckDiagonal = function(startI /*int*/ , centerJ /*int*/ , maxCount /*int*/ , originalStateCountTotal /*int*/ ) {
        var stateCount = this.getCrossCheckStateCount();
        // Start counting up, left from center finding black center mass
        var i = 0;
        var image = this.image;
        while(startI >= i && centerJ >= i && image.get(centerJ - i, startI - i)){
            stateCount[2]++;
            i++;
        }
        if (startI < i || centerJ < i) {
            return false;
        }
        // Continue up, left finding white space
        while(startI >= i && centerJ >= i && !image.get(centerJ - i, startI - i) && stateCount[1] <= maxCount){
            stateCount[1]++;
            i++;
        }
        // If already too many modules in this state or ran off the edge:
        if (startI < i || centerJ < i || stateCount[1] > maxCount) {
            return false;
        }
        // Continue up, left finding black border
        while(startI >= i && centerJ >= i && image.get(centerJ - i, startI - i) && stateCount[0] <= maxCount){
            stateCount[0]++;
            i++;
        }
        if (stateCount[0] > maxCount) {
            return false;
        }
        var maxI = image.getHeight();
        var maxJ = image.getWidth();
        // Now also count down, right from center
        i = 1;
        while(startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i)){
            stateCount[2]++;
            i++;
        }
        // Ran off the edge?
        if (startI + i >= maxI || centerJ + i >= maxJ) {
            return false;
        }
        while(startI + i < maxI && centerJ + i < maxJ && !image.get(centerJ + i, startI + i) && stateCount[3] < maxCount){
            stateCount[3]++;
            i++;
        }
        if (startI + i >= maxI || centerJ + i >= maxJ || stateCount[3] >= maxCount) {
            return false;
        }
        while(startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i) && stateCount[4] < maxCount){
            stateCount[4]++;
            i++;
        }
        if (stateCount[4] >= maxCount) {
            return false;
        }
        // If we found a finder-pattern-like section, but its size is more than 100% different than
        // the original, assume it's a false positive
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];
        return Math.abs(stateCountTotal - originalStateCountTotal) < 2 * originalStateCountTotal && FinderPatternFinder.foundPatternCross(stateCount);
    };
    /**
     * <p>After a horizontal scan finds a potential finder pattern, this method
     * "cross-checks" by scanning down vertically through the center of the possible
     * finder pattern to see if the same proportion is detected.</p>
     *
     * @param startI row where a finder pattern was detected
     * @param centerJ center of the section that appears to cross a finder pattern
     * @param maxCount maximum reasonable number of modules that should be
     * observed in any reading state, based on the results of the horizontal scan
     * @return vertical center of finder pattern, or {@link Float#NaN} if not found
     */ FinderPatternFinder.prototype.crossCheckVertical = function(startI /*int*/ , centerJ /*int*/ , maxCount /*int*/ , originalStateCountTotal /*int*/ ) {
        var image = this.image;
        var maxI = image.getHeight();
        var stateCount = this.getCrossCheckStateCount();
        // Start counting up from center
        var i = startI;
        while(i >= 0 && image.get(centerJ, i)){
            stateCount[2]++;
            i--;
        }
        if (i < 0) {
            return NaN;
        }
        while(i >= 0 && !image.get(centerJ, i) && stateCount[1] <= maxCount){
            stateCount[1]++;
            i--;
        }
        // If already too many modules in this state or ran off the edge:
        if (i < 0 || stateCount[1] > maxCount) {
            return NaN;
        }
        while(i >= 0 && image.get(centerJ, i) && stateCount[0] <= maxCount){
            stateCount[0]++;
            i--;
        }
        if (stateCount[0] > maxCount) {
            return NaN;
        }
        // Now also count down from center
        i = startI + 1;
        while(i < maxI && image.get(centerJ, i)){
            stateCount[2]++;
            i++;
        }
        if (i === maxI) {
            return NaN;
        }
        while(i < maxI && !image.get(centerJ, i) && stateCount[3] < maxCount){
            stateCount[3]++;
            i++;
        }
        if (i === maxI || stateCount[3] >= maxCount) {
            return NaN;
        }
        while(i < maxI && image.get(centerJ, i) && stateCount[4] < maxCount){
            stateCount[4]++;
            i++;
        }
        if (stateCount[4] >= maxCount) {
            return NaN;
        }
        // If we found a finder-pattern-like section, but its size is more than 40% different than
        // the original, assume it's a false positive
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];
        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {
            return NaN;
        }
        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, i) : NaN;
    };
    /**
     * <p>Like {@link #crossCheckVertical(int, int, int, int)}, and in fact is basically identical,
     * except it reads horizontally instead of vertically. This is used to cross-cross
     * check a vertical cross check and locate the real center of the alignment pattern.</p>
     */ FinderPatternFinder.prototype.crossCheckHorizontal = function(startJ /*int*/ , centerI /*int*/ , maxCount /*int*/ , originalStateCountTotal /*int*/ ) {
        var image = this.image;
        var maxJ = image.getWidth();
        var stateCount = this.getCrossCheckStateCount();
        var j = startJ;
        while(j >= 0 && image.get(j, centerI)){
            stateCount[2]++;
            j--;
        }
        if (j < 0) {
            return NaN;
        }
        while(j >= 0 && !image.get(j, centerI) && stateCount[1] <= maxCount){
            stateCount[1]++;
            j--;
        }
        if (j < 0 || stateCount[1] > maxCount) {
            return NaN;
        }
        while(j >= 0 && image.get(j, centerI) && stateCount[0] <= maxCount){
            stateCount[0]++;
            j--;
        }
        if (stateCount[0] > maxCount) {
            return NaN;
        }
        j = startJ + 1;
        while(j < maxJ && image.get(j, centerI)){
            stateCount[2]++;
            j++;
        }
        if (j === maxJ) {
            return NaN;
        }
        while(j < maxJ && !image.get(j, centerI) && stateCount[3] < maxCount){
            stateCount[3]++;
            j++;
        }
        if (j === maxJ || stateCount[3] >= maxCount) {
            return NaN;
        }
        while(j < maxJ && image.get(j, centerI) && stateCount[4] < maxCount){
            stateCount[4]++;
            j++;
        }
        if (stateCount[4] >= maxCount) {
            return NaN;
        }
        // If we found a finder-pattern-like section, but its size is significantly different than
        // the original, assume it's a false positive
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];
        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= originalStateCountTotal) {
            return NaN;
        }
        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, j) : NaN;
    };
    /**
     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will
     * cross check with a vertical scan, and if successful, will, ah, cross-cross-check
     * with another horizontal scan. This is needed primarily to locate the real horizontal
     * center of the pattern in cases of extreme skew.
     * And then we cross-cross-cross check with another diagonal scan.</p>
     *
     * <p>If that succeeds the finder pattern location is added to a list that tracks
     * the number of times each location has been nearly-matched as a finder pattern.
     * Each additional find is more evidence that the location is in fact a finder
     * pattern center
     *
     * @param stateCount reading state module counts from horizontal scan
     * @param i row where finder pattern may be found
     * @param j end of possible finder pattern in row
     * @param pureBarcode true if in "pure barcode" mode
     * @return true if a finder pattern candidate was found this time
     */ FinderPatternFinder.prototype.handlePossibleCenter = function(stateCount, i /*int*/ , j /*int*/ , pureBarcode) {
        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];
        var centerJ = FinderPatternFinder.centerFromEnd(stateCount, j);
        var centerI = this.crossCheckVertical(i, /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal);
        if (!isNaN(centerI)) {
            // Re-cross check
            centerJ = this.crossCheckHorizontal(/*(int) */ Math.floor(centerJ), /*(int) */ Math.floor(centerI), stateCount[2], stateCountTotal);
            if (!isNaN(centerJ) && (!pureBarcode || this.crossCheckDiagonal(/*(int) */ Math.floor(centerI), /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal))) {
                var estimatedModuleSize = stateCountTotal / 7.0;
                var found = false;
                var possibleCenters = this.possibleCenters;
                for(var index = 0, length_1 = possibleCenters.length; index < length_1; index++){
                    var center = possibleCenters[index];
                    // Look for about the same center and module size:
                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {
                        possibleCenters[index] = center.combineEstimate(centerI, centerJ, estimatedModuleSize);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    var point = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPattern$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerJ, centerI, estimatedModuleSize);
                    possibleCenters.push(point);
                    if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {
                        this.resultPointCallback.foundPossibleResultPoint(point);
                    }
                }
                return true;
            }
        }
        return false;
    };
    /**
     * @return number of rows we could safely skip during scanning, based on the first
     *         two finder patterns that have been located. In some cases their position will
     *         allow us to infer that the third pattern must lie below a certain point farther
     *         down in the image.
     */ FinderPatternFinder.prototype.findRowSkip = function() {
        var e_1, _a;
        var max = this.possibleCenters.length;
        if (max <= 1) {
            return 0;
        }
        var firstConfirmedCenter = null;
        try {
            for(var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()){
                var center = _c.value;
                if (center.getCount() >= FinderPatternFinder.CENTER_QUORUM) {
                    if (firstConfirmedCenter == null) {
                        firstConfirmedCenter = center;
                    } else {
                        // We have two confirmed centers
                        // How far down can we skip before resuming looking for the next
                        // pattern? In the worst case, only the difference between the
                        // difference in the x / y coordinates of the two centers.
                        // This is the case where you find top left last.
                        this.hasSkipped = true;
                        return /*(int) */ Math.floor((Math.abs(firstConfirmedCenter.getX() - center.getX()) - Math.abs(firstConfirmedCenter.getY() - center.getY())) / 2);
                    }
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return 0;
    };
    /**
     * @return true iff we have found at least 3 finder patterns that have been detected
     *         at least {@link #CENTER_QUORUM} times each, and, the estimated module size of the
     *         candidates is "pretty similar"
     */ FinderPatternFinder.prototype.haveMultiplyConfirmedCenters = function() {
        var e_2, _a, e_3, _b;
        var confirmedCount = 0;
        var totalModuleSize = 0.0;
        var max = this.possibleCenters.length;
        try {
            for(var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()){
                var pattern = _d.value;
                if (pattern.getCount() >= FinderPatternFinder.CENTER_QUORUM) {
                    confirmedCount++;
                    totalModuleSize += pattern.getEstimatedModuleSize();
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        if (confirmedCount < 3) {
            return false;
        }
        // OK, we have at least 3 confirmed centers, but, it's possible that one is a "false positive"
        // and that we need to keep looking. We detect this by asking if the estimated module sizes
        // vary too much. We arbitrarily say that when the total deviation from average exceeds
        // 5% of the total module size estimates, it's too much.
        var average = totalModuleSize / max;
        var totalDeviation = 0.0;
        try {
            for(var _e = __values(this.possibleCenters), _f = _e.next(); !_f.done; _f = _e.next()){
                var pattern = _f.value;
                totalDeviation += Math.abs(pattern.getEstimatedModuleSize() - average);
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
        return totalDeviation <= 0.05 * totalModuleSize;
    };
    /**
     * @return the 3 best {@link FinderPattern}s from our list of candidates. The "best" are
     *         those that have been detected at least {@link #CENTER_QUORUM} times, and whose module
     *         size differs from the average among those patterns the least
     * @throws NotFoundException if 3 such finder patterns do not exist
     */ FinderPatternFinder.prototype.selectBestPatterns = function() {
        var e_4, _a, e_5, _b;
        var startSize = this.possibleCenters.length;
        if (startSize < 3) {
            // Couldn't find enough finder patterns
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var possibleCenters = this.possibleCenters;
        var average;
        // Filter outlier possibilities whose module size is too different
        if (startSize > 3) {
            // But we can only afford to do so if we have at least 4 possibilities to choose from
            var totalModuleSize = 0.0;
            var square = 0.0;
            try {
                for(var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()){
                    var center = _d.value;
                    var size = center.getEstimatedModuleSize();
                    totalModuleSize += size;
                    square += size * size;
                }
            } catch (e_4_1) {
                e_4 = {
                    error: e_4_1
                };
            } finally{
                try {
                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
                } finally{
                    if (e_4) throw e_4.error;
                }
            }
            average = totalModuleSize / startSize;
            var stdDev = Math.sqrt(square / startSize - average * average);
            possibleCenters.sort(/**
             * <p>Orders by furthest from average</p>
             */ // FurthestFromAverageComparator implements Comparator<FinderPattern>
            function(center1, center2) {
                var dA = Math.abs(center2.getEstimatedModuleSize() - average);
                var dB = Math.abs(center1.getEstimatedModuleSize() - average);
                return dA < dB ? -1 : dA > dB ? 1 : 0;
            });
            var limit = Math.max(0.2 * average, stdDev);
            for(var i = 0; i < possibleCenters.length && possibleCenters.length > 3; i++){
                var pattern = possibleCenters[i];
                if (Math.abs(pattern.getEstimatedModuleSize() - average) > limit) {
                    possibleCenters.splice(i, 1);
                    i--;
                }
            }
        }
        if (possibleCenters.length > 3) {
            // Throw away all but those first size candidate points we found.
            var totalModuleSize = 0.0;
            try {
                for(var possibleCenters_1 = __values(possibleCenters), possibleCenters_1_1 = possibleCenters_1.next(); !possibleCenters_1_1.done; possibleCenters_1_1 = possibleCenters_1.next()){
                    var possibleCenter = possibleCenters_1_1.value;
                    totalModuleSize += possibleCenter.getEstimatedModuleSize();
                }
            } catch (e_5_1) {
                e_5 = {
                    error: e_5_1
                };
            } finally{
                try {
                    if (possibleCenters_1_1 && !possibleCenters_1_1.done && (_b = possibleCenters_1.return)) _b.call(possibleCenters_1);
                } finally{
                    if (e_5) throw e_5.error;
                }
            }
            average = totalModuleSize / possibleCenters.length;
            possibleCenters.sort(/**
             * <p>Orders by {@link FinderPattern#getCount()}, descending.</p>
             */ // CenterComparator implements Comparator<FinderPattern>
            function(center1, center2) {
                if (center2.getCount() === center1.getCount()) {
                    var dA = Math.abs(center2.getEstimatedModuleSize() - average);
                    var dB = Math.abs(center1.getEstimatedModuleSize() - average);
                    return dA < dB ? 1 : dA > dB ? -1 : 0;
                } else {
                    return center2.getCount() - center1.getCount();
                }
            });
            possibleCenters.splice(3); // this is not realy necessary as we only return first 3 anyway
        }
        return [
            possibleCenters[0],
            possibleCenters[1],
            possibleCenters[2]
        ];
    };
    FinderPatternFinder.CENTER_QUORUM = 2;
    FinderPatternFinder.MIN_SKIP = 3; // 1 pixel/module times 3 modules/center
    FinderPatternFinder.MAX_MODULES = 57; // support up to version 10 for mobile clients
    return FinderPatternFinder;
}();
const __TURBOPACK__default__export__ = FinderPatternFinder;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/detector/Detector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-client] (ecmascript)");
// import GridSampler from '../../common/GridSampler';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$AlignmentPatternFinder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPatternFinder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPatternFinder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternFinder.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
/*import java.util.Map;*/ /**
 * <p>Encapsulates logic that can detect a QR Code in an image, even if the QR Code
 * is rotated or skewed, or partially obscured.</p>
 *
 * <AUTHOR> Owen
 */ var Detector = function() {
    function Detector(image) {
        this.image = image;
    }
    Detector.prototype.getImage = function() {
        return this.image;
    };
    Detector.prototype.getResultPointCallback = function() {
        return this.resultPointCallback;
    };
    /**
     * <p>Detects a QR Code in an image.</p>
     *
     * @return {@link DetectorResult} encapsulating results of detecting a QR Code
     * @throws NotFoundException if QR Code cannot be found
     * @throws FormatException if a QR Code cannot be decoded
     */ // public detect(): DetectorResult /*throws NotFoundException, FormatException*/ {
    //   return detect(null)
    // }
    /**
     * <p>Detects a QR Code in an image.</p>
     *
     * @param hints optional hints to detector
     * @return {@link DetectorResult} encapsulating results of detecting a QR Code
     * @throws NotFoundException if QR Code cannot be found
     * @throws FormatException if a QR Code cannot be decoded
     */ Detector.prototype.detect = function(hints) {
        this.resultPointCallback = hints === null || hints === undefined ? null : /*(ResultPointCallback) */ hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK);
        var finder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$FinderPatternFinder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.image, this.resultPointCallback);
        var info = finder.find(hints);
        return this.processFinderPatternInfo(info);
    };
    Detector.prototype.processFinderPatternInfo = function(info) {
        var topLeft = info.getTopLeft();
        var topRight = info.getTopRight();
        var bottomLeft = info.getBottomLeft();
        var moduleSize = this.calculateModuleSize(topLeft, topRight, bottomLeft);
        if (moduleSize < 1.0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('No pattern found in proccess finder.');
        }
        var dimension = Detector.computeDimension(topLeft, topRight, bottomLeft, moduleSize);
        var provisionalVersion = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getProvisionalVersionForDimension(dimension);
        var modulesBetweenFPCenters = provisionalVersion.getDimensionForVersion() - 7;
        var alignmentPattern = null;
        // Anything above version 1 has an alignment pattern
        if (provisionalVersion.getAlignmentPatternCenters().length > 0) {
            // Guess where a "bottom right" finder pattern would have been
            var bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();
            var bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();
            // Estimate that alignment pattern is closer by 3 modules
            // from "bottom right" to known top left location
            var correctionToTopLeft = 1.0 - 3.0 / modulesBetweenFPCenters;
            var estAlignmentX = /*(int) */ Math.floor(topLeft.getX() + correctionToTopLeft * (bottomRightX - topLeft.getX()));
            var estAlignmentY = /*(int) */ Math.floor(topLeft.getY() + correctionToTopLeft * (bottomRightY - topLeft.getY()));
            // Kind of arbitrary -- expand search radius before giving up
            for(var i = 4; i <= 16; i <<= 1){
                try {
                    alignmentPattern = this.findAlignmentInRegion(moduleSize, estAlignmentX, estAlignmentY, i);
                    break;
                } catch (re /*NotFoundException*/ ) {
                    if (!(re instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])) {
                        throw re;
                    }
                // try next round
                }
            }
        // If we didn't find alignment pattern... well try anyway without it
        }
        var transform = Detector.createTransform(topLeft, topRight, bottomLeft, alignmentPattern, dimension);
        var bits = Detector.sampleGrid(this.image, transform, dimension);
        var points;
        if (alignmentPattern === null) {
            points = [
                bottomLeft,
                topLeft,
                topRight
            ];
        } else {
            points = [
                bottomLeft,
                topLeft,
                topRight,
                alignmentPattern
            ];
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](bits, points);
    };
    Detector.createTransform = function(topLeft, topRight, bottomLeft, alignmentPattern, dimension /*int*/ ) {
        var dimMinusThree = dimension - 3.5;
        var bottomRightX; /*float*/ 
        var bottomRightY; /*float*/ 
        var sourceBottomRightX; /*float*/ 
        var sourceBottomRightY; /*float*/ 
        if (alignmentPattern !== null) {
            bottomRightX = alignmentPattern.getX();
            bottomRightY = alignmentPattern.getY();
            sourceBottomRightX = dimMinusThree - 3.0;
            sourceBottomRightY = sourceBottomRightX;
        } else {
            // Don't have an alignment pattern, just make up the bottom-right point
            bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();
            bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();
            sourceBottomRightX = dimMinusThree;
            sourceBottomRightY = dimMinusThree;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].quadrilateralToQuadrilateral(3.5, 3.5, dimMinusThree, 3.5, sourceBottomRightX, sourceBottomRightY, 3.5, dimMinusThree, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRightX, bottomRightY, bottomLeft.getX(), bottomLeft.getY());
    };
    Detector.sampleGrid = function(image, transform, dimension /*int*/ ) {
        var sampler = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getInstance();
        return sampler.sampleGridWithTransform(image, dimension, dimension, transform);
    };
    /**
     * <p>Computes the dimension (number of modules on a size) of the QR Code based on the position
     * of the finder patterns and estimated module size.</p>
     */ Detector.computeDimension = function(topLeft, topRight, bottomLeft, moduleSize /*float*/ ) {
        var tltrCentersDimension = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(topLeft, topRight) / moduleSize);
        var tlblCentersDimension = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(topLeft, bottomLeft) / moduleSize);
        var dimension = Math.floor((tltrCentersDimension + tlblCentersDimension) / 2) + 7;
        switch(dimension & 0x03){
            case 0:
                dimension++;
                break;
            // 1? do nothing
            case 2:
                dimension--;
                break;
            case 3:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Dimensions could be not found.');
        }
        return dimension;
    };
    /**
     * <p>Computes an average estimated module size based on estimated derived from the positions
     * of the three finder patterns.</p>
     *
     * @param topLeft detected top-left finder pattern center
     * @param topRight detected top-right finder pattern center
     * @param bottomLeft detected bottom-left finder pattern center
     * @return estimated module size
     */ Detector.prototype.calculateModuleSize = function(topLeft, topRight, bottomLeft) {
        // Take the average
        return (this.calculateModuleSizeOneWay(topLeft, topRight) + this.calculateModuleSizeOneWay(topLeft, bottomLeft)) / 2.0;
    };
    /**
     * <p>Estimates module size based on two finder patterns -- it uses
     * {@link #sizeOfBlackWhiteBlackRunBothWays(int, int, int, int)} to figure the
     * width of each, measuring along the axis between their centers.</p>
     */ Detector.prototype.calculateModuleSizeOneWay = function(pattern, otherPattern) {
        var moduleSizeEst1 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(pattern.getX()), /*(int) */ Math.floor(pattern.getY()), /*(int) */ Math.floor(otherPattern.getX()), /*(int) */ Math.floor(otherPattern.getY()));
        var moduleSizeEst2 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(otherPattern.getX()), /*(int) */ Math.floor(otherPattern.getY()), /*(int) */ Math.floor(pattern.getX()), /*(int) */ Math.floor(pattern.getY()));
        if (isNaN(moduleSizeEst1)) {
            return moduleSizeEst2 / 7.0;
        }
        if (isNaN(moduleSizeEst2)) {
            return moduleSizeEst1 / 7.0;
        }
        // Average them, and divide by 7 since we've counted the width of 3 black modules,
        // and 1 white and 1 black module on either side. Ergo, divide sum by 14.
        return (moduleSizeEst1 + moduleSizeEst2) / 14.0;
    };
    /**
     * See {@link #sizeOfBlackWhiteBlackRun(int, int, int, int)}; computes the total width of
     * a finder pattern by looking for a black-white-black run from the center in the direction
     * of another point (another finder pattern center), and in the opposite direction too.
     */ Detector.prototype.sizeOfBlackWhiteBlackRunBothWays = function(fromX /*int*/ , fromY /*int*/ , toX /*int*/ , toY /*int*/ ) {
        var result = this.sizeOfBlackWhiteBlackRun(fromX, fromY, toX, toY);
        // Now count other way -- don't run off image though of course
        var scale = 1.0;
        var otherToX = fromX - (toX - fromX);
        if (otherToX < 0) {
            scale = fromX / /*(float) */ (fromX - otherToX);
            otherToX = 0;
        } else if (otherToX >= this.image.getWidth()) {
            scale = (this.image.getWidth() - 1 - fromX) / /*(float) */ (otherToX - fromX);
            otherToX = this.image.getWidth() - 1;
        }
        var otherToY = /*(int) */ Math.floor(fromY - (toY - fromY) * scale);
        scale = 1.0;
        if (otherToY < 0) {
            scale = fromY / /*(float) */ (fromY - otherToY);
            otherToY = 0;
        } else if (otherToY >= this.image.getHeight()) {
            scale = (this.image.getHeight() - 1 - fromY) / /*(float) */ (otherToY - fromY);
            otherToY = this.image.getHeight() - 1;
        }
        otherToX = /*(int) */ Math.floor(fromX + (otherToX - fromX) * scale);
        result += this.sizeOfBlackWhiteBlackRun(fromX, fromY, otherToX, otherToY);
        // Middle pixel is double-counted this way; subtract 1
        return result - 1.0;
    };
    /**
     * <p>This method traces a line from a point in the image, in the direction towards another point.
     * It begins in a black region, and keeps going until it finds white, then black, then white again.
     * It reports the distance from the start to this point.</p>
     *
     * <p>This is used when figuring out how wide a finder pattern is, when the finder pattern
     * may be skewed or rotated.</p>
     */ Detector.prototype.sizeOfBlackWhiteBlackRun = function(fromX /*int*/ , fromY /*int*/ , toX /*int*/ , toY /*int*/ ) {
        // Mild variant of Bresenham's algorithm
        // see http://en.wikipedia.org/wiki/Bresenham's_line_algorithm
        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);
        if (steep) {
            var temp = fromX;
            fromX = fromY;
            fromY = temp;
            temp = toX;
            toX = toY;
            toY = temp;
        }
        var dx = Math.abs(toX - fromX);
        var dy = Math.abs(toY - fromY);
        var error = -dx / 2;
        var xstep = fromX < toX ? 1 : -1;
        var ystep = fromY < toY ? 1 : -1;
        // In black pixels, looking for white, first or second time.
        var state = 0;
        // Loop up until x == toX, but not beyond
        var xLimit = toX + xstep;
        for(var x = fromX, y = fromY; x !== xLimit; x += xstep){
            var realX = steep ? y : x;
            var realY = steep ? x : y;
            // Does current pixel mean we have moved white to black or vice versa?
            // Scanning black in state 0,2 and white in state 1, so if we find the wrong
            // color, advance to next state or end if we are in state 2 already
            if (state === 1 === this.image.get(realX, realY)) {
                if (state === 2) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(x, y, fromX, fromY);
                }
                state++;
            }
            error += dy;
            if (error > 0) {
                if (y === toY) {
                    break;
                }
                y += ystep;
                error -= dx;
            }
        }
        // Found black-white-black; give the benefit of the doubt that the next pixel outside the image
        // is "white" so this last point at (toX+xStep,toY) is the right ending. This is really a
        // small approximation; (toX+xStep,toY+yStep) might be really correct. Ignore this.
        if (state === 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(toX + xstep, toY, fromX, fromY);
        }
        // else we didn't find even black-white-black; no estimate is really possible
        return NaN;
    };
    /**
     * <p>Attempts to locate an alignment pattern in a limited region of the image, which is
     * guessed to contain it. This method uses {@link AlignmentPattern}.</p>
     *
     * @param overallEstModuleSize estimated module size so far
     * @param estAlignmentX x coordinate of center of area probably containing alignment pattern
     * @param estAlignmentY y coordinate of above
     * @param allowanceFactor number of pixels in all directions to search from the center
     * @return {@link AlignmentPattern} if found, or null otherwise
     * @throws NotFoundException if an unexpected error occurs during detection
     */ Detector.prototype.findAlignmentInRegion = function(overallEstModuleSize /*float*/ , estAlignmentX /*int*/ , estAlignmentY /*int*/ , allowanceFactor /*float*/ ) {
        // Look for an alignment pattern (3 modules in size) around where it
        // should be
        var allowance = /*(int) */ Math.floor(allowanceFactor * overallEstModuleSize);
        var alignmentAreaLeftX = Math.max(0, estAlignmentX - allowance);
        var alignmentAreaRightX = Math.min(this.image.getWidth() - 1, estAlignmentX + allowance);
        if (alignmentAreaRightX - alignmentAreaLeftX < overallEstModuleSize * 3) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Alignment top exceeds estimated module size.');
        }
        var alignmentAreaTopY = Math.max(0, estAlignmentY - allowance);
        var alignmentAreaBottomY = Math.min(this.image.getHeight() - 1, estAlignmentY + allowance);
        if (alignmentAreaBottomY - alignmentAreaTopY < overallEstModuleSize * 3) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Alignment bottom exceeds estimated module size.');
        }
        var alignmentFinder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$AlignmentPatternFinder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.image, alignmentAreaLeftX, alignmentAreaTopY, alignmentAreaRightX - alignmentAreaLeftX, alignmentAreaBottomY - alignmentAreaTopY, overallEstModuleSize, this.resultPointCallback);
        return alignmentFinder.find();
    };
    return Detector;
}();
const __TURBOPACK__default__export__ = Detector;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Result.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-client] (ecmascript)");
// import DetectorResult from '../common/DetectorResult';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Decoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$QRCodeDecoderMetaData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/QRCodeDecoderMetaData.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$Detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/detector/Detector.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
/*import java.util.List;*/ /*import java.util.Map;*/ /**
 * This implementation can detect and decode QR Codes in an image.
 *
 * <AUTHOR> Owen
 */ var QRCodeReader = function() {
    function QRCodeReader() {
        this.decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    }
    QRCodeReader.prototype.getDecoder = function() {
        return this.decoder;
    };
    /**
     * Locates and decodes a QR code in an image.
     *
     * @return a representing: string the content encoded by the QR code
     * @throws NotFoundException if a QR code cannot be found
     * @throws FormatException if a QR code cannot be decoded
     * @throws ChecksumException if error correction fails
     */ /*@Override*/ // public decode(image: BinaryBitmap): Result /*throws NotFoundException, ChecksumException, FormatException */ {
    //   return this.decode(image, null)
    // }
    /*@Override*/ QRCodeReader.prototype.decode = function(image, hints) {
        var decoderResult;
        var points;
        if (hints !== undefined && hints !== null && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].PURE_BARCODE)) {
            var bits = QRCodeReader.extractPureBits(image.getBlackMatrix());
            decoderResult = this.decoder.decodeBitMatrix(bits, hints);
            points = QRCodeReader.NO_POINTS;
        } else {
            var detectorResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$detector$2f$Detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](image.getBlackMatrix()).detect(hints);
            decoderResult = this.decoder.decodeBitMatrix(detectorResult.getBits(), hints);
            points = detectorResult.getPoints();
        }
        // If the code was mirrored: swap the bottom-left and the top-right points.
        if (decoderResult.getOther() instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$QRCodeDecoderMetaData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]) {
            decoderResult.getOther().applyMirroredCorrection(points);
        }
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](decoderResult.getText(), decoderResult.getRawBytes(), undefined, points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_CODE, undefined);
        var byteSegments = decoderResult.getByteSegments();
        if (byteSegments !== null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE_SEGMENTS, byteSegments);
        }
        var ecLevel = decoderResult.getECLevel();
        if (ecLevel !== null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION_LEVEL, ecLevel);
        }
        if (decoderResult.hasStructuredAppend()) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].STRUCTURED_APPEND_SEQUENCE, decoderResult.getStructuredAppendSequenceNumber());
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].STRUCTURED_APPEND_PARITY, decoderResult.getStructuredAppendParity());
        }
        return result;
    };
    /*@Override*/ QRCodeReader.prototype.reset = function() {
    // do nothing
    };
    /**
     * This method detects a code in a "pure" image -- that is, pure monochrome image
     * which contains only an unrotated, unskewed, image of a code, with some white border
     * around it. This is a specialized method that works exceptionally fast in this special
     * case.
     *
     * @see com.google.zxing.datamatrix.DataMatrixReader#extractPureBits(BitMatrix)
     */ QRCodeReader.extractPureBits = function(image) {
        var leftTopBlack = image.getTopLeftOnBit();
        var rightBottomBlack = image.getBottomRightOnBit();
        if (leftTopBlack === null || rightBottomBlack === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var moduleSize = this.moduleSize(leftTopBlack, image);
        var top = leftTopBlack[1];
        var bottom = rightBottomBlack[1];
        var left = leftTopBlack[0];
        var right = rightBottomBlack[0];
        // Sanity check!
        if (left >= right || top >= bottom) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (bottom - top !== right - left) {
            // Special case, where bottom-right module wasn't black so we found something else in the last row
            // Assume it's a square, so use height as the width
            right = left + (bottom - top);
            if (right >= image.getWidth()) {
                // Abort if that would not make sense -- off image
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
        }
        var matrixWidth = Math.round((right - left + 1) / moduleSize);
        var matrixHeight = Math.round((bottom - top + 1) / moduleSize);
        if (matrixWidth <= 0 || matrixHeight <= 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (matrixHeight !== matrixWidth) {
            // Only possibly decode square regions
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Push in the "border" by half the module width so that we start
        // sampling in the middle of the module. Just in case the image is a
        // little off, this will help recover.
        var nudge = /*(int) */ Math.floor(moduleSize / 2.0);
        top += nudge;
        left += nudge;
        // But careful that this does not sample off the edge
        // "right" is the farthest-right valid pixel location -- right+1 is not necessarily
        // This is positive by how much the inner x loop below would be too large
        var nudgedTooFarRight = left + /*(int) */ Math.floor((matrixWidth - 1) * moduleSize) - right;
        if (nudgedTooFarRight > 0) {
            if (nudgedTooFarRight > nudge) {
                // Neither way fits; abort
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            left -= nudgedTooFarRight;
        }
        // See logic above
        var nudgedTooFarDown = top + /*(int) */ Math.floor((matrixHeight - 1) * moduleSize) - bottom;
        if (nudgedTooFarDown > 0) {
            if (nudgedTooFarDown > nudge) {
                // Neither way fits; abort
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            top -= nudgedTooFarDown;
        }
        // Now just read off the bits
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](matrixWidth, matrixHeight);
        for(var y = 0; y < matrixHeight; y++){
            var iOffset = top + /*(int) */ Math.floor(y * moduleSize);
            for(var x = 0; x < matrixWidth; x++){
                if (image.get(left + /*(int) */ Math.floor(x * moduleSize), iOffset)) {
                    bits.set(x, y);
                }
            }
        }
        return bits;
    };
    QRCodeReader.moduleSize = function(leftTopBlack, image) {
        var height = image.getHeight();
        var width = image.getWidth();
        var x = leftTopBlack[0];
        var y = leftTopBlack[1];
        var inBlack = true;
        var transitions = 0;
        while(x < width && y < height){
            if (inBlack !== image.get(x, y)) {
                if (++transitions === 5) {
                    break;
                }
                inBlack = !inBlack;
            }
            x++;
            y++;
        }
        if (x === width || y === height) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return (x - leftTopBlack[0]) / 7.0;
    };
    QRCodeReader.NO_POINTS = new Array();
    return QRCodeReader;
}();
const __TURBOPACK__default__export__ = QRCodeReader;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
/**
 * <AUTHOR> Takabayashi
 * <AUTHOR> Switkin
 * <AUTHOR> Owen
 */ var MaskUtil = function() {
    function MaskUtil() {
    // do nothing
    }
    /**
     * Apply mask penalty rule 1 and return the penalty. Find repetitive cells with the same color and
     * give penalty to them. Example: 00000 or 11111.
     */ MaskUtil.applyMaskPenaltyRule1 = function(matrix) {
        return MaskUtil.applyMaskPenaltyRule1Internal(matrix, true) + MaskUtil.applyMaskPenaltyRule1Internal(matrix, false);
    };
    /**
     * Apply mask penalty rule 2 and return the penalty. Find 2x2 blocks with the same color and give
     * penalty to them. This is actually equivalent to the spec's rule, which is to find MxN blocks and give a
     * penalty proportional to (M-1)x(N-1), because this is the number of 2x2 blocks inside such a block.
     */ MaskUtil.applyMaskPenaltyRule2 = function(matrix) {
        var penalty = 0;
        var array = matrix.getArray();
        var width = matrix.getWidth();
        var height = matrix.getHeight();
        for(var y = 0; y < height - 1; y++){
            var arrayY = array[y];
            for(var x = 0; x < width - 1; x++){
                var value = arrayY[x];
                if (value === arrayY[x + 1] && value === array[y + 1][x] && value === array[y + 1][x + 1]) {
                    penalty++;
                }
            }
        }
        return MaskUtil.N2 * penalty;
    };
    /**
     * Apply mask penalty rule 3 and return the penalty. Find consecutive runs of 1:1:3:1:1:4
     * starting with black, or 4:1:1:3:1:1 starting with white, and give penalty to them.  If we
     * find patterns like 000010111010000, we give penalty once.
     */ MaskUtil.applyMaskPenaltyRule3 = function(matrix) {
        var numPenalties = 0;
        var array = matrix.getArray();
        var width = matrix.getWidth();
        var height = matrix.getHeight();
        for(var y = 0; y < height; y++){
            for(var x = 0; x < width; x++){
                var arrayY = array[y]; // We can at least optimize this access
                if (x + 6 < width && arrayY[x] === 1 && arrayY[x + 1] === 0 && arrayY[x + 2] === 1 && arrayY[x + 3] === 1 && arrayY[x + 4] === 1 && arrayY[x + 5] === 0 && arrayY[x + 6] === 1 && (MaskUtil.isWhiteHorizontal(arrayY, x - 4, x) || MaskUtil.isWhiteHorizontal(arrayY, x + 7, x + 11))) {
                    numPenalties++;
                }
                if (y + 6 < height && array[y][x] === 1 && array[y + 1][x] === 0 && array[y + 2][x] === 1 && array[y + 3][x] === 1 && array[y + 4][x] === 1 && array[y + 5][x] === 0 && array[y + 6][x] === 1 && (MaskUtil.isWhiteVertical(array, x, y - 4, y) || MaskUtil.isWhiteVertical(array, x, y + 7, y + 11))) {
                    numPenalties++;
                }
            }
        }
        return numPenalties * MaskUtil.N3;
    };
    MaskUtil.isWhiteHorizontal = function(rowArray, from /*int*/ , to /*int*/ ) {
        from = Math.max(from, 0);
        to = Math.min(to, rowArray.length);
        for(var i = from; i < to; i++){
            if (rowArray[i] === 1) {
                return false;
            }
        }
        return true;
    };
    MaskUtil.isWhiteVertical = function(array, col /*int*/ , from /*int*/ , to /*int*/ ) {
        from = Math.max(from, 0);
        to = Math.min(to, array.length);
        for(var i = from; i < to; i++){
            if (array[i][col] === 1) {
                return false;
            }
        }
        return true;
    };
    /**
     * Apply mask penalty rule 4 and return the penalty. Calculate the ratio of dark cells and give
     * penalty if the ratio is far from 50%. It gives 10 penalty for 5% distance.
     */ MaskUtil.applyMaskPenaltyRule4 = function(matrix) {
        var numDarkCells = 0;
        var array = matrix.getArray();
        var width = matrix.getWidth();
        var height = matrix.getHeight();
        for(var y = 0; y < height; y++){
            var arrayY = array[y];
            for(var x = 0; x < width; x++){
                if (arrayY[x] === 1) {
                    numDarkCells++;
                }
            }
        }
        var numTotalCells = matrix.getHeight() * matrix.getWidth();
        var fivePercentVariances = Math.floor(Math.abs(numDarkCells * 2 - numTotalCells) * 10 / numTotalCells);
        return fivePercentVariances * MaskUtil.N4;
    };
    /**
     * Return the mask bit for "getMaskPattern" at "x" and "y". See 8.8 of JISX0510:2004 for mask
     * pattern conditions.
     */ MaskUtil.getDataMaskBit = function(maskPattern /*int*/ , x /*int*/ , y /*int*/ ) {
        var intermediate; /*int*/ 
        var temp; /*int*/ 
        switch(maskPattern){
            case 0:
                intermediate = y + x & 0x1;
                break;
            case 1:
                intermediate = y & 0x1;
                break;
            case 2:
                intermediate = x % 3;
                break;
            case 3:
                intermediate = (y + x) % 3;
                break;
            case 4:
                intermediate = Math.floor(y / 2) + Math.floor(x / 3) & 0x1;
                break;
            case 5:
                temp = y * x;
                intermediate = (temp & 0x1) + temp % 3;
                break;
            case 6:
                temp = y * x;
                intermediate = (temp & 0x1) + temp % 3 & 0x1;
                break;
            case 7:
                temp = y * x;
                intermediate = temp % 3 + (y + x & 0x1) & 0x1;
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Invalid mask pattern: ' + maskPattern);
        }
        return intermediate === 0;
    };
    /**
     * Helper function for applyMaskPenaltyRule1. We need this for doing this calculation in both
     * vertical and horizontal orders respectively.
     */ MaskUtil.applyMaskPenaltyRule1Internal = function(matrix, isHorizontal) {
        var penalty = 0;
        var iLimit = isHorizontal ? matrix.getHeight() : matrix.getWidth();
        var jLimit = isHorizontal ? matrix.getWidth() : matrix.getHeight();
        var array = matrix.getArray();
        for(var i = 0; i < iLimit; i++){
            var numSameBitCells = 0;
            var prevBit = -1;
            for(var j = 0; j < jLimit; j++){
                var bit = isHorizontal ? array[i][j] : array[j][i];
                if (bit === prevBit) {
                    numSameBitCells++;
                } else {
                    if (numSameBitCells >= 5) {
                        penalty += MaskUtil.N1 + (numSameBitCells - 5);
                    }
                    numSameBitCells = 1; // Include the cell itself.
                    prevBit = bit;
                }
            }
            if (numSameBitCells >= 5) {
                penalty += MaskUtil.N1 + (numSameBitCells - 5);
            }
        }
        return penalty;
    };
    // Penalty weights from section *******
    MaskUtil.N1 = 3;
    MaskUtil.N2 = 3;
    MaskUtil.N3 = 40;
    MaskUtil.N4 = 10;
    return MaskUtil;
}();
const __TURBOPACK__default__export__ = MaskUtil;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.encoder {*/ /*import java.util.Arrays;*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Arrays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
/**
 * JAVAPORT: The original code was a 2D array of ints, but since it only ever gets assigned
 * -1, 0, and 1, I'm going to use less memory and go with bytes.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var ByteMatrix = function() {
    function ByteMatrix(width /*int*/ , height /*int*/ ) {
        this.width = width;
        this.height = height;
        var bytes = new Array(height); // [height][width]
        for(var i = 0; i !== height; i++){
            bytes[i] = new Uint8Array(width);
        }
        this.bytes = bytes;
    }
    ByteMatrix.prototype.getHeight = function() {
        return this.height;
    };
    ByteMatrix.prototype.getWidth = function() {
        return this.width;
    };
    ByteMatrix.prototype.get = function(x /*int*/ , y /*int*/ ) {
        return this.bytes[y][x];
    };
    /**
     * @return an internal representation as bytes, in row-major order. array[y][x] represents point (x,y)
     */ ByteMatrix.prototype.getArray = function() {
        return this.bytes;
    };
    // TYPESCRIPTPORT: preffer to let two methods instead of override to avoid type comparison inside
    ByteMatrix.prototype.setNumber = function(x /*int*/ , y /*int*/ , value /*byte|int*/ ) {
        this.bytes[y][x] = value;
    };
    // public set(x: number /*int*/, y: number /*int*/, value: number /*int*/): void {
    //   bytes[y][x] = (byte) value
    // }
    ByteMatrix.prototype.setBoolean = function(x /*int*/ , y /*int*/ , value) {
        this.bytes[y][x] = value ? 1 : 0;
    };
    ByteMatrix.prototype.clear = function(value /*byte*/ ) {
        var e_1, _a;
        try {
            for(var _b = __values(this.bytes), _c = _b.next(); !_c.done; _c = _b.next()){
                var aByte = _c.value;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fill(aByte, value);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
    };
    ByteMatrix.prototype.equals = function(o) {
        if (!(o instanceof ByteMatrix)) {
            return false;
        }
        var other = o;
        if (this.width !== other.width) {
            return false;
        }
        if (this.height !== other.height) {
            return false;
        }
        for(var y = 0, height = this.height; y < height; ++y){
            var bytesY = this.bytes[y];
            var otherBytesY = other.bytes[y];
            for(var x = 0, width = this.width; x < width; ++x){
                if (bytesY[x] !== otherBytesY[x]) {
                    return false;
                }
            }
        }
        return true;
    };
    /*@Override*/ ByteMatrix.prototype.toString = function() {
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](); // (2 * width * height + 2)
        for(var y = 0, height = this.height; y < height; ++y){
            var bytesY = this.bytes[y];
            for(var x = 0, width = this.width; x < width; ++x){
                switch(bytesY[x]){
                    case 0:
                        result.append(' 0');
                        break;
                    case 1:
                        result.append(' 1');
                        break;
                    default:
                        result.append('  ');
                        break;
                }
            }
            result.append('\n');
        }
        return result.toString();
    };
    return ByteMatrix;
}();
const __TURBOPACK__default__export__ = ByteMatrix;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-client] (ecmascript)");
;
/**
 * <AUTHOR> (Satoru Takabayashi) - creator
 * <AUTHOR> (Daniel Switkin) - ported from C++
 */ var QRCode = function() {
    function QRCode() {
        this.maskPattern = -1;
    }
    QRCode.prototype.getMode = function() {
        return this.mode;
    };
    QRCode.prototype.getECLevel = function() {
        return this.ecLevel;
    };
    QRCode.prototype.getVersion = function() {
        return this.version;
    };
    QRCode.prototype.getMaskPattern = function() {
        return this.maskPattern;
    };
    QRCode.prototype.getMatrix = function() {
        return this.matrix;
    };
    /*@Override*/ QRCode.prototype.toString = function() {
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](); // (200)
        result.append('<<\n');
        result.append(' mode: ');
        result.append(this.mode ? this.mode.toString() : 'null');
        result.append('\n ecLevel: ');
        result.append(this.ecLevel ? this.ecLevel.toString() : 'null');
        result.append('\n version: ');
        result.append(this.version ? this.version.toString() : 'null');
        result.append('\n maskPattern: ');
        result.append(this.maskPattern.toString());
        if (this.matrix) {
            result.append('\n matrix:\n');
            result.append(this.matrix.toString());
        } else {
            result.append('\n matrix: null\n');
        }
        result.append('>>\n');
        return result.toString();
    };
    QRCode.prototype.setMode = function(value) {
        this.mode = value;
    };
    QRCode.prototype.setECLevel = function(value) {
        this.ecLevel = value;
    };
    QRCode.prototype.setVersion = function(version) {
        this.version = version;
    };
    QRCode.prototype.setMaskPattern = function(value /*int*/ ) {
        this.maskPattern = value;
    };
    QRCode.prototype.setMatrix = function(value) {
        this.matrix = value;
    };
    // Check if "mask_pattern" is valid.
    QRCode.isValidMaskPattern = function(maskPattern /*int*/ ) {
        return maskPattern >= 0 && maskPattern < QRCode.NUM_MASK_PATTERNS;
    };
    QRCode.NUM_MASK_PATTERNS = 8;
    return QRCode;
}();
const __TURBOPACK__default__export__ = QRCode;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MatrixUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode.encoder {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/WriterException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
;
;
;
;
;
/**
 * <AUTHOR> (Satoru Takabayashi) - creator
 * <AUTHOR> (Daniel Switkin) - ported from C++
 */ var MatrixUtil = function() {
    function MatrixUtil() {
    // do nothing
    }
    // Set all cells to -1 (TYPESCRIPTPORT: 255).  -1 (TYPESCRIPTPORT: 255) means that the cell is empty (not set yet).
    //
    // JAVAPORT: We shouldn't need to do this at all. The code should be rewritten to begin encoding
    // with the ByteMatrix initialized all to zero.
    MatrixUtil.clearMatrix = function(matrix) {
        // TYPESCRIPTPORT: we use UintArray se changed here from -1 to 255
        matrix.clear(/*(byte) */ /*-1*/ 255);
    };
    // Build 2D matrix of QR Code from "dataBits" with "ecLevel", "version" and "getMaskPattern". On
    // success, store the result in "matrix" and return true.
    MatrixUtil.buildMatrix = function(dataBits, ecLevel, version, maskPattern /*int*/ , matrix) {
        MatrixUtil.clearMatrix(matrix);
        MatrixUtil.embedBasicPatterns(version, matrix);
        // Type information appear with any version.
        MatrixUtil.embedTypeInfo(ecLevel, maskPattern, matrix);
        // Version info appear if version >= 7.
        MatrixUtil.maybeEmbedVersionInfo(version, matrix);
        // Data should be embedded at end.
        MatrixUtil.embedDataBits(dataBits, maskPattern, matrix);
    };
    // Embed basic patterns. On success, modify the matrix and return true.
    // The basic patterns are:
    // - Position detection patterns
    // - Timing patterns
    // - Dark dot at the left bottom corner
    // - Position adjustment patterns, if need be
    MatrixUtil.embedBasicPatterns = function(version, matrix) {
        // Let's get started with embedding big squares at corners.
        MatrixUtil.embedPositionDetectionPatternsAndSeparators(matrix);
        // Then, embed the dark dot at the left bottom corner.
        MatrixUtil.embedDarkDotAtLeftBottomCorner(matrix);
        // Position adjustment patterns appear if version >= 2.
        MatrixUtil.maybeEmbedPositionAdjustmentPatterns(version, matrix);
        // Timing patterns should be embedded after position adj. patterns.
        MatrixUtil.embedTimingPatterns(matrix);
    };
    // Embed type information. On success, modify the matrix.
    MatrixUtil.embedTypeInfo = function(ecLevel, maskPattern /*int*/ , matrix) {
        var typeInfoBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        MatrixUtil.makeTypeInfoBits(ecLevel, maskPattern, typeInfoBits);
        for(var i = 0, size = typeInfoBits.getSize(); i < size; ++i){
            // Place bits in LSB to MSB order.  LSB (least significant bit) is the last value in
            // "typeInfoBits".
            var bit = typeInfoBits.get(typeInfoBits.getSize() - 1 - i);
            // Type info bits at the left top corner. See 8.9 of JISX0510:2004 (p.46).
            var coordinates = MatrixUtil.TYPE_INFO_COORDINATES[i];
            var x1 = coordinates[0];
            var y1 = coordinates[1];
            matrix.setBoolean(x1, y1, bit);
            if (i < 8) {
                // Right top corner.
                var x2 = matrix.getWidth() - i - 1;
                var y2 = 8;
                matrix.setBoolean(x2, y2, bit);
            } else {
                // Left bottom corner.
                var x2 = 8;
                var y2 = matrix.getHeight() - 7 + (i - 8);
                matrix.setBoolean(x2, y2, bit);
            }
        }
    };
    // Embed version information if need be. On success, modify the matrix and return true.
    // See 8.10 of JISX0510:2004 (p.47) for how to embed version information.
    MatrixUtil.maybeEmbedVersionInfo = function(version, matrix) {
        if (version.getVersionNumber() < 7) {
            return; // Don't need version info.
        }
        var versionInfoBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        MatrixUtil.makeVersionInfoBits(version, versionInfoBits);
        var bitIndex = 6 * 3 - 1; // It will decrease from 17 to 0.
        for(var i = 0; i < 6; ++i){
            for(var j = 0; j < 3; ++j){
                // Place bits in LSB (least significant bit) to MSB order.
                var bit = versionInfoBits.get(bitIndex);
                bitIndex--;
                // Left bottom corner.
                matrix.setBoolean(i, matrix.getHeight() - 11 + j, bit);
                // Right bottom corner.
                matrix.setBoolean(matrix.getHeight() - 11 + j, i, bit);
            }
        }
    };
    // Embed "dataBits" using "getMaskPattern". On success, modify the matrix and return true.
    // For debugging purposes, it skips masking process if "getMaskPattern" is -1(TYPESCRIPTPORT: 255).
    // See 8.7 of JISX0510:2004 (p.38) for how to embed data bits.
    MatrixUtil.embedDataBits = function(dataBits, maskPattern /*int*/ , matrix) {
        var bitIndex = 0;
        var direction = -1;
        // Start from the right bottom cell.
        var x = matrix.getWidth() - 1;
        var y = matrix.getHeight() - 1;
        while(x > 0){
            // Skip the vertical timing pattern.
            if (x === 6) {
                x -= 1;
            }
            while(y >= 0 && y < matrix.getHeight()){
                for(var i = 0; i < 2; ++i){
                    var xx = x - i;
                    // Skip the cell if it's not empty.
                    if (!MatrixUtil.isEmpty(matrix.get(xx, y))) {
                        continue;
                    }
                    var bit = void 0;
                    if (bitIndex < dataBits.getSize()) {
                        bit = dataBits.get(bitIndex);
                        ++bitIndex;
                    } else {
                        // Padding bit. If there is no bit left, we'll fill the left cells with 0, as described
                        // in 8.4.9 of JISX0510:2004 (p. 24).
                        bit = false;
                    }
                    // Skip masking if mask_pattern is -1 (TYPESCRIPTPORT: 255).
                    if (maskPattern !== 255 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDataMaskBit(maskPattern, xx, y)) {
                        bit = !bit;
                    }
                    matrix.setBoolean(xx, y, bit);
                }
                y += direction;
            }
            direction = -direction; // Reverse the direction.
            y += direction;
            x -= 2; // Move to the left.
        }
        // All bits should be consumed.
        if (bitIndex !== dataBits.getSize()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Not all bits consumed: ' + bitIndex + '/' + dataBits.getSize());
        }
    };
    // Return the position of the most significant bit set (one: to) in the "value". The most
    // significant bit is position 32. If there is no bit set, return 0. Examples:
    // - findMSBSet(0) => 0
    // - findMSBSet(1) => 1
    // - findMSBSet(255) => 8
    MatrixUtil.findMSBSet = function(value /*int*/ ) {
        return 32 - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].numberOfLeadingZeros(value);
    };
    // Calculate BCH (Bose-Chaudhuri-Hocquenghem) code for "value" using polynomial "poly". The BCH
    // code is used for encoding type information and version information.
    // Example: Calculation of version information of 7.
    // f(x) is created from 7.
    //   - 7 = 000111 in 6 bits
    //   - f(x) = x^2 + x^1 + x^0
    // g(x) is given by the standard (p. 67)
    //   - g(x) = x^12 + x^11 + x^10 + x^9 + x^8 + x^5 + x^2 + 1
    // Multiply f(x) by x^(18 - 6)
    //   - f'(x) = f(x) * x^(18 - 6)
    //   - f'(x) = x^14 + x^13 + x^12
    // Calculate the remainder of f'(x) / g(x)
    //         x^2
    //         __________________________________________________
    //   g(x) )x^14 + x^13 + x^12
    //         x^14 + x^13 + x^12 + x^11 + x^10 + x^7 + x^4 + x^2
    //         --------------------------------------------------
    //                              x^11 + x^10 + x^7 + x^4 + x^2
    //
    // The remainder is x^11 + x^10 + x^7 + x^4 + x^2
    // Encode it in binary: 110010010100
    // The return value is 0xc94 (1100 1001 0100)
    //
    // Since all coefficients in the polynomials are 1 or 0, we can do the calculation by bit
    // operations. We don't care if coefficients are positive or negative.
    MatrixUtil.calculateBCHCode = function(value /*int*/ , poly /*int*/ ) {
        if (poly === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('0 polynomial');
        }
        // If poly is "1 1111 0010 0101" (version info poly), msbSetInPoly is 13. We'll subtract 1
        // from 13 to make it 12.
        var msbSetInPoly = MatrixUtil.findMSBSet(poly);
        value <<= msbSetInPoly - 1;
        // Do the division business using exclusive-or operations.
        while(MatrixUtil.findMSBSet(value) >= msbSetInPoly){
            value ^= poly << MatrixUtil.findMSBSet(value) - msbSetInPoly;
        }
        // Now the "value" is the remainder (i.e. the BCH code)
        return value;
    };
    // Make bit vector of type information. On success, store the result in "bits" and return true.
    // Encode error correction level and mask pattern. See 8.9 of
    // JISX0510:2004 (p.45) for details.
    MatrixUtil.makeTypeInfoBits = function(ecLevel, maskPattern /*int*/ , bits) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isValidMaskPattern(maskPattern)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Invalid mask pattern');
        }
        var typeInfo = ecLevel.getBits() << 3 | maskPattern;
        bits.appendBits(typeInfo, 5);
        var bchCode = MatrixUtil.calculateBCHCode(typeInfo, MatrixUtil.TYPE_INFO_POLY);
        bits.appendBits(bchCode, 10);
        var maskBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        maskBits.appendBits(MatrixUtil.TYPE_INFO_MASK_PATTERN, 15);
        bits.xor(maskBits);
        if (bits.getSize() !== 15) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('should not happen but we got: ' + bits.getSize());
        }
    };
    // Make bit vector of version information. On success, store the result in "bits" and return true.
    // See 8.10 of JISX0510:2004 (p.45) for details.
    MatrixUtil.makeVersionInfoBits = function(version, bits) {
        bits.appendBits(version.getVersionNumber(), 6);
        var bchCode = MatrixUtil.calculateBCHCode(version.getVersionNumber(), MatrixUtil.VERSION_INFO_POLY);
        bits.appendBits(bchCode, 12);
        if (bits.getSize() !== 18) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('should not happen but we got: ' + bits.getSize());
        }
    };
    // Check if "value" is empty.
    MatrixUtil.isEmpty = function(value /*int*/ ) {
        return value === 255; // -1
    };
    MatrixUtil.embedTimingPatterns = function(matrix) {
        // -8 is for skipping position detection patterns (7: size), and two horizontal/vertical
        // separation patterns (1: size). Thus, 8 = 7 + 1.
        for(var i = 8; i < matrix.getWidth() - 8; ++i){
            var bit = (i + 1) % 2;
            // Horizontal line.
            if (MatrixUtil.isEmpty(matrix.get(i, 6))) {
                matrix.setNumber(i, 6, bit);
            }
            // Vertical line.
            if (MatrixUtil.isEmpty(matrix.get(6, i))) {
                matrix.setNumber(6, i, bit);
            }
        }
    };
    // Embed the lonely dark dot at left bottom corner. JISX0510:2004 (p.46)
    MatrixUtil.embedDarkDotAtLeftBottomCorner = function(matrix) {
        if (matrix.get(8, matrix.getHeight() - 8) === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        matrix.setNumber(8, matrix.getHeight() - 8, 1);
    };
    MatrixUtil.embedHorizontalSeparationPattern = function(xStart /*int*/ , yStart /*int*/ , matrix) {
        for(var x = 0; x < 8; ++x){
            if (!MatrixUtil.isEmpty(matrix.get(xStart + x, yStart))) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            matrix.setNumber(xStart + x, yStart, 0);
        }
    };
    MatrixUtil.embedVerticalSeparationPattern = function(xStart /*int*/ , yStart /*int*/ , matrix) {
        for(var y = 0; y < 7; ++y){
            if (!MatrixUtil.isEmpty(matrix.get(xStart, yStart + y))) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            matrix.setNumber(xStart, yStart + y, 0);
        }
    };
    MatrixUtil.embedPositionAdjustmentPattern = function(xStart /*int*/ , yStart /*int*/ , matrix) {
        for(var y = 0; y < 5; ++y){
            var patternY = MatrixUtil.POSITION_ADJUSTMENT_PATTERN[y];
            for(var x = 0; x < 5; ++x){
                matrix.setNumber(xStart + x, yStart + y, patternY[x]);
            }
        }
    };
    MatrixUtil.embedPositionDetectionPattern = function(xStart /*int*/ , yStart /*int*/ , matrix) {
        for(var y = 0; y < 7; ++y){
            var patternY = MatrixUtil.POSITION_DETECTION_PATTERN[y];
            for(var x = 0; x < 7; ++x){
                matrix.setNumber(xStart + x, yStart + y, patternY[x]);
            }
        }
    };
    // Embed position detection patterns and surrounding vertical/horizontal separators.
    MatrixUtil.embedPositionDetectionPatternsAndSeparators = function(matrix) {
        // Embed three big squares at corners.
        var pdpWidth = MatrixUtil.POSITION_DETECTION_PATTERN[0].length;
        // Left top corner.
        MatrixUtil.embedPositionDetectionPattern(0, 0, matrix);
        // Right top corner.
        MatrixUtil.embedPositionDetectionPattern(matrix.getWidth() - pdpWidth, 0, matrix);
        // Left bottom corner.
        MatrixUtil.embedPositionDetectionPattern(0, matrix.getWidth() - pdpWidth, matrix);
        // Embed horizontal separation patterns around the squares.
        var hspWidth = 8;
        // Left top corner.
        MatrixUtil.embedHorizontalSeparationPattern(0, hspWidth - 1, matrix);
        // Right top corner.
        MatrixUtil.embedHorizontalSeparationPattern(matrix.getWidth() - hspWidth, hspWidth - 1, matrix);
        // Left bottom corner.
        MatrixUtil.embedHorizontalSeparationPattern(0, matrix.getWidth() - hspWidth, matrix);
        // Embed vertical separation patterns around the squares.
        var vspSize = 7;
        // Left top corner.
        MatrixUtil.embedVerticalSeparationPattern(vspSize, 0, matrix);
        // Right top corner.
        MatrixUtil.embedVerticalSeparationPattern(matrix.getHeight() - vspSize - 1, 0, matrix);
        // Left bottom corner.
        MatrixUtil.embedVerticalSeparationPattern(vspSize, matrix.getHeight() - vspSize, matrix);
    };
    // Embed position adjustment patterns if need be.
    MatrixUtil.maybeEmbedPositionAdjustmentPatterns = function(version, matrix) {
        if (version.getVersionNumber() < 2) {
            return;
        }
        var index = version.getVersionNumber() - 1;
        var coordinates = MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[index];
        for(var i = 0, length_1 = coordinates.length; i !== length_1; i++){
            var y = coordinates[i];
            if (y >= 0) {
                for(var j = 0; j !== length_1; j++){
                    var x = coordinates[j];
                    if (x >= 0 && MatrixUtil.isEmpty(matrix.get(x, y))) {
                        // If the cell is unset, we embed the position adjustment pattern here.
                        // -2 is necessary since the x/y coordinates point to the center of the pattern, not the
                        // left top corner.
                        MatrixUtil.embedPositionAdjustmentPattern(x - 2, y - 2, matrix);
                    }
                }
            }
        }
    };
    MatrixUtil.POSITION_DETECTION_PATTERN = Array.from([
        Int32Array.from([
            1,
            1,
            1,
            1,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            0,
            0,
            0,
            0,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            1,
            1,
            1,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            1,
            1,
            1,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            1,
            1,
            1,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            0,
            0,
            0,
            0,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            1,
            1,
            1,
            1
        ])
    ]);
    MatrixUtil.POSITION_ADJUSTMENT_PATTERN = Array.from([
        Int32Array.from([
            1,
            1,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            0,
            0,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            1,
            0,
            1
        ]),
        Int32Array.from([
            1,
            0,
            0,
            0,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            1,
            1
        ])
    ]);
    // From Appendix E. Table 1, JIS0510X:2004 (71: p). The table was double-checked by komatsu.
    MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE = Array.from([
        Int32Array.from([
            -1,
            -1,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            18,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            22,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            -1,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            22,
            38,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            24,
            42,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            46,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            28,
            50,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            54,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            32,
            58,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            62,
            -1,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            46,
            66,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            48,
            70,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            50,
            74,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            54,
            78,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            56,
            82,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            58,
            86,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            62,
            90,
            -1,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            28,
            50,
            72,
            94,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            50,
            74,
            98,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            54,
            78,
            102,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            28,
            54,
            80,
            106,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            32,
            58,
            84,
            110,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            58,
            86,
            114,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            62,
            90,
            118,
            -1,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            50,
            74,
            98,
            122,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            54,
            78,
            102,
            126,
            -1
        ]),
        Int32Array.from([
            6,
            26,
            52,
            78,
            104,
            130,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            56,
            82,
            108,
            134,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            60,
            86,
            112,
            138,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            58,
            86,
            114,
            142,
            -1
        ]),
        Int32Array.from([
            6,
            34,
            62,
            90,
            118,
            146,
            -1
        ]),
        Int32Array.from([
            6,
            30,
            54,
            78,
            102,
            126,
            150
        ]),
        Int32Array.from([
            6,
            24,
            50,
            76,
            102,
            128,
            154
        ]),
        Int32Array.from([
            6,
            28,
            54,
            80,
            106,
            132,
            158
        ]),
        Int32Array.from([
            6,
            32,
            58,
            84,
            110,
            136,
            162
        ]),
        Int32Array.from([
            6,
            26,
            54,
            82,
            110,
            138,
            166
        ]),
        Int32Array.from([
            6,
            30,
            58,
            86,
            114,
            142,
            170
        ])
    ]);
    // Type info cells at the left top corner.
    MatrixUtil.TYPE_INFO_COORDINATES = Array.from([
        Int32Array.from([
            8,
            0
        ]),
        Int32Array.from([
            8,
            1
        ]),
        Int32Array.from([
            8,
            2
        ]),
        Int32Array.from([
            8,
            3
        ]),
        Int32Array.from([
            8,
            4
        ]),
        Int32Array.from([
            8,
            5
        ]),
        Int32Array.from([
            8,
            7
        ]),
        Int32Array.from([
            8,
            8
        ]),
        Int32Array.from([
            7,
            8
        ]),
        Int32Array.from([
            5,
            8
        ]),
        Int32Array.from([
            4,
            8
        ]),
        Int32Array.from([
            3,
            8
        ]),
        Int32Array.from([
            2,
            8
        ]),
        Int32Array.from([
            1,
            8
        ]),
        Int32Array.from([
            0,
            8
        ])
    ]);
    // From Appendix D in JISX0510:2004 (p. 67)
    MatrixUtil.VERSION_INFO_POLY = 0x1f25; // 1 1111 0010 0101
    // From Appendix C in JISX0510:2004 (p.65).
    MatrixUtil.TYPE_INFO_POLY = 0x537;
    MatrixUtil.TYPE_INFO_MASK_PATTERN = 0x5412;
    return MatrixUtil;
}();
const __TURBOPACK__default__export__ = MatrixUtil;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/BlockPair.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode.encoder {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var BlockPair = function() {
    function BlockPair(dataBytes, errorCorrectionBytes) {
        this.dataBytes = dataBytes;
        this.errorCorrectionBytes = errorCorrectionBytes;
    }
    BlockPair.prototype.getDataBytes = function() {
        return this.dataBytes;
    };
    BlockPair.prototype.getErrorCorrectionBytes = function() {
        return this.errorCorrectionBytes;
    };
    return BlockPair;
}();
const __TURBOPACK__default__export__ = BlockPair;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.qrcode.encoder {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MatrixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MatrixUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$BlockPair$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/BlockPair.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/WriterException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/*import java.io.UnsupportedEncodingException;*/ /*import java.util.ArrayList;*/ /*import java.util.Collection;*/ /*import java.util.Map;*/ /**
 * <AUTHOR> (Satoru Takabayashi) - creator
 * <AUTHOR> (Daniel Switkin) - ported from C++
 */ var Encoder = function() {
    // TYPESCRIPTPORT: changed to UTF8, the default for js
    function Encoder() {}
    // The mask penalty calculation is complicated.  See Table 21 of JISX0510:2004 (p.45) for details.
    // Basically it applies four rules and summate all penalties.
    Encoder.calculateMaskPenalty = function(matrix) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].applyMaskPenaltyRule1(matrix) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].applyMaskPenaltyRule2(matrix) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].applyMaskPenaltyRule3(matrix) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].applyMaskPenaltyRule4(matrix);
    };
    /**
     * @param content text to encode
     * @param ecLevel error correction level to use
     * @return {@link QRCode} representing the encoded QR code
     * @throws WriterException if encoding can't succeed, because of for example invalid content
     *   or configuration
     */ // public static encode(content: string, ecLevel: ErrorCorrectionLevel): QRCode /*throws WriterException*/ {
    //   return encode(content, ecLevel, null)
    // }
    Encoder.encode = function(content, ecLevel, hints) {
        if (hints === void 0) {
            hints = null;
        }
        // Determine what character encoding has been specified by the caller, if any
        var encoding = Encoder.DEFAULT_BYTE_MODE_ENCODING;
        var hasEncodingHint = hints !== null && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET);
        if (hasEncodingHint) {
            encoding = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET).toString();
        }
        // Pick an encoding mode appropriate for the content. Note that this will not attempt to use
        // multiple modes / segments even if that were more efficient. Twould be nice.
        var mode = this.chooseMode(content, encoding);
        // This will store the header information, like mode and
        // length, as well as "header" segments like an ECI segment.
        var headerBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        // Append ECI segment if applicable
        if (mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE && (hasEncodingHint || Encoder.DEFAULT_BYTE_MODE_ENCODING !== encoding)) {
            var eci = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharacterSetECIByName(encoding);
            if (eci !== undefined) {
                this.appendECI(eci, headerBits);
            }
        }
        // (With ECI in place,) Write the mode marker
        this.appendModeInfo(mode, headerBits);
        // Collect data within the main segment, separately, to count its size if needed. Don't add it to
        // main payload yet.
        var dataBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        this.appendBytes(content, mode, dataBits, encoding);
        var version;
        if (hints !== null && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_VERSION)) {
            var versionNumber = Number.parseInt(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_VERSION).toString(), 10);
            version = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getVersionForNumber(versionNumber);
            var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, version);
            if (!this.willFit(bitsNeeded, version, ecLevel)) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data too big for requested version');
            }
        } else {
            version = this.recommendVersion(ecLevel, mode, headerBits, dataBits);
        }
        var headerAndDataBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        headerAndDataBits.appendBitArray(headerBits);
        // Find "length" of main segment and write it
        var numLetters = mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE ? dataBits.getSizeInBytes() : content.length;
        this.appendLengthInfo(numLetters, version, mode, headerAndDataBits);
        // Put data together into the overall payload
        headerAndDataBits.appendBitArray(dataBits);
        var ecBlocks = version.getECBlocksForLevel(ecLevel);
        var numDataBytes = version.getTotalCodewords() - ecBlocks.getTotalECCodewords();
        // Terminate the bits properly.
        this.terminateBits(numDataBytes, headerAndDataBits);
        // Interleave data bits with error correction code.
        var finalBits = this.interleaveWithECBytes(headerAndDataBits, version.getTotalCodewords(), numDataBytes, ecBlocks.getNumBlocks());
        var qrCode = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        qrCode.setECLevel(ecLevel);
        qrCode.setMode(mode);
        qrCode.setVersion(version);
        //  Choose the mask pattern and set to "qrCode".
        var dimension = version.getDimensionForVersion();
        var matrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](dimension, dimension);
        var maskPattern = this.chooseMaskPattern(finalBits, ecLevel, version, matrix);
        qrCode.setMaskPattern(maskPattern);
        // Build the matrix and set it to "qrCode".
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MatrixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].buildMatrix(finalBits, ecLevel, version, maskPattern, matrix);
        qrCode.setMatrix(matrix);
        return qrCode;
    };
    /**
     * Decides the smallest version of QR code that will contain all of the provided data.
     *
     * @throws WriterException if the data cannot fit in any version
     */ Encoder.recommendVersion = function(ecLevel, mode, headerBits, dataBits) {
        // Hard part: need to know version to know how many bits length takes. But need to know how many
        // bits it takes to know version. First we take a guess at version by assuming version will be
        // the minimum, 1:
        var provisionalBitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getVersionForNumber(1));
        var provisionalVersion = this.chooseVersion(provisionalBitsNeeded, ecLevel);
        // Use that guess to calculate the right version. I am still not sure this works in 100% of cases.
        var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, provisionalVersion);
        return this.chooseVersion(bitsNeeded, ecLevel);
    };
    Encoder.calculateBitsNeeded = function(mode, headerBits, dataBits, version) {
        return headerBits.getSize() + mode.getCharacterCountBits(version) + dataBits.getSize();
    };
    /**
     * @return the code point of the table used in alphanumeric mode or
     *  -1 if there is no corresponding code in the table.
     */ Encoder.getAlphanumericCode = function(code /*int*/ ) {
        if (code < Encoder.ALPHANUMERIC_TABLE.length) {
            return Encoder.ALPHANUMERIC_TABLE[code];
        }
        return -1;
    };
    // public static chooseMode(content: string): Mode {
    //   return chooseMode(content, null);
    // }
    /**
     * Choose the best mode by examining the content. Note that 'encoding' is used as a hint;
     * if it is Shift_JIS, and the input is only double-byte Kanji, then we return {@link Mode#KANJI}.
     */ Encoder.chooseMode = function(content, encoding) {
        if (encoding === void 0) {
            encoding = null;
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SJIS.getName() === encoding && this.isOnlyDoubleByteKanji(content)) {
            // Choose Kanji mode if all input are double-byte characters
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].KANJI;
        }
        var hasNumeric = false;
        var hasAlphanumeric = false;
        for(var i = 0, length_1 = content.length; i < length_1; ++i){
            var c = content.charAt(i);
            if (Encoder.isDigit(c)) {
                hasNumeric = true;
            } else if (this.getAlphanumericCode(c.charCodeAt(0)) !== -1) {
                hasAlphanumeric = true;
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE;
            }
        }
        if (hasAlphanumeric) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ALPHANUMERIC;
        }
        if (hasNumeric) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NUMERIC;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE;
    };
    Encoder.isOnlyDoubleByteKanji = function(content) {
        var bytes;
        try {
            bytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(content, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SJIS); // content.getBytes("Shift_JIS"))
        } catch (ignored /*: UnsupportedEncodingException*/ ) {
            return false;
        }
        var length = bytes.length;
        if (length % 2 !== 0) {
            return false;
        }
        for(var i = 0; i < length; i += 2){
            var byte1 = bytes[i] & 0xFF;
            if ((byte1 < 0x81 || byte1 > 0x9F) && (byte1 < 0xE0 || byte1 > 0xEB)) {
                return false;
            }
        }
        return true;
    };
    Encoder.chooseMaskPattern = function(bits, ecLevel, version, matrix) {
        var minPenalty = Number.MAX_SAFE_INTEGER; // Lower penalty is better.
        var bestMaskPattern = -1;
        // We try all mask patterns to choose the best one.
        for(var maskPattern = 0; maskPattern < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NUM_MASK_PATTERNS; maskPattern++){
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MatrixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].buildMatrix(bits, ecLevel, version, maskPattern, matrix);
            var penalty = this.calculateMaskPenalty(matrix);
            if (penalty < minPenalty) {
                minPenalty = penalty;
                bestMaskPattern = maskPattern;
            }
        }
        return bestMaskPattern;
    };
    Encoder.chooseVersion = function(numInputBits /*int*/ , ecLevel) {
        for(var versionNum = 1; versionNum <= 40; versionNum++){
            var version = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getVersionForNumber(versionNum);
            if (Encoder.willFit(numInputBits, version, ecLevel)) {
                return version;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data too big');
    };
    /**
     * @return true if the number of input bits will fit in a code with the specified version and
     * error correction level.
     */ Encoder.willFit = function(numInputBits /*int*/ , version, ecLevel) {
        // In the following comments, we use numbers of Version 7-H.
        // numBytes = 196
        var numBytes = version.getTotalCodewords();
        // getNumECBytes = 130
        var ecBlocks = version.getECBlocksForLevel(ecLevel);
        var numEcBytes = ecBlocks.getTotalECCodewords();
        // getNumDataBytes = 196 - 130 = 66
        var numDataBytes = numBytes - numEcBytes;
        var totalInputBytes = (numInputBits + 7) / 8;
        return numDataBytes >= totalInputBytes;
    };
    /**
     * Terminate bits as described in 8.4.8 and 8.4.9 of JISX0510:2004 (p.24).
     */ Encoder.terminateBits = function(numDataBytes /*int*/ , bits) {
        var capacity = numDataBytes * 8;
        if (bits.getSize() > capacity) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('data bits cannot fit in the QR Code' + bits.getSize() + ' > ' + capacity);
        }
        for(var i = 0; i < 4 && bits.getSize() < capacity; ++i){
            bits.appendBit(false);
        }
        // Append termination bits. See 8.4.8 of JISX0510:2004 (p.24) for details.
        // If the last byte isn't 8-bit aligned, we'll add padding bits.
        var numBitsInLastByte = bits.getSize() & 0x07;
        if (numBitsInLastByte > 0) {
            for(var i = numBitsInLastByte; i < 8; i++){
                bits.appendBit(false);
            }
        }
        // If we have more space, we'll fill the space with padding patterns defined in 8.4.9 (p.24).
        var numPaddingBytes = numDataBytes - bits.getSizeInBytes();
        for(var i = 0; i < numPaddingBytes; ++i){
            bits.appendBits((i & 0x01) === 0 ? 0xEC : 0x11, 8);
        }
        if (bits.getSize() !== capacity) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Bits size does not equal capacity');
        }
    };
    /**
     * Get number of data bytes and number of error correction bytes for block id "blockID". Store
     * the result in "numDataBytesInBlock", and "numECBytesInBlock". See table 12 in 8.5.1 of
     * JISX0510:2004 (p.30)
     */ Encoder.getNumDataBytesAndNumECBytesForBlockID = function(numTotalBytes /*int*/ , numDataBytes /*int*/ , numRSBlocks /*int*/ , blockID /*int*/ , numDataBytesInBlock, numECBytesInBlock) {
        if (blockID >= numRSBlocks) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Block ID too large');
        }
        // numRsBlocksInGroup2 = 196 % 5 = 1
        var numRsBlocksInGroup2 = numTotalBytes % numRSBlocks;
        // numRsBlocksInGroup1 = 5 - 1 = 4
        var numRsBlocksInGroup1 = numRSBlocks - numRsBlocksInGroup2;
        // numTotalBytesInGroup1 = 196 / 5 = 39
        var numTotalBytesInGroup1 = Math.floor(numTotalBytes / numRSBlocks);
        // numTotalBytesInGroup2 = 39 + 1 = 40
        var numTotalBytesInGroup2 = numTotalBytesInGroup1 + 1;
        // numDataBytesInGroup1 = 66 / 5 = 13
        var numDataBytesInGroup1 = Math.floor(numDataBytes / numRSBlocks);
        // numDataBytesInGroup2 = 13 + 1 = 14
        var numDataBytesInGroup2 = numDataBytesInGroup1 + 1;
        // numEcBytesInGroup1 = 39 - 13 = 26
        var numEcBytesInGroup1 = numTotalBytesInGroup1 - numDataBytesInGroup1;
        // numEcBytesInGroup2 = 40 - 14 = 26
        var numEcBytesInGroup2 = numTotalBytesInGroup2 - numDataBytesInGroup2;
        // Sanity checks.
        // 26 = 26
        if (numEcBytesInGroup1 !== numEcBytesInGroup2) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('EC bytes mismatch');
        }
        // 5 = 4 + 1.
        if (numRSBlocks !== numRsBlocksInGroup1 + numRsBlocksInGroup2) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('RS blocks mismatch');
        }
        // 196 = (13 + 26) * 4 + (14 + 26) * 1
        if (numTotalBytes !== (numDataBytesInGroup1 + numEcBytesInGroup1) * numRsBlocksInGroup1 + (numDataBytesInGroup2 + numEcBytesInGroup2) * numRsBlocksInGroup2) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Total bytes mismatch');
        }
        if (blockID < numRsBlocksInGroup1) {
            numDataBytesInBlock[0] = numDataBytesInGroup1;
            numECBytesInBlock[0] = numEcBytesInGroup1;
        } else {
            numDataBytesInBlock[0] = numDataBytesInGroup2;
            numECBytesInBlock[0] = numEcBytesInGroup2;
        }
    };
    /**
     * Interleave "bits" with corresponding error correction bytes. On success, store the result in
     * "result". The interleave rule is complicated. See 8.6 of JISX0510:2004 (p.37) for details.
     */ Encoder.interleaveWithECBytes = function(bits, numTotalBytes /*int*/ , numDataBytes /*int*/ , numRSBlocks /*int*/ ) {
        var e_1, _a, e_2, _b;
        // "bits" must have "getNumDataBytes" bytes of data.
        if (bits.getSizeInBytes() !== numDataBytes) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Number of bits and data bytes does not match');
        }
        // Step 1.  Divide data bytes into blocks and generate error correction bytes for them. We'll
        // store the divided data bytes blocks and error correction bytes blocks into "blocks".
        var dataBytesOffset = 0;
        var maxNumDataBytes = 0;
        var maxNumEcBytes = 0;
        // Since, we know the number of reedsolmon blocks, we can initialize the vector with the number.
        var blocks = new Array(); // new Array<BlockPair>(numRSBlocks)
        for(var i = 0; i < numRSBlocks; ++i){
            var numDataBytesInBlock = new Int32Array(1);
            var numEcBytesInBlock = new Int32Array(1);
            Encoder.getNumDataBytesAndNumECBytesForBlockID(numTotalBytes, numDataBytes, numRSBlocks, i, numDataBytesInBlock, numEcBytesInBlock);
            var size = numDataBytesInBlock[0];
            var dataBytes = new Uint8Array(size);
            bits.toBytes(8 * dataBytesOffset, dataBytes, 0, size);
            var ecBytes = Encoder.generateECBytes(dataBytes, numEcBytesInBlock[0]);
            blocks.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$BlockPair$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](dataBytes, ecBytes));
            maxNumDataBytes = Math.max(maxNumDataBytes, size);
            maxNumEcBytes = Math.max(maxNumEcBytes, ecBytes.length);
            dataBytesOffset += numDataBytesInBlock[0];
        }
        if (numDataBytes !== dataBytesOffset) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data bytes does not match offset');
        }
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        // First, place data blocks.
        for(var i = 0; i < maxNumDataBytes; ++i){
            try {
                for(var blocks_1 = (e_1 = void 0, __values(blocks)), blocks_1_1 = blocks_1.next(); !blocks_1_1.done; blocks_1_1 = blocks_1.next()){
                    var block = blocks_1_1.value;
                    var dataBytes = block.getDataBytes();
                    if (i < dataBytes.length) {
                        result.appendBits(dataBytes[i], 8);
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (blocks_1_1 && !blocks_1_1.done && (_a = blocks_1.return)) _a.call(blocks_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        }
        // Then, place error correction blocks.
        for(var i = 0; i < maxNumEcBytes; ++i){
            try {
                for(var blocks_2 = (e_2 = void 0, __values(blocks)), blocks_2_1 = blocks_2.next(); !blocks_2_1.done; blocks_2_1 = blocks_2.next()){
                    var block = blocks_2_1.value;
                    var ecBytes = block.getErrorCorrectionBytes();
                    if (i < ecBytes.length) {
                        result.appendBits(ecBytes[i], 8);
                    }
                }
            } catch (e_2_1) {
                e_2 = {
                    error: e_2_1
                };
            } finally{
                try {
                    if (blocks_2_1 && !blocks_2_1.done && (_b = blocks_2.return)) _b.call(blocks_2);
                } finally{
                    if (e_2) throw e_2.error;
                }
            }
        }
        if (numTotalBytes !== result.getSizeInBytes()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Interleaving error: ' + numTotalBytes + ' and ' + result.getSizeInBytes() + ' differ.');
        }
        return result;
    };
    Encoder.generateECBytes = function(dataBytes, numEcBytesInBlock /*int*/ ) {
        var numDataBytes = dataBytes.length;
        var toEncode = new Int32Array(numDataBytes + numEcBytesInBlock); // int[numDataBytes + numEcBytesInBlock]
        for(var i = 0; i < numDataBytes; i++){
            toEncode[i] = dataBytes[i] & 0xFF;
        }
        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_CODE_FIELD_256).encode(toEncode, numEcBytesInBlock);
        var ecBytes = new Uint8Array(numEcBytesInBlock);
        for(var i = 0; i < numEcBytesInBlock; i++){
            ecBytes[i] = /*(byte) */ toEncode[numDataBytes + i];
        }
        return ecBytes;
    };
    /**
     * Append mode info. On success, store the result in "bits".
     */ Encoder.appendModeInfo = function(mode, bits) {
        bits.appendBits(mode.getBits(), 4);
    };
    /**
     * Append length info. On success, store the result in "bits".
     */ Encoder.appendLengthInfo = function(numLetters /*int*/ , version, mode, bits) {
        var numBits = mode.getCharacterCountBits(version);
        if (numLetters >= 1 << numBits) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](numLetters + ' is bigger than ' + ((1 << numBits) - 1));
        }
        bits.appendBits(numLetters, numBits);
    };
    /**
     * Append "bytes" in "mode" mode (encoding) into "bits". On success, store the result in "bits".
     */ Encoder.appendBytes = function(content, mode, bits, encoding) {
        switch(mode){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NUMERIC:
                Encoder.appendNumericBytes(content, bits);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ALPHANUMERIC:
                Encoder.appendAlphanumericBytes(content, bits);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE:
                Encoder.append8BitBytes(content, bits, encoding);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].KANJI:
                Encoder.appendKanjiBytes(content, bits);
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Invalid mode: ' + mode);
        }
    };
    Encoder.getDigit = function(singleCharacter) {
        return singleCharacter.charCodeAt(0) - 48;
    };
    Encoder.isDigit = function(singleCharacter) {
        var cn = Encoder.getDigit(singleCharacter);
        return cn >= 0 && cn <= 9;
    };
    Encoder.appendNumericBytes = function(content, bits) {
        var length = content.length;
        var i = 0;
        while(i < length){
            var num1 = Encoder.getDigit(content.charAt(i));
            if (i + 2 < length) {
                // Encode three numeric letters in ten bits.
                var num2 = Encoder.getDigit(content.charAt(i + 1));
                var num3 = Encoder.getDigit(content.charAt(i + 2));
                bits.appendBits(num1 * 100 + num2 * 10 + num3, 10);
                i += 3;
            } else if (i + 1 < length) {
                // Encode two numeric letters in seven bits.
                var num2 = Encoder.getDigit(content.charAt(i + 1));
                bits.appendBits(num1 * 10 + num2, 7);
                i += 2;
            } else {
                // Encode one numeric letter in four bits.
                bits.appendBits(num1, 4);
                i++;
            }
        }
    };
    Encoder.appendAlphanumericBytes = function(content, bits) {
        var length = content.length;
        var i = 0;
        while(i < length){
            var code1 = Encoder.getAlphanumericCode(content.charCodeAt(i));
            if (code1 === -1) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            if (i + 1 < length) {
                var code2 = Encoder.getAlphanumericCode(content.charCodeAt(i + 1));
                if (code2 === -1) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
                }
                // Encode two alphanumeric letters in 11 bits.
                bits.appendBits(code1 * 45 + code2, 11);
                i += 2;
            } else {
                // Encode one alphanumeric letter in six bits.
                bits.appendBits(code1, 6);
                i++;
            }
        }
    };
    Encoder.append8BitBytes = function(content, bits, encoding) {
        var bytes;
        try {
            bytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(content, encoding);
        } catch (uee /*: UnsupportedEncodingException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](uee);
        }
        for(var i = 0, length_2 = bytes.length; i !== length_2; i++){
            var b = bytes[i];
            bits.appendBits(b, 8);
        }
    };
    /**
     * @throws WriterException
     */ Encoder.appendKanjiBytes = function(content, bits) {
        var bytes;
        try {
            bytes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(content, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SJIS);
        } catch (uee /*: UnsupportedEncodingException*/ ) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](uee);
        }
        var length = bytes.length;
        for(var i = 0; i < length; i += 2){
            var byte1 = bytes[i] & 0xFF;
            var byte2 = bytes[i + 1] & 0xFF;
            var code = byte1 << 8 & 0xFFFFFFFF | byte2;
            var subtracted = -1;
            if (code >= 0x8140 && code <= 0x9ffc) {
                subtracted = code - 0x8140;
            } else if (code >= 0xe040 && code <= 0xebbf) {
                subtracted = code - 0xc140;
            }
            if (subtracted === -1) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Invalid byte sequence');
            }
            var encoded = (subtracted >> 8) * 0xc0 + (subtracted & 0xff);
            bits.appendBits(encoded, 13);
        }
    };
    Encoder.appendECI = function(eci, bits) {
        bits.appendBits(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ECI.getBits(), 4);
        // This is correct for values up to 127, which is all we need now.
        bits.appendBits(eci.getValue(), 8);
    };
    // The original table is defined in the table 5 of JISX0510:2004 (p.19).
    Encoder.ALPHANUMERIC_TABLE = Int32Array.from([
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        36,
        -1,
        -1,
        -1,
        37,
        38,
        -1,
        -1,
        -1,
        -1,
        39,
        40,
        -1,
        41,
        42,
        43,
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        44,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        35,
        -1,
        -1,
        -1,
        -1,
        -1
    ]);
    Encoder.DEFAULT_BYTE_MODE_ENCODING = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].UTF8.getName(); // "ISO-8859-1"
    return Encoder;
}();
const __TURBOPACK__default__export__ = Encoder;
}),
"[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeWriter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.qrcode {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
/*import java.util.Map;*/ /**
 * This object renders a QR Code as a BitMatrix 2D array of greyscale values.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var QRCodeWriter = function() {
    function QRCodeWriter() {}
    /*@Override*/ // public encode(contents: string, format: BarcodeFormat, width: number /*int*/, height: number /*int*/): BitMatrix
    //     /*throws WriterException */ {
    //   return encode(contents, format, width, height, null)
    // }
    /*@Override*/ QRCodeWriter.prototype.encode = function(contents, format, width /*int*/ , height /*int*/ , hints) {
        if (contents.length === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Found empty contents');
        }
        if (format !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].QR_CODE) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Can only encode QR_CODE, but got ' + format);
        }
        if (width < 0 || height < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("Requested dimensions are too small: " + width + "x" + height);
        }
        var errorCorrectionLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].L;
        var quietZone = QRCodeWriter.QUIET_ZONE_SIZE;
        if (hints !== null) {
            if (undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION)) {
                errorCorrectionLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fromString(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION).toString());
            }
            if (undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MARGIN)) {
                quietZone = Number.parseInt(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MARGIN).toString(), 10);
            }
        }
        var code = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(contents, errorCorrectionLevel, hints);
        return QRCodeWriter.renderResult(code, width, height, quietZone);
    };
    // Note that the input matrix uses 0 == white, 1 == black, while the output matrix uses
    // 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).
    QRCodeWriter.renderResult = function(code, width /*int*/ , height /*int*/ , quietZone /*int*/ ) {
        var input = code.getMatrix();
        if (input === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var inputWidth = input.getWidth();
        var inputHeight = input.getHeight();
        var qrWidth = inputWidth + quietZone * 2;
        var qrHeight = inputHeight + quietZone * 2;
        var outputWidth = Math.max(width, qrWidth);
        var outputHeight = Math.max(height, qrHeight);
        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));
        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested
        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.
        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will
        // handle all the padding from 100x100 (the actual QR) up to 200x160.
        var leftPadding = Math.floor((outputWidth - inputWidth * multiple) / 2);
        var topPadding = Math.floor((outputHeight - inputHeight * multiple) / 2);
        var output = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](outputWidth, outputHeight);
        for(var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple){
            // Write the contents of this row of the barcode
            for(var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple){
                if (input.get(inputX, inputY) === 1) {
                    output.setRegion(outputX, outputY, multiple, multiple);
                }
            }
        }
        return output;
    };
    QRCodeWriter.QUIET_ZONE_SIZE = 4;
    return QRCodeWriter;
}();
const __TURBOPACK__default__export__ = QRCodeWriter;
}),
}]);

//# sourceMappingURL=node_modules_%40zxing_library_esm_core_qrcode_1eb958b4._.js.map