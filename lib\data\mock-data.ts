import { Student, Subject, TimePeriod, AttendanceRecord } from "@/lib/types/scanner"

// Mock student data with photos
export const mockStudents: Student[] = [
  {
    id: "STU001",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "3rd Year",
    section: "IT-3A",
    grade: "11",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU001_2025"
  },
  {
    id: "STU002",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "2nd Year",
    section: "CS-2B",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU002_2025"
  },
  {
    id: "STU003",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "1st Year",
    section: "IT-1C",
    grade: "9",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU003_2025"
  },
  {
    id: "STU004",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "4th Year",
    section: "CS-4A",
    grade: "12",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU004_2025"
  },
  {
    id: "STU005",
    name: "Alex Chen",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "2nd Year",
    section: "IT-2A",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU005_2025"
  },
  {
    id: "STU006",
    name: "Maria Garcia",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "3rd Year",
    section: "CS-3B",
    grade: "11",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU006_2025"
  },
  {
    id: "STU007",
    name: "David Brown",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "4th Year",
    section: "IT-4A",
    grade: "12",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU007_2025"
  },
  {
    id: "STU008",
    name: "Lisa Anderson",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "1st Year",
    section: "CS-1A",
    grade: "9",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU008_2025"
  },
  {
    id: "STU009",
    name: "Robert Taylor",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "3rd Year",
    section: "IT-3B",
    grade: "11",
    status: "Inactive",
    photo: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU009_2025"
  },
  {
    id: "STU010",
    name: "Emily Davis",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "2nd Year",
    section: "CS-2A",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU010_2025"
  }
]

// Mock subjects
export const mockSubjects: Subject[] = [
  {
    id: "SUBJ001",
    name: "Programming Fundamentals",
    code: "IT101",
    instructor: "Prof. Martinez",
    schedule: [
      { day: "Monday", startTime: "08:00", endTime: "10:00" },
      { day: "Wednesday", startTime: "08:00", endTime: "10:00" },
      { day: "Friday", startTime: "08:00", endTime: "10:00" }
    ]
  },
  {
    id: "SUBJ002",
    name: "Database Management",
    code: "IT201",
    instructor: "Prof. Rodriguez",
    schedule: [
      { day: "Tuesday", startTime: "10:00", endTime: "12:00" },
      { day: "Thursday", startTime: "10:00", endTime: "12:00" }
    ]
  },
  {
    id: "SUBJ003",
    name: "Web Development",
    code: "IT301",
    instructor: "Prof. Santos",
    schedule: [
      { day: "Monday", startTime: "13:00", endTime: "15:00" },
      { day: "Wednesday", startTime: "13:00", endTime: "15:00" }
    ]
  },
  {
    id: "SUBJ004",
    name: "Data Structures",
    code: "CS201",
    instructor: "Prof. Reyes",
    schedule: [
      { day: "Tuesday", startTime: "08:00", endTime: "10:00" },
      { day: "Thursday", startTime: "08:00", endTime: "10:00" }
    ]
  },
  {
    id: "SUBJ005",
    name: "Software Engineering",
    code: "CS301",
    instructor: "Prof. Cruz",
    schedule: [
      { day: "Monday", startTime: "15:00", endTime: "17:00" },
      { day: "Friday", startTime: "15:00", endTime: "17:00" }
    ]
  }
]

// Mock time periods
export const mockTimePeriods: TimePeriod[] = [
  {
    id: "PERIOD001",
    name: "1st Period",
    startTime: "08:00",
    endTime: "10:00",
    type: "morning"
  },
  {
    id: "PERIOD002",
    name: "2nd Period",
    startTime: "10:00",
    endTime: "12:00",
    type: "morning"
  },
  {
    id: "PERIOD003",
    name: "3rd Period",
    startTime: "13:00",
    endTime: "15:00",
    type: "afternoon"
  },
  {
    id: "PERIOD004",
    name: "4th Period",
    startTime: "15:00",
    endTime: "17:00",
    type: "afternoon"
  },
  {
    id: "PERIOD005",
    name: "Evening Class",
    startTime: "18:00",
    endTime: "20:00",
    type: "evening"
  }
]

// Mock attendance records
export const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: "ATT001",
    studentId: "STU001",
    studentName: "John Doe",
    course: "Information Technology",
    checkIn: "8:30 AM",
    checkOut: "5:00 PM",
    date: "2025-01-02",
    status: "Present",
    type: "gate",
    timestamp: new Date("2025-01-02T08:30:00")
  },
  {
    id: "ATT002",
    studentId: "STU002",
    studentName: "Jane Smith",
    course: "Computer Science",
    checkIn: "8:25 AM",
    checkOut: "4:55 PM",
    date: "2025-01-02",
    status: "Present",
    type: "gate",
    timestamp: new Date("2025-01-02T08:25:00")
  },
  {
    id: "ATT003",
    studentId: "STU003",
    studentName: "Mike Johnson",
    course: "Information Technology",
    checkIn: "8:45 AM",
    checkOut: "5:10 PM",
    date: "2025-01-02",
    status: "Late",
    type: "gate",
    timestamp: new Date("2025-01-02T08:45:00")
  },
  {
    id: "ATT004",
    studentId: "STU004",
    studentName: "Sarah Wilson",
    course: "Computer Science",
    date: "2025-01-02",
    status: "Absent",
    type: "subject",
    subject: "Programming Fundamentals",
    period: "1st Period",
    timestamp: new Date("2025-01-02T08:00:00")
  }
]

// Helper functions
export function findStudentById(id: string): Student | undefined {
  return mockStudents.find(student => student.id === id)
}

export function findStudentByQRCode(qrCode: string): Student | undefined {
  return mockStudents.find(student => student.qrCode === qrCode)
}

export function findSubjectById(id: string): Subject | undefined {
  return mockSubjects.find(subject => subject.id === id)
}

export function findPeriodById(id: string): TimePeriod | undefined {
  return mockTimePeriods.find(period => period.id === id)
}

export function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {
  return mockAttendanceRecords.filter(record => record.studentId === studentId)
}

export function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {
  const today = new Date().toISOString().split('T')[0]
  return mockAttendanceRecords.find(record => 
    record.studentId === studentId && record.date === today
  )
}
