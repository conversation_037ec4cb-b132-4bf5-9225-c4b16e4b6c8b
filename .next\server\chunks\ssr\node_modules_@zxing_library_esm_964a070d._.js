module.exports = {

"[project]/node_modules/@zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "HTMLCanvasElementLuminanceSource": ()=>HTMLCanvasElementLuminanceSource
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
/**
 * @deprecated Moving to @zxing/browser
 */ var HTMLCanvasElementLuminanceSource = function(_super) {
    __extends(HTMLCanvasElementLuminanceSource, _super);
    function HTMLCanvasElementLuminanceSource(canvas, doAutoInvert) {
        if (doAutoInvert === void 0) {
            doAutoInvert = false;
        }
        var _this = _super.call(this, canvas.width, canvas.height) || this;
        _this.canvas = canvas;
        _this.tempCanvasElement = null;
        _this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(canvas, doAutoInvert);
        return _this;
    }
    HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData = function(canvas, doAutoInvert) {
        if (doAutoInvert === void 0) {
            doAutoInvert = false;
        }
        var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height);
        return HTMLCanvasElementLuminanceSource.toGrayscaleBuffer(imageData.data, canvas.width, canvas.height, doAutoInvert);
    };
    HTMLCanvasElementLuminanceSource.toGrayscaleBuffer = function(imageBuffer, width, height, doAutoInvert) {
        if (doAutoInvert === void 0) {
            doAutoInvert = false;
        }
        var grayscaleBuffer = new Uint8ClampedArray(width * height);
        HTMLCanvasElementLuminanceSource.FRAME_INDEX = !HTMLCanvasElementLuminanceSource.FRAME_INDEX;
        if (HTMLCanvasElementLuminanceSource.FRAME_INDEX || !doAutoInvert) {
            for(var i = 0, j = 0, length_1 = imageBuffer.length; i < length_1; i += 4, j++){
                var gray = void 0;
                var alpha = imageBuffer[i + 3];
                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent
                // black (0 alpha, and then 0 RGB). They are often used, of course as the "white" area in a
                // barcode image. Force any such pixel to be white:
                if (alpha === 0) {
                    gray = 0xFF;
                } else {
                    var pixelR = imageBuffer[i];
                    var pixelG = imageBuffer[i + 1];
                    var pixelB = imageBuffer[i + 2];
                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),
                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.
                    // 0x200 >> 10 is 0.5, it implements rounding.
                    gray = 306 * pixelR + 601 * pixelG + 117 * pixelB + 0x200 >> 10;
                }
                grayscaleBuffer[j] = gray;
            }
        } else {
            for(var i = 0, j = 0, length_2 = imageBuffer.length; i < length_2; i += 4, j++){
                var gray = void 0;
                var alpha = imageBuffer[i + 3];
                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent
                // black (0 alpha, and then 0 RGB). They are often used, of course as the "white" area in a
                // barcode image. Force any such pixel to be white:
                if (alpha === 0) {
                    gray = 0xFF;
                } else {
                    var pixelR = imageBuffer[i];
                    var pixelG = imageBuffer[i + 1];
                    var pixelB = imageBuffer[i + 2];
                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),
                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.
                    // 0x200 >> 10 is 0.5, it implements rounding.
                    gray = 306 * pixelR + 601 * pixelG + 117 * pixelB + 0x200 >> 10;
                }
                grayscaleBuffer[j] = 0xFF - gray;
            }
        }
        return grayscaleBuffer;
    };
    HTMLCanvasElementLuminanceSource.prototype.getRow = function(y /*int*/ , row) {
        if (y < 0 || y >= this.getHeight()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Requested row is outside the image: ' + y);
        }
        var width = this.getWidth();
        var start = y * width;
        if (row === null) {
            row = this.buffer.slice(start, start + width);
        } else {
            if (row.length < width) {
                row = new Uint8ClampedArray(width);
            }
            // The underlying raster of image consists of bytes with the luminance values
            // TODO: can avoid set/slice?
            row.set(this.buffer.slice(start, start + width));
        }
        return row;
    };
    HTMLCanvasElementLuminanceSource.prototype.getMatrix = function() {
        return this.buffer;
    };
    HTMLCanvasElementLuminanceSource.prototype.isCropSupported = function() {
        return true;
    };
    HTMLCanvasElementLuminanceSource.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        _super.prototype.crop.call(this, left, top, width, height);
        return this;
    };
    /**
     * This is always true, since the image is a gray-scale image.
     *
     * @return true
     */ HTMLCanvasElementLuminanceSource.prototype.isRotateSupported = function() {
        return true;
    };
    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise = function() {
        this.rotate(-90);
        return this;
    };
    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise45 = function() {
        this.rotate(-45);
        return this;
    };
    HTMLCanvasElementLuminanceSource.prototype.getTempCanvasElement = function() {
        if (null === this.tempCanvasElement) {
            var tempCanvasElement = this.canvas.ownerDocument.createElement('canvas');
            tempCanvasElement.width = this.canvas.width;
            tempCanvasElement.height = this.canvas.height;
            this.tempCanvasElement = tempCanvasElement;
        }
        return this.tempCanvasElement;
    };
    HTMLCanvasElementLuminanceSource.prototype.rotate = function(angle) {
        var tempCanvasElement = this.getTempCanvasElement();
        var tempContext = tempCanvasElement.getContext('2d');
        var angleRadians = angle * HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS;
        // Calculate and set new dimensions for temp canvas
        var width = this.canvas.width;
        var height = this.canvas.height;
        var newWidth = Math.ceil(Math.abs(Math.cos(angleRadians)) * width + Math.abs(Math.sin(angleRadians)) * height);
        var newHeight = Math.ceil(Math.abs(Math.sin(angleRadians)) * width + Math.abs(Math.cos(angleRadians)) * height);
        tempCanvasElement.width = newWidth;
        tempCanvasElement.height = newHeight;
        // Draw at center of temp canvas to prevent clipping of image data
        tempContext.translate(newWidth / 2, newHeight / 2);
        tempContext.rotate(angleRadians);
        tempContext.drawImage(this.canvas, width / -2, height / -2);
        this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(tempCanvasElement);
        return this;
    };
    HTMLCanvasElementLuminanceSource.prototype.invert = function() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this);
    };
    HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS = Math.PI / 180;
    HTMLCanvasElementLuminanceSource.FRAME_INDEX = true;
    return HTMLCanvasElementLuminanceSource;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/VideoInputDevice.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @deprecated Moving to @zxing/browser
 *
 * Video input device metadata containing the id and label of the device if available.
 */ __turbopack_context__.s({
    "VideoInputDevice": ()=>VideoInputDevice
});
var VideoInputDevice = function() {
    /**
     * Creates an instance of VideoInputDevice.
     *
     * @param {string} deviceId the video input device id
     * @param {string} label the label of the device if available
     */ function VideoInputDevice(deviceId, label, groupId) {
        this.deviceId = deviceId;
        this.label = label;
        /** @inheritdoc */ this.kind = 'videoinput';
        this.groupId = groupId || undefined;
    }
    /** @inheritdoc */ VideoInputDevice.prototype.toJSON = function() {
        return {
            kind: this.kind,
            groupId: this.groupId,
            deviceId: this.deviceId,
            label: this.label
        };
    };
    return VideoInputDevice;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserCodeReader": ()=>BrowserCodeReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BinaryBitmap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BinaryBitmap.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$HybridBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/HybridBinarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLCanvasElementLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$VideoInputDevice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/VideoInputDevice.js [app-ssr] (ecmascript)");
var __awaiter = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    //TURBOPACK unreachable
    ;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
/**
 * @deprecated Moving to @zxing/browser
 *
 * Base class for browser code reader.
 */ var BrowserCodeReader = function() {
    /**
     * Creates an instance of BrowserCodeReader.
     * @param {Reader} reader The reader instance to decode the barcode
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent successful decode tries
     *
     * @memberOf BrowserCodeReader
     */ function BrowserCodeReader(reader, timeBetweenScansMillis, _hints) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        this.reader = reader;
        this.timeBetweenScansMillis = timeBetweenScansMillis;
        this._hints = _hints;
        /**
         * This will break the loop.
         */ this._stopContinuousDecode = false;
        /**
         * This will break the loop.
         */ this._stopAsyncDecode = false;
        /**
         * Delay time between decode attempts made by the scanner.
         */ this._timeBetweenDecodingAttempts = 0;
    }
    Object.defineProperty(BrowserCodeReader.prototype, "hasNavigator", {
        /**
         * If navigator is present.
         */ get: function() {
            return typeof navigator !== 'undefined';
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BrowserCodeReader.prototype, "isMediaDevicesSuported", {
        /**
         * If mediaDevices under navigator is supported.
         */ get: function() {
            return this.hasNavigator && !!navigator.mediaDevices;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BrowserCodeReader.prototype, "canEnumerateDevices", {
        /**
         * If enumerateDevices under navigator is supported.
         */ get: function() {
            return !!(this.isMediaDevicesSuported && navigator.mediaDevices.enumerateDevices);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BrowserCodeReader.prototype, "timeBetweenDecodingAttempts", {
        /** Time between two decoding tries in milli seconds. */ get: function() {
            return this._timeBetweenDecodingAttempts;
        },
        /**
         * Change the time span the decoder waits between two decoding tries.
         *
         * @param {number} millis Time between two decoding tries in milli seconds.
         */ set: function(millis) {
            this._timeBetweenDecodingAttempts = millis < 0 ? 0 : millis;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BrowserCodeReader.prototype, "hints", {
        /**
         * Sets the hints.
         */ get: function() {
            return this._hints;
        },
        /**
         * Sets the hints.
         */ set: function(hints) {
            this._hints = hints || null;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Lists all the available video input devices.
     */ BrowserCodeReader.prototype.listVideoInputDevices = function() {
        return __awaiter(this, void 0, void 0, function() {
            var devices, videoDevices, devices_1, devices_1_1, device, kind, deviceId, label, groupId, videoDevice;
            var e_1, _a;
            return __generator(this, function(_b) {
                switch(_b.label){
                    case 0:
                        if (!this.hasNavigator) {
                            throw new Error("Can't enumerate devices, navigator is not present.");
                        }
                        if (!this.canEnumerateDevices) {
                            throw new Error("Can't enumerate devices, method not supported.");
                        }
                        return [
                            4 /*yield*/ ,
                            navigator.mediaDevices.enumerateDevices()
                        ];
                    case 1:
                        devices = _b.sent();
                        videoDevices = [];
                        try {
                            for(devices_1 = __values(devices), devices_1_1 = devices_1.next(); !devices_1_1.done; devices_1_1 = devices_1.next()){
                                device = devices_1_1.value;
                                kind = device.kind === 'video' ? 'videoinput' : device.kind;
                                if (kind !== 'videoinput') {
                                    continue;
                                }
                                deviceId = device.deviceId || device.id;
                                label = device.label || "Video device " + (videoDevices.length + 1);
                                groupId = device.groupId;
                                videoDevice = {
                                    deviceId: deviceId,
                                    label: label,
                                    kind: kind,
                                    groupId: groupId
                                };
                                videoDevices.push(videoDevice);
                            }
                        } catch (e_1_1) {
                            e_1 = {
                                error: e_1_1
                            };
                        } finally{
                            try {
                                if (devices_1_1 && !devices_1_1.done && (_a = devices_1.return)) _a.call(devices_1);
                            } finally{
                                if (e_1) throw e_1.error;
                            }
                        }
                        return [
                            2 /*return*/ ,
                            videoDevices
                        ];
                }
            });
        });
    };
    /**
     * Obtain the list of available devices with type 'videoinput'.
     *
     * @returns {Promise<VideoInputDevice[]>} an array of available video input devices
     *
     * @memberOf BrowserCodeReader
     *
     * @deprecated Use `listVideoInputDevices` instead.
     */ BrowserCodeReader.prototype.getVideoInputDevices = function() {
        return __awaiter(this, void 0, void 0, function() {
            var devices;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            this.listVideoInputDevices()
                        ];
                    case 1:
                        devices = _a.sent();
                        return [
                            2 /*return*/ ,
                            devices.map(function(d) {
                                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$VideoInputDevice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VideoInputDevice"](d.deviceId, d.label);
                            })
                        ];
                }
            });
        });
    };
    /**
     * Let's you find a device using it's Id.
     */ BrowserCodeReader.prototype.findDeviceById = function(deviceId) {
        return __awaiter(this, void 0, void 0, function() {
            var devices;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            this.listVideoInputDevices()
                        ];
                    case 1:
                        devices = _a.sent();
                        if (!devices) {
                            return [
                                2 /*return*/ ,
                                null
                            ];
                        }
                        return [
                            2 /*return*/ ,
                            devices.find(function(x) {
                                return x.deviceId === deviceId;
                            })
                        ];
                }
            });
        });
    };
    /**
     * Decodes the barcode from the device specified by deviceId while showing the video in the specified video element.
     *
     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.
     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns The decoding result.
     *
     * @memberOf BrowserCodeReader
     *
     * @deprecated Use `decodeOnceFromVideoDevice` instead.
     */ BrowserCodeReader.prototype.decodeFromInputVideoDevice = function(deviceId, videoSource) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            this.decodeOnceFromVideoDevice(deviceId, videoSource)
                        ];
                    case 1:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * In one attempt, tries to decode the barcode from the device specified by deviceId while showing the video in the specified video element.
     *
     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.
     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeOnceFromVideoDevice = function(deviceId, videoSource) {
        return __awaiter(this, void 0, void 0, function() {
            var videoConstraints, constraints;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this.reset();
                        if (!deviceId) {
                            videoConstraints = {
                                facingMode: 'environment'
                            };
                        } else {
                            videoConstraints = {
                                deviceId: {
                                    exact: deviceId
                                }
                            };
                        }
                        constraints = {
                            video: videoConstraints
                        };
                        return [
                            4 /*yield*/ ,
                            this.decodeOnceFromConstraints(constraints, videoSource)
                        ];
                    case 1:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.
     *
     * @param constraints the media stream constraints to get s valid media stream to decode from
     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeOnceFromConstraints = function(constraints, videoSource) {
        return __awaiter(this, void 0, void 0, function() {
            var stream;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            navigator.mediaDevices.getUserMedia(constraints)
                        ];
                    case 1:
                        stream = _a.sent();
                        return [
                            4 /*yield*/ ,
                            this.decodeOnceFromStream(stream, videoSource)
                        ];
                    case 2:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.
     *
     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from
     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeOnceFromStream = function(stream, videoSource) {
        return __awaiter(this, void 0, void 0, function() {
            var video, result;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this.reset();
                        return [
                            4 /*yield*/ ,
                            this.attachStreamToVideo(stream, videoSource)
                        ];
                    case 1:
                        video = _a.sent();
                        return [
                            4 /*yield*/ ,
                            this.decodeOnce(video)
                        ];
                    case 2:
                        result = _a.sent();
                        return [
                            2 /*return*/ ,
                            result
                        ];
                }
            });
        });
    };
    /**
     * Continuously decodes the barcode from the device specified by device while showing the video in the specified video element.
     *
     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.
     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns {Promise<void>}
     *
     * @memberOf BrowserCodeReader
     *
     * @deprecated Use `decodeFromVideoDevice` instead.
     */ BrowserCodeReader.prototype.decodeFromInputVideoDeviceContinuously = function(deviceId, videoSource, callbackFn) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            this.decodeFromVideoDevice(deviceId, videoSource, callbackFn)
                        ];
                    case 1:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * Continuously tries to decode the barcode from the device specified by device while showing the video in the specified video element.
     *
     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.
     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns {Promise<void>}
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeFromVideoDevice = function(deviceId, videoSource, callbackFn) {
        return __awaiter(this, void 0, void 0, function() {
            var videoConstraints, constraints;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        if (!deviceId) {
                            videoConstraints = {
                                facingMode: 'environment'
                            };
                        } else {
                            videoConstraints = {
                                deviceId: {
                                    exact: deviceId
                                }
                            };
                        }
                        constraints = {
                            video: videoConstraints
                        };
                        return [
                            4 /*yield*/ ,
                            this.decodeFromConstraints(constraints, videoSource, callbackFn)
                        ];
                    case 1:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * Continuously tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.
     *
     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from
     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeFromConstraints = function(constraints, videoSource, callbackFn) {
        return __awaiter(this, void 0, void 0, function() {
            var stream;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            navigator.mediaDevices.getUserMedia(constraints)
                        ];
                    case 1:
                        stream = _a.sent();
                        return [
                            4 /*yield*/ ,
                            this.decodeFromStream(stream, videoSource, callbackFn)
                        ];
                    case 2:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.
     *
     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from
     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeFromStream = function(stream, videoSource, callbackFn) {
        return __awaiter(this, void 0, void 0, function() {
            var video;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this.reset();
                        return [
                            4 /*yield*/ ,
                            this.attachStreamToVideo(stream, videoSource)
                        ];
                    case 1:
                        video = _a.sent();
                        return [
                            4 /*yield*/ ,
                            this.decodeContinuously(video, callbackFn)
                        ];
                    case 2:
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    /**
     * Breaks the decoding loop.
     */ BrowserCodeReader.prototype.stopAsyncDecode = function() {
        this._stopAsyncDecode = true;
    };
    /**
     * Breaks the decoding loop.
     */ BrowserCodeReader.prototype.stopContinuousDecode = function() {
        this._stopContinuousDecode = true;
    };
    /**
     * Sets the new stream and request a new decoding-with-delay.
     *
     * @param stream The stream to be shown in the video element.
     * @param decodeFn A callback for the decode method.
     */ BrowserCodeReader.prototype.attachStreamToVideo = function(stream, videoSource) {
        return __awaiter(this, void 0, void 0, function() {
            var videoElement;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        videoElement = this.prepareVideoElement(videoSource);
                        this.addVideoSource(videoElement, stream);
                        this.videoElement = videoElement;
                        this.stream = stream;
                        return [
                            4 /*yield*/ ,
                            this.playVideoOnLoadAsync(videoElement)
                        ];
                    case 1:
                        _a.sent();
                        return [
                            2 /*return*/ ,
                            videoElement
                        ];
                }
            });
        });
    };
    /**
     *
     * @param videoElement
     */ BrowserCodeReader.prototype.playVideoOnLoadAsync = function(videoElement) {
        var _this = this;
        return new Promise(function(resolve, reject) {
            return _this.playVideoOnLoad(videoElement, function() {
                return resolve();
            });
        });
    };
    /**
     * Binds listeners and callbacks to the videoElement.
     *
     * @param element
     * @param callbackFn
     */ BrowserCodeReader.prototype.playVideoOnLoad = function(element, callbackFn) {
        var _this = this;
        this.videoEndedListener = function() {
            return _this.stopStreams();
        };
        this.videoCanPlayListener = function() {
            return _this.tryPlayVideo(element);
        };
        element.addEventListener('ended', this.videoEndedListener);
        element.addEventListener('canplay', this.videoCanPlayListener);
        element.addEventListener('playing', callbackFn);
        // if canplay was already fired, we won't know when to play, so just give it a try
        this.tryPlayVideo(element);
    };
    /**
     * Checks if the given video element is currently playing.
     */ BrowserCodeReader.prototype.isVideoPlaying = function(video) {
        return video.currentTime > 0 && !video.paused && !video.ended && video.readyState > 2;
    };
    /**
     * Just tries to play the video and logs any errors.
     * The play call is only made is the video is not already playing.
     */ BrowserCodeReader.prototype.tryPlayVideo = function(videoElement) {
        return __awaiter(this, void 0, void 0, function() {
            var _a;
            return __generator(this, function(_b) {
                switch(_b.label){
                    case 0:
                        if (this.isVideoPlaying(videoElement)) {
                            console.warn('Trying to play video that is already playing.');
                            return [
                                2 /*return*/ 
                            ];
                        }
                        _b.label = 1;
                    case 1:
                        _b.trys.push([
                            1,
                            3,
                            ,
                            4
                        ]);
                        return [
                            4 /*yield*/ ,
                            videoElement.play()
                        ];
                    case 2:
                        _b.sent();
                        return [
                            3 /*break*/ ,
                            4
                        ];
                    case 3:
                        _a = _b.sent();
                        console.warn('It was not possible to play the video.');
                        return [
                            3 /*break*/ ,
                            4
                        ];
                    case 4:
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    /**
     * Searches and validates a media element.
     */ BrowserCodeReader.prototype.getMediaElement = function(mediaElementId, type) {
        var mediaElement = document.getElementById(mediaElementId);
        if (!mediaElement) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]("element with id '" + mediaElementId + "' not found");
        }
        if (mediaElement.nodeName.toLowerCase() !== type.toLowerCase()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]("element with id '" + mediaElementId + "' must be an " + type + " element");
        }
        return mediaElement;
    };
    /**
     * Decodes the barcode from an image.
     *
     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.
     * @param {string} [url]
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeFromImage = function(source, url) {
        if (!source && !url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('either imageElement with a src set or an url must be provided');
        }
        if (url && !source) {
            return this.decodeFromImageUrl(url);
        }
        return this.decodeFromImageElement(source);
    };
    /**
     * Decodes the barcode from a video.
     *
     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.
     * @param {string} [url]
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.decodeFromVideo = function(source, url) {
        if (!source && !url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Either an element with a src set or an URL must be provided');
        }
        if (url && !source) {
            return this.decodeFromVideoUrl(url);
        }
        return this.decodeFromVideoElement(source);
    };
    /**
     * Decodes continuously the barcode from a video.
     *
     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.
     * @param {string} [url]
     * @returns {Promise<Result>} The decoding result.
     *
     * @memberOf BrowserCodeReader
     *
     * @experimental
     */ BrowserCodeReader.prototype.decodeFromVideoContinuously = function(source, url, callbackFn) {
        if (undefined === source && undefined === url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Either an element with a src set or an URL must be provided');
        }
        if (url && !source) {
            return this.decodeFromVideoUrlContinuously(url, callbackFn);
        }
        return this.decodeFromVideoElementContinuously(source, callbackFn);
    };
    /**
     * Decodes something from an image HTML element.
     */ BrowserCodeReader.prototype.decodeFromImageElement = function(source) {
        if (!source) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('An image element must be provided.');
        }
        this.reset();
        var element = this.prepareImageElement(source);
        this.imageElement = element;
        var task;
        if (this.isImageLoaded(element)) {
            task = this.decodeOnce(element, false, true);
        } else {
            task = this._decodeOnLoadImage(element);
        }
        return task;
    };
    /**
     * Decodes something from an image HTML element.
     */ BrowserCodeReader.prototype.decodeFromVideoElement = function(source) {
        var element = this._decodeFromVideoElementSetup(source);
        return this._decodeOnLoadVideo(element);
    };
    /**
     * Decodes something from an image HTML element.
     */ BrowserCodeReader.prototype.decodeFromVideoElementContinuously = function(source, callbackFn) {
        var element = this._decodeFromVideoElementSetup(source);
        return this._decodeOnLoadVideoContinuously(element, callbackFn);
    };
    /**
     * Sets up the video source so it can be decoded when loaded.
     *
     * @param source The video source element.
     */ BrowserCodeReader.prototype._decodeFromVideoElementSetup = function(source) {
        if (!source) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('A video element must be provided.');
        }
        this.reset();
        var element = this.prepareVideoElement(source);
        // defines the video element before starts decoding
        this.videoElement = element;
        return element;
    };
    /**
     * Decodes an image from a URL.
     */ BrowserCodeReader.prototype.decodeFromImageUrl = function(url) {
        if (!url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('An URL must be provided.');
        }
        this.reset();
        var element = this.prepareImageElement();
        this.imageElement = element;
        var decodeTask = this._decodeOnLoadImage(element);
        element.src = url;
        return decodeTask;
    };
    /**
     * Decodes an image from a URL.
     */ BrowserCodeReader.prototype.decodeFromVideoUrl = function(url) {
        if (!url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('An URL must be provided.');
        }
        this.reset();
        // creates a new element
        var element = this.prepareVideoElement();
        var decodeTask = this.decodeFromVideoElement(element);
        element.src = url;
        return decodeTask;
    };
    /**
     * Decodes an image from a URL.
     *
     * @experimental
     */ BrowserCodeReader.prototype.decodeFromVideoUrlContinuously = function(url, callbackFn) {
        if (!url) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('An URL must be provided.');
        }
        this.reset();
        // creates a new element
        var element = this.prepareVideoElement();
        var decodeTask = this.decodeFromVideoElementContinuously(element, callbackFn);
        element.src = url;
        return decodeTask;
    };
    BrowserCodeReader.prototype._decodeOnLoadImage = function(element) {
        var _this = this;
        return new Promise(function(resolve, reject) {
            _this.imageLoadedListener = function() {
                return _this.decodeOnce(element, false, true).then(resolve, reject);
            };
            element.addEventListener('load', _this.imageLoadedListener);
        });
    };
    BrowserCodeReader.prototype._decodeOnLoadVideo = function(videoElement) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        // plays the video
                        return [
                            4 /*yield*/ ,
                            this.playVideoOnLoadAsync(videoElement)
                        ];
                    case 1:
                        // plays the video
                        _a.sent();
                        return [
                            4 /*yield*/ ,
                            this.decodeOnce(videoElement)
                        ];
                    case 2:
                        // starts decoding after played the video
                        return [
                            2 /*return*/ ,
                            _a.sent()
                        ];
                }
            });
        });
    };
    BrowserCodeReader.prototype._decodeOnLoadVideoContinuously = function(videoElement, callbackFn) {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        // plays the video
                        return [
                            4 /*yield*/ ,
                            this.playVideoOnLoadAsync(videoElement)
                        ];
                    case 1:
                        // plays the video
                        _a.sent();
                        // starts decoding after played the video
                        this.decodeContinuously(videoElement, callbackFn);
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    BrowserCodeReader.prototype.isImageLoaded = function(img) {
        // During the onload event, IE correctly identifies any images that
        // weren’t downloaded as not complete. Others should too. Gecko-based
        // browsers act like NS4 in that they report this incorrectly.
        if (!img.complete) {
            return false;
        }
        // However, they do have two very useful properties: naturalWidth and
        // naturalHeight. These give the true size of the image. If it failed
        // to load, either of these should be zero.
        if (img.naturalWidth === 0) {
            return false;
        }
        // No other way of checking: assume it’s ok.
        return true;
    };
    BrowserCodeReader.prototype.prepareImageElement = function(imageSource) {
        var imageElement;
        if (typeof imageSource === 'undefined') {
            imageElement = document.createElement('img');
            imageElement.width = 200;
            imageElement.height = 200;
        }
        if (typeof imageSource === 'string') {
            imageElement = this.getMediaElement(imageSource, 'img');
        }
        if (imageSource instanceof HTMLImageElement) {
            imageElement = imageSource;
        }
        return imageElement;
    };
    /**
     * Sets a HTMLVideoElement for scanning or creates a new one.
     *
     * @param videoSource The HTMLVideoElement to be set.
     */ BrowserCodeReader.prototype.prepareVideoElement = function(videoSource) {
        var videoElement;
        if (!videoSource && typeof document !== 'undefined') {
            videoElement = document.createElement('video');
            videoElement.width = 200;
            videoElement.height = 200;
        }
        if (typeof videoSource === 'string') {
            videoElement = this.getMediaElement(videoSource, 'video');
        }
        if (videoSource instanceof HTMLVideoElement) {
            videoElement = videoSource;
        }
        // Needed for iOS 11
        videoElement.setAttribute('autoplay', 'true');
        videoElement.setAttribute('muted', 'true');
        videoElement.setAttribute('playsinline', 'true');
        return videoElement;
    };
    /**
     * Tries to decode from the video input until it finds some value.
     */ BrowserCodeReader.prototype.decodeOnce = function(element, retryIfNotFound, retryIfChecksumOrFormatError) {
        var _this = this;
        if (retryIfNotFound === void 0) {
            retryIfNotFound = true;
        }
        if (retryIfChecksumOrFormatError === void 0) {
            retryIfChecksumOrFormatError = true;
        }
        this._stopAsyncDecode = false;
        var loop = function(resolve, reject) {
            if (_this._stopAsyncDecode) {
                reject(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Video stream has ended before any code could be detected.'));
                _this._stopAsyncDecode = undefined;
                return;
            }
            try {
                var result = _this.decode(element);
                resolve(result);
            } catch (e) {
                var ifNotFound = retryIfNotFound && e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                var isChecksumOrFormatError = e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] || e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                var ifChecksumOrFormat = isChecksumOrFormatError && retryIfChecksumOrFormatError;
                if (ifNotFound || ifChecksumOrFormat) {
                    // trying again
                    return setTimeout(loop, _this._timeBetweenDecodingAttempts, resolve, reject);
                }
                reject(e);
            }
        };
        return new Promise(function(resolve, reject) {
            return loop(resolve, reject);
        });
    };
    /**
     * Continuously decodes from video input.
     */ BrowserCodeReader.prototype.decodeContinuously = function(element, callbackFn) {
        var _this = this;
        this._stopContinuousDecode = false;
        var loop = function() {
            if (_this._stopContinuousDecode) {
                _this._stopContinuousDecode = undefined;
                return;
            }
            try {
                var result = _this.decode(element);
                callbackFn(result, null);
                setTimeout(loop, _this.timeBetweenScansMillis);
            } catch (e) {
                callbackFn(null, e);
                var isChecksumOrFormatError = e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] || e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                var isNotFound = e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                if (isChecksumOrFormatError || isNotFound) {
                    // trying again
                    setTimeout(loop, _this._timeBetweenDecodingAttempts);
                }
            }
        };
        loop();
    };
    /**
     * Gets the BinaryBitmap for ya! (and decodes it)
     */ BrowserCodeReader.prototype.decode = function(element) {
        // get binary bitmap for decode function
        var binaryBitmap = this.createBinaryBitmap(element);
        return this.decodeBitmap(binaryBitmap);
    };
    /**
     * Creates a binaryBitmap based in some image source.
     *
     * @param mediaElement HTML element containing drawable image source.
     */ BrowserCodeReader.prototype.createBinaryBitmap = function(mediaElement) {
        var ctx = this.getCaptureCanvasContext(mediaElement);
        // doing a scan with inverted colors on the second scan should only happen for video elements
        var doAutoInvert = false;
        if (mediaElement instanceof HTMLVideoElement) {
            this.drawFrameOnCanvas(mediaElement);
            doAutoInvert = true;
        } else {
            this.drawImageOnCanvas(mediaElement);
        }
        var canvas = this.getCaptureCanvas(mediaElement);
        var luminanceSource = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLCanvasElementLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HTMLCanvasElementLuminanceSource"](canvas, doAutoInvert);
        var hybridBinarizer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$HybridBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](luminanceSource);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BinaryBitmap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hybridBinarizer);
    };
    /**
     *
     */ BrowserCodeReader.prototype.getCaptureCanvasContext = function(mediaElement) {
        if (!this.captureCanvasContext) {
            var elem = this.getCaptureCanvas(mediaElement);
            var ctx = void 0;
            try {
                ctx = elem.getContext('2d', {
                    willReadFrequently: true
                });
            } catch (e) {
                ctx = elem.getContext('2d');
            }
            this.captureCanvasContext = ctx;
        }
        return this.captureCanvasContext;
    };
    /**
     *
     */ BrowserCodeReader.prototype.getCaptureCanvas = function(mediaElement) {
        if (!this.captureCanvas) {
            var elem = this.createCaptureCanvas(mediaElement);
            this.captureCanvas = elem;
        }
        return this.captureCanvas;
    };
    /**
     * Overwriting this allows you to manipulate the next frame in anyway you want before decode.
     */ BrowserCodeReader.prototype.drawFrameOnCanvas = function(srcElement, dimensions, canvasElementContext) {
        if (dimensions === void 0) {
            dimensions = {
                sx: 0,
                sy: 0,
                sWidth: srcElement.videoWidth,
                sHeight: srcElement.videoHeight,
                dx: 0,
                dy: 0,
                dWidth: srcElement.videoWidth,
                dHeight: srcElement.videoHeight
            };
        }
        if (canvasElementContext === void 0) {
            canvasElementContext = this.captureCanvasContext;
        }
        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);
    };
    /**
     * Ovewriting this allows you to manipulate the snapshot image in anyway you want before decode.
     */ BrowserCodeReader.prototype.drawImageOnCanvas = function(srcElement, dimensions, canvasElementContext) {
        if (dimensions === void 0) {
            dimensions = {
                sx: 0,
                sy: 0,
                sWidth: srcElement.naturalWidth,
                sHeight: srcElement.naturalHeight,
                dx: 0,
                dy: 0,
                dWidth: srcElement.naturalWidth,
                dHeight: srcElement.naturalHeight
            };
        }
        if (canvasElementContext === void 0) {
            canvasElementContext = this.captureCanvasContext;
        }
        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);
    };
    /**
     * Call the encapsulated readers decode
     */ BrowserCodeReader.prototype.decodeBitmap = function(binaryBitmap) {
        return this.reader.decode(binaryBitmap, this._hints);
    };
    /**
     * 🖌 Prepares the canvas for capture and scan frames.
     */ BrowserCodeReader.prototype.createCaptureCanvas = function(mediaElement) {
        if (typeof document === 'undefined') {
            this._destroyCaptureCanvas();
            return null;
        }
        var canvasElement = document.createElement('canvas');
        var width;
        var height;
        if (typeof mediaElement !== 'undefined') {
            if (mediaElement instanceof HTMLVideoElement) {
                width = mediaElement.videoWidth;
                height = mediaElement.videoHeight;
            } else if (mediaElement instanceof HTMLImageElement) {
                width = mediaElement.naturalWidth || mediaElement.width;
                height = mediaElement.naturalHeight || mediaElement.height;
            }
        }
        canvasElement.style.width = width + 'px';
        canvasElement.style.height = height + 'px';
        canvasElement.width = width;
        canvasElement.height = height;
        return canvasElement;
    };
    /**
     * Stops the continuous scan and cleans the stream.
     */ BrowserCodeReader.prototype.stopStreams = function() {
        if (this.stream) {
            this.stream.getVideoTracks().forEach(function(t) {
                return t.stop();
            });
            this.stream = undefined;
        }
        if (this._stopAsyncDecode === false) {
            this.stopAsyncDecode();
        }
        if (this._stopContinuousDecode === false) {
            this.stopContinuousDecode();
        }
    };
    /**
     * Resets the code reader to the initial state. Cancels any ongoing barcode scanning from video or camera.
     *
     * @memberOf BrowserCodeReader
     */ BrowserCodeReader.prototype.reset = function() {
        // stops the camera, preview and scan 🔴
        this.stopStreams();
        // clean and forget about HTML elements
        this._destroyVideoElement();
        this._destroyImageElement();
        this._destroyCaptureCanvas();
    };
    BrowserCodeReader.prototype._destroyVideoElement = function() {
        if (!this.videoElement) {
            return;
        }
        // first gives freedon to the element 🕊
        if (typeof this.videoEndedListener !== 'undefined') {
            this.videoElement.removeEventListener('ended', this.videoEndedListener);
        }
        if (typeof this.videoPlayingEventListener !== 'undefined') {
            this.videoElement.removeEventListener('playing', this.videoPlayingEventListener);
        }
        if (typeof this.videoCanPlayListener !== 'undefined') {
            this.videoElement.removeEventListener('loadedmetadata', this.videoCanPlayListener);
        }
        // then forgets about that element 😢
        this.cleanVideoSource(this.videoElement);
        this.videoElement = undefined;
    };
    BrowserCodeReader.prototype._destroyImageElement = function() {
        if (!this.imageElement) {
            return;
        }
        // first gives freedon to the element 🕊
        if (undefined !== this.imageLoadedListener) {
            this.imageElement.removeEventListener('load', this.imageLoadedListener);
        }
        // then forget about that element 😢
        this.imageElement.src = undefined;
        this.imageElement.removeAttribute('src');
        this.imageElement = undefined;
    };
    /**
     * Cleans canvas references 🖌
     */ BrowserCodeReader.prototype._destroyCaptureCanvas = function() {
        // then forget about that element 😢
        this.captureCanvasContext = undefined;
        this.captureCanvas = undefined;
    };
    /**
     * Defines what the videoElement src will be.
     *
     * @param videoElement
     * @param stream
     */ BrowserCodeReader.prototype.addVideoSource = function(videoElement, stream) {
        // Older browsers may not have `srcObject`
        try {
            // @note Throws Exception if interrupted by a new loaded request
            videoElement.srcObject = stream;
        } catch (err) {
            // @note Avoid using this in new browsers, as it is going away.
            // @ts-ignore
            videoElement.src = URL.createObjectURL(stream);
        }
    };
    /**
     * Unbinds a HTML video src property.
     *
     * @param videoElement
     */ BrowserCodeReader.prototype.cleanVideoSource = function(videoElement) {
        try {
            videoElement.srcObject = null;
        } catch (err) {
            videoElement.src = '';
        }
        this.videoElement.removeAttribute('src');
    };
    return BrowserCodeReader;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserAztecCodeReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserAztecCodeReader": ()=>BrowserAztecCodeReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * Aztec Code reader to use from browser.
 *
 * @class BrowserAztecCodeReader
 * @extends {BrowserCodeReader}
 */ var BrowserAztecCodeReader = function(_super) {
    __extends(BrowserAztecCodeReader, _super);
    /**
     * Creates an instance of BrowserAztecCodeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     *
     * @memberOf BrowserAztecCodeReader
     */ function BrowserAztecCodeReader(timeBetweenScansMillis) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        return _super.call(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](), timeBetweenScansMillis) || this;
    }
    return BrowserAztecCodeReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserBarcodeReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserBarcodeReader": ()=>BrowserBarcodeReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * @deprecated Moving to @zxing/browser
 *
 * Barcode reader reader to use from browser.
 */ var BrowserBarcodeReader = function(_super) {
    __extends(BrowserBarcodeReader, _super);
    /**
     * Creates an instance of BrowserBarcodeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     * @param {Map<DecodeHintType, any>} hints
     */ function BrowserBarcodeReader(timeBetweenScansMillis, hints) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        return _super.call(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints), timeBetweenScansMillis, hints) || this;
    }
    return BrowserBarcodeReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserDatamatrixCodeReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserDatamatrixCodeReader": ()=>BrowserDatamatrixCodeReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * @deprecated Moving to @zxing/browser
 *
 * QR Code reader to use from browser.
 */ var BrowserDatamatrixCodeReader = function(_super) {
    __extends(BrowserDatamatrixCodeReader, _super);
    /**
     * Creates an instance of BrowserQRCodeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     */ function BrowserDatamatrixCodeReader(timeBetweenScansMillis) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        return _super.call(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](), timeBetweenScansMillis) || this;
    }
    return BrowserDatamatrixCodeReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserMultiFormatReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserMultiFormatReader": ()=>BrowserMultiFormatReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/MultiFormatReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var BrowserMultiFormatReader = function(_super) {
    __extends(BrowserMultiFormatReader, _super);
    function BrowserMultiFormatReader(hints, timeBetweenScansMillis) {
        if (hints === void 0) {
            hints = null;
        }
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        var _this = this;
        var reader = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        reader.setHints(hints);
        _this = _super.call(this, reader, timeBetweenScansMillis) || this;
        return _this;
    }
    /**
     * Overwrite decodeBitmap to call decodeWithState, which will pay
     * attention to the hints set in the constructor function
     */ BrowserMultiFormatReader.prototype.decodeBitmap = function(binaryBitmap) {
        return this.reader.decodeWithState(binaryBitmap);
    };
    return BrowserMultiFormatReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserPDF417Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserPDF417Reader": ()=>BrowserPDF417Reader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/PDF417Reader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * @deprecated Moving to @zxing/browser
 *
 * QR Code reader to use from browser.
 */ var BrowserPDF417Reader = function(_super) {
    __extends(BrowserPDF417Reader, _super);
    /**
     * Creates an instance of BrowserPDF417Reader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     */ function BrowserPDF417Reader(timeBetweenScansMillis) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        return _super.call(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](), timeBetweenScansMillis) || this;
    }
    return BrowserPDF417Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserQRCodeReader": ()=>BrowserQRCodeReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * @deprecated Moving to @zxing/browser
 *
 * QR Code reader to use from browser.
 */ var BrowserQRCodeReader = function(_super) {
    __extends(BrowserQRCodeReader, _super);
    /**
     * Creates an instance of BrowserQRCodeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     */ function BrowserQRCodeReader(timeBetweenScansMillis) {
        if (timeBetweenScansMillis === void 0) {
            timeBetweenScansMillis = 500;
        }
        return _super.call(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](), timeBetweenScansMillis) || this;
    }
    return BrowserQRCodeReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrowserCodeReader"]);
;
}),
"[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeSvgWriter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserQRCodeSvgWriter": ()=>BrowserQRCodeSvgWriter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * @deprecated Moving to @zxing/browser
 */ var BrowserQRCodeSvgWriter = function() {
    function BrowserQRCodeSvgWriter() {}
    /**
     * Writes and renders a QRCode SVG element.
     *
     * @param contents
     * @param width
     * @param height
     * @param hints
     */ BrowserQRCodeSvgWriter.prototype.write = function(contents, width, height, hints) {
        if (hints === void 0) {
            hints = null;
        }
        if (contents.length === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Found empty contents');
        }
        // if (format != BarcodeFormat.QR_CODE) {
        //   throw new IllegalArgumentException("Can only encode QR_CODE, but got " + format)
        // }
        if (width < 0 || height < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Requested dimensions are too small: ' + width + 'x' + height);
        }
        var errorCorrectionLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L;
        var quietZone = BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE;
        if (hints !== null) {
            if (undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION)) {
                errorCorrectionLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fromString(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION).toString());
            }
            if (undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MARGIN)) {
                quietZone = Number.parseInt(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MARGIN).toString(), 10);
            }
        }
        var code = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].encode(contents, errorCorrectionLevel, hints);
        return this.renderResult(code, width, height, quietZone);
    };
    /**
     * Renders the result and then appends it to the DOM.
     */ BrowserQRCodeSvgWriter.prototype.writeToDom = function(containerElement, contents, width, height, hints) {
        if (hints === void 0) {
            hints = null;
        }
        if (typeof containerElement === 'string') {
            containerElement = document.querySelector(containerElement);
        }
        var svgElement = this.write(contents, width, height, hints);
        if (containerElement) containerElement.appendChild(svgElement);
    };
    /**
     * Note that the input matrix uses 0 == white, 1 == black.
     * The output matrix uses 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).
     */ BrowserQRCodeSvgWriter.prototype.renderResult = function(code, width /*int*/ , height /*int*/ , quietZone /*int*/ ) {
        var input = code.getMatrix();
        if (input === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var inputWidth = input.getWidth();
        var inputHeight = input.getHeight();
        var qrWidth = inputWidth + quietZone * 2;
        var qrHeight = inputHeight + quietZone * 2;
        var outputWidth = Math.max(width, qrWidth);
        var outputHeight = Math.max(height, qrHeight);
        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));
        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested
        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.
        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will
        // handle all the padding from 100x100 (the actual QR) up to 200x160.
        var leftPadding = Math.floor((outputWidth - inputWidth * multiple) / 2);
        var topPadding = Math.floor((outputHeight - inputHeight * multiple) / 2);
        var svgElement = this.createSVGElement(outputWidth, outputHeight);
        for(var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple){
            // Write the contents of this row of the barcode
            for(var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple){
                if (input.get(inputX, inputY) === 1) {
                    var svgRectElement = this.createSvgRectElement(outputX, outputY, multiple, multiple);
                    svgElement.appendChild(svgRectElement);
                }
            }
        }
        return svgElement;
    };
    /**
     * Creates a SVG element.
     *
     * @param w SVG's width attribute
     * @param h SVG's height attribute
     */ BrowserQRCodeSvgWriter.prototype.createSVGElement = function(w, h) {
        var svgElement = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'svg');
        svgElement.setAttributeNS(null, 'height', w.toString());
        svgElement.setAttributeNS(null, 'width', h.toString());
        return svgElement;
    };
    /**
     * Creates a SVG rect element.
     *
     * @param x Element's x coordinate
     * @param y Element's y coordinate
     * @param w Element's width attribute
     * @param h Element's height attribute
     */ BrowserQRCodeSvgWriter.prototype.createSvgRectElement = function(x, y, w, h) {
        var rect = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'rect');
        rect.setAttributeNS(null, 'x', x.toString());
        rect.setAttributeNS(null, 'y', y.toString());
        rect.setAttributeNS(null, 'height', w.toString());
        rect.setAttributeNS(null, 'width', h.toString());
        rect.setAttributeNS(null, 'fill', '#000000');
        return rect;
    };
    BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE = 4;
    /**
     * SVG markup NameSpace
     */ BrowserQRCodeSvgWriter.SVG_NS = 'http://www.w3.org/2000/svg';
    return BrowserQRCodeSvgWriter;
}();
;
}),
"[project]/node_modules/@zxing/library/esm/browser/DecodeContinuouslyCallback.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/node_modules/@zxing/library/esm/browser/HTMLVisualMediaElement.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/node_modules/@zxing/library/esm/browser.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// browser
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserAztecCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserAztecCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserBarcodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserBarcodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserDatamatrixCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserDatamatrixCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserMultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserMultiFormatReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserPDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserPDF417Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserQRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserQRCodeSvgWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeSvgWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$DecodeContinuouslyCallback$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/DecodeContinuouslyCallback.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLCanvasElementLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLVisualMediaElement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/HTMLVisualMediaElement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$VideoInputDevice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/VideoInputDevice.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@zxing/library/esm/browser.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserAztecCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserAztecCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserBarcodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserBarcodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserDatamatrixCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserDatamatrixCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserMultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserMultiFormatReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserPDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserPDF417Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserQRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$BrowserQRCodeSvgWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/BrowserQRCodeSvgWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$DecodeContinuouslyCallback$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/DecodeContinuouslyCallback.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLCanvasElementLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$HTMLVisualMediaElement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/HTMLVisualMediaElement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2f$VideoInputDevice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser/VideoInputDevice.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/@zxing/library/esm/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser.js [app-ssr] (ecmascript) <module evaluation>");
// Exceptions
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArithmeticException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArithmeticException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReaderException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ReaderException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ReedSolomonException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/UnsupportedOperationException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/WriterException.js [app-ssr] (ecmascript)");
// core
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Binarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Binarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BinaryBitmap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BinaryBitmap.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/MultiFormatReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/MultiFormatWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$PlanarYUVLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/PlanarYUVLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$RGBLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/RGBLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
// core/util
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Charset.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StandardCharsets.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)");
// core/common
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DefaultGridSampler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DefaultGridSampler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GlobalHistogramBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GlobalHistogramBinarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSampler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSampler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$HybridBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/HybridBinarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
// core/common/detector
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
// export { default as MonochromeRectangleDetector } from './core/common/detector/MonochromeRectangleDetector';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js [app-ssr] (ecmascript)");
// core/common/reedsolomon
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js [app-ssr] (ecmascript)");
// core/datamatrix
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixWriter.js [app-ssr] (ecmascript)");
// core/pdf417
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/PDF417Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417ResultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/PDF417ResultMetadata.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$decoder$2f$ec$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ErrorCorrection.js [app-ssr] (ecmascript)");
// core/twod/qrcode
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/FormatInformation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataMask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataMask.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MatrixUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MatrixUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js [app-ssr] (ecmascript)");
// core/twod/aztec
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecDetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecDetectorResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$AztecCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/AztecCode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/decoder/Decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$detector$2f$Detector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/detector/Detector.js [app-ssr] (ecmascript)");
// core/oned
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/EAN13Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code128Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code128Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$ITFReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/ITFReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code39Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code39Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code93Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code93Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSS14Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/RSS14Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$RSSExpandedReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoderComplement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$CodaBarReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/CodaBarReader.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@zxing/library/esm/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$browser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/browser.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArithmeticException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ArithmeticException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReaderException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ReaderException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ReedSolomonException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/UnsupportedOperationException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$WriterException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/WriterException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Binarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Binarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BinaryBitmap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/BinaryBitmap.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/MultiFormatReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$MultiFormatWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/MultiFormatWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$PlanarYUVLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/PlanarYUVLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$RGBLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/RGBLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Charset.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/StandardCharsets.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/BitSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DefaultGridSampler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DefaultGridSampler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GlobalHistogramBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GlobalHistogramBinarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSampler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSampler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$HybridBinarizer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/HybridBinarizer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$DefaultPlacement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$SymbolInfo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$encoder$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/encoder/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/PDF417Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417ResultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/PDF417ResultMetadata.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$decoder$2f$ec$2f$ErrorCorrection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ErrorCorrection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/QRCodeWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$ErrorCorrectionLevel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$FormatInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/FormatInformation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$Mode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DecodedBitStreamParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$decoder$2f$DataMask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/decoder/DataMask.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$QRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MatrixUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MatrixUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$ByteMatrix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$encoder$2f$MaskUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecDetectorResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/AztecDetectorResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/Encoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$AztecCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/encoder/AztecCode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/decoder/Decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$detector$2f$Detector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/aztec/detector/Detector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/EAN13Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code128Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code128Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$ITFReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/ITFReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code39Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code39Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code93Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/Code93Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSS14Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/RSS14Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$RSSExpandedReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoderComplement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$CodaBarReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/core/oned/CodaBarReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@zxing/library/esm/index.js [app-ssr] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_%40zxing_library_esm_964a070d._.js.map