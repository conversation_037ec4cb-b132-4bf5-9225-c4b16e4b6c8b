{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/student-filters.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from \"@/components/ui/sheet\"\nimport { Search, Filter, X, SlidersHorizontal } from \"lucide-react\"\nimport { StudentFilters } from \"@/lib/types/student\"\nimport { cn } from \"@/lib/utils\"\n\ninterface StudentFiltersProps {\n  filters: StudentFilters\n  onFiltersChange: (filters: StudentFilters) => void\n  availableGrades: string[]\n  availableSections: string[]\n  availableCourses: string[]\n  availableYears: string[]\n  className?: string\n}\n\nexport function StudentFiltersComponent({\n  filters,\n  onFiltersChange,\n  availableGrades,\n  availableSections,\n  availableCourses,\n  availableYears,\n  className\n}: StudentFiltersProps) {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const updateFilter = (key: keyof StudentFilters, value: any) => {\n    onFiltersChange({\n      ...filters,\n      [key]: value\n    })\n  }\n\n  const toggleArrayFilter = (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => {\n    const currentArray = filters[key] || []\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value]\n    \n    updateFilter(key, newArray.length > 0 ? newArray : undefined)\n  }\n\n  const clearFilters = () => {\n    onFiltersChange({})\n  }\n\n  const getActiveFiltersCount = () => {\n    let count = 0\n    if (filters.search) count++\n    if (filters.grade?.length) count++\n    if (filters.section?.length) count++\n    if (filters.status?.length) count++\n    if (filters.course?.length) count++\n    if (filters.year?.length) count++\n    return count\n  }\n\n  const hasActiveFilters = getActiveFiltersCount() > 0\n\n  const statusOptions = ['Active', 'Inactive', 'Transferred', 'Graduated']\n\n  return (\n    <div className={cn(\"space-y-4\", className)}>\n      {/* Search Bar */}\n      <div className=\"flex gap-2\">\n        <div className=\"relative flex-1\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search students by name, ID, email, or guardian...\"\n            value={filters.search || ''}\n            onChange={(e) => updateFilter('search', e.target.value || undefined)}\n            className=\"pl-10\"\n          />\n        </div>\n        \n        {/* Mobile Filter Button */}\n        <Sheet open={isOpen} onOpenChange={setIsOpen}>\n          <SheetTrigger asChild>\n            <Button variant=\"outline\" className=\"md:hidden\">\n              <SlidersHorizontal className=\"h-4 w-4 mr-2\" />\n              Filters\n              {hasActiveFilters && (\n                <Badge variant=\"secondary\" className=\"ml-2 h-5 w-5 p-0 text-xs\">\n                  {getActiveFiltersCount()}\n                </Badge>\n              )}\n            </Button>\n          </SheetTrigger>\n          \n          <SheetContent side=\"right\" className=\"w-80\">\n            <SheetHeader>\n              <SheetTitle>Filter Students</SheetTitle>\n              <SheetDescription>\n                Apply filters to narrow down the student list\n              </SheetDescription>\n            </SheetHeader>\n            \n            <div className=\"mt-6\">\n              <MobileFilters\n                filters={filters}\n                onFiltersChange={onFiltersChange}\n                availableGrades={availableGrades}\n                availableSections={availableSections}\n                availableCourses={availableCourses}\n                availableYears={availableYears}\n                statusOptions={statusOptions}\n                onClearFilters={clearFilters}\n              />\n            </div>\n          </SheetContent>\n        </Sheet>\n\n        {/* Desktop Filter Button */}\n        <Button \n          variant=\"outline\" \n          className=\"hidden md:flex\"\n          onClick={() => setIsOpen(!isOpen)}\n        >\n          <Filter className=\"h-4 w-4 mr-2\" />\n          Filters\n          {hasActiveFilters && (\n            <Badge variant=\"secondary\" className=\"ml-2 h-5 w-5 p-0 text-xs\">\n              {getActiveFiltersCount()}\n            </Badge>\n          )}\n        </Button>\n\n        {hasActiveFilters && (\n          <Button variant=\"ghost\" onClick={clearFilters}>\n            <X className=\"h-4 w-4 mr-2\" />\n            Clear\n          </Button>\n        )}\n      </div>\n\n      {/* Desktop Filters */}\n      {isOpen && (\n        <Card className=\"hidden md:block\">\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Filter Options</CardTitle>\n            <CardDescription>\n              Select multiple options to filter the student list\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <DesktopFilters\n              filters={filters}\n              onFiltersChange={onFiltersChange}\n              availableGrades={availableGrades}\n              availableSections={availableSections}\n              availableCourses={availableCourses}\n              availableYears={availableYears}\n              statusOptions={statusOptions}\n              toggleArrayFilter={toggleArrayFilter}\n            />\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"flex flex-wrap gap-2\">\n          {filters.search && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Search: {filters.search}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => updateFilter('search', undefined)}\n              />\n            </Badge>\n          )}\n          \n          {filters.grade?.map(grade => (\n            <Badge key={grade} variant=\"secondary\" className=\"gap-1\">\n              Grade {grade}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => toggleArrayFilter('grade', grade)}\n              />\n            </Badge>\n          ))}\n          \n          {filters.section?.map(section => (\n            <Badge key={section} variant=\"secondary\" className=\"gap-1\">\n              Section {section}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => toggleArrayFilter('section', section)}\n              />\n            </Badge>\n          ))}\n          \n          {filters.status?.map(status => (\n            <Badge key={status} variant=\"secondary\" className=\"gap-1\">\n              {status}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => toggleArrayFilter('status', status)}\n              />\n            </Badge>\n          ))}\n          \n          {filters.course?.map(course => (\n            <Badge key={course} variant=\"secondary\" className=\"gap-1\">\n              {course}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => toggleArrayFilter('course', course)}\n              />\n            </Badge>\n          ))}\n          \n          {filters.year?.map(year => (\n            <Badge key={year} variant=\"secondary\" className=\"gap-1\">\n              {year}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => toggleArrayFilter('year', year)}\n              />\n            </Badge>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Desktop Filters Component\nfunction DesktopFilters({\n  filters,\n  onFiltersChange,\n  availableGrades,\n  availableSections,\n  availableCourses,\n  availableYears,\n  statusOptions,\n  toggleArrayFilter\n}: {\n  filters: StudentFilters\n  onFiltersChange: (filters: StudentFilters) => void\n  availableGrades: string[]\n  availableSections: string[]\n  availableCourses: string[]\n  availableYears: string[]\n  statusOptions: string[]\n  toggleArrayFilter: (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => void\n}) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {/* Grade Level Filter */}\n      <div className=\"space-y-3\">\n        <Label className=\"text-sm font-medium\">Grade Level</Label>\n        <div className=\"space-y-2\">\n          {availableGrades.map(grade => (\n            <div key={grade} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`grade-${grade}`}\n                checked={filters.grade?.includes(grade) || false}\n                onCheckedChange={() => toggleArrayFilter('grade', grade)}\n              />\n              <Label htmlFor={`grade-${grade}`} className=\"text-sm\">\n                Grade {grade}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Section Filter */}\n      <div className=\"space-y-3\">\n        <Label className=\"text-sm font-medium\">Section</Label>\n        <div className=\"space-y-2\">\n          {availableSections.map(section => (\n            <div key={section} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`section-${section}`}\n                checked={filters.section?.includes(section) || false}\n                onCheckedChange={() => toggleArrayFilter('section', section)}\n              />\n              <Label htmlFor={`section-${section}`} className=\"text-sm\">\n                Section {section}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Status Filter */}\n      <div className=\"space-y-3\">\n        <Label className=\"text-sm font-medium\">Status</Label>\n        <div className=\"space-y-2\">\n          {statusOptions.map(status => (\n            <div key={status} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`status-${status}`}\n                checked={filters.status?.includes(status) || false}\n                onCheckedChange={() => toggleArrayFilter('status', status)}\n              />\n              <Label htmlFor={`status-${status}`} className=\"text-sm\">\n                {status}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Course Filter */}\n      <div className=\"space-y-3\">\n        <Label className=\"text-sm font-medium\">Course/Track</Label>\n        <div className=\"space-y-2\">\n          {availableCourses.map(course => (\n            <div key={course} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`course-${course}`}\n                checked={filters.course?.includes(course) || false}\n                onCheckedChange={() => toggleArrayFilter('course', course)}\n              />\n              <Label htmlFor={`course-${course}`} className=\"text-sm\">\n                {course}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Year Filter */}\n      <div className=\"space-y-3\">\n        <Label className=\"text-sm font-medium\">Year Level</Label>\n        <div className=\"space-y-2\">\n          {availableYears.map(year => (\n            <div key={year} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`year-${year}`}\n                checked={filters.year?.includes(year) || false}\n                onCheckedChange={() => toggleArrayFilter('year', year)}\n              />\n              <Label htmlFor={`year-${year}`} className=\"text-sm\">\n                {year}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Mobile Filters Component\nfunction MobileFilters({\n  filters,\n  onFiltersChange,\n  availableGrades,\n  availableSections,\n  availableCourses,\n  availableYears,\n  statusOptions,\n  onClearFilters\n}: {\n  filters: StudentFilters\n  onFiltersChange: (filters: StudentFilters) => void\n  availableGrades: string[]\n  availableSections: string[]\n  availableCourses: string[]\n  availableYears: string[]\n  statusOptions: string[]\n  onClearFilters: () => void\n}) {\n  const toggleArrayFilter = (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => {\n    const currentArray = filters[key] || []\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value]\n    \n    onFiltersChange({\n      ...filters,\n      [key]: newArray.length > 0 ? newArray : undefined\n    })\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <DesktopFilters\n        filters={filters}\n        onFiltersChange={onFiltersChange}\n        availableGrades={availableGrades}\n        availableSections={availableSections}\n        availableCourses={availableCourses}\n        availableYears={availableYears}\n        statusOptions={statusOptions}\n        toggleArrayFilter={toggleArrayFilter}\n      />\n      \n      <Separator />\n      \n      <Button variant=\"outline\" onClick={onClearFilters} className=\"w-full\">\n        <X className=\"h-4 w-4 mr-2\" />\n        Clear All Filters\n      </Button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAdA;;;;;;;;;;;;;AA0BO,SAAS,wBAAwB,EACtC,OAAO,EACP,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,SAAS,EACW;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,eAAe,CAAC,KAA2B;QAC/C,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC,KAAyD;QAClF,MAAM,eAAe,OAAO,CAAC,IAAI,IAAI,EAAE;QACvC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAE5B,aAAa,KAAK,SAAS,MAAM,GAAG,IAAI,WAAW;IACrD;IAEA,MAAM,eAAe;QACnB,gBAAgB,CAAC;IACnB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,EAAE;QACpB,IAAI,QAAQ,KAAK,EAAE,QAAQ;QAC3B,IAAI,QAAQ,OAAO,EAAE,QAAQ;QAC7B,IAAI,QAAQ,MAAM,EAAE,QAAQ;QAC5B,IAAI,QAAQ,MAAM,EAAE,QAAQ;QAC5B,IAAI,QAAQ,IAAI,EAAE,QAAQ;QAC1B,OAAO;IACT;IAEA,MAAM,mBAAmB,0BAA0B;IAEnD,MAAM,gBAAgB;QAAC;QAAU;QAAY;QAAe;KAAY;IAExE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,0HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;gCAC1D,WAAU;;;;;;;;;;;;kCAKd,8OAAC,0HAAA,CAAA,QAAK;wBAAC,MAAM;wBAAQ,cAAc;;0CACjC,8OAAC,0HAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,gOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;wCAAiB;wCAE7C,kCACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC;;;;;;;;;;;;;;;;;0CAMT,8OAAC,0HAAA,CAAA,eAAY;gCAAC,MAAK;gCAAQ,WAAU;;kDACnC,8OAAC,0HAAA,CAAA,cAAW;;0DACV,8OAAC,0HAAA,CAAA,aAAU;0DAAC;;;;;;0DACZ,8OAAC,0HAAA,CAAA,mBAAgB;0DAAC;;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS;4CACT,iBAAiB;4CACjB,iBAAiB;4CACjB,mBAAmB;4CACnB,kBAAkB;4CAClB,gBAAgB;4CAChB,eAAe;4CACf,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAOxB,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,UAAU,CAAC;;0CAE1B,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;4BAElC,kCACC,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC;;;;;;;;;;;;oBAKN,kCACC,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,SAAS;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAOnC,wBACC,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BACC,SAAS;4BACT,iBAAiB;4BACjB,iBAAiB;4BACjB,mBAAmB;4BACnB,kBAAkB;4BAClB,gBAAgB;4BAChB,eAAe;4BACf,mBAAmB;;;;;;;;;;;;;;;;;YAO1B,kCACC,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,MAAM,kBACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BAClC,QAAQ,MAAM;0CACvB,8OAAC,4LAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,aAAa,UAAU;;;;;;;;;;;;oBAK3C,QAAQ,KAAK,EAAE,IAAI,CAAA,sBAClB,8OAAC,0HAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAAQ;gCAChD;8CACP,8OAAC,4LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,kBAAkB,SAAS;;;;;;;2BAJlC;;;;;oBASb,QAAQ,OAAO,EAAE,IAAI,CAAA,wBACpB,8OAAC,0HAAA,CAAA,QAAK;4BAAe,SAAQ;4BAAY,WAAU;;gCAAQ;gCAChD;8CACT,8OAAC,4LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,kBAAkB,WAAW;;;;;;;2BAJpC;;;;;oBASb,QAAQ,MAAM,EAAE,IAAI,CAAA,uBACnB,8OAAC,0HAAA,CAAA,QAAK;4BAAc,SAAQ;4BAAY,WAAU;;gCAC/C;8CACD,8OAAC,4LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,kBAAkB,UAAU;;;;;;;2BAJnC;;;;;oBASb,QAAQ,MAAM,EAAE,IAAI,CAAA,uBACnB,8OAAC,0HAAA,CAAA,QAAK;4BAAc,SAAQ;4BAAY,WAAU;;gCAC/C;8CACD,8OAAC,4LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,kBAAkB,UAAU;;;;;;;2BAJnC;;;;;oBASb,QAAQ,IAAI,EAAE,IAAI,CAAA,qBACjB,8OAAC,0HAAA,CAAA,QAAK;4BAAY,SAAQ;4BAAY,WAAU;;gCAC7C;8CACD,8OAAC,4LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,kBAAkB,QAAQ;;;;;;;2BAJjC;;;;;;;;;;;;;;;;;AAYxB;AAEA,4BAA4B;AAC5B,SAAS,eAAe,EACtB,OAAO,EACP,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,iBAAiB,EAUlB;IACC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAA,sBACnB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAI,CAAC,MAAM,EAAE,OAAO;wCACpB,SAAS,QAAQ,KAAK,EAAE,SAAS,UAAU;wCAC3C,iBAAiB,IAAM,kBAAkB,SAAS;;;;;;kDAEpD,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAS,CAAC,MAAM,EAAE,OAAO;wCAAE,WAAU;;4CAAU;4CAC7C;;;;;;;;+BAPD;;;;;;;;;;;;;;;;0BAehB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAA,wBACrB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAI,CAAC,QAAQ,EAAE,SAAS;wCACxB,SAAS,QAAQ,OAAO,EAAE,SAAS,YAAY;wCAC/C,iBAAiB,IAAM,kBAAkB,WAAW;;;;;;kDAEtD,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAS,CAAC,QAAQ,EAAE,SAAS;wCAAE,WAAU;;4CAAU;4CAC/C;;;;;;;;+BAPH;;;;;;;;;;;;;;;;0BAehB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC;gCAAiB,WAAU;;kDAC1B,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAI,CAAC,OAAO,EAAE,QAAQ;wCACtB,SAAS,QAAQ,MAAM,EAAE,SAAS,WAAW;wCAC7C,iBAAiB,IAAM,kBAAkB,UAAU;;;;;;kDAErD,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAS,CAAC,OAAO,EAAE,QAAQ;wCAAE,WAAU;kDAC3C;;;;;;;+BAPK;;;;;;;;;;;;;;;;0BAehB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;gCAAiB,WAAU;;kDAC1B,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAI,CAAC,OAAO,EAAE,QAAQ;wCACtB,SAAS,QAAQ,MAAM,EAAE,SAAS,WAAW;wCAC7C,iBAAiB,IAAM,kBAAkB,UAAU;;;;;;kDAErD,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAS,CAAC,OAAO,EAAE,QAAQ;wCAAE,WAAU;kDAC3C;;;;;;;+BAPK;;;;;;;;;;;;;;;;0BAehB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAA,qBAClB,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAI,CAAC,KAAK,EAAE,MAAM;wCAClB,SAAS,QAAQ,IAAI,EAAE,SAAS,SAAS;wCACzC,iBAAiB,IAAM,kBAAkB,QAAQ;;;;;;kDAEnD,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAS,CAAC,KAAK,EAAE,MAAM;wCAAE,WAAU;kDACvC;;;;;;;+BAPK;;;;;;;;;;;;;;;;;;;;;;AAetB;AAEA,2BAA2B;AAC3B,SAAS,cAAc,EACrB,OAAO,EACP,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,cAAc,EAUf;IACC,MAAM,oBAAoB,CAAC,KAAyD;QAClF,MAAM,eAAe,OAAO,CAAC,IAAI,IAAI,EAAE;QACvC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAE5B,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE,SAAS,MAAM,GAAG,IAAI,WAAW;QAC1C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,gBAAgB;gBAChB,eAAe;gBACf,mBAAmB;;;;;;0BAGrB,8OAAC,8HAAA,CAAA,YAAS;;;;;0BAEV,8OAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,SAAS;gBAAgB,WAAU;;kCAC3D,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/qr-code-display.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport {\n  QrCode,\n  Download,\n  Printer,\n  RefreshCw,\n  Copy,\n  CheckCircle,\n  AlertTriangle\n} from \"lucide-react\"\nimport { Student, getFullName } from \"@/lib/types/student\"\nimport { qrGenerator, qrManager } from \"@/lib/utils/qr-code\"\nimport { toast } from \"sonner\"\nimport { cn } from \"@/lib/utils\"\n\ninterface QRCodeDisplayProps {\n  student: Student\n  onQRGenerated?: (qrId: string) => void\n  onQRRegenerated?: (qrId: string) => void\n  className?: string\n}\n\nexport function QRCodeDisplay({\n  student,\n  onQRGenerated,\n  onQRRegenerated,\n  className\n}: QRCodeDisplayProps) {\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [qrData, setQrData] = useState<string | null>(null)\n  const [qrId, setQrId] = useState<string | null>(student.qrCode || null)\n\n  useEffect(() => {\n    if (student.qrCode) {\n      // Generate QR data string for display\n      const qrString = qrGenerator.generateQRString(student)\n      setQrData(qrString)\n    }\n  }, [student])\n\n  const handleGenerate = async () => {\n    setIsGenerating(true)\n    try {\n      const newQrId = await qrManager.generateForStudent(student)\n      const qrString = qrGenerator.generateQRString(student)\n      \n      setQrId(newQrId)\n      setQrData(qrString)\n      \n      if (onQRGenerated) {\n        onQRGenerated(newQrId)\n      }\n      \n      toast.success(\"QR code generated successfully!\")\n    } catch (error) {\n      toast.error(\"Failed to generate QR code\")\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleRegenerate = async () => {\n    setIsGenerating(true)\n    try {\n      const newQrId = await qrManager.regenerateForStudent(student)\n      const qrString = qrGenerator.generateQRString(student)\n      \n      setQrId(newQrId)\n      setQrData(qrString)\n      \n      if (onQRRegenerated) {\n        onQRRegenerated(newQrId)\n      }\n      \n      toast.success(\"QR code regenerated successfully!\")\n    } catch (error) {\n      toast.error(\"Failed to regenerate QR code\")\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleCopyData = async () => {\n    if (qrData) {\n      try {\n        await navigator.clipboard.writeText(qrData)\n        toast.success(\"QR code data copied to clipboard\")\n      } catch (error) {\n        toast.error(\"Failed to copy QR code data\")\n      }\n    }\n  }\n\n  const handleDownload = () => {\n    if (!qrId) return\n    \n    // In a real implementation, this would download the actual QR code image\n    // For now, we'll simulate the download\n    toast.success(\"QR code download started\")\n    console.log(\"Downloading QR code:\", qrId)\n  }\n\n  const handlePrint = () => {\n    if (!qrId) return\n    \n    // Create a print window with the QR code\n    const printWindow = window.open('', '_blank')\n    if (printWindow) {\n      const fullName = getFullName(student)\n      \n      printWindow.document.write(`\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <title>QR Code - ${fullName}</title>\n          <style>\n            body {\n              font-family: Arial, sans-serif;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              min-height: 100vh;\n              margin: 0;\n              background: white;\n            }\n            .qr-container {\n              text-align: center;\n              padding: 20px;\n              border: 2px solid #ddd;\n              border-radius: 8px;\n            }\n            .qr-placeholder {\n              width: 200px;\n              height: 200px;\n              border: 2px dashed #ccc;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              margin: 20px auto;\n              font-size: 14px;\n              color: #666;\n            }\n            .student-info {\n              margin-top: 15px;\n            }\n            .student-name {\n              font-size: 18px;\n              font-weight: bold;\n              margin-bottom: 5px;\n            }\n            .student-details {\n              font-size: 14px;\n              color: #666;\n              line-height: 1.4;\n            }\n            @media print {\n              body { margin: 0; }\n              .qr-container { border: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"qr-container\">\n            <div class=\"qr-placeholder\">\n              QR Code<br/>\n              ${qrId}\n            </div>\n            <div class=\"student-info\">\n              <div class=\"student-name\">${fullName}</div>\n              <div class=\"student-details\">\n                Student ID: ${student.id}<br/>\n                Grade ${student.grade}${student.section ? ` - Section ${student.section}` : ''}<br/>\n                ${student.course} - ${student.year}<br/>\n                Tanauan School of Arts and Trade\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `)\n      \n      printWindow.document.close()\n      printWindow.print()\n    }\n  }\n\n  const fullName = getFullName(student)\n\n  return (\n    <Card className={cn(\"w-full\", className)}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <QrCode className=\"h-5 w-5\" />\n          Student QR Code\n        </CardTitle>\n        <CardDescription>\n          QR code for {fullName} ({student.id})\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* QR Code Display */}\n        <div className=\"flex flex-col items-center space-y-4\">\n          <div className=\"w-48 h-48 bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center\">\n            {qrId ? (\n              <div className=\"text-center\">\n                <QrCode className=\"h-24 w-24 mx-auto mb-2 text-muted-foreground\" />\n                <p className=\"text-xs text-muted-foreground\">QR Code Preview</p>\n                <p className=\"text-xs font-mono break-all px-2\">{qrId}</p>\n              </div>\n            ) : (\n              <div className=\"text-center text-muted-foreground\">\n                <QrCode className=\"h-12 w-12 mx-auto mb-2\" />\n                <p className=\"text-sm\">No QR Code Generated</p>\n              </div>\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-wrap gap-2 justify-center\">\n            {!qrId ? (\n              <Button \n                onClick={handleGenerate} \n                disabled={isGenerating}\n                className=\"min-w-[120px]\"\n              >\n                {isGenerating ? (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <QrCode className=\"h-4 w-4 mr-2\" />\n                    Generate QR\n                  </>\n                )}\n              </Button>\n            ) : (\n              <>\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={handleRegenerate}\n                  disabled={isGenerating}\n                >\n                  {isGenerating ? (\n                    <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  ) : (\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  )}\n                  Regenerate\n                </Button>\n                \n                <Button variant=\"outline\" size=\"sm\" onClick={handleDownload}>\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Download\n                </Button>\n                \n                <Button variant=\"outline\" size=\"sm\" onClick={handlePrint}>\n                  <Printer className=\"h-4 w-4 mr-2\" />\n                  Print\n                </Button>\n                \n                <Button variant=\"outline\" size=\"sm\" onClick={handleCopyData}>\n                  <Copy className=\"h-4 w-4 mr-2\" />\n                  Copy Data\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* QR Code Information */}\n        {qrId && (\n          <div className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <span className=\"text-muted-foreground\">QR Code ID:</span>\n              <span className=\"font-mono text-xs break-all\">{qrId}</span>\n              \n              <span className=\"text-muted-foreground\">Student ID:</span>\n              <span className=\"font-mono\">{student.id}</span>\n              \n              <span className=\"text-muted-foreground\">Student Name:</span>\n              <span>{fullName}</span>\n              \n              <span className=\"text-muted-foreground\">Grade & Section:</span>\n              <span>Grade {student.grade}{student.section ? ` - ${student.section}` : ''}</span>\n              \n              <span className=\"text-muted-foreground\">Course:</span>\n              <span>{student.course}</span>\n              \n              <span className=\"text-muted-foreground\">Status:</span>\n              <Badge variant={student.status === 'Active' ? 'default' : 'secondary'}>\n                {student.status}\n              </Badge>\n            </div>\n          </div>\n        )}\n\n        {/* Usage Instructions */}\n        <Alert>\n          <AlertTriangle className=\"h-4 w-4\" />\n          <AlertDescription>\n            <strong>Usage Instructions:</strong>\n            <ul className=\"list-disc list-inside mt-2 space-y-1 text-sm\">\n              <li>Use this QR code for quick attendance marking</li>\n              <li>Scan with the QRSAMS mobile app or scanner</li>\n              <li>Print and laminate for durability</li>\n              <li>Keep the QR code secure and report if lost</li>\n              <li>QR codes are valid for one academic year</li>\n            </ul>\n          </AlertDescription>\n        </Alert>\n\n        {/* Validation Status */}\n        {qrData && (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <CheckCircle className=\"h-4 w-4 text-green-600\" />\n            <span className=\"text-green-600\">QR code is valid and ready to use</span>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AAnBA;;;;;;;;;;;;AA4BO,SAAS,cAAc,EAC5B,OAAO,EACP,aAAa,EACb,eAAe,EACf,SAAS,EACU;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,QAAQ,MAAM,IAAI;IAElE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM,EAAE;YAClB,sCAAsC;YACtC,MAAM,WAAW,0HAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;YAC9C,UAAU;QACZ;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,0HAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC;YACnD,MAAM,WAAW,0HAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;YAE9C,QAAQ;YACR,UAAU;YAEV,IAAI,eAAe;gBACjB,cAAc;YAChB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,0HAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC;YACrD,MAAM,WAAW,0HAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;YAE9C,QAAQ;YACR,UAAU;YAEV,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,yEAAyE;QACzE,uCAAuC;QACvC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,QAAQ,GAAG,CAAC,wBAAwB;IACtC;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;QAEX,yCAAyC;QACzC,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,aAAa;YACf,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;YAE7B,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;2BAIP,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAmDxB,EAAE,KAAK;;;wCAGmB,EAAE,SAAS;;4BAEvB,EAAE,QAAQ,EAAE,CAAC;sBACnB,EAAE,QAAQ,KAAK,GAAG,QAAQ,OAAO,GAAG,CAAC,WAAW,EAAE,QAAQ,OAAO,EAAE,GAAG,GAAG;gBAC/E,EAAE,QAAQ,MAAM,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC;;;;;;;MAO7C,CAAC;YAED,YAAY,QAAQ,CAAC,KAAK;YAC1B,YAAY,KAAK;QACnB;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAE7B,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,8OAAC,yHAAA,CAAA,kBAAe;;4BAAC;4BACF;4BAAS;4BAAG,QAAQ,EAAE;4BAAC;;;;;;;;;;;;;0BAGxC,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,qBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;yDAGnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAM7B,8OAAC;gCAAI,WAAU;0CACZ,CAAC,qBACA,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA8B;;qEAIrD;;0DACE,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;yDAMzC;;sDACE,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU;;gDAET,6BACC,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDACrB;;;;;;;sDAIJ,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;;8DAC3C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIvC,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;;8DAC3C,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAItC,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;;8DAC3C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;oBAS1C,sBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAE/C,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAK,WAAU;8CAAa,QAAQ,EAAE;;;;;;8CAEvC,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;8CAAM;;;;;;8CAEP,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;;wCAAK;wCAAO,QAAQ,KAAK;wCAAE,QAAQ,OAAO,GAAG,CAAC,GAAG,EAAE,QAAQ,OAAO,EAAE,GAAG;;;;;;;8CAExE,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;8CAAM,QAAQ,MAAM;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;8CACvD,QAAQ,MAAM;;;;;;;;;;;;;;;;;kCAOvB,8OAAC,0HAAA,CAAA,QAAK;;0CACJ,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,0HAAA,CAAA,mBAAgB;;kDACf,8OAAC;kDAAO;;;;;;kDACR,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAMT,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/attendance-statistics.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { \n  TrendingUp, \n  TrendingDown, \n  Calendar, \n  Clock, \n  CheckCircle, \n  XCircle, \n  AlertCircle,\n  BarChart3,\n  Download,\n  Eye\n} from \"lucide-react\"\nimport { Student, AttendanceStats } from \"@/lib/types/student\"\nimport { cn } from \"@/lib/utils\"\n\ninterface AttendanceStatisticsProps {\n  student: Student\n  showDetailed?: boolean\n  showActions?: boolean\n  className?: string\n}\n\nexport function AttendanceStatistics({\n  student,\n  showDetailed = false,\n  showActions = false,\n  className\n}: AttendanceStatisticsProps) {\n  const stats = student.attendanceStats\n\n  if (!stats) {\n    return (\n      <Card className={cn(\"w-full\", className)}>\n        <CardContent className=\"text-center py-8\">\n          <Clock className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">No Attendance Data</h3>\n          <p className=\"text-muted-foreground\">\n            Attendance statistics will appear here once the student starts attending classes.\n          </p>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  const getAttendanceGrade = (rate: number): { grade: string; color: string; icon: React.ReactNode } => {\n    if (rate >= 95) {\n      return {\n        grade: 'Excellent',\n        color: 'text-green-600',\n        icon: <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      }\n    } else if (rate >= 85) {\n      return {\n        grade: 'Good',\n        color: 'text-blue-600',\n        icon: <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n      }\n    } else if (rate >= 75) {\n      return {\n        grade: 'Fair',\n        color: 'text-yellow-600',\n        icon: <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n      }\n    } else {\n      return {\n        grade: 'Poor',\n        color: 'text-red-600',\n        icon: <XCircle className=\"h-4 w-4 text-red-600\" />\n      }\n    }\n  }\n\n  const attendanceGrade = getAttendanceGrade(stats.attendanceRate)\n\n  const getTrendIcon = () => {\n    // Mock trend calculation - in real app, this would compare with previous period\n    const trend = stats.attendanceRate >= 85 ? 'up' : 'down'\n    return trend === 'up' ? (\n      <TrendingUp className=\"h-4 w-4 text-green-600\" />\n    ) : (\n      <TrendingDown className=\"h-4 w-4 text-red-600\" />\n    )\n  }\n\n  return (\n    <Card className={cn(\"w-full\", className)}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <BarChart3 className=\"h-5 w-5\" />\n          Attendance Statistics\n          {showActions && (\n            <div className=\"ml-auto flex gap-2\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Eye className=\"h-4 w-4 mr-2\" />\n                View Details\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export\n              </Button>\n            </div>\n          )}\n        </CardTitle>\n        <CardDescription>\n          Attendance summary for the current academic period\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Overall Attendance Rate */}\n        <div className=\"text-center space-y-2\">\n          <div className=\"flex items-center justify-center gap-2\">\n            <span className=\"text-3xl font-bold\">{stats.attendanceRate.toFixed(1)}%</span>\n            {getTrendIcon()}\n          </div>\n          <div className=\"flex items-center justify-center gap-2\">\n            {attendanceGrade.icon}\n            <span className={cn(\"font-medium\", attendanceGrade.color)}>\n              {attendanceGrade.grade} Attendance\n            </span>\n          </div>\n          <Progress value={stats.attendanceRate} className=\"w-full max-w-xs mx-auto\" />\n        </div>\n\n        <Separator />\n\n        {/* Attendance Breakdown */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <div className=\"text-center space-y-1\">\n            <div className=\"text-2xl font-bold text-green-600\">{stats.presentDays}</div>\n            <div className=\"text-sm text-muted-foreground\">Present</div>\n            <div className=\"text-xs text-muted-foreground\">\n              {((stats.presentDays / stats.totalDays) * 100).toFixed(1)}%\n            </div>\n          </div>\n          \n          <div className=\"text-center space-y-1\">\n            <div className=\"text-2xl font-bold text-yellow-600\">{stats.lateDays}</div>\n            <div className=\"text-sm text-muted-foreground\">Late</div>\n            <div className=\"text-xs text-muted-foreground\">\n              {((stats.lateDays / stats.totalDays) * 100).toFixed(1)}%\n            </div>\n          </div>\n          \n          <div className=\"text-center space-y-1\">\n            <div className=\"text-2xl font-bold text-red-600\">{stats.absentDays}</div>\n            <div className=\"text-sm text-muted-foreground\">Absent</div>\n            <div className=\"text-xs text-muted-foreground\">\n              {((stats.absentDays / stats.totalDays) * 100).toFixed(1)}%\n            </div>\n          </div>\n          \n          <div className=\"text-center space-y-1\">\n            <div className=\"text-2xl font-bold\">{stats.totalDays}</div>\n            <div className=\"text-sm text-muted-foreground\">Total Days</div>\n            <div className=\"text-xs text-muted-foreground\">\n              School Days\n            </div>\n          </div>\n        </div>\n\n        {showDetailed && (\n          <>\n            <Separator />\n            \n            {/* Detailed Breakdown */}\n            <div className=\"space-y-3\">\n              <h4 className=\"font-medium\">Detailed Breakdown</h4>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span className=\"font-medium\">Present Days</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"font-bold\">{stats.presentDays}</div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {((stats.presentDays / stats.totalDays) * 100).toFixed(1)}%\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n                    <span className=\"font-medium\">Late Arrivals</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"font-bold\">{stats.lateDays}</div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {((stats.lateDays / stats.totalDays) * 100).toFixed(1)}%\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-red-50 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <XCircle className=\"h-4 w-4 text-red-600\" />\n                    <span className=\"font-medium\">Absent Days</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"font-bold\">{stats.absentDays}</div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {((stats.absentDays / stats.totalDays) * 100).toFixed(1)}%\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Last Attendance */}\n        {stats.lastAttendance && (\n          <>\n            <Separator />\n            <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n              <Calendar className=\"h-4 w-4\" />\n              <span>\n                Last attendance: {new Date(stats.lastAttendance).toLocaleDateString()}\n              </span>\n            </div>\n          </>\n        )}\n\n        {/* Attendance Alerts */}\n        {stats.attendanceRate < 75 && (\n          <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\n            <div className=\"flex items-center gap-2 text-red-800\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <span className=\"font-medium\">Low Attendance Alert</span>\n            </div>\n            <p className=\"text-sm text-red-700 mt-1\">\n              This student's attendance rate is below the minimum requirement of 75%. \n              Consider reaching out to the student and guardian.\n            </p>\n          </div>\n        )}\n\n        {stats.absentDays >= 5 && (\n          <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <div className=\"flex items-center gap-2 text-yellow-800\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <span className=\"font-medium\">Frequent Absences</span>\n            </div>\n            <p className=\"text-sm text-yellow-700 mt-1\">\n              This student has been absent for {stats.absentDays} days. \n              Monitor attendance closely and consider intervention.\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n\n// Compact version for table display\nexport function AttendanceStatisticsCompact({\n  student,\n  className\n}: {\n  student: Student\n  className?: string\n}) {\n  const stats = student.attendanceStats\n\n  if (!stats) {\n    return (\n      <div className={cn(\"text-center\", className)}>\n        <span className=\"text-muted-foreground text-sm\">No data</span>\n      </div>\n    )\n  }\n\n  const getAttendanceIcon = (rate: number) => {\n    if (rate >= 95) return <CheckCircle className=\"h-3 w-3 text-green-500\" />\n    if (rate >= 85) return <CheckCircle className=\"h-3 w-3 text-blue-500\" />\n    if (rate >= 75) return <AlertCircle className=\"h-3 w-3 text-yellow-500\" />\n    return <XCircle className=\"h-3 w-3 text-red-500\" />\n  }\n\n  return (\n    <div className={cn(\"flex items-center gap-1\", className)}>\n      {getAttendanceIcon(stats.attendanceRate)}\n      <span className=\"text-sm font-medium\">\n        {stats.attendanceRate.toFixed(1)}%\n      </span>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AApBA;;;;;;;;AA6BO,SAAS,qBAAqB,EACnC,OAAO,EACP,eAAe,KAAK,EACpB,cAAc,KAAK,EACnB,SAAS,EACiB;IAC1B,MAAM,QAAQ,QAAQ,eAAe;IAErC,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;sBAC5B,cAAA,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,QAAQ,IAAI;YACd,OAAO;gBACL,OAAO;gBACP,OAAO;gBACP,oBAAM,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/B;QACF,OAAO,IAAI,QAAQ,IAAI;YACrB,OAAO;gBACL,OAAO;gBACP,OAAO;gBACP,oBAAM,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/B;QACF,OAAO,IAAI,QAAQ,IAAI;YACrB,OAAO;gBACL,OAAO;gBACP,OAAO;gBACP,oBAAM,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/B;QACF,OAAO;YACL,OAAO;gBACL,OAAO;gBACP,OAAO;gBACP,oBAAM,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3B;QACF;IACF;IAEA,MAAM,kBAAkB,mBAAmB,MAAM,cAAc;IAE/D,MAAM,eAAe;QACnB,gFAAgF;QAChF,MAAM,QAAQ,MAAM,cAAc,IAAI,KAAK,OAAO;QAClD,OAAO,UAAU,qBACf,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;iCAEtB,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IAE5B;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;4BAEhC,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAsB,MAAM,cAAc,CAAC,OAAO,CAAC;4CAAG;;;;;;;oCACrE;;;;;;;0CAEH,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,IAAI;kDACrB,8OAAC;wCAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe,gBAAgB,KAAK;;4CACrD,gBAAgB,KAAK;4CAAC;;;;;;;;;;;;;0CAG3B,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,OAAO,MAAM,cAAc;gCAAE,WAAU;;;;;;;;;;;;kCAGnD,8OAAC,8HAAA,CAAA,YAAS;;;;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC,MAAM,WAAW;;;;;;kDACrE,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,AAAC,MAAM,WAAW,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAI9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsC,MAAM,QAAQ;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,AAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAI3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmC,MAAM,UAAU;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,AAAC,MAAM,UAAU,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsB,MAAM,SAAS;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;oBAMlD,8BACC;;0CACE,8OAAC,8HAAA,CAAA,YAAS;;;;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAa,MAAM,WAAW;;;;;;0EAC7C,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,AAAC,MAAM,WAAW,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAKhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAa,MAAM,QAAQ;;;;;;0EAC1C,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,AAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAK7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAa,MAAM,UAAU;;;;;;0EAC5C,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,AAAC,MAAM,UAAU,GAAG,MAAM,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUtE,MAAM,cAAc,kBACnB;;0CACE,8OAAC,8HAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;4CAAK;4CACc,IAAI,KAAK,MAAM,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;;;oBAO1E,MAAM,cAAc,GAAG,oBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;oBAO5C,MAAM,UAAU,IAAI,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,8OAAC;gCAAE,WAAU;;oCAA+B;oCACR,MAAM,UAAU;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;AAGO,SAAS,4BAA4B,EAC1C,OAAO,EACP,SAAS,EAIV;IACC,MAAM,QAAQ,QAAQ,eAAe;IAErC,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAChC,cAAA,8OAAC;gBAAK,WAAU;0BAAgC;;;;;;;;;;;IAGtD;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ,IAAI,qBAAO,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC9C,IAAI,QAAQ,IAAI,qBAAO,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC9C,IAAI,QAAQ,IAAI,qBAAO,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC9C,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;YAC3C,kBAAkB,MAAM,cAAc;0BACvC,8OAAC;gBAAK,WAAU;;oBACb,MAAM,cAAc,CAAC,OAAO,CAAC;oBAAG;;;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 2285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/student-profile-view.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Progress } from \"@/components/ui/progress\"\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  GraduationCap,\n  Users,\n  QrCode,\n  Edit,\n  Printer,\n  Download,\n  Calendar,\n  TrendingUp,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertCircle\n} from \"lucide-react\"\nimport { Student, getFullName } from \"@/lib/types/student\"\nimport { QRCodeDisplay } from \"./qr-code-display\"\nimport { AttendanceStatistics } from \"./attendance-statistics\"\nimport { cn } from \"@/lib/utils\"\n\ninterface StudentProfileViewProps {\n  student: Student\n  onEdit?: () => void\n  onGenerateQR?: () => void\n  onPrintQR?: () => void\n  className?: string\n}\n\nexport function StudentProfileView({\n  student,\n  onEdit,\n  onGenerateQR,\n  onPrintQR,\n  className\n}: StudentProfileViewProps) {\n  const [activeTab, setActiveTab] = useState(\"overview\")\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Active':\n        return 'bg-green-500'\n      case 'Inactive':\n        return 'bg-yellow-500'\n      case 'Transferred':\n        return 'bg-blue-500'\n      case 'Graduated':\n        return 'bg-purple-500'\n      default:\n        return 'bg-gray-500'\n    }\n  }\n\n  const getAttendanceIcon = (rate: number) => {\n    if (rate >= 95) return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (rate >= 85) return <AlertCircle className=\"h-4 w-4 text-yellow-500\" />\n    return <XCircle className=\"h-4 w-4 text-red-500\" />\n  }\n\n  const fullName = getFullName(student)\n\n  return (\n    <div className={cn(\"space-y-6\", className)}>\n      {/* Header Section */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex flex-col md:flex-row gap-6\">\n            {/* Student Photo and Basic Info */}\n            <div className=\"flex flex-col items-center md:items-start gap-4\">\n              <Avatar className=\"h-32 w-32 border-4 border-border\">\n                <AvatarImage \n                  src={student.photo} \n                  alt={fullName}\n                  className=\"object-cover\"\n                />\n                <AvatarFallback className=\"text-2xl font-bold bg-primary/10\">\n                  {getInitials(fullName)}\n                </AvatarFallback>\n              </Avatar>\n              \n              <div className=\"text-center md:text-left\">\n                <Badge \n                  variant={student.status === 'Active' ? 'default' : 'secondary'}\n                  className={cn(\"text-xs\", getStatusColor(student.status))}\n                >\n                  {student.status}\n                </Badge>\n              </div>\n            </div>\n\n            {/* Student Details */}\n            <div className=\"flex-1 space-y-4\">\n              <div>\n                <h1 className=\"text-3xl font-bold\">{fullName}</h1>\n                <p className=\"text-lg text-muted-foreground\">{student.email}</p>\n                <p className=\"text-sm text-muted-foreground\">ID: {student.id}</p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"flex items-center gap-2\">\n                  <GraduationCap className=\"h-4 w-4 text-muted-foreground\" />\n                  <span className=\"text-sm\">\n                    Grade {student.grade} - {student.section ? `Section ${student.section}` : 'No Section'}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center gap-2\">\n                  <User className=\"h-4 w-4 text-muted-foreground\" />\n                  <span className=\"text-sm\">{student.course} - {student.year}</span>\n                </div>\n                \n                <div className=\"flex items-center gap-2\">\n                  <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                  <span className=\"text-sm\">\n                    Enrolled: {new Date(student.enrollmentDate).toLocaleDateString()}\n                  </span>\n                </div>\n                \n                {student.attendanceStats && (\n                  <div className=\"flex items-center gap-2\">\n                    {getAttendanceIcon(student.attendanceStats.attendanceRate)}\n                    <span className=\"text-sm\">\n                      Attendance: {student.attendanceStats.attendanceRate.toFixed(1)}%\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col gap-2\">\n              {onEdit && (\n                <Button onClick={onEdit} variant=\"outline\">\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  Edit\n                </Button>\n              )}\n              \n              {onGenerateQR && (\n                <Button onClick={onGenerateQR} variant=\"outline\">\n                  <QrCode className=\"h-4 w-4 mr-2\" />\n                  Generate QR\n                </Button>\n              )}\n              \n              {onPrintQR && (\n                <Button onClick={onPrintQR} variant=\"outline\">\n                  <Printer className=\"h-4 w-4 mr-2\" />\n                  Print QR\n                </Button>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Detailed Information Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n          <TabsTrigger value=\"contacts\">Contacts</TabsTrigger>\n          <TabsTrigger value=\"attendance\">Attendance</TabsTrigger>\n          <TabsTrigger value=\"qr-code\">QR Code</TabsTrigger>\n        </TabsList>\n\n        {/* Overview Tab */}\n        <TabsContent value=\"overview\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Personal Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <User className=\"h-5 w-5\" />\n                  Personal Information\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  <span className=\"text-muted-foreground\">First Name:</span>\n                  <span>{student.firstName}</span>\n                  \n                  {student.middleName && (\n                    <>\n                      <span className=\"text-muted-foreground\">Middle Name:</span>\n                      <span>{student.middleName}</span>\n                    </>\n                  )}\n                  \n                  <span className=\"text-muted-foreground\">Last Name:</span>\n                  <span>{student.lastName}</span>\n                  \n                  {student.dateOfBirth && (\n                    <>\n                      <span className=\"text-muted-foreground\">Date of Birth:</span>\n                      <span>{new Date(student.dateOfBirth).toLocaleDateString()}</span>\n                    </>\n                  )}\n                  \n                  {student.gender && (\n                    <>\n                      <span className=\"text-muted-foreground\">Gender:</span>\n                      <span>{student.gender}</span>\n                    </>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Academic Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <GraduationCap className=\"h-5 w-5\" />\n                  Academic Information\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  <span className=\"text-muted-foreground\">Grade Level:</span>\n                  <span>Grade {student.grade}</span>\n                  \n                  <span className=\"text-muted-foreground\">Section:</span>\n                  <span>{student.section || 'Not assigned'}</span>\n                  \n                  <span className=\"text-muted-foreground\">Course/Track:</span>\n                  <span>{student.course}</span>\n                  \n                  <span className=\"text-muted-foreground\">Year Level:</span>\n                  <span>{student.year}</span>\n                  \n                  <span className=\"text-muted-foreground\">Status:</span>\n                  <Badge variant={student.status === 'Active' ? 'default' : 'secondary'}>\n                    {student.status}\n                  </Badge>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Address Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <MapPin className=\"h-5 w-5\" />\n                Address Information\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-sm\">\n                <p>{student.address.street}</p>\n                <p>Barangay {student.address.barangay}</p>\n                <p>{student.address.city}, {student.address.province} {student.address.zipCode}</p>\n                <p>{student.address.country}</p>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Contacts Tab */}\n        <TabsContent value=\"contacts\" className=\"space-y-6\">\n          {/* Guardian Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Users className=\"h-5 w-5\" />\n                Guardian Information\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <p className=\"font-medium\">{student.guardian.name}</p>\n                  <p className=\"text-sm text-muted-foreground\">{student.guardian.relationship}</p>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Phone className=\"h-4 w-4 text-muted-foreground\" />\n                    <span className=\"text-sm\">{student.guardian.phone}</span>\n                  </div>\n                  \n                  {student.guardian.email && (\n                    <div className=\"flex items-center gap-2\">\n                      <Mail className=\"h-4 w-4 text-muted-foreground\" />\n                      <span className=\"text-sm\">{student.guardian.email}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n              \n              {student.guardian.address && (\n                <div className=\"pt-2 border-t\">\n                  <p className=\"text-sm text-muted-foreground\">Address:</p>\n                  <p className=\"text-sm\">{student.guardian.address}</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Emergency Contacts */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Emergency Contacts</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {student.emergencyContacts.map((contact, index) => (\n                  <div key={index} className=\"p-4 border rounded-lg\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <p className=\"font-medium\">{contact.name}</p>\n                        <p className=\"text-sm text-muted-foreground\">{contact.relationship}</p>\n                      </div>\n                      \n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center gap-2\">\n                          <Phone className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"text-sm\">{contact.phone}</span>\n                        </div>\n                        \n                        {contact.address && (\n                          <div className=\"flex items-center gap-2\">\n                            <MapPin className=\"h-4 w-4 text-muted-foreground\" />\n                            <span className=\"text-sm\">{contact.address}</span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Attendance Tab */}\n        <TabsContent value=\"attendance\" className=\"space-y-6\">\n          <AttendanceStatistics\n            student={student}\n            showDetailed={true}\n            showActions={true}\n          />\n        </TabsContent>\n\n        {/* QR Code Tab */}\n        <TabsContent value=\"qr-code\" className=\"space-y-6\">\n          <QRCodeDisplay\n            student={student}\n            onQRGenerated={onGenerateQR}\n            onQRRegenerated={onGenerateQR}\n          />\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;AAyCO,SAAS,mBAAmB,EACjC,OAAO,EACP,MAAM,EACN,YAAY,EACZ,SAAS,EACT,SAAS,EACe;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ,IAAI,qBAAO,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC9C,IAAI,QAAQ,IAAI,qBAAO,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC9C,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAE7B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,2HAAA,CAAA,cAAW;gDACV,KAAK,QAAQ,KAAK;gDAClB,KAAK;gDACL,WAAU;;;;;;0DAEZ,8OAAC,2HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,YAAY;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;4CACnD,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,QAAQ,MAAM;sDAErD,QAAQ,MAAM;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAE,WAAU;0DAAiC,QAAQ,KAAK;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;;oDAAgC;oDAAK,QAAQ,EAAE;;;;;;;;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;;4DAAU;4DACjB,QAAQ,KAAK;4DAAC;4DAAI,QAAQ,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE,GAAG;;;;;;;;;;;;;0DAI9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;4DAAW,QAAQ,MAAM;4DAAC;4DAAI,QAAQ,IAAI;;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;;4DAAU;4DACb,IAAI,KAAK,QAAQ,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;4CAIjE,QAAQ,eAAe,kBACtB,8OAAC;gDAAI,WAAU;;oDACZ,kBAAkB,QAAQ,eAAe,CAAC,cAAc;kEACzD,8OAAC;wDAAK,WAAU;;4DAAU;4DACX,QAAQ,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzE,8OAAC;gCAAI,WAAU;;oCACZ,wBACC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAQ,SAAQ;;0DAC/B,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAKpC,8BACC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAc,SAAQ;;0DACrC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAKtC,2BACC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAW,SAAQ;;0DAClC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhD,8OAAC,yHAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,8OAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;0CAChC,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;;;;;;;kCAI/B,8OAAC,yHAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIhC,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,QAAQ,SAAS;;;;;;wDAEvB,QAAQ,UAAU,kBACjB;;8EACE,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;8EAAM,QAAQ,UAAU;;;;;;;;sEAI7B,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,QAAQ,QAAQ;;;;;;wDAEtB,QAAQ,WAAW,kBAClB;;8EACE,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;8EAAM,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;;;wDAI1D,QAAQ,MAAM,kBACb;;8EACE,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;8EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/B,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIzC,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;;gEAAK;gEAAO,QAAQ,KAAK;;;;;;;sEAE1B,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,QAAQ,OAAO,IAAI;;;;;;sEAE1B,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,QAAQ,MAAM;;;;;;sEAErB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,QAAQ,IAAI;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;sEACvD,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIlC,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,QAAQ,OAAO,CAAC,MAAM;;;;;;8DAC1B,8OAAC;;wDAAE;wDAAU,QAAQ,OAAO,CAAC,QAAQ;;;;;;;8DACrC,8OAAC;;wDAAG,QAAQ,OAAO,CAAC,IAAI;wDAAC;wDAAG,QAAQ,OAAO,CAAC,QAAQ;wDAAC;wDAAE,QAAQ,OAAO,CAAC,OAAO;;;;;;;8DAC9E,8OAAC;8DAAG,QAAQ,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CAEtC,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIjC,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAe,QAAQ,QAAQ,CAAC,IAAI;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiC,QAAQ,QAAQ,CAAC,YAAY;;;;;;;;;;;;kEAG7E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAAW,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;4DAGlD,QAAQ,QAAQ,CAAC,KAAK,kBACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAW,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;4CAMxD,QAAQ,QAAQ,CAAC,OAAO,kBACvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAC7C,8OAAC;wDAAE,WAAU;kEAAW,QAAQ,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAOxD,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,iBAAiB,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;oDAAgB,WAAU;8DACzB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAe,QAAQ,IAAI;;;;;;kFACxC,8OAAC;wEAAE,WAAU;kFAAiC,QAAQ,YAAY;;;;;;;;;;;;0EAGpE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,8OAAC;gFAAK,WAAU;0FAAW,QAAQ,KAAK;;;;;;;;;;;;oEAGzC,QAAQ,OAAO,kBACd,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;gFAAK,WAAU;0FAAW,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;mDAhB1C;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA6BpB,8OAAC,yHAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACxC,cAAA,8OAAC,mJAAA,CAAA,uBAAoB;4BACnB,SAAS;4BACT,cAAc;4BACd,aAAa;;;;;;;;;;;kCAKjB,8OAAC,yHAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gJAAA,CAAA,gBAAa;4BACZ,SAAS;4BACT,eAAe;4BACf,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAM7B", "debugId": null}}, {"offset": {"line": 3420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/photo-upload.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef, useCallback } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Label } from \"@/components/ui/label\"\nimport { \n  Upload, \n  X, \n  User, \n  Camera, \n  AlertTriangle, \n  CheckCircle,\n  Loader2,\n  Image as ImageIcon\n} from \"lucide-react\"\nimport { photoManager, PhotoValidationResult, PhotoMetadata } from \"@/lib/utils/photo-management\"\nimport { toast } from \"sonner\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PhotoUploadProps {\n  currentPhoto?: string\n  onPhotoChange?: (photo: File | string | null) => void\n  onPhotoUploaded?: (photoUrl: string) => void\n  studentId?: string\n  studentName?: string\n  disabled?: boolean\n  className?: string\n}\n\nexport function PhotoUpload({\n  currentPhoto,\n  onPhotoChange,\n  onPhotoUploaded,\n  studentId,\n  studentName,\n  disabled = false,\n  className\n}: PhotoUploadProps) {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [previewUrl, setPreviewUrl] = useState<string | null>(currentPhoto || null)\n  const [isUploading, setIsUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const [validationResult, setValidationResult] = useState<PhotoValidationResult | null>(null)\n  const [metadata, setMetadata] = useState<PhotoMetadata | null>(null)\n  const [isDragOver, setIsDragOver] = useState(false)\n  \n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = useCallback(async (file: File) => {\n    try {\n      const result = await photoManager.processPhotoForUpload(file, {\n        maxWidth: 800,\n        maxHeight: 800,\n        quality: 0.8,\n        format: 'jpeg'\n      })\n\n      setSelectedFile(result.processedFile)\n      setPreviewUrl(result.previewUrl)\n      setValidationResult(result.validationResult)\n      setMetadata(result.metadata)\n\n      if (onPhotoChange) {\n        onPhotoChange(result.processedFile)\n      }\n\n      if (result.validationResult.warnings?.length) {\n        result.validationResult.warnings.forEach(warning => {\n          toast.warning(warning)\n        })\n      }\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to process photo'\n      toast.error(errorMessage)\n      setValidationResult({ valid: false, error: errorMessage })\n    }\n  }, [onPhotoChange])\n\n  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      handleFileSelect(file)\n    }\n  }\n\n  const handleDragOver = (event: React.DragEvent) => {\n    event.preventDefault()\n    setIsDragOver(true)\n  }\n\n  const handleDragLeave = (event: React.DragEvent) => {\n    event.preventDefault()\n    setIsDragOver(false)\n  }\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault()\n    setIsDragOver(false)\n    \n    const file = event.dataTransfer.files[0]\n    if (file && file.type.startsWith('image/')) {\n      handleFileSelect(file)\n    } else {\n      toast.error('Please drop a valid image file')\n    }\n  }\n\n  const handleUpload = async () => {\n    if (!selectedFile || !studentId) return\n\n    setIsUploading(true)\n    setUploadProgress(0)\n\n    try {\n      // Simulate upload progress\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval)\n            return 90\n          }\n          return prev + 10\n        })\n      }, 200)\n\n      const photoUrl = await photoManager.uploadPhoto(selectedFile, studentId)\n      \n      clearInterval(progressInterval)\n      setUploadProgress(100)\n\n      if (onPhotoUploaded) {\n        onPhotoUploaded(photoUrl)\n      }\n\n      toast.success('Photo uploaded successfully!')\n      \n      // Clean up\n      setTimeout(() => {\n        setUploadProgress(0)\n        setIsUploading(false)\n      }, 1000)\n\n    } catch (error) {\n      setIsUploading(false)\n      setUploadProgress(0)\n      toast.error('Failed to upload photo')\n    }\n  }\n\n  const handleRemove = () => {\n    if (previewUrl && previewUrl !== currentPhoto) {\n      photoManager.revokePreviewUrl(previewUrl)\n    }\n    \n    setSelectedFile(null)\n    setPreviewUrl(currentPhoto || null)\n    setValidationResult(null)\n    setMetadata(null)\n    \n    if (onPhotoChange) {\n      onPhotoChange(null)\n    }\n    \n    // Reset file input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = ''\n    }\n  }\n\n  const handleRemoveCurrentPhoto = () => {\n    setPreviewUrl(null)\n    if (onPhotoChange) {\n      onPhotoChange(null)\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  return (\n    <Card className={cn(\"w-full\", className)}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Camera className=\"h-5 w-5\" />\n          Student Photo\n        </CardTitle>\n        <CardDescription>\n          Upload a clear photo of the student for identification purposes\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Photo Preview */}\n        <div className=\"flex flex-col items-center gap-4\">\n          <Avatar className=\"h-32 w-32 border-4 border-border\">\n            <AvatarImage src={previewUrl || undefined} alt={studentName || \"Student photo\"} />\n            <AvatarFallback className=\"text-2xl\">\n              {studentName ? getInitials(studentName) : <User className=\"h-12 w-12\" />}\n            </AvatarFallback>\n          </Avatar>\n\n          {/* Photo Status */}\n          {validationResult && (\n            <div className=\"text-center\">\n              {validationResult.valid ? (\n                <Badge variant=\"default\" className=\"gap-1\">\n                  <CheckCircle className=\"h-3 w-3\" />\n                  Valid Photo\n                </Badge>\n              ) : (\n                <Badge variant=\"destructive\" className=\"gap-1\">\n                  <AlertTriangle className=\"h-3 w-3\" />\n                  Invalid Photo\n                </Badge>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Upload Area */}\n        <div\n          className={cn(\n            \"border-2 border-dashed rounded-lg p-6 text-center transition-colors\",\n            isDragOver ? \"border-primary bg-primary/5\" : \"border-muted-foreground/25\",\n            disabled && \"opacity-50 pointer-events-none\"\n          )}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n        >\n          <ImageIcon className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n          <div className=\"space-y-2\">\n            <p className=\"text-lg font-medium\">\n              {selectedFile ? 'Photo Selected' : 'Drop your photo here'}\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              or click to browse files\n            </p>\n          </div>\n          \n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleFileInputChange}\n            className=\"hidden\"\n            disabled={disabled}\n          />\n          \n          <Button \n            onClick={() => fileInputRef.current?.click()}\n            className=\"mt-4\"\n            disabled={disabled}\n          >\n            <Upload className=\"mr-2 h-4 w-4\" />\n            Select Photo\n          </Button>\n        </div>\n\n        {/* Photo Information */}\n        {metadata && (\n          <div className=\"space-y-2\">\n            <Label className=\"text-sm font-medium\">Photo Information</Label>\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <span className=\"text-muted-foreground\">File Size:</span>\n              <span>{(metadata.size / 1024).toFixed(1)} KB</span>\n              \n              <span className=\"text-muted-foreground\">Dimensions:</span>\n              <span>{metadata.dimensions.width} × {metadata.dimensions.height}</span>\n              \n              <span className=\"text-muted-foreground\">Format:</span>\n              <span>{metadata.type.split('/')[1].toUpperCase()}</span>\n            </div>\n          </div>\n        )}\n\n        {/* Upload Progress */}\n        {isUploading && (\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span>Uploading photo...</span>\n              <span>{uploadProgress}%</span>\n            </div>\n            <Progress value={uploadProgress} className=\"w-full\" />\n          </div>\n        )}\n\n        {/* Validation Errors */}\n        {validationResult && !validationResult.valid && (\n          <Alert variant=\"destructive\">\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              {validationResult.error}\n            </AlertDescription>\n          </Alert>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-center gap-2\">\n          {selectedFile && !isUploading && (\n            <>\n              {studentId && (\n                <Button onClick={handleUpload} disabled={!validationResult?.valid}>\n                  <Upload className=\"mr-2 h-4 w-4\" />\n                  Upload Photo\n                </Button>\n              )}\n              \n              <Button variant=\"outline\" onClick={handleRemove}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Remove\n              </Button>\n            </>\n          )}\n          \n          {previewUrl && !selectedFile && (\n            <Button variant=\"outline\" onClick={handleRemoveCurrentPhoto}>\n              <X className=\"mr-2 h-4 w-4\" />\n              Remove Current Photo\n            </Button>\n          )}\n          \n          {isUploading && (\n            <Button disabled>\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              Uploading...\n            </Button>\n          )}\n        </div>\n\n        {/* Guidelines */}\n        <Alert>\n          <AlertTriangle className=\"h-4 w-4\" />\n          <AlertDescription>\n            <strong>Photo Guidelines:</strong>\n            <ul className=\"list-disc list-inside mt-2 space-y-1 text-sm\">\n              <li>Use a clear, recent photo of the student</li>\n              <li>Face should be clearly visible and well-lit</li>\n              <li>Avoid sunglasses, hats, or face coverings</li>\n              <li>Recommended size: 300×300 pixels or larger</li>\n              <li>Supported formats: JPEG, PNG, WebP (max 10MB)</li>\n            </ul>\n          </AlertDescription>\n        </Alert>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAtBA;;;;;;;;;;;;;;AAkCO,SAAS,YAAY,EAC1B,YAAY,EACZ,aAAa,EACb,eAAe,EACf,SAAS,EACT,WAAW,EACX,WAAW,KAAK,EAChB,SAAS,EACQ;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,gBAAgB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,MAAM,SAAS,MAAM,mIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,MAAM;gBAC5D,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;YAEA,gBAAgB,OAAO,aAAa;YACpC,cAAc,OAAO,UAAU;YAC/B,oBAAoB,OAAO,gBAAgB;YAC3C,YAAY,OAAO,QAAQ;YAE3B,IAAI,eAAe;gBACjB,cAAc,OAAO,aAAa;YACpC;YAEA,IAAI,OAAO,gBAAgB,CAAC,QAAQ,EAAE,QAAQ;gBAC5C,OAAO,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACvC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,oBAAoB;gBAAE,OAAO;gBAAO,OAAO;YAAa;QAC1D;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,cAAc;QAEd,MAAM,OAAO,MAAM,YAAY,CAAC,KAAK,CAAC,EAAE;QACxC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,iBAAiB;QACnB,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAEjC,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY;gBACnC,kBAAkB,CAAA;oBAChB,IAAI,QAAQ,IAAI;wBACd,cAAc;wBACd,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,mIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,cAAc;YAE9D,cAAc;YACd,kBAAkB;YAElB,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,WAAW;YACX,WAAW;gBACT,kBAAkB;gBAClB,eAAe;YACjB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,eAAe;YACf,kBAAkB;YAClB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc,eAAe,cAAc;YAC7C,mIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC;QAChC;QAEA,gBAAgB;QAChB,cAAc,gBAAgB;QAC9B,oBAAoB;QACpB,YAAY;QAEZ,IAAI,eAAe;YACjB,cAAc;QAChB;QAEA,mBAAmB;QACnB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,2BAA2B;QAC/B,cAAc;QACd,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,2HAAA,CAAA,cAAW;wCAAC,KAAK,cAAc;wCAAW,KAAK,eAAe;;;;;;kDAC/D,8OAAC,2HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,cAAc,YAAY,6BAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAK7D,kCACC,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,KAAK,iBACrB,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;;;;;;yDAIrC,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;sDACrC,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAS/C,8OAAC;wBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uEACA,aAAa,gCAAgC,8BAC7C,YAAY;wBAEd,YAAY;wBACZ,aAAa;wBACb,QAAQ;;0CAER,8OAAC,oMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,eAAe,mBAAmB;;;;;;kDAErC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,8OAAC;gCACC,KAAK;gCACL,MAAK;gCACL,QAAO;gCACP,UAAU;gCACV,WAAU;gCACV,UAAU;;;;;;0CAGZ,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,WAAU;gCACV,UAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;oBAMtC,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;;4CAAM,CAAC,SAAS,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAEzC,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;;4CAAM,SAAS,UAAU,CAAC,KAAK;4CAAC;4CAAI,SAAS,UAAU,CAAC,MAAM;;;;;;;kDAE/D,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;kDAAM,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;;;;;;;;;;;;;;;;;;oBAMnD,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAM;4CAAe;;;;;;;;;;;;;0CAExB,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAgB,WAAU;;;;;;;;;;;;oBAK9C,oBAAoB,CAAC,iBAAiB,KAAK,kBAC1C,8OAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,0HAAA,CAAA,mBAAgB;0CACd,iBAAiB,KAAK;;;;;;;;;;;;kCAM7B,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,CAAC,6BAChB;;oCACG,2BACC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAc,UAAU,CAAC,kBAAkB;;0DAC1D,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKvC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;4BAMnC,cAAc,CAAC,8BACd,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAKjC,6BACC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,QAAQ;;kDACd,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;;;;;;;;;;;;kCAOvD,8OAAC,0HAAA,CAAA,QAAK;;0CACJ,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,0HAAA,CAAA,mBAAgB;;kDACf,8OAAC;kDAAO;;;;;;kDACR,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 4093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/student-registration-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { Button } from \"@/components/ui/button\"\nimport { Plus } from \"lucide-react\"\nimport { StudentRegistrationForm } from \"./student-registration-form\"\nimport { type StudentRegistrationFormData } from \"@/lib/validations/student\"\nimport { Student } from \"@/lib/types/student\"\nimport { toast } from \"sonner\"\n\ninterface StudentRegistrationDialogProps {\n  trigger?: React.ReactNode\n  onStudentCreated?: (student: Student) => void\n  initialData?: Partial<StudentRegistrationFormData>\n  mode?: 'create' | 'edit'\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}\n\nexport function StudentRegistrationDialog({\n  trigger,\n  onStudentCreated,\n  initialData,\n  mode = 'create',\n  open,\n  onOpenChange\n}: StudentRegistrationDialogProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (onOpenChange) {\n      onOpenChange(newOpen)\n    } else {\n      setIsOpen(newOpen)\n    }\n  }\n\n  const handleSubmit = async (data: StudentRegistrationFormData) => {\n    setIsLoading(true)\n    try {\n      // Convert form data to Student object\n      const student: Student = {\n        id: data.id,\n        firstName: data.firstName,\n        middleName: data.middleName,\n        lastName: data.lastName,\n        email: data.email,\n        course: data.course,\n        year: data.year,\n        section: data.section,\n        grade: data.grade,\n        status: 'Active',\n        photo: typeof data.photo === 'string' ? data.photo : undefined,\n        qrCode: `QR_${data.id}_2025`,\n        dateOfBirth: data.dateOfBirth,\n        gender: data.gender,\n        guardian: {\n          name: data.guardianName,\n          phone: data.guardianPhone,\n          email: data.guardianEmail,\n          relationship: data.guardianRelationship,\n          address: data.guardianAddress\n        },\n        emergencyContacts: data.emergencyContacts,\n        address: {\n          street: data.street,\n          barangay: data.barangay,\n          city: data.city,\n          province: data.province,\n          zipCode: data.zipCode,\n          country: data.country || 'Philippines'\n        },\n        enrollmentDate: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      }\n\n      // Here you would typically make an API call to save the student\n      // For now, we'll simulate a successful save\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      toast.success(\n        mode === 'edit' \n          ? \"Student information updated successfully!\" \n          : \"Student registered successfully!\"\n      )\n\n      if (onStudentCreated) {\n        onStudentCreated(student)\n      }\n\n      handleOpenChange(false)\n    } catch (error) {\n      console.error('Error saving student:', error)\n      toast.error(\"Failed to save student information. Please try again.\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCancel = () => {\n    handleOpenChange(false)\n  }\n\n  const dialogOpen = open !== undefined ? open : isOpen\n\n  return (\n    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>\n      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}\n      \n      {!trigger && (\n        <DialogTrigger asChild>\n          <Button>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Student\n          </Button>\n        </DialogTrigger>\n      )}\n\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>\n            {mode === 'edit' ? 'Edit Student Information' : 'Register New Student'}\n          </DialogTitle>\n          <DialogDescription>\n            {mode === 'edit' \n              ? 'Update the student\\'s information below.' \n              : 'Fill in the student\\'s information to register them in the system.'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <StudentRegistrationForm\n          onSubmit={handleSubmit}\n          onCancel={handleCancel}\n          initialData={initialData}\n          isLoading={isLoading}\n          mode={mode}\n        />\n      </DialogContent>\n    </Dialog>\n  )\n}\n\n// Quick registration dialog with minimal fields\nexport function QuickStudentRegistrationDialog({\n  trigger,\n  onStudentCreated\n}: {\n  trigger?: React.ReactNode\n  onStudentCreated?: (student: Student) => void\n}) {\n  return (\n    <StudentRegistrationDialog\n      trigger={trigger}\n      onStudentCreated={onStudentCreated}\n      mode=\"create\"\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAGA;AATA;;;;;;;;AAoBO,SAAS,0BAA0B,EACxC,OAAO,EACP,gBAAgB,EAChB,WAAW,EACX,OAAO,QAAQ,EACf,IAAI,EACJ,YAAY,EACmB;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,aAAa;QACf,OAAO;YACL,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,IAAI;YACF,sCAAsC;YACtC,MAAM,UAAmB;gBACvB,IAAI,KAAK,EAAE;gBACX,WAAW,KAAK,SAAS;gBACzB,YAAY,KAAK,UAAU;gBAC3B,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,SAAS,KAAK,OAAO;gBACrB,OAAO,KAAK,KAAK;gBACjB,QAAQ;gBACR,OAAO,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;gBACrD,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;gBAC5B,aAAa,KAAK,WAAW;gBAC7B,QAAQ,KAAK,MAAM;gBACnB,UAAU;oBACR,MAAM,KAAK,YAAY;oBACvB,OAAO,KAAK,aAAa;oBACzB,OAAO,KAAK,aAAa;oBACzB,cAAc,KAAK,oBAAoB;oBACvC,SAAS,KAAK,eAAe;gBAC/B;gBACA,mBAAmB,KAAK,iBAAiB;gBACzC,SAAS;oBACP,QAAQ,KAAK,MAAM;oBACnB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,SAAS,KAAK,OAAO,IAAI;gBAC3B;gBACA,gBAAgB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtD,aAAa,IAAI,OAAO,WAAW;YACrC;YAEA,gEAAgE;YAChE,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,SAAS,SACL,8CACA;YAGN,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;YAEA,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,iBAAiB;IACnB;IAEA,MAAM,aAAa,SAAS,YAAY,OAAO;IAE/C,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAY,cAAc;;YACrC,yBAAW,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BAAE;;;;;;YAEnC,CAAC,yBACA,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;;sCACL,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAMvC,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,2HAAA,CAAA,eAAY;;0CACX,8OAAC,2HAAA,CAAA,cAAW;0CACT,SAAS,SAAS,6BAA6B;;;;;;0CAElD,8OAAC,2HAAA,CAAA,oBAAiB;0CACf,SAAS,SACN,6CACA;;;;;;;;;;;;kCAKR,8OAAC,0JAAA,CAAA,0BAAuB;wBACtB,UAAU;wBACV,UAAU;wBACV,aAAa;wBACb,WAAW;wBACX,MAAM;;;;;;;;;;;;;;;;;;AAKhB;AAGO,SAAS,+BAA+B,EAC7C,OAAO,EACP,gBAAgB,EAIjB;IACC,qBACE,8OAAC;QACC,SAAS;QACT,kBAAkB;QAClB,MAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 4278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/student-profile-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { Button } from \"@/components/ui/button\"\nimport { Eye } from \"lucide-react\"\nimport { StudentProfileView } from \"./student-profile-view\"\nimport { StudentRegistrationDialog } from \"./student-registration-dialog\"\nimport { Student, getFullName } from \"@/lib/types/student\"\nimport { StudentRegistrationFormData } from \"@/lib/validations/student\"\nimport { toast } from \"sonner\"\n\ninterface StudentProfileDialogProps {\n  student: Student\n  trigger?: React.ReactNode\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  onStudentUpdated?: (student: Student) => void\n}\n\nexport function StudentProfileDialog({\n  student,\n  trigger,\n  open,\n  onOpenChange,\n  onStudentUpdated\n}: StudentProfileDialogProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (onOpenChange) {\n      onOpenChange(newOpen)\n    } else {\n      setIsOpen(newOpen)\n    }\n  }\n\n  const handleEdit = () => {\n    setShowEditDialog(true)\n  }\n\n  const handleGenerateQR = () => {\n    // Simulate QR code generation\n    toast.success(\"QR code generated successfully!\")\n    console.log(\"Generating QR code for student:\", student.id)\n  }\n\n  const handlePrintQR = () => {\n    // Simulate QR code printing\n    toast.success(\"QR code sent to printer!\")\n    console.log(\"Printing QR code for student:\", student.id)\n  }\n\n  const handleStudentUpdated = (updatedStudent: Student) => {\n    if (onStudentUpdated) {\n      onStudentUpdated(updatedStudent)\n    }\n    setShowEditDialog(false)\n    toast.success(\"Student information updated successfully!\")\n  }\n\n  const convertStudentToFormData = (student: Student): Partial<StudentRegistrationFormData> => {\n    return {\n      id: student.id,\n      firstName: student.firstName,\n      middleName: student.middleName,\n      lastName: student.lastName,\n      email: student.email,\n      dateOfBirth: student.dateOfBirth,\n      gender: student.gender,\n      course: student.course,\n      year: student.year,\n      section: student.section,\n      grade: student.grade,\n      guardianName: student.guardian.name,\n      guardianPhone: student.guardian.phone,\n      guardianEmail: student.guardian.email,\n      guardianRelationship: student.guardian.relationship,\n      guardianAddress: student.guardian.address,\n      emergencyContacts: student.emergencyContacts,\n      street: student.address.street,\n      barangay: student.address.barangay,\n      city: student.address.city,\n      province: student.address.province,\n      zipCode: student.address.zipCode,\n      country: student.address.country,\n      photo: student.photo\n    }\n  }\n\n  const dialogOpen = open !== undefined ? open : isOpen\n  const fullName = getFullName(student)\n\n  return (\n    <>\n      <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>\n        {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}\n        \n        {!trigger && (\n          <DialogTrigger asChild>\n            <Button variant=\"ghost\" size=\"sm\">\n              <Eye className=\"h-4 w-4 mr-2\" />\n              View\n            </Button>\n          </DialogTrigger>\n        )}\n\n        <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>Student Profile - {fullName}</DialogTitle>\n            <DialogDescription>\n              Complete student information and attendance details\n            </DialogDescription>\n          </DialogHeader>\n\n          <StudentProfileView\n            student={student}\n            onEdit={handleEdit}\n            onGenerateQR={handleGenerateQR}\n            onPrintQR={handlePrintQR}\n          />\n        </DialogContent>\n      </Dialog>\n\n      {/* Edit Student Dialog */}\n      <StudentRegistrationDialog\n        mode=\"edit\"\n        open={showEditDialog}\n        onOpenChange={setShowEditDialog}\n        initialData={convertStudentToFormData(student)}\n        onStudentCreated={handleStudentUpdated}\n      />\n    </>\n  )\n}\n\n// Quick view component for table rows\nexport function StudentQuickView({ student }: { student: Student }) {\n  return (\n    <StudentProfileDialog\n      student={student}\n      trigger={\n        <Button variant=\"ghost\" size=\"sm\">\n          <Eye className=\"h-4 w-4 mr-2\" />\n          View\n        </Button>\n      }\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAoBO,SAAS,qBAAqB,EACnC,OAAO,EACP,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,gBAAgB,EACU;IAC1B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,aAAa;QACf,OAAO;YACL,UAAU;QACZ;IACF;IAEA,MAAM,aAAa;QACjB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB;QACvB,8BAA8B;QAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,QAAQ,GAAG,CAAC,mCAAmC,QAAQ,EAAE;IAC3D;IAEA,MAAM,gBAAgB;QACpB,4BAA4B;QAC5B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,QAAQ,GAAG,CAAC,iCAAiC,QAAQ,EAAE;IACzD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;QACA,kBAAkB;QAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,2BAA2B,CAAC;QAChC,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,WAAW,QAAQ,SAAS;YAC5B,YAAY,QAAQ,UAAU;YAC9B,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO;YACxB,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,QAAQ,CAAC,IAAI;YACnC,eAAe,QAAQ,QAAQ,CAAC,KAAK;YACrC,eAAe,QAAQ,QAAQ,CAAC,KAAK;YACrC,sBAAsB,QAAQ,QAAQ,CAAC,YAAY;YACnD,iBAAiB,QAAQ,QAAQ,CAAC,OAAO;YACzC,mBAAmB,QAAQ,iBAAiB;YAC5C,QAAQ,QAAQ,OAAO,CAAC,MAAM;YAC9B,UAAU,QAAQ,OAAO,CAAC,QAAQ;YAClC,MAAM,QAAQ,OAAO,CAAC,IAAI;YAC1B,UAAU,QAAQ,OAAO,CAAC,QAAQ;YAClC,SAAS,QAAQ,OAAO,CAAC,OAAO;YAChC,SAAS,QAAQ,OAAO,CAAC,OAAO;YAChC,OAAO,QAAQ,KAAK;QACtB;IACF;IAEA,MAAM,aAAa,SAAS,YAAY,OAAO;IAC/C,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAE7B,qBACE;;0BACE,8OAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAY,cAAc;;oBACrC,yBAAW,8OAAC,2HAAA,CAAA,gBAAa;wBAAC,OAAO;kCAAE;;;;;;oBAEnC,CAAC,yBACA,8OAAC,2HAAA,CAAA,gBAAa;wBAAC,OAAO;kCACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAMtC,8OAAC,2HAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,2HAAA,CAAA,eAAY;;kDACX,8OAAC,2HAAA,CAAA,cAAW;;4CAAC;4CAAmB;;;;;;;kDAChC,8OAAC,2HAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;0CAKrB,8OAAC,qJAAA,CAAA,qBAAkB;gCACjB,SAAS;gCACT,QAAQ;gCACR,cAAc;gCACd,WAAW;;;;;;;;;;;;;;;;;;0BAMjB,8OAAC,4JAAA,CAAA,4BAAyB;gBACxB,MAAK;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa,yBAAyB;gBACtC,kBAAkB;;;;;;;;AAI1B;AAGO,SAAS,iBAAiB,EAAE,OAAO,EAAwB;IAChE,qBACE,8OAAC;QACC,SAAS;QACT,uBACE,8OAAC,2HAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;;8BAC3B,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 4495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/student-status-manager.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Label } from \"@/components/ui/label\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { \n  UserCheck, \n  UserX, \n  UserMinus, \n  GraduationCap,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  FileText,\n  Calendar\n} from \"lucide-react\"\nimport { Student, getFullName } from \"@/lib/types/student\"\nimport { toast } from \"sonner\"\nimport { cn } from \"@/lib/utils\"\n\ninterface StudentStatusManagerProps {\n  student: Student\n  onStatusChanged?: (student: Student, newStatus: Student['status'], reason?: string) => void\n  trigger?: React.ReactNode\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}\n\ninterface StatusTransition {\n  from: Student['status']\n  to: Student['status']\n  allowed: boolean\n  requiresReason: boolean\n  warning?: string\n}\n\nexport function StudentStatusManager({\n  student,\n  onStatusChanged,\n  trigger,\n  open,\n  onOpenChange\n}: StudentStatusManagerProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [selectedStatus, setSelectedStatus] = useState<Student['status']>(student.status)\n  const [reason, setReason] = useState('')\n  const [isUpdating, setIsUpdating] = useState(false)\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (onOpenChange) {\n      onOpenChange(newOpen)\n    } else {\n      setIsOpen(newOpen)\n    }\n    \n    if (!newOpen) {\n      // Reset state when dialog closes\n      setSelectedStatus(student.status)\n      setReason('')\n      setIsUpdating(false)\n    }\n  }\n\n  // Define valid status transitions\n  const statusTransitions: StatusTransition[] = [\n    { from: 'Active', to: 'Inactive', allowed: true, requiresReason: true, warning: 'Student will be marked as temporarily inactive' },\n    { from: 'Active', to: 'Transferred', allowed: true, requiresReason: true, warning: 'Student will be removed from active enrollment' },\n    { from: 'Active', to: 'Graduated', allowed: true, requiresReason: false, warning: 'Student will be marked as graduated' },\n    { from: 'Inactive', to: 'Active', allowed: true, requiresReason: true, warning: 'Student will be reactivated' },\n    { from: 'Inactive', to: 'Transferred', allowed: true, requiresReason: true, warning: 'Student will be marked as transferred' },\n    { from: 'Transferred', to: 'Active', allowed: true, requiresReason: true, warning: 'Student will be re-enrolled' },\n    { from: 'Graduated', to: 'Active', allowed: false, requiresReason: false, warning: 'Cannot reactivate graduated students' }\n  ]\n\n  const getValidTransitions = () => {\n    return statusTransitions.filter(t => t.from === student.status && t.allowed)\n  }\n\n  const getCurrentTransition = () => {\n    return statusTransitions.find(t => t.from === student.status && t.to === selectedStatus)\n  }\n\n  const getStatusIcon = (status: Student['status']) => {\n    switch (status) {\n      case 'Active':\n        return <UserCheck className=\"h-4 w-4 text-green-600\" />\n      case 'Inactive':\n        return <UserX className=\"h-4 w-4 text-yellow-600\" />\n      case 'Transferred':\n        return <UserMinus className=\"h-4 w-4 text-blue-600\" />\n      case 'Graduated':\n        return <GraduationCap className=\"h-4 w-4 text-purple-600\" />\n      default:\n        return <Clock className=\"h-4 w-4\" />\n    }\n  }\n\n  const getStatusColor = (status: Student['status']) => {\n    switch (status) {\n      case 'Active':\n        return 'bg-green-500 text-white'\n      case 'Inactive':\n        return 'bg-yellow-500 text-white'\n      case 'Transferred':\n        return 'bg-blue-500 text-white'\n      case 'Graduated':\n        return 'bg-purple-500 text-white'\n      default:\n        return 'bg-gray-500 text-white'\n    }\n  }\n\n  const getStatusDescription = (status: Student['status']) => {\n    switch (status) {\n      case 'Active':\n        return 'Student is currently enrolled and attending classes'\n      case 'Inactive':\n        return 'Student is temporarily not attending (e.g., medical leave, suspension)'\n      case 'Transferred':\n        return 'Student has transferred to another school or program'\n      case 'Graduated':\n        return 'Student has completed their studies and graduated'\n      default:\n        return 'Unknown status'\n    }\n  }\n\n  const handleStatusUpdate = async () => {\n    const transition = getCurrentTransition()\n    \n    if (!transition?.allowed) {\n      toast.error('Invalid status transition')\n      return\n    }\n\n    if (transition.requiresReason && !reason.trim()) {\n      toast.error('Please provide a reason for this status change')\n      return\n    }\n\n    setIsUpdating(true)\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      const updatedStudent: Student = {\n        ...student,\n        status: selectedStatus,\n        lastUpdated: new Date().toISOString()\n      }\n\n      if (onStatusChanged) {\n        onStatusChanged(updatedStudent, selectedStatus, reason.trim() || undefined)\n      }\n\n      toast.success(`Student status updated to ${selectedStatus}`)\n      handleOpenChange(false)\n    } catch (error) {\n      toast.error('Failed to update student status')\n    } finally {\n      setIsUpdating(false)\n    }\n  }\n\n  const dialogOpen = open !== undefined ? open : isOpen\n  const fullName = getFullName(student)\n  const currentTransition = getCurrentTransition()\n  const validTransitions = getValidTransitions()\n\n  return (\n    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>\n      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}\n      \n      {!trigger && (\n        <DialogTrigger asChild>\n          <Button variant=\"outline\" size=\"sm\">\n            {getStatusIcon(student.status)}\n            <span className=\"ml-2\">Manage Status</span>\n          </Button>\n        </DialogTrigger>\n      )}\n\n      <DialogContent className=\"max-w-2xl\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <UserCheck className=\"h-5 w-5\" />\n            Manage Student Status\n          </DialogTitle>\n          <DialogDescription>\n            Update the enrollment status for {fullName}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Current Status */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Current Status</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center gap-3\">\n                {getStatusIcon(student.status)}\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    <Badge className={getStatusColor(student.status)}>\n                      {student.status}\n                    </Badge>\n                    <span className=\"text-sm text-muted-foreground\">\n                      Since {new Date(student.lastUpdated).toLocaleDateString()}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-muted-foreground mt-1\">\n                    {getStatusDescription(student.status)}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Status Change */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Change Status</CardTitle>\n              <CardDescription>\n                Select a new status for the student\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>New Status</Label>\n                <Select value={selectedStatus} onValueChange={(value: Student['status']) => setSelectedStatus(value)}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value={student.status} disabled>\n                      <div className=\"flex items-center gap-2\">\n                        {getStatusIcon(student.status)}\n                        {student.status} (Current)\n                      </div>\n                    </SelectItem>\n                    {validTransitions.map(transition => (\n                      <SelectItem key={transition.to} value={transition.to}>\n                        <div className=\"flex items-center gap-2\">\n                          {getStatusIcon(transition.to)}\n                          {transition.to}\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Transition Warning */}\n              {currentTransition && selectedStatus !== student.status && (\n                <Alert>\n                  <AlertTriangle className=\"h-4 w-4\" />\n                  <AlertDescription>\n                    {currentTransition.warning}\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              {/* Reason Input */}\n              {currentTransition?.requiresReason && selectedStatus !== student.status && (\n                <div className=\"space-y-2\">\n                  <Label>Reason for Status Change *</Label>\n                  <Textarea\n                    placeholder=\"Please provide a reason for this status change...\"\n                    value={reason}\n                    onChange={(e) => setReason(e.target.value)}\n                    rows={3}\n                  />\n                  <p className=\"text-xs text-muted-foreground\">\n                    This reason will be recorded in the student's history\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Status History Preview */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                Status History\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {/* Current status entry */}\n                <div className=\"flex items-center gap-3 p-3 bg-muted rounded-lg\">\n                  {getStatusIcon(student.status)}\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"font-medium\">{student.status}</span>\n                      <Badge variant=\"outline\" className=\"text-xs\">Current</Badge>\n                    </div>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Since {new Date(student.lastUpdated).toLocaleDateString()}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Preview of new status entry */}\n                {selectedStatus !== student.status && currentTransition?.allowed && (\n                  <div className=\"flex items-center gap-3 p-3 border-2 border-dashed border-primary/50 rounded-lg\">\n                    {getStatusIcon(selectedStatus)}\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"font-medium\">{selectedStatus}</span>\n                        <Badge variant=\"outline\" className=\"text-xs\">Pending</Badge>\n                      </div>\n                      <p className=\"text-xs text-muted-foreground\">\n                        Will be effective {new Date().toLocaleDateString()}\n                      </p>\n                      {reason && (\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          Reason: {reason}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Action Buttons */}\n          <div className=\"flex justify-end gap-2\">\n            <Button variant=\"outline\" onClick={() => handleOpenChange(false)}>\n              Cancel\n            </Button>\n            <Button \n              onClick={handleStatusUpdate}\n              disabled={\n                selectedStatus === student.status || \n                !currentTransition?.allowed ||\n                (currentTransition?.requiresReason && !reason.trim()) ||\n                isUpdating\n              }\n            >\n              {isUpdating ? (\n                <>\n                  <Clock className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Updating...\n                </>\n              ) : (\n                <>\n                  <CheckCircle className=\"mr-2 h-4 w-4\" />\n                  Update Status\n                </>\n              )}\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAxBA;;;;;;;;;;;;;;AA2CO,SAAS,qBAAqB,EACnC,OAAO,EACP,eAAe,EACf,OAAO,EACP,IAAI,EACJ,YAAY,EACc;IAC1B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,QAAQ,MAAM;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,aAAa;QACf,OAAO;YACL,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS;YACZ,iCAAiC;YACjC,kBAAkB,QAAQ,MAAM;YAChC,UAAU;YACV,cAAc;QAChB;IACF;IAEA,kCAAkC;IAClC,MAAM,oBAAwC;QAC5C;YAAE,MAAM;YAAU,IAAI;YAAY,SAAS;YAAM,gBAAgB;YAAM,SAAS;QAAiD;QACjI;YAAE,MAAM;YAAU,IAAI;YAAe,SAAS;YAAM,gBAAgB;YAAM,SAAS;QAAiD;QACpI;YAAE,MAAM;YAAU,IAAI;YAAa,SAAS;YAAM,gBAAgB;YAAO,SAAS;QAAsC;QACxH;YAAE,MAAM;YAAY,IAAI;YAAU,SAAS;YAAM,gBAAgB;YAAM,SAAS;QAA8B;QAC9G;YAAE,MAAM;YAAY,IAAI;YAAe,SAAS;YAAM,gBAAgB;YAAM,SAAS;QAAwC;QAC7H;YAAE,MAAM;YAAe,IAAI;YAAU,SAAS;YAAM,gBAAgB;YAAM,SAAS;QAA8B;QACjH;YAAE,MAAM;YAAa,IAAI;YAAU,SAAS;YAAO,gBAAgB;YAAO,SAAS;QAAuC;KAC3H;IAED,MAAM,sBAAsB;QAC1B,OAAO,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM,IAAI,EAAE,OAAO;IAC7E;IAEA,MAAM,uBAAuB;QAC3B,OAAO,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM,IAAI,EAAE,EAAE,KAAK;IAC3E;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,aAAa;QAEnB,IAAI,CAAC,YAAY,SAAS;YACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,cAAc,IAAI,CAAC,OAAO,IAAI,IAAI;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,cAAc;QAEd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,iBAA0B;gBAC9B,GAAG,OAAO;gBACV,QAAQ;gBACR,aAAa,IAAI,OAAO,WAAW;YACrC;YAEA,IAAI,iBAAiB;gBACnB,gBAAgB,gBAAgB,gBAAgB,OAAO,IAAI,MAAM;YACnE;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,0BAA0B,EAAE,gBAAgB;YAC3D,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,SAAS,YAAY,OAAO;IAC/C,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,oBAAoB;IAC1B,MAAM,mBAAmB;IAEzB,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAY,cAAc;;YACrC,yBAAW,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BAAE;;;;;;YAEnC,CAAC,yBACA,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;wBAC5B,cAAc,QAAQ,MAAM;sCAC7B,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAK7B,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,2HAAA,CAAA,eAAY;;0CACX,8OAAC,2HAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGnC,8OAAC,2HAAA,CAAA,oBAAiB;;oCAAC;oCACiB;;;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,QAAQ,MAAM;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0HAAA,CAAA,QAAK;oEAAC,WAAW,eAAe,QAAQ,MAAM;8EAC5C,QAAQ,MAAM;;;;;;8EAEjB,8OAAC;oEAAK,WAAU;;wEAAgC;wEACvC,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;sEAG3D,8OAAC;4DAAE,WAAU;sEACV,qBAAqB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ9C,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;;0DACT,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,2HAAA,CAAA,SAAM;wDAAC,OAAO;wDAAgB,eAAe,CAAC,QAA6B,kBAAkB;;0EAC5F,8OAAC,2HAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,2HAAA,CAAA,gBAAa;;kFACZ,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAO,QAAQ,MAAM;wEAAE,QAAQ;kFACzC,cAAA,8OAAC;4EAAI,WAAU;;gFACZ,cAAc,QAAQ,MAAM;gFAC5B,QAAQ,MAAM;gFAAC;;;;;;;;;;;;oEAGnB,iBAAiB,GAAG,CAAC,CAAA,2BACpB,8OAAC,2HAAA,CAAA,aAAU;4EAAqB,OAAO,WAAW,EAAE;sFAClD,cAAA,8OAAC;gFAAI,WAAU;;oFACZ,cAAc,WAAW,EAAE;oFAC3B,WAAW,EAAE;;;;;;;2EAHD,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;4CAYrC,qBAAqB,mBAAmB,QAAQ,MAAM,kBACrD,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC,0HAAA,CAAA,mBAAgB;kEACd,kBAAkB,OAAO;;;;;;;;;;;;4CAM/B,mBAAmB,kBAAkB,mBAAmB,QAAQ,MAAM,kBACrE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,6HAAA,CAAA,WAAQ;wDACP,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wDACzC,MAAM;;;;;;kEAER,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;0CASrD,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,QAAQ,MAAM;sEAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAe,QAAQ,MAAM;;;;;;sFAC7C,8OAAC,0HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAAU;;;;;;;;;;;;8EAE/C,8OAAC;oEAAE,WAAU;;wEAAgC;wEACpC,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;gDAM5D,mBAAmB,QAAQ,MAAM,IAAI,mBAAmB,yBACvD,8OAAC;oDAAI,WAAU;;wDACZ,cAAc;sEACf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAe;;;;;;sFAC/B,8OAAC,0HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAAU;;;;;;;;;;;;8EAE/C,8OAAC;oEAAE,WAAU;;wEAAgC;wEACxB,IAAI,OAAO,kBAAkB;;;;;;;gEAEjD,wBACC,8OAAC;oEAAE,WAAU;;wEAAqC;wEACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,iBAAiB;kDAAQ;;;;;;kDAGlE,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UACE,mBAAmB,QAAQ,MAAM,IACjC,CAAC,mBAAmB,WACnB,mBAAmB,kBAAkB,CAAC,OAAO,IAAI,MAClD;kDAGD,2BACC;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA8B;;yEAIjD;;8DACE,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D", "debugId": null}}, {"offset": {"line": 5296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/mobile-student-card.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { \n  MoreHorizontal, \n  Eye, \n  Edit, \n  QrCode, \n  Trash2,\n  Phone,\n  Mail,\n  MapPin,\n  GraduationCap,\n  User\n} from \"lucide-react\"\nimport { Student, getFullName } from \"@/lib/types/student\"\nimport { StudentProfileDialog } from \"./student-profile-dialog\"\nimport { StudentRegistrationDialog } from \"./student-registration-dialog\"\nimport { StudentStatusManager } from \"./student-status-manager\"\nimport { AttendanceStatisticsCompact } from \"./attendance-statistics\"\nimport { cn } from \"@/lib/utils\"\n\ninterface MobileStudentCardProps {\n  student: Student\n  isSelected: boolean\n  onSelectionChange: (selected: boolean) => void\n  onStudentUpdated?: (student: Student) => void\n  onStudentDeleted?: (studentId: string) => void\n  className?: string\n}\n\nexport function MobileStudentCard({\n  student,\n  isSelected,\n  onSelectionChange,\n  onStudentUpdated,\n  onStudentDeleted,\n  className\n}: MobileStudentCardProps) {\n  const fullName = getFullName(student)\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Active':\n        return 'bg-green-500'\n      case 'Inactive':\n        return 'bg-yellow-500'\n      case 'Transferred':\n        return 'bg-blue-500'\n      case 'Graduated':\n        return 'bg-purple-500'\n      default:\n        return 'bg-gray-500'\n    }\n  }\n\n  return (\n    <Card className={cn(\"w-full\", isSelected && \"ring-2 ring-primary\", className)}>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start gap-3\">\n          {/* Selection Checkbox */}\n          <Checkbox\n            checked={isSelected}\n            onCheckedChange={onSelectionChange}\n            className=\"mt-1\"\n            aria-label={`Select ${fullName}`}\n          />\n\n          {/* Student Avatar */}\n          <Avatar className=\"h-12 w-12 flex-shrink-0\">\n            <AvatarImage src={student.photo} alt={fullName} />\n            <AvatarFallback className=\"text-sm\">\n              {getInitials(fullName)}\n            </AvatarFallback>\n          </Avatar>\n\n          {/* Student Information */}\n          <div className=\"flex-1 min-w-0\">\n            {/* Header Row */}\n            <div className=\"flex items-start justify-between gap-2 mb-2\">\n              <div className=\"min-w-0 flex-1\">\n                <h3 className=\"font-semibold text-base truncate\">{fullName}</h3>\n                <p className=\"text-sm text-muted-foreground truncate\">{student.email}</p>\n              </div>\n              \n              {/* Actions Menu */}\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0 flex-shrink-0\">\n                    <MoreHorizontal className=\"h-4 w-4\" />\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent align=\"end\">\n                  <StudentProfileDialog\n                    student={student}\n                    onStudentUpdated={onStudentUpdated}\n                    trigger={\n                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                        <Eye className=\"h-4 w-4 mr-2\" />\n                        View Profile\n                      </DropdownMenuItem>\n                    }\n                  />\n                  <DropdownMenuItem>\n                    <Edit className=\"h-4 w-4 mr-2\" />\n                    Edit Student\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <QrCode className=\"h-4 w-4 mr-2\" />\n                    Generate QR Code\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <StudentStatusManager\n                    student={student}\n                    onStatusChanged={(updatedStudent) => onStudentUpdated?.(updatedStudent)}\n                    trigger={\n                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                        <Edit className=\"h-4 w-4 mr-2\" />\n                        Manage Status\n                      </DropdownMenuItem>\n                    }\n                  />\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem \n                    className=\"text-destructive\"\n                    onClick={() => onStudentDeleted?.(student.id)}\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    Delete Student\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n\n            {/* Student Details Grid */}\n            <div className=\"grid grid-cols-2 gap-2 text-sm mb-3\">\n              <div className=\"flex items-center gap-1\">\n                <User className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                <span className=\"font-mono text-xs truncate\">{student.id}</span>\n              </div>\n              \n              <div className=\"flex items-center gap-1\">\n                <GraduationCap className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                <span className=\"text-xs truncate\">Grade {student.grade}</span>\n              </div>\n              \n              <div className=\"flex items-center gap-1\">\n                <Phone className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                <span className=\"text-xs truncate\">{student.guardian.phone}</span>\n              </div>\n              \n              <div className=\"flex items-center gap-1\">\n                <Mail className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                <span className=\"text-xs truncate\">{student.guardian.email || 'No email'}</span>\n              </div>\n            </div>\n\n            {/* Course and Section */}\n            <div className=\"flex items-center gap-2 mb-3\">\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {student.course}\n              </Badge>\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {student.year}\n              </Badge>\n              {student.section && (\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Section {student.section}\n                </Badge>\n              )}\n            </div>\n\n            {/* Bottom Row - Status and Attendance */}\n            <div className=\"flex items-center justify-between\">\n              <Badge \n                variant={student.status === 'Active' ? 'default' : 'secondary'}\n                className={cn(\"text-xs\", getStatusColor(student.status))}\n              >\n                {student.status}\n              </Badge>\n              \n              <AttendanceStatisticsCompact student={student} />\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Mobile-friendly student list component\ninterface MobileStudentListProps {\n  students: Student[]\n  selectedStudents: string[]\n  onSelectionChange: (studentIds: string[]) => void\n  onStudentUpdated?: (student: Student) => void\n  onStudentDeleted?: (studentId: string) => void\n  className?: string\n}\n\nexport function MobileStudentList({\n  students,\n  selectedStudents,\n  onSelectionChange,\n  onStudentUpdated,\n  onStudentDeleted,\n  className\n}: MobileStudentListProps) {\n  const handleSelectStudent = (studentId: string, checked: boolean) => {\n    if (checked) {\n      onSelectionChange([...selectedStudents, studentId])\n    } else {\n      onSelectionChange(selectedStudents.filter(id => id !== studentId))\n    }\n  }\n\n  if (students.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <User className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n        <h3 className=\"text-lg font-semibold mb-2\">No students found</h3>\n        <p className=\"text-muted-foreground\">\n          No students match your current search and filter criteria.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className={cn(\"space-y-3\", className)}>\n      {students.map((student) => (\n        <MobileStudentCard\n          key={student.id}\n          student={student}\n          isSelected={selectedStudents.includes(student.id)}\n          onSelectionChange={(checked) => handleSelectStudent(student.id, checked)}\n          onStudentUpdated={onStudentUpdated}\n          onStudentDeleted={onStudentDeleted}\n        />\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAEA;AACA;AACA;AAzBA;;;;;;;;;;;;;;AAoCO,SAAS,kBAAkB,EAChC,OAAO,EACP,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAE7B,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU,cAAc,uBAAuB;kBACjE,cAAA,8OAAC,yHAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,6HAAA,CAAA,WAAQ;wBACP,SAAS;wBACT,iBAAiB;wBACjB,WAAU;wBACV,cAAY,CAAC,OAAO,EAAE,UAAU;;;;;;kCAIlC,8OAAC,2HAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,2HAAA,CAAA,cAAW;gCAAC,KAAK,QAAQ,KAAK;gCAAE,KAAK;;;;;;0CACtC,8OAAC,2HAAA,CAAA,iBAAc;gCAAC,WAAU;0CACvB,YAAY;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAA0C,QAAQ,KAAK;;;;;;;;;;;;kDAItE,8OAAC,qIAAA,CAAA,eAAY;;0DACX,8OAAC,qIAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,WAAU;8DAC1C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG9B,8OAAC,qIAAA,CAAA,sBAAmB;gDAAC,OAAM;;kEACzB,8OAAC,uJAAA,CAAA,uBAAoB;wDACnB,SAAS;wDACT,kBAAkB;wDAClB,uBACE,8OAAC,qIAAA,CAAA,mBAAgB;4DAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;8EACjD,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAKtC,8OAAC,qIAAA,CAAA,mBAAgB;;0EACf,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,qIAAA,CAAA,mBAAgB;;0EACf,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGrC,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,uJAAA,CAAA,uBAAoB;wDACnB,SAAS;wDACT,iBAAiB,CAAC,iBAAmB,mBAAmB;wDACxD,uBACE,8OAAC,qIAAA,CAAA,mBAAgB;4DAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;8EACjD,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAKvC,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,qIAAA,CAAA,mBAAgB;wDACf,WAAU;wDACV,SAAS,IAAM,mBAAmB,QAAQ,EAAE;;0EAE5C,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAA8B,QAAQ,EAAE;;;;;;;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;;oDAAmB;oDAAO,QAAQ,KAAK;;;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAoB,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoB,QAAQ,QAAQ,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;;;;0CAKlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,QAAQ,MAAM;;;;;;kDAEjB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,QAAQ,IAAI;;;;;;oCAEd,QAAQ,OAAO,kBACd,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAAU;4CACpC,QAAQ,OAAO;;;;;;;;;;;;;0CAM9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCACJ,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;wCACnD,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,QAAQ,MAAM;kDAErD,QAAQ,MAAM;;;;;;kDAGjB,8OAAC,mJAAA,CAAA,8BAA2B;wCAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAYO,SAAS,kBAAkB,EAChC,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,IAAI,SAAS;YACX,kBAAkB;mBAAI;gBAAkB;aAAU;QACpD,OAAO;YACL,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO;QACzD;IACF;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,8OAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gBAEC,SAAS;gBACT,YAAY,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;gBAChD,mBAAmB,CAAC,UAAY,oBAAoB,QAAQ,EAAE,EAAE;gBAChE,kBAAkB;gBAClB,kBAAkB;eALb,QAAQ,EAAE;;;;;;;;;;AAUzB", "debugId": null}}, {"offset": {"line": 5846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/students-table.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { \n  ChevronUp, \n  ChevronDown, \n  MoreHorizontal, \n  Eye, \n  Edit, \n  QrCode, \n  Trash2,\n  Phone,\n  Mail,\n  MapPin\n} from \"lucide-react\"\nimport { Student, StudentSortConfig, getFullName } from \"@/lib/types/student\"\nimport { StudentProfileDialog } from \"./student-profile-dialog\"\nimport { StudentRegistrationDialog } from \"./student-registration-dialog\"\nimport { StudentStatusManager } from \"./student-status-manager\"\nimport { AttendanceStatisticsCompact } from \"./attendance-statistics\"\nimport { MobileStudentList } from \"./mobile-student-card\"\nimport { cn } from \"@/lib/utils\"\n\ninterface StudentsTableProps {\n  students: Student[]\n  selectedStudents: string[]\n  onSelectionChange: (studentIds: string[]) => void\n  sortConfig: StudentSortConfig\n  onSortChange: (config: StudentSortConfig) => void\n  onStudentUpdated?: (student: Student) => void\n  onStudentDeleted?: (studentId: string) => void\n  className?: string\n}\n\nexport function StudentsTable({\n  students,\n  selectedStudents,\n  onSelectionChange,\n  sortConfig,\n  onSortChange,\n  onStudentUpdated,\n  onStudentDeleted,\n  className\n}: StudentsTableProps) {\n  const [showProfileDialog, setShowProfileDialog] = useState<Student | null>(null)\n  const [showEditDialog, setShowEditDialog] = useState<Student | null>(null)\n\n  const handleSort = (field: keyof Student | 'name') => {\n    const direction = sortConfig.field === field && sortConfig.direction === 'asc' ? 'desc' : 'asc'\n    onSortChange({ field, direction })\n  }\n\n  const handleSelectAll = (checked: boolean) => {\n    if (checked) {\n      onSelectionChange(students.map(s => s.id))\n    } else {\n      onSelectionChange([])\n    }\n  }\n\n  const handleSelectStudent = (studentId: string, checked: boolean) => {\n    if (checked) {\n      onSelectionChange([...selectedStudents, studentId])\n    } else {\n      onSelectionChange(selectedStudents.filter(id => id !== studentId))\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Active':\n        return 'bg-green-500'\n      case 'Inactive':\n        return 'bg-yellow-500'\n      case 'Transferred':\n        return 'bg-blue-500'\n      case 'Graduated':\n        return 'bg-purple-500'\n      default:\n        return 'bg-gray-500'\n    }\n  }\n\n  const SortButton = ({ field, children }: { field: keyof Student | 'name', children: React.ReactNode }) => {\n    const isActive = sortConfig.field === field\n    const direction = sortConfig.direction\n\n    return (\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"h-auto p-0 font-medium hover:bg-transparent\"\n        onClick={() => handleSort(field)}\n      >\n        <span className=\"flex items-center gap-1\">\n          {children}\n          {isActive ? (\n            direction === 'asc' ? (\n              <ChevronUp className=\"h-4 w-4\" />\n            ) : (\n              <ChevronDown className=\"h-4 w-4\" />\n            )\n          ) : (\n            <div className=\"h-4 w-4\" />\n          )}\n        </span>\n      </Button>\n    )\n  }\n\n  const isAllSelected = students.length > 0 && selectedStudents.length === students.length\n  const isIndeterminate = selectedStudents.length > 0 && selectedStudents.length < students.length\n\n  return (\n    <div className={cn(\"space-y-4\", className)}>\n      {/* Desktop Table View */}\n      <div className=\"hidden lg:block\">\n        <div className=\"rounded-md border\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead className=\"w-12\">\n                  <Checkbox\n                    checked={isAllSelected}\n                    onCheckedChange={handleSelectAll}\n                    aria-label=\"Select all students\"\n                    className={cn(isIndeterminate && \"data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground opacity-50\")}\n                  />\n                </TableHead>\n                <TableHead className=\"w-16\">Photo</TableHead>\n                <TableHead>\n                  <SortButton field=\"name\">Name</SortButton>\n                </TableHead>\n                <TableHead>\n                  <SortButton field=\"id\">Student ID</SortButton>\n                </TableHead>\n                <TableHead>\n                  <SortButton field=\"grade\">Grade</SortButton>\n                </TableHead>\n                <TableHead>Section</TableHead>\n                <TableHead>\n                  <SortButton field=\"course\">Course</SortButton>\n                </TableHead>\n                <TableHead>Contact</TableHead>\n                <TableHead>\n                  <SortButton field=\"status\">Status</SortButton>\n                </TableHead>\n                <TableHead>Attendance</TableHead>\n                <TableHead className=\"text-right\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n            {students.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={11} className=\"text-center py-8 text-muted-foreground\">\n                  No students found matching your criteria\n                </TableCell>\n              </TableRow>\n            ) : (\n              students.map((student) => {\n                const fullName = getFullName(student)\n                const isSelected = selectedStudents.includes(student.id)\n                \n                return (\n                  <TableRow key={student.id} className={cn(isSelected && \"bg-muted/50\")}>\n                    <TableCell>\n                      <Checkbox\n                        checked={isSelected}\n                        onCheckedChange={(checked) => handleSelectStudent(student.id, checked as boolean)}\n                        aria-label={`Select ${fullName}`}\n                      />\n                    </TableCell>\n                    \n                    <TableCell>\n                      <Avatar className=\"h-10 w-10\">\n                        <AvatarImage src={student.photo} alt={fullName} />\n                        <AvatarFallback className=\"text-xs\">\n                          {getInitials(fullName)}\n                        </AvatarFallback>\n                      </Avatar>\n                    </TableCell>\n                    \n                    <TableCell>\n                      <div>\n                        <div className=\"font-medium\">{fullName}</div>\n                        <div className=\"text-sm text-muted-foreground\">{student.email}</div>\n                      </div>\n                    </TableCell>\n                    \n                    <TableCell className=\"font-mono text-sm\">{student.id}</TableCell>\n                    \n                    <TableCell>\n                      <Badge variant=\"outline\">Grade {student.grade}</Badge>\n                    </TableCell>\n                    \n                    <TableCell>\n                      {student.section ? (\n                        <Badge variant=\"secondary\">{student.section}</Badge>\n                      ) : (\n                        <span className=\"text-muted-foreground text-sm\">No section</span>\n                      )}\n                    </TableCell>\n                    \n                    <TableCell>\n                      <div className=\"text-sm\">\n                        <div>{student.course}</div>\n                        <div className=\"text-muted-foreground\">{student.year}</div>\n                      </div>\n                    </TableCell>\n                    \n                    <TableCell>\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                          <Phone className=\"h-3 w-3\" />\n                          <span>{student.guardian.phone}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                          <Mail className=\"h-3 w-3\" />\n                          <span className=\"truncate max-w-[120px]\">{student.guardian.email || 'No email'}</span>\n                        </div>\n                      </div>\n                    </TableCell>\n                    \n                    <TableCell>\n                      <Badge \n                        variant={student.status === 'Active' ? 'default' : 'secondary'}\n                        className={cn(\"text-xs\", getStatusColor(student.status))}\n                      >\n                        {student.status}\n                      </Badge>\n                    </TableCell>\n                    \n                    <TableCell>\n                      <AttendanceStatisticsCompact student={student} />\n                    </TableCell>\n                    \n                    <TableCell className=\"text-right\">\n                      <div className=\"flex items-center justify-end gap-2\">\n                        <StudentProfileDialog\n                          student={student}\n                          onStudentUpdated={onStudentUpdated}\n                          trigger={\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                          }\n                        />\n                        \n                        <DropdownMenu>\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <MoreHorizontal className=\"h-4 w-4\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent align=\"end\">\n                            <DropdownMenuItem onClick={() => setShowEditDialog(student)}>\n                              <Edit className=\"h-4 w-4 mr-2\" />\n                              Edit Student\n                            </DropdownMenuItem>\n                            <DropdownMenuItem>\n                              <QrCode className=\"h-4 w-4 mr-2\" />\n                              Generate QR Code\n                            </DropdownMenuItem>\n                            <DropdownMenuSeparator />\n                            <StudentStatusManager\n                              student={student}\n                              onStatusChanged={(updatedStudent) => onStudentUpdated?.(updatedStudent)}\n                              trigger={\n                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                                  <Edit className=\"h-4 w-4 mr-2\" />\n                                  Manage Status\n                                </DropdownMenuItem>\n                              }\n                            />\n                            <DropdownMenuSeparator />\n                            <DropdownMenuItem\n                              className=\"text-destructive\"\n                              onClick={() => onStudentDeleted?.(student.id)}\n                            >\n                              <Trash2 className=\"h-4 w-4 mr-2\" />\n                              Delete Student\n                            </DropdownMenuItem>\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                )\n              })\n            )}\n            </TableBody>\n          </Table>\n        </div>\n      </div>\n\n      {/* Mobile Card View */}\n      <div className=\"lg:hidden\">\n        <MobileStudentList\n          students={students}\n          selectedStudents={selectedStudents}\n          onSelectionChange={onSelectionChange}\n          onStudentUpdated={onStudentUpdated}\n          onStudentDeleted={onStudentDeleted}\n        />\n      </div>\n\n      {/* Edit Dialog */}\n      {showEditDialog && (\n        <StudentRegistrationDialog\n          mode=\"edit\"\n          open={!!showEditDialog}\n          onOpenChange={(open) => !open && setShowEditDialog(null)}\n          initialData={{\n            id: showEditDialog.id,\n            firstName: showEditDialog.firstName,\n            middleName: showEditDialog.middleName,\n            lastName: showEditDialog.lastName,\n            email: showEditDialog.email,\n            dateOfBirth: showEditDialog.dateOfBirth,\n            gender: showEditDialog.gender,\n            course: showEditDialog.course,\n            year: showEditDialog.year,\n            section: showEditDialog.section,\n            grade: showEditDialog.grade,\n            guardianName: showEditDialog.guardian.name,\n            guardianPhone: showEditDialog.guardian.phone,\n            guardianEmail: showEditDialog.guardian.email,\n            guardianRelationship: showEditDialog.guardian.relationship,\n            guardianAddress: showEditDialog.guardian.address,\n            emergencyContacts: showEditDialog.emergencyContacts,\n            street: showEditDialog.address.street,\n            barangay: showEditDialog.address.barangay,\n            city: showEditDialog.address.city,\n            province: showEditDialog.address.province,\n            zipCode: showEditDialog.address.zipCode,\n            country: showEditDialog.address.country,\n            photo: showEditDialog.photo\n          }}\n          onStudentCreated={(updatedStudent) => {\n            onStudentUpdated?.(updatedStudent)\n            setShowEditDialog(null)\n          }}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;;;;;;AAwCO,SAAS,cAAc,EAC5B,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACU;IACnB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,MAAM,aAAa,CAAC;QAClB,MAAM,YAAY,WAAW,KAAK,KAAK,SAAS,WAAW,SAAS,KAAK,QAAQ,SAAS;QAC1F,aAAa;YAAE;YAAO;QAAU;IAClC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS;YACX,kBAAkB,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC1C,OAAO;YACL,kBAAkB,EAAE;QACtB;IACF;IAEA,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,IAAI,SAAS;YACX,kBAAkB;mBAAI;gBAAkB;aAAU;QACpD,OAAO;YACL,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO;QACzD;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAgE;QACnG,MAAM,WAAW,WAAW,KAAK,KAAK;QACtC,MAAM,YAAY,WAAW,SAAS;QAEtC,qBACE,8OAAC,2HAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,IAAM,WAAW;sBAE1B,cAAA,8OAAC;gBAAK,WAAU;;oBACb;oBACA,WACC,cAAc,sBACZ,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;6CAErB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAGzB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,gBAAgB,SAAS,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,SAAS,MAAM;IACxF,MAAM,kBAAkB,iBAAiB,MAAM,GAAG,KAAK,iBAAiB,MAAM,GAAG,SAAS,MAAM;IAEhG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;0CACJ,8OAAC,0HAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;sDACP,8OAAC,0HAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,8OAAC,6HAAA,CAAA,WAAQ;gDACP,SAAS;gDACT,iBAAiB;gDACjB,cAAW;gDACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;;;;;;;;;;sDAGrC,8OAAC,0HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAO;;;;;;sDAC5B,8OAAC,0HAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAW,OAAM;0DAAO;;;;;;;;;;;sDAE3B,8OAAC,0HAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAW,OAAM;0DAAK;;;;;;;;;;;sDAEzB,8OAAC,0HAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAW,OAAM;0DAAQ;;;;;;;;;;;sDAE5B,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAW,OAAM;0DAAS;;;;;;;;;;;sDAE7B,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAW,OAAM;0DAAS;;;;;;;;;;;sDAE7B,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;;;;;;;;;;;;0CAGtC,8OAAC,0HAAA,CAAA,YAAS;0CACT,SAAS,MAAM,KAAK,kBACnB,8OAAC,0HAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,0HAAA,CAAA,YAAS;wCAAC,SAAS;wCAAI,WAAU;kDAAyC;;;;;;;;;;2CAK7E,SAAS,GAAG,CAAC,CAAC;oCACZ,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;oCAC7B,MAAM,aAAa,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;oCAEvD,qBACE,8OAAC,0HAAA,CAAA,WAAQ;wCAAkB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;;0DACrD,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oDACP,SAAS;oDACT,iBAAiB,CAAC,UAAY,oBAAoB,QAAQ,EAAE,EAAE;oDAC9D,cAAY,CAAC,OAAO,EAAE,UAAU;;;;;;;;;;;0DAIpC,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,2HAAA,CAAA,cAAW;4DAAC,KAAK,QAAQ,KAAK;4DAAE,KAAK;;;;;;sEACtC,8OAAC,2HAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,YAAY;;;;;;;;;;;;;;;;;0DAKnB,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAe;;;;;;sEAC9B,8OAAC;4DAAI,WAAU;sEAAiC,QAAQ,KAAK;;;;;;;;;;;;;;;;;0DAIjE,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAqB,QAAQ,EAAE;;;;;;0DAEpD,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAU;wDAAO,QAAQ,KAAK;;;;;;;;;;;;0DAG/C,8OAAC,0HAAA,CAAA,YAAS;0DACP,QAAQ,OAAO,iBACd,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa,QAAQ,OAAO;;;;;yEAE3C,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;0DAIpD,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK,QAAQ,MAAM;;;;;;sEACpB,8OAAC;4DAAI,WAAU;sEAAyB,QAAQ,IAAI;;;;;;;;;;;;;;;;;0DAIxD,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sEAE/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAA0B,QAAQ,QAAQ,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;0DAK1E,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDACJ,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;oDACnD,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,QAAQ,MAAM;8DAErD,QAAQ,MAAM;;;;;;;;;;;0DAInB,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC,mJAAA,CAAA,8BAA2B;oDAAC,SAAS;;;;;;;;;;;0DAGxC,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,uJAAA,CAAA,uBAAoB;4DACnB,SAAS;4DACT,kBAAkB;4DAClB,uBACE,8OAAC,2HAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAKrB,8OAAC,qIAAA,CAAA,eAAY;;8EACX,8OAAC,qIAAA,CAAA,sBAAmB;oEAAC,OAAO;8EAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAG9B,8OAAC,qIAAA,CAAA,sBAAmB;oEAAC,OAAM;;sFACzB,8OAAC,qIAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,kBAAkB;;8FACjD,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,8OAAC,qIAAA,CAAA,mBAAgB;;8FACf,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGrC,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sFACtB,8OAAC,uJAAA,CAAA,uBAAoB;4EACnB,SAAS;4EACT,iBAAiB,CAAC,iBAAmB,mBAAmB;4EACxD,uBACE,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;kGACjD,8OAAC,2MAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;sFAKvC,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sFACtB,8OAAC,qIAAA,CAAA,mBAAgB;4EACf,WAAU;4EACV,SAAS,IAAM,mBAAmB,QAAQ,EAAE;;8FAE5C,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAnHhC,QAAQ,EAAE;;;;;gCA4H7B;;;;;;;;;;;;;;;;;;;;;;0BAQR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oJAAA,CAAA,oBAAiB;oBAChB,UAAU;oBACV,kBAAkB;oBAClB,mBAAmB;oBACnB,kBAAkB;oBAClB,kBAAkB;;;;;;;;;;;YAKrB,gCACC,8OAAC,4JAAA,CAAA,4BAAyB;gBACxB,MAAK;gBACL,MAAM,CAAC,CAAC;gBACR,cAAc,CAAC,OAAS,CAAC,QAAQ,kBAAkB;gBACnD,aAAa;oBACX,IAAI,eAAe,EAAE;oBACrB,WAAW,eAAe,SAAS;oBACnC,YAAY,eAAe,UAAU;oBACrC,UAAU,eAAe,QAAQ;oBACjC,OAAO,eAAe,KAAK;oBAC3B,aAAa,eAAe,WAAW;oBACvC,QAAQ,eAAe,MAAM;oBAC7B,QAAQ,eAAe,MAAM;oBAC7B,MAAM,eAAe,IAAI;oBACzB,SAAS,eAAe,OAAO;oBAC/B,OAAO,eAAe,KAAK;oBAC3B,cAAc,eAAe,QAAQ,CAAC,IAAI;oBAC1C,eAAe,eAAe,QAAQ,CAAC,KAAK;oBAC5C,eAAe,eAAe,QAAQ,CAAC,KAAK;oBAC5C,sBAAsB,eAAe,QAAQ,CAAC,YAAY;oBAC1D,iBAAiB,eAAe,QAAQ,CAAC,OAAO;oBAChD,mBAAmB,eAAe,iBAAiB;oBACnD,QAAQ,eAAe,OAAO,CAAC,MAAM;oBACrC,UAAU,eAAe,OAAO,CAAC,QAAQ;oBACzC,MAAM,eAAe,OAAO,CAAC,IAAI;oBACjC,UAAU,eAAe,OAAO,CAAC,QAAQ;oBACzC,SAAS,eAAe,OAAO,CAAC,OAAO;oBACvC,SAAS,eAAe,OAAO,CAAC,OAAO;oBACvC,OAAO,eAAe,KAAK;gBAC7B;gBACA,kBAAkB,CAAC;oBACjB,mBAAmB;oBACnB,kBAAkB;gBACpB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 6649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/bulk-actions.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from \"@/components/ui/alert-dialog\"\nimport { \n  Download, \n  Upload, \n  QrCode, \n  Trash2, \n  FileText, \n  Users, \n  CheckCircle,\n  AlertTriangle,\n  X\n} from \"lucide-react\"\nimport { Student } from \"@/lib/types/student\"\nimport { toast } from \"sonner\"\n\ninterface BulkActionsProps {\n  selectedStudents: string[]\n  students: Student[]\n  onClearSelection: () => void\n  onStudentsUpdated?: (students: Student[]) => void\n  className?: string\n}\n\nexport function BulkActions({\n  selectedStudents,\n  students,\n  onClearSelection,\n  onStudentsUpdated,\n  className\n}: BulkActionsProps) {\n  const [isExporting, setIsExporting] = useState(false)\n  const [isGeneratingQR, setIsGeneratingQR] = useState(false)\n  const [showStatusDialog, setShowStatusDialog] = useState(false)\n  const [newStatus, setNewStatus] = useState<string>('')\n\n  const selectedStudentData = students.filter(s => selectedStudents.includes(s.id))\n\n  const handleExportCSV = async () => {\n    setIsExporting(true)\n    try {\n      // Simulate CSV export\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Create CSV content\n      const headers = ['ID', 'Name', 'Email', 'Grade', 'Section', 'Course', 'Year', 'Status', 'Guardian Name', 'Guardian Phone']\n      const csvContent = [\n        headers.join(','),\n        ...selectedStudentData.map(student => [\n          student.id,\n          `\"${student.firstName} ${student.middleName || ''} ${student.lastName}\".trim()`,\n          student.email,\n          student.grade,\n          student.section || '',\n          `\"${student.course}\"`,\n          `\"${student.year}\"`,\n          student.status,\n          `\"${student.guardian.name}\"`,\n          student.guardian.phone\n        ].join(','))\n      ].join('\\n')\n\n      // Download CSV\n      const blob = new Blob([csvContent], { type: 'text/csv' })\n      const url = window.URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `students_export_${new Date().toISOString().split('T')[0]}.csv`\n      a.click()\n      window.URL.revokeObjectURL(url)\n\n      toast.success(`Exported ${selectedStudents.length} students to CSV`)\n    } catch (error) {\n      toast.error('Failed to export students')\n    } finally {\n      setIsExporting(false)\n    }\n  }\n\n  const handleGenerateQRCodes = async () => {\n    setIsGeneratingQR(true)\n    try {\n      // Simulate QR code generation\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      toast.success(`Generated QR codes for ${selectedStudents.length} students`)\n      \n      // Here you would typically trigger the QR code generation and download\n      console.log('Generating QR codes for students:', selectedStudents)\n    } catch (error) {\n      toast.error('Failed to generate QR codes')\n    } finally {\n      setIsGeneratingQR(false)\n    }\n  }\n\n  const handleStatusUpdate = async () => {\n    if (!newStatus) return\n\n    try {\n      // Simulate status update\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      const updatedStudents = students.map(student => \n        selectedStudents.includes(student.id) \n          ? { ...student, status: newStatus as any }\n          : student\n      )\n\n      onStudentsUpdated?.(updatedStudents)\n      toast.success(`Updated status for ${selectedStudents.length} students`)\n      setShowStatusDialog(false)\n      setNewStatus('')\n      onClearSelection()\n    } catch (error) {\n      toast.error('Failed to update student status')\n    }\n  }\n\n  const handleDeleteStudents = async () => {\n    try {\n      // Simulate deletion\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      const remainingStudents = students.filter(s => !selectedStudents.includes(s.id))\n      onStudentsUpdated?.(remainingStudents)\n      toast.success(`Deleted ${selectedStudents.length} students`)\n      onClearSelection()\n    } catch (error) {\n      toast.error('Failed to delete students')\n    }\n  }\n\n  const handleExportGuardianContacts = async () => {\n    try {\n      // Create guardian contacts CSV\n      const headers = ['Student ID', 'Student Name', 'Guardian Name', 'Relationship', 'Phone', 'Email', 'Address']\n      const csvContent = [\n        headers.join(','),\n        ...selectedStudentData.map(student => [\n          student.id,\n          `\"${student.firstName} ${student.middleName || ''} ${student.lastName}\".trim()`,\n          `\"${student.guardian.name}\"`,\n          student.guardian.relationship,\n          student.guardian.phone,\n          student.guardian.email || '',\n          `\"${student.guardian.address || ''}\"`\n        ].join(','))\n      ].join('\\n')\n\n      // Download CSV\n      const blob = new Blob([csvContent], { type: 'text/csv' })\n      const url = window.URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `guardian_contacts_${new Date().toISOString().split('T')[0]}.csv`\n      a.click()\n      window.URL.revokeObjectURL(url)\n\n      toast.success(`Exported guardian contacts for ${selectedStudents.length} students`)\n    } catch (error) {\n      toast.error('Failed to export guardian contacts')\n    }\n  }\n\n  if (selectedStudents.length === 0) {\n    return null\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Bulk Actions\n            <Badge variant=\"secondary\">{selectedStudents.length} selected</Badge>\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={onClearSelection}>\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </CardTitle>\n        <CardDescription>\n          Perform actions on {selectedStudents.length} selected student{selectedStudents.length !== 1 ? 's' : ''}\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"flex flex-wrap gap-2\">\n          {/* Export Actions */}\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={handleExportCSV}\n            disabled={isExporting}\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            {isExporting ? 'Exporting...' : 'Export CSV'}\n          </Button>\n\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={handleExportGuardianContacts}\n          >\n            <FileText className=\"h-4 w-4 mr-2\" />\n            Export Contacts\n          </Button>\n\n          {/* QR Code Generation */}\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={handleGenerateQRCodes}\n            disabled={isGeneratingQR}\n          >\n            <QrCode className=\"h-4 w-4 mr-2\" />\n            {isGeneratingQR ? 'Generating...' : 'Generate QR Codes'}\n          </Button>\n\n          {/* Status Update */}\n          <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>\n            <DialogTrigger asChild>\n              <Button variant=\"outline\" size=\"sm\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Update Status\n              </Button>\n            </DialogTrigger>\n            <DialogContent>\n              <DialogHeader>\n                <DialogTitle>Update Student Status</DialogTitle>\n                <DialogDescription>\n                  Change the status for {selectedStudents.length} selected student{selectedStudents.length !== 1 ? 's' : ''}\n                </DialogDescription>\n              </DialogHeader>\n              <div className=\"space-y-4\">\n                <Select value={newStatus} onValueChange={setNewStatus}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select new status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Active\">Active</SelectItem>\n                    <SelectItem value=\"Inactive\">Inactive</SelectItem>\n                    <SelectItem value=\"Transferred\">Transferred</SelectItem>\n                    <SelectItem value=\"Graduated\">Graduated</SelectItem>\n                  </SelectContent>\n                </Select>\n                <div className=\"flex justify-end gap-2\">\n                  <Button variant=\"outline\" onClick={() => setShowStatusDialog(false)}>\n                    Cancel\n                  </Button>\n                  <Button onClick={handleStatusUpdate} disabled={!newStatus}>\n                    Update Status\n                  </Button>\n                </div>\n              </div>\n            </DialogContent>\n          </Dialog>\n\n          {/* Delete Action */}\n          <AlertDialog>\n            <AlertDialogTrigger asChild>\n              <Button variant=\"outline\" size=\"sm\" className=\"text-destructive hover:text-destructive\">\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete\n              </Button>\n            </AlertDialogTrigger>\n            <AlertDialogContent>\n              <AlertDialogHeader>\n                <AlertDialogTitle className=\"flex items-center gap-2\">\n                  <AlertTriangle className=\"h-5 w-5 text-destructive\" />\n                  Delete Students\n                </AlertDialogTitle>\n                <AlertDialogDescription>\n                  Are you sure you want to delete {selectedStudents.length} student{selectedStudents.length !== 1 ? 's' : ''}? \n                  This action cannot be undone and will permanently remove all student data including:\n                  <ul className=\"list-disc list-inside mt-2 space-y-1\">\n                    <li>Personal information</li>\n                    <li>Academic records</li>\n                    <li>Attendance history</li>\n                    <li>Guardian and emergency contacts</li>\n                  </ul>\n                </AlertDialogDescription>\n              </AlertDialogHeader>\n              <AlertDialogFooter>\n                <AlertDialogCancel>Cancel</AlertDialogCancel>\n                <AlertDialogAction \n                  onClick={handleDeleteStudents}\n                  className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\n                >\n                  Delete Students\n                </AlertDialogAction>\n              </AlertDialogFooter>\n            </AlertDialogContent>\n          </AlertDialog>\n        </div>\n\n        {/* Selected Students Preview */}\n        <div className=\"mt-4 pt-4 border-t\">\n          <h4 className=\"text-sm font-medium mb-2\">Selected Students:</h4>\n          <div className=\"flex flex-wrap gap-1 max-h-20 overflow-y-auto\">\n            {selectedStudentData.slice(0, 10).map(student => (\n              <Badge key={student.id} variant=\"secondary\" className=\"text-xs\">\n                {student.firstName} {student.lastName}\n              </Badge>\n            ))}\n            {selectedStudentData.length > 10 && (\n              <Badge variant=\"secondary\" className=\"text-xs\">\n                +{selectedStudentData.length - 10} more\n              </Badge>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AArBA;;;;;;;;;;;AA+BO,SAAS,YAAY,EAC1B,gBAAgB,EAChB,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACQ;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,sBAAsB,SAAS,MAAM,CAAC,CAAA,IAAK,iBAAiB,QAAQ,CAAC,EAAE,EAAE;IAE/E,MAAM,kBAAkB;QACtB,eAAe;QACf,IAAI;YACF,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,qBAAqB;YACrB,MAAM,UAAU;gBAAC;gBAAM;gBAAQ;gBAAS;gBAAS;gBAAW;gBAAU;gBAAQ;gBAAU;gBAAiB;aAAiB;YAC1H,MAAM,aAAa;gBACjB,QAAQ,IAAI,CAAC;mBACV,oBAAoB,GAAG,CAAC,CAAA,UAAW;wBACpC,QAAQ,EAAE;wBACV,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,IAAI,GAAG,CAAC,EAAE,QAAQ,QAAQ,CAAC,QAAQ,CAAC;wBAC/E,QAAQ,KAAK;wBACb,QAAQ,KAAK;wBACb,QAAQ,OAAO,IAAI;wBACnB,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;wBACrB,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;wBACnB,QAAQ,MAAM;wBACd,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5B,QAAQ,QAAQ,CAAC,KAAK;qBACvB,CAAC,IAAI,CAAC;aACR,CAAC,IAAI,CAAC;YAEP,eAAe;YACf,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAW;YACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC5E,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,iBAAiB,MAAM,CAAC,gBAAgB,CAAC;QACrE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,IAAI;YACF,8BAA8B;YAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,uBAAuB,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC;YAE1E,uEAAuE;YACvE,QAAQ,GAAG,CAAC,qCAAqC;QACnD,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,yBAAyB;YACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,UACnC,iBAAiB,QAAQ,CAAC,QAAQ,EAAE,IAChC;oBAAE,GAAG,OAAO;oBAAE,QAAQ;gBAAiB,IACvC;YAGN,oBAAoB;YACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC;YACtE,oBAAoB;YACpB,aAAa;YACb;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,iBAAiB,QAAQ,CAAC,EAAE,EAAE;YAC9E,oBAAoB;YACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,+BAA+B;QACnC,IAAI;YACF,+BAA+B;YAC/B,MAAM,UAAU;gBAAC;gBAAc;gBAAgB;gBAAiB;gBAAgB;gBAAS;gBAAS;aAAU;YAC5G,MAAM,aAAa;gBACjB,QAAQ,IAAI,CAAC;mBACV,oBAAoB,GAAG,CAAC,CAAA,UAAW;wBACpC,QAAQ,EAAE;wBACV,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,IAAI,GAAG,CAAC,EAAE,QAAQ,QAAQ,CAAC,QAAQ,CAAC;wBAC/E,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5B,QAAQ,QAAQ,CAAC,YAAY;wBAC7B,QAAQ,QAAQ,CAAC,KAAK;wBACtB,QAAQ,QAAQ,CAAC,KAAK,IAAI;wBAC1B,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC;qBACtC,CAAC,IAAI,CAAC;aACR,CAAC,IAAI,CAAC;YAEP,eAAe;YACf,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAW;YACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC9E,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,+BAA+B,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC;QACpF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,OAAO;IACT;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;kDAE7B,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAa,iBAAiB,MAAM;4CAAC;;;;;;;;;;;;;0CAEtD,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGjB,8OAAC,yHAAA,CAAA,kBAAe;;4BAAC;4BACK,iBAAiB,MAAM;4BAAC;4BAAkB,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;0BAGxG,8OAAC,yHAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,cAAc,iBAAiB;;;;;;;0CAGlC,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;;kDAET,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAKvC,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,iBAAiB,kBAAkB;;;;;;;0CAItC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAM;gCAAkB,cAAc;;kDAC5C,8OAAC,2HAAA,CAAA,gBAAa;wCAAC,OAAO;kDACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI5C,8OAAC,2HAAA,CAAA,gBAAa;;0DACZ,8OAAC,2HAAA,CAAA,eAAY;;kEACX,8OAAC,2HAAA,CAAA,cAAW;kEAAC;;;;;;kEACb,8OAAC,2HAAA,CAAA,oBAAiB;;4DAAC;4DACM,iBAAiB,MAAM;4DAAC;4DAAkB,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;0DAG3G,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2HAAA,CAAA,SAAM;wDAAC,OAAO;wDAAW,eAAe;;0EACvC,8OAAC,2HAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kFACZ,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;kFAC3B,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAW;;;;;;kFAC7B,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;kFAChC,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;;;;;;;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;0EAGrE,8OAAC,2HAAA,CAAA,SAAM;gEAAC,SAAS;gEAAoB,UAAU,CAAC;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASnE,8OAAC,oIAAA,CAAA,cAAW;;kDACV,8OAAC,oIAAA,CAAA,qBAAkB;wCAAC,OAAO;kDACzB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIvC,8OAAC,oIAAA,CAAA,qBAAkB;;0DACjB,8OAAC,oIAAA,CAAA,oBAAiB;;kEAChB,8OAAC,oIAAA,CAAA,mBAAgB;wDAAC,WAAU;;0EAC1B,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAA6B;;;;;;;kEAGxD,8OAAC,oIAAA,CAAA,yBAAsB;;4DAAC;4DACW,iBAAiB,MAAM;4DAAC;4DAAS,iBAAiB,MAAM,KAAK,IAAI,MAAM;4DAAG;0EAE3G,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAIV,8OAAC,oIAAA,CAAA,oBAAiB;;kEAChB,8OAAC,oIAAA,CAAA,oBAAiB;kEAAC;;;;;;kEACnB,8OAAC,oIAAA,CAAA,oBAAiB;wDAChB,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;;oCACZ,oBAAoB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,wBACpC,8OAAC,0HAAA,CAAA,QAAK;4CAAkB,SAAQ;4CAAY,WAAU;;gDACnD,QAAQ,SAAS;gDAAC;gDAAE,QAAQ,QAAQ;;2CAD3B,QAAQ,EAAE;;;;;oCAIvB,oBAAoB,MAAM,GAAG,oBAC5B,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAAU;4CAC3C,oBAAoB,MAAM,GAAG;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 7351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/pagination.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\nimport { PaginationConfig } from \"@/lib/types/student\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PaginationProps {\n  pagination: PaginationConfig\n  onPaginationChange: (config: PaginationConfig) => void\n  className?: string\n}\n\nexport function Pagination({\n  pagination,\n  onPaginationChange,\n  className\n}: PaginationProps) {\n  const { page, pageSize, total } = pagination\n  \n  const totalPages = Math.ceil(total / pageSize)\n  const startItem = (page - 1) * pageSize + 1\n  const endItem = Math.min(page * pageSize, total)\n\n  const handlePageChange = (newPage: number) => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      onPaginationChange({ ...pagination, page: newPage })\n    }\n  }\n\n  const handlePageSizeChange = (newPageSize: string) => {\n    const size = parseInt(newPageSize)\n    const newTotalPages = Math.ceil(total / size)\n    const newPage = Math.min(page, newTotalPages)\n    \n    onPaginationChange({ \n      ...pagination, \n      pageSize: size, \n      page: Math.max(1, newPage)\n    })\n  }\n\n  const getVisiblePages = () => {\n    const delta = 2\n    const range = []\n    const rangeWithDots = []\n\n    for (let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++) {\n      range.push(i)\n    }\n\n    if (page - delta > 2) {\n      rangeWithDots.push(1, '...')\n    } else {\n      rangeWithDots.push(1)\n    }\n\n    rangeWithDots.push(...range)\n\n    if (page + delta < totalPages - 1) {\n      rangeWithDots.push('...', totalPages)\n    } else if (totalPages > 1) {\n      rangeWithDots.push(totalPages)\n    }\n\n    return rangeWithDots\n  }\n\n  if (total === 0) {\n    return null\n  }\n\n  return (\n    <div className={cn(\"flex flex-col sm:flex-row items-center justify-between gap-4\", className)}>\n      {/* Results Info */}\n      <div className=\"flex flex-col sm:flex-row items-center gap-2 text-sm text-muted-foreground\">\n        <span className=\"text-center sm:text-left\">\n          Showing {startItem} to {endItem} of {total} students\n        </span>\n\n        {/* Page Size Selector */}\n        <div className=\"flex items-center gap-2\">\n          <span className=\"hidden sm:inline\">Show</span>\n          <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>\n            <SelectTrigger className=\"w-16 h-8\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"10\">10</SelectItem>\n              <SelectItem value=\"20\">20</SelectItem>\n              <SelectItem value=\"50\">50</SelectItem>\n              <SelectItem value=\"100\">100</SelectItem>\n            </SelectContent>\n          </Select>\n          <span className=\"hidden sm:inline\">per page</span>\n        </div>\n      </div>\n\n      {/* Pagination Controls */}\n      <div className=\"flex items-center gap-1 sm:gap-2\">\n        {/* First Page - Hidden on mobile */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(1)}\n          disabled={page === 1}\n          className=\"hidden sm:flex h-8 w-8 p-0\"\n        >\n          <ChevronsLeft className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Previous Page */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(page - 1)}\n          disabled={page === 1}\n          className=\"h-8 w-8 p-0\"\n        >\n          <ChevronLeft className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Page Numbers - Responsive */}\n        <div className=\"flex items-center gap-1\">\n          {getVisiblePages().map((pageNum, index) => {\n            if (pageNum === '...') {\n              return (\n                <span key={`dots-${index}`} className=\"px-1 sm:px-2 text-muted-foreground text-sm\">\n                  ...\n                </span>\n              )\n            }\n\n            const pageNumber = pageNum as number\n            const isCurrentPage = pageNumber === page\n\n            return (\n              <Button\n                key={pageNumber}\n                variant={isCurrentPage ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => handlePageChange(pageNumber)}\n                className=\"h-8 w-8 p-0 text-sm\"\n              >\n                {pageNumber}\n              </Button>\n            )\n          })}\n        </div>\n\n        {/* Next Page */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(page + 1)}\n          disabled={page === totalPages}\n          className=\"h-8 w-8 p-0\"\n        >\n          <ChevronRight className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Last Page - Hidden on mobile */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(totalPages)}\n          disabled={page === totalPages}\n          className=\"hidden sm:flex h-8 w-8 p-0\"\n        >\n          <ChevronsRight className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n\n// Simplified pagination for mobile\nexport function SimplePagination({\n  pagination,\n  onPaginationChange,\n  className\n}: PaginationProps) {\n  const { page, pageSize, total } = pagination\n  const totalPages = Math.ceil(total / pageSize)\n\n  const handlePageChange = (newPage: number) => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      onPaginationChange({ ...pagination, page: newPage })\n    }\n  }\n\n  if (total === 0) {\n    return null\n  }\n\n  return (\n    <div className={cn(\"flex items-center justify-between\", className)}>\n      <div className=\"text-sm text-muted-foreground\">\n        Page {page} of {totalPages}\n      </div>\n      \n      <div className=\"flex items-center gap-2\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(page - 1)}\n          disabled={page === 1}\n        >\n          <ChevronLeft className=\"h-4 w-4 mr-1\" />\n          Previous\n        </Button>\n        \n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => handlePageChange(page + 1)}\n          disabled={page === totalPages}\n        >\n          Next\n          <ChevronRight className=\"h-4 w-4 ml-1\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAcO,SAAS,WAAW,EACzB,UAAU,EACV,kBAAkB,EAClB,SAAS,EACO;IAChB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAElC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IACrC,MAAM,YAAY,CAAC,OAAO,CAAC,IAAI,WAAW;IAC1C,MAAM,UAAU,KAAK,GAAG,CAAC,OAAO,UAAU;IAE1C,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,KAAK,WAAW,YAAY;YACzC,mBAAmB;gBAAE,GAAG,UAAU;gBAAE,MAAM;YAAQ;QACpD;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,SAAS;QACtB,MAAM,gBAAgB,KAAK,IAAI,CAAC,QAAQ;QACxC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM;QAE/B,mBAAmB;YACjB,GAAG,UAAU;YACb,UAAU;YACV,MAAM,KAAK,GAAG,CAAC,GAAG;QACpB;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ;QACd,MAAM,QAAQ,EAAE;QAChB,MAAM,gBAAgB,EAAE;QAExB,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,QAAQ,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO,QAAQ,IAAK;YACxF,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,OAAO,QAAQ,GAAG;YACpB,cAAc,IAAI,CAAC,GAAG;QACxB,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,cAAc,IAAI,IAAI;QAEtB,IAAI,OAAO,QAAQ,aAAa,GAAG;YACjC,cAAc,IAAI,CAAC,OAAO;QAC5B,OAAO,IAAI,aAAa,GAAG;YACzB,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;IACT;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;;0BAEjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;4BAA2B;4BAChC;4BAAU;4BAAK;4BAAQ;4BAAK;4BAAM;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO,SAAS,QAAQ;gCAAI,eAAe;;kDACjD,8OAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,2HAAA,CAAA,gBAAa;;0DACZ,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;;;;;;;;;;;;;0CAG5B,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;;;;;;;0BAKvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB;wBAChC,UAAU,SAAS;wBACnB,WAAU;kCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAI1B,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,OAAO;wBACvC,UAAU,SAAS;wBACnB,WAAU;kCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS;4BAC/B,IAAI,YAAY,OAAO;gCACrB,qBACE,8OAAC;oCAA2B,WAAU;8CAA6C;mCAAxE,CAAC,KAAK,EAAE,OAAO;;;;;4BAI9B;4BAEA,MAAM,aAAa;4BACnB,MAAM,gBAAgB,eAAe;4BAErC,qBACE,8OAAC,2HAAA,CAAA,SAAM;gCAEL,SAAS,gBAAgB,YAAY;gCACrC,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CAET;+BANI;;;;;wBASX;;;;;;kCAIF,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,OAAO;wBACvC,UAAU,SAAS;wBACnB,WAAU;kCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAI1B,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB;wBAChC,UAAU,SAAS;wBACnB,WAAU;kCAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKnC;AAGO,SAAS,iBAAiB,EAC/B,UAAU,EACV,kBAAkB,EAClB,SAAS,EACO;IAChB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAClC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IAErC,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,KAAK,WAAW,YAAY;YACzC,mBAAmB;gBAAE,GAAG,UAAU;gBAAE,MAAM;YAAQ;QACpD;IACF;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;;0BACtD,8OAAC;gBAAI,WAAU;;oBAAgC;oBACvC;oBAAK;oBAAK;;;;;;;0BAGlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,OAAO;wBACvC,UAAU,SAAS;;0CAEnB,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,OAAO;wBACvC,UAAU,SAAS;;4BACpB;0CAEC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 7740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/students/csv-import-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef } from \"react\"\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { \n  Upload, \n  Download, \n  FileText, \n  CheckCircle, \n  XCircle, \n  AlertTriangle,\n  Users,\n  FileSpreadsheet\n} from \"lucide-react\"\nimport { Student } from \"@/lib/types/student\"\nimport { CSVImportResult } from \"@/lib/types/student\"\nimport { toast } from \"sonner\"\n\ninterface CSVImportDialogProps {\n  trigger?: React.ReactNode\n  onStudentsImported?: (students: Student[]) => void\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}\n\ninterface ImportStep {\n  id: string\n  title: string\n  description: string\n  completed: boolean\n}\n\nexport function CSVImportDialog({\n  trigger,\n  onStudentsImported,\n  open,\n  onOpenChange\n}: CSVImportDialogProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [currentStep, setCurrentStep] = useState(0)\n  const [isProcessing, setIsProcessing] = useState(false)\n  const [importResult, setImportResult] = useState<CSVImportResult | null>(null)\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [previewData, setPreviewData] = useState<any[]>([])\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const steps: ImportStep[] = [\n    {\n      id: 'upload',\n      title: 'Upload CSV File',\n      description: 'Select and upload your student data CSV file',\n      completed: false\n    },\n    {\n      id: 'preview',\n      title: 'Preview Data',\n      description: 'Review the data before importing',\n      completed: false\n    },\n    {\n      id: 'import',\n      title: 'Import Students',\n      description: 'Process and import student records',\n      completed: false\n    },\n    {\n      id: 'complete',\n      title: 'Complete',\n      description: 'Import completed successfully',\n      completed: false\n    }\n  ]\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (onOpenChange) {\n      onOpenChange(newOpen)\n    } else {\n      setIsOpen(newOpen)\n    }\n    \n    if (!newOpen) {\n      // Reset state when dialog closes\n      setCurrentStep(0)\n      setSelectedFile(null)\n      setPreviewData([])\n      setImportResult(null)\n      setIsProcessing(false)\n    }\n  }\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file && file.type === 'text/csv') {\n      setSelectedFile(file)\n      parseCSVPreview(file)\n    } else {\n      toast.error('Please select a valid CSV file')\n    }\n  }\n\n  const parseCSVPreview = async (file: File) => {\n    try {\n      const text = await file.text()\n      const lines = text.split('\\n').filter(line => line.trim())\n      const headers = lines[0].split(',').map(h => h.trim().replace(/\"/g, ''))\n      \n      // Parse first 5 rows for preview\n      const preview = lines.slice(1, 6).map(line => {\n        const values = line.split(',').map(v => v.trim().replace(/\"/g, ''))\n        const row: any = {}\n        headers.forEach((header, index) => {\n          row[header] = values[index] || ''\n        })\n        return row\n      })\n      \n      setPreviewData(preview)\n      setCurrentStep(1)\n    } catch (error) {\n      toast.error('Failed to parse CSV file')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!selectedFile) return\n\n    setIsProcessing(true)\n    setCurrentStep(2)\n\n    try {\n      // Simulate import process\n      await new Promise(resolve => setTimeout(resolve, 2000))\n\n      // Mock import result\n      const mockResult: CSVImportResult = {\n        success: true,\n        imported: previewData.length,\n        failed: 0,\n        errors: []\n      }\n\n      // Create mock students from preview data\n      const importedStudents: Student[] = previewData.map((row, index) => ({\n        id: row.id || `IMP${Date.now()}${index}`,\n        firstName: row.firstName || row['First Name'] || 'Unknown',\n        middleName: row.middleName || row['Middle Name'],\n        lastName: row.lastName || row['Last Name'] || 'Student',\n        email: row.email || row['Email'] || `student${index}@tanauan.edu.ph`,\n        course: row.course || row['Course'] || 'General',\n        year: row.year || row['Year'] || '1st Year',\n        section: row.section || row['Section'],\n        grade: (row.grade || row['Grade'] || '7') as any,\n        status: 'Active' as any,\n        guardian: {\n          name: row.guardianName || row['Guardian Name'] || 'Unknown Guardian',\n          phone: row.guardianPhone || row['Guardian Phone'] || '09000000000',\n          email: row.guardianEmail || row['Guardian Email'],\n          relationship: (row.guardianRelationship || row['Guardian Relationship'] || 'Guardian') as any,\n          address: row.guardianAddress || row['Guardian Address']\n        },\n        emergencyContacts: [],\n        address: {\n          street: row.street || row['Street'] || 'Unknown Street',\n          barangay: row.barangay || row['Barangay'] || 'Unknown Barangay',\n          city: row.city || row['City'] || 'Tanauan City',\n          province: row.province || row['Province'] || 'Batangas',\n          zipCode: row.zipCode || row['ZIP Code'] || '4232',\n          country: 'Philippines'\n        },\n        enrollmentDate: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString(),\n        qrCode: `QR_${row.id || `IMP${Date.now()}${index}`}_2025`\n      }))\n\n      setImportResult(mockResult)\n      setCurrentStep(3)\n\n      if (onStudentsImported) {\n        onStudentsImported(importedStudents)\n      }\n\n      toast.success(`Successfully imported ${mockResult.imported} students`)\n    } catch (error) {\n      toast.error('Failed to import students')\n      setImportResult({\n        success: false,\n        imported: 0,\n        failed: previewData.length,\n        errors: [{ row: 1, field: 'general', message: 'Import failed' }]\n      })\n    } finally {\n      setIsProcessing(false)\n    }\n  }\n\n  const downloadTemplate = () => {\n    const template = `id,firstName,middleName,lastName,email,grade,section,course,year,guardianName,guardianPhone,guardianEmail,guardianRelationship,street,barangay,city,province,zipCode\n123456789001,John,Michael,Doe,<EMAIL>,11,A,Information Technology,3rd Year,Robert Doe,09123456789,<EMAIL>,Father,123 Main St,Poblacion,Tanauan City,Batangas,4232\n123456789002,Jane,Marie,Smith,<EMAIL>,10,B,Computer Science,2nd Year,Patricia Smith,09123456790,<EMAIL>,Mother,456 Oak Ave,San Jose,Tanauan City,Batangas,4232`\n\n    const blob = new Blob([template], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = 'student_import_template.csv'\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  const dialogOpen = open !== undefined ? open : isOpen\n\n  return (\n    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>\n      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}\n      \n      {!trigger && (\n        <DialogTrigger asChild>\n          <Button variant=\"outline\">\n            <Upload className=\"mr-2 h-4 w-4\" />\n            Import CSV\n          </Button>\n        </DialogTrigger>\n      )}\n\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileSpreadsheet className=\"h-5 w-5\" />\n            Import Students from CSV\n          </DialogTitle>\n          <DialogDescription>\n            Upload a CSV file to import multiple student records at once\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Progress Steps */}\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${\n                  index <= currentStep \n                    ? 'border-primary bg-primary text-primary-foreground' \n                    : 'border-muted-foreground bg-background'\n                }`}>\n                  {index < currentStep ? (\n                    <CheckCircle className=\"h-4 w-4\" />\n                  ) : (\n                    <span className=\"text-sm\">{index + 1}</span>\n                  )}\n                </div>\n                {index < steps.length - 1 && (\n                  <div className={`w-12 h-0.5 mx-2 transition-colors ${\n                    index < currentStep ? 'bg-primary' : 'bg-muted'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Step Content */}\n          {currentStep === 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Upload CSV File</CardTitle>\n                <CardDescription>\n                  Select a CSV file containing student data to import\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center\">\n                  <FileText className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n                  <div className=\"space-y-2\">\n                    <p className=\"text-lg font-medium\">Drop your CSV file here</p>\n                    <p className=\"text-sm text-muted-foreground\">or click to browse</p>\n                  </div>\n                  <input\n                    ref={fileInputRef}\n                    type=\"file\"\n                    accept=\".csv\"\n                    onChange={handleFileSelect}\n                    className=\"hidden\"\n                  />\n                  <Button \n                    onClick={() => fileInputRef.current?.click()}\n                    className=\"mt-4\"\n                  >\n                    <Upload className=\"mr-2 h-4 w-4\" />\n                    Select CSV File\n                  </Button>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">\n                    Need a template? Download our sample CSV file\n                  </div>\n                  <Button variant=\"outline\" size=\"sm\" onClick={downloadTemplate}>\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Download Template\n                  </Button>\n                </div>\n\n                {selectedFile && (\n                  <Alert>\n                    <CheckCircle className=\"h-4 w-4\" />\n                    <AlertDescription>\n                      File selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)\n                    </AlertDescription>\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {currentStep === 1 && previewData.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Preview Data</CardTitle>\n                <CardDescription>\n                  Review the first few rows of your data before importing\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"w-full border-collapse border border-border\">\n                    <thead>\n                      <tr className=\"bg-muted\">\n                        {Object.keys(previewData[0]).map(header => (\n                          <th key={header} className=\"border border-border p-2 text-left text-sm font-medium\">\n                            {header}\n                          </th>\n                        ))}\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {previewData.map((row, index) => (\n                        <tr key={index}>\n                          {Object.values(row).map((value: any, cellIndex) => (\n                            <td key={cellIndex} className=\"border border-border p-2 text-sm\">\n                              {value || '-'}\n                            </td>\n                          ))}\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n                \n                <div className=\"flex justify-between mt-4\">\n                  <Button variant=\"outline\" onClick={() => setCurrentStep(0)}>\n                    Back\n                  </Button>\n                  <Button onClick={handleImport}>\n                    <Users className=\"mr-2 h-4 w-4\" />\n                    Import {previewData.length} Students\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {currentStep === 2 && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Importing Students</CardTitle>\n                <CardDescription>\n                  Please wait while we process your student data\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"text-center py-8\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n                  <p className=\"text-lg font-medium\">Processing student records...</p>\n                  <p className=\"text-sm text-muted-foreground\">This may take a few moments</p>\n                </div>\n                <Progress value={isProcessing ? 50 : 100} className=\"w-full\" />\n              </CardContent>\n            </Card>\n          )}\n\n          {currentStep === 3 && importResult && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  {importResult.success ? (\n                    <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                  ) : (\n                    <XCircle className=\"h-5 w-5 text-red-600\" />\n                  )}\n                  Import {importResult.success ? 'Completed' : 'Failed'}\n                </CardTitle>\n                <CardDescription>\n                  {importResult.success \n                    ? 'Your student data has been successfully imported'\n                    : 'There were errors during the import process'\n                  }\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-600\">{importResult.imported}</div>\n                    <div className=\"text-sm text-muted-foreground\">Imported</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-red-600\">{importResult.failed}</div>\n                    <div className=\"text-sm text-muted-foreground\">Failed</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">{importResult.imported + importResult.failed}</div>\n                    <div className=\"text-sm text-muted-foreground\">Total</div>\n                  </div>\n                </div>\n\n                {importResult.errors.length > 0 && (\n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-medium text-destructive\">Errors:</h4>\n                    {importResult.errors.map((error, index) => (\n                      <Alert key={index} variant=\"destructive\">\n                        <AlertTriangle className=\"h-4 w-4\" />\n                        <AlertDescription>\n                          Row {error.row}, Field \"{error.field}\": {error.message}\n                        </AlertDescription>\n                      </Alert>\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"flex justify-end\">\n                  <Button onClick={() => handleOpenChange(false)}>\n                    Close\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAtBA;;;;;;;;;;AAsCO,SAAS,gBAAgB,EAC9B,OAAO,EACP,kBAAkB,EAClB,IAAI,EACJ,YAAY,EACS;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,QAAsB;QAC1B;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,aAAa;QACf,OAAO;YACL,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS;YACZ,iCAAiC;YACjC,eAAe;YACf,gBAAgB;YAChB,eAAe,EAAE;YACjB,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,QAAQ,KAAK,IAAI,KAAK,YAAY;YACpC,gBAAgB;YAChB,gBAAgB;QAClB,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;YACvD,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM;YAEpE,iCAAiC;YACjC,MAAM,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;gBACpC,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM;gBAC/D,MAAM,MAAW,CAAC;gBAClB,QAAQ,OAAO,CAAC,CAAC,QAAQ;oBACvB,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI;gBACjC;gBACA,OAAO;YACT;YAEA,eAAe;YACf,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;QAEnB,gBAAgB;QAChB,eAAe;QAEf,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,qBAAqB;YACrB,MAAM,aAA8B;gBAClC,SAAS;gBACT,UAAU,YAAY,MAAM;gBAC5B,QAAQ;gBACR,QAAQ,EAAE;YACZ;YAEA,yCAAyC;YACzC,MAAM,mBAA8B,YAAY,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;oBACnE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,OAAO;oBACxC,WAAW,IAAI,SAAS,IAAI,GAAG,CAAC,aAAa,IAAI;oBACjD,YAAY,IAAI,UAAU,IAAI,GAAG,CAAC,cAAc;oBAChD,UAAU,IAAI,QAAQ,IAAI,GAAG,CAAC,YAAY,IAAI;oBAC9C,OAAO,IAAI,KAAK,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,EAAE,MAAM,eAAe,CAAC;oBACpE,QAAQ,IAAI,MAAM,IAAI,GAAG,CAAC,SAAS,IAAI;oBACvC,MAAM,IAAI,IAAI,IAAI,GAAG,CAAC,OAAO,IAAI;oBACjC,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC,UAAU;oBACtC,OAAQ,IAAI,KAAK,IAAI,GAAG,CAAC,QAAQ,IAAI;oBACrC,QAAQ;oBACR,UAAU;wBACR,MAAM,IAAI,YAAY,IAAI,GAAG,CAAC,gBAAgB,IAAI;wBAClD,OAAO,IAAI,aAAa,IAAI,GAAG,CAAC,iBAAiB,IAAI;wBACrD,OAAO,IAAI,aAAa,IAAI,GAAG,CAAC,iBAAiB;wBACjD,cAAe,IAAI,oBAAoB,IAAI,GAAG,CAAC,wBAAwB,IAAI;wBAC3E,SAAS,IAAI,eAAe,IAAI,GAAG,CAAC,mBAAmB;oBACzD;oBACA,mBAAmB,EAAE;oBACrB,SAAS;wBACP,QAAQ,IAAI,MAAM,IAAI,GAAG,CAAC,SAAS,IAAI;wBACvC,UAAU,IAAI,QAAQ,IAAI,GAAG,CAAC,WAAW,IAAI;wBAC7C,MAAM,IAAI,IAAI,IAAI,GAAG,CAAC,OAAO,IAAI;wBACjC,UAAU,IAAI,QAAQ,IAAI,GAAG,CAAC,WAAW,IAAI;wBAC7C,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC,WAAW,IAAI;wBAC3C,SAAS;oBACX;oBACA,gBAAgB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACtD,aAAa,IAAI,OAAO,WAAW;oBACnC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,OAAO,CAAC,KAAK,CAAC;gBAC3D,CAAC;YAED,gBAAgB;YAChB,eAAe;YAEf,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,WAAW,QAAQ,CAAC,SAAS,CAAC;QACvE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;gBACd,SAAS;gBACT,UAAU;gBACV,QAAQ,YAAY,MAAM;gBAC1B,QAAQ;oBAAC;wBAAE,KAAK;wBAAG,OAAO;wBAAW,SAAS;oBAAgB;iBAAE;YAClE;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,CAAC;;yMAEmL,CAAC;QAEtM,MAAM,OAAO,IAAI,KAAK;YAAC;SAAS,EAAE;YAAE,MAAM;QAAW;QACrD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,aAAa,SAAS,YAAY,OAAO;IAE/C,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAY,cAAc;;YACrC,yBAAW,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BAAE;;;;;;YAEnC,CAAC,yBACA,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;;sCACd,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAMzC,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,2HAAA,CAAA,eAAY;;0CACX,8OAAC,2HAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGzC,8OAAC,2HAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAKrB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC;gDAAI,WAAW,CAAC,iFAAiF,EAChG,SAAS,cACL,sDACA,yCACJ;0DACC,QAAQ,4BACP,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC;oDAAK,WAAU;8DAAW,QAAQ;;;;;;;;;;;4CAGtC,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAW,CAAC,kCAAkC,EACjD,QAAQ,cAAc,eAAe,YACrC;;;;;;;uCAfI,KAAK,EAAE;;;;;;;;;;4BAsBpB,gBAAgB,mBACf,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;;0DACT,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,8OAAC;wDACC,KAAK;wDACL,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC,2HAAA,CAAA,SAAM;wDACL,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,WAAU;;0EAEV,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAKvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;kEAG/C,8OAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,SAAS;;0EAC3C,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;4CAKxC,8BACC,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC,0HAAA,CAAA,mBAAgB;;4DAAC;4DACA,aAAa,IAAI;4DAAC;4DAAG,CAAC,aAAa,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;4BAQtF,gBAAgB,KAAK,YAAY,MAAM,GAAG,mBACzC,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;;0DACT,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,yHAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;sEACC,cAAA,8OAAC;gEAAG,WAAU;0EACX,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA,uBAC/B,8OAAC;wEAAgB,WAAU;kFACxB;uEADM;;;;;;;;;;;;;;;sEAMf,8OAAC;sEACE,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC;8EACE,OAAO,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,OAAY,0BACnC,8OAAC;4EAAmB,WAAU;sFAC3B,SAAS;2EADH;;;;;mEAFJ;;;;;;;;;;;;;;;;;;;;;0DAYjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,SAAS,IAAM,eAAe;kEAAI;;;;;;kEAG5D,8OAAC,2HAAA,CAAA,SAAM;wDAAC,SAAS;;0EACf,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;4DAC1B,YAAY,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAOpC,gBAAgB,mBACf,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;;0DACT,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,OAAO,eAAe,KAAK;gDAAK,WAAU;;;;;;;;;;;;;;;;;;4BAKzD,gBAAgB,KAAK,8BACpB,8OAAC,yHAAA,CAAA,OAAI;;kDACH,8OAAC,yHAAA,CAAA,aAAU;;0DACT,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;oDAClB,aAAa,OAAO,iBACnB,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDACnB;oDACM,aAAa,OAAO,GAAG,cAAc;;;;;;;0DAE/C,8OAAC,yHAAA,CAAA,kBAAe;0DACb,aAAa,OAAO,GACjB,qDACA;;;;;;;;;;;;kDAIR,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAqC,aAAa,QAAQ;;;;;;0EACzE,8OAAC;gEAAI,WAAU;0EAAgC;;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAmC,aAAa,MAAM;;;;;;0EACrE,8OAAC;gEAAI,WAAU;0EAAgC;;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAsB,aAAa,QAAQ,GAAG,aAAa,MAAM;;;;;;0EAChF,8OAAC;gEAAI,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;4CAIlD,aAAa,MAAM,CAAC,MAAM,GAAG,mBAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;oDAC5C,aAAa,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,8OAAC,0HAAA,CAAA,QAAK;4DAAa,SAAQ;;8EACzB,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EACzB,8OAAC,0HAAA,CAAA,mBAAgB;;wEAAC;wEACX,MAAM,GAAG;wEAAC;wEAAU,MAAM,KAAK;wEAAC;wEAAI,MAAM,OAAO;;;;;;;;2DAH9C;;;;;;;;;;;0DAUlB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAS,IAAM,iBAAiB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlE", "debugId": null}}]}