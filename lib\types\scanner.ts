// Student related types
export interface Guardian {
  name: string
  phone: string
  email?: string
  relationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  address?: string
}

export interface EmergencyContact {
  name: string
  phone: string
  relationship: string
  address?: string
}

export interface Address {
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
}

export interface Student {
  id: string // DepEd ID format
  firstName: string
  middleName?: string
  lastName: string
  email: string
  course: string
  year: string
  section?: string
  grade: '7' | '8' | '9' | '10' | '11' | '12'
  status: 'Active' | 'Inactive' | 'Transferred' | 'Graduated'
  photo?: string
  qrCode?: string
  dateOfBirth?: string
  gender?: 'Male' | 'Female'
  guardian: Guardian
  emergencyContacts: EmergencyContact[]
  address: Address
  enrollmentDate: string
  lastUpdated: string
  attendanceStats?: {
    totalDays: number
    presentDays: number
    lateDays: number
    absentDays: number
    attendanceRate: number
  }
}

// Legacy support - computed property
export interface StudentWithName extends Omit<Student, 'name'> {
  name: string // computed from firstName, middleName, lastName
}

// Attendance related types
export interface AttendanceRecord {
  id: string
  studentId: string
  studentName: string
  course: string
  checkIn?: string
  checkOut?: string
  date: string
  status: 'Present' | 'Late' | 'Absent'
  type: 'gate' | 'subject'
  subject?: string
  period?: string
  timestamp: Date
}

// Scanner specific types
export interface ScanResult {
  success: boolean
  data?: string
  student?: Student
  error?: string
  timestamp: Date
}

export interface ScanSession {
  id: string
  startTime: Date
  endTime?: Date
  totalScans: number
  successfulScans: number
  failedScans: number
  mode: ScanMode
  subject?: string
  period?: string
}

export type ScanMode = 'gate' | 'subject' | 'batch'

export type AttendanceAction = 'check-in' | 'check-out' | 'present' | 'late' | 'absent'

export interface ScannerSettings {
  cameraDeviceId?: string
  audioEnabled: boolean
  vibrationEnabled: boolean
  scanDelay: number // milliseconds between scans
  autoAdvance: boolean // auto advance to next student in batch mode
  offlineMode: boolean
  syncInterval: number // minutes
}

// Camera and device types
export interface CameraDevice {
  deviceId: string
  label: string
  kind: 'videoinput'
}

export interface ScannerState {
  isScanning: boolean
  isLoading: boolean
  currentStudent?: Student
  lastScanResult?: ScanResult
  scanHistory: ScanResult[]
  settings: ScannerSettings
  availableCameras: CameraDevice[]
  selectedCamera?: CameraDevice
  isOnline: boolean
  pendingScans: AttendanceRecord[]
}

// Manual entry types
export interface ManualEntry {
  studentId: string
  action: AttendanceAction
  timestamp: Date
  reason?: string
}

// Batch scanning types
export interface BatchScanItem {
  student: Student
  scanned: boolean
  timestamp?: Date
  status?: AttendanceAction
}

export interface BatchSession {
  id: string
  name: string
  students: BatchScanItem[]
  subject?: string
  period?: string
  startTime: Date
  completedCount: number
  totalCount: number
}

// Notification types
export interface ScanNotification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: Date
  duration?: number // auto-dismiss after milliseconds
  action?: {
    label: string
    handler: () => void
  }
}

// Offline sync types
export interface OfflineQueueItem {
  id: string
  type: 'attendance' | 'scan'
  data: AttendanceRecord | ScanResult
  timestamp: Date
  retryCount: number
  lastRetry?: Date
}

export interface SyncStatus {
  isOnline: boolean
  lastSync?: Date
  pendingItems: number
  isSyncing: boolean
  syncError?: string
}

// Subject and period types
export interface Subject {
  id: string
  name: string
  code: string
  instructor?: string
  schedule?: {
    day: string
    startTime: string
    endTime: string
  }[]
}

export interface TimePeriod {
  id: string
  name: string
  startTime: string
  endTime: string
  type: 'morning' | 'afternoon' | 'evening'
}

// API response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface StudentLookupResponse extends ApiResponse<Student> {}
export interface AttendanceResponse extends ApiResponse<AttendanceRecord> {}
export interface ScanResponse extends ApiResponse<ScanResult> {}
