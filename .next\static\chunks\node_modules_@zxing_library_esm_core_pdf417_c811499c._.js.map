{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/PDF417Common.js"], "sourcesContent": ["/*\n* Copyright 2009 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417;\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import java.util.Arrays;\nimport Arrays from '../util/Arrays';\n// import java.util.Collection;\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../common/detector/MathUtils';\n/**\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> Grau\n */\nvar PDF417Common = /** @class */ (function () {\n    function PDF417Common() {\n    }\n    PDF417Common.prototype.PDF417Common = function () {\n    };\n    /**\n     * @param moduleBitCount values to sum\n     * @return sum of values\n     * @deprecated call {@link MathUtils#sum(int[])}\n     */\n    // @Deprecated\n    PDF417Common.getBitCountSum = function (moduleBitCount) {\n        return MathUtils.sum(moduleBitCount);\n    };\n    PDF417Common.toIntArray = function (list) {\n        var e_1, _a;\n        if (list == null || !list.length) {\n            return PDF417Common.EMPTY_INT_ARRAY;\n        }\n        var result = new Int32Array(list.length);\n        var i = 0;\n        try {\n            for (var list_1 = __values(list), list_1_1 = list_1.next(); !list_1_1.done; list_1_1 = list_1.next()) {\n                var integer = list_1_1.value;\n                result[i++] = integer;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (list_1_1 && !list_1_1.done && (_a = list_1.return)) _a.call(list_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return result;\n    };\n    /**\n     * @param symbol encoded symbol to translate to a codeword\n     * @return the codeword corresponding to the symbol.\n     */\n    PDF417Common.getCodeword = function (symbol /*int*/) {\n        var i = Arrays.binarySearch(PDF417Common.SYMBOL_TABLE, symbol & 0x3FFFF);\n        if (i < 0) {\n            return -1;\n        }\n        return (PDF417Common.CODEWORD_TABLE[i] - 1) % PDF417Common.NUMBER_OF_CODEWORDS;\n    };\n    PDF417Common.NUMBER_OF_CODEWORDS = 929;\n    // Maximum Codewords (Data + Error).\n    PDF417Common.MAX_CODEWORDS_IN_BARCODE = PDF417Common.NUMBER_OF_CODEWORDS - 1;\n    PDF417Common.MIN_ROWS_IN_BARCODE = 3;\n    PDF417Common.MAX_ROWS_IN_BARCODE = 90;\n    // One left row indication column + max 30 data columns + one right row indicator column\n    // public static /*final*/ MAX_CODEWORDS_IN_ROW: /*int*/ number = 32;\n    PDF417Common.MODULES_IN_CODEWORD = 17;\n    PDF417Common.MODULES_IN_STOP_PATTERN = 18;\n    PDF417Common.BARS_IN_MODULE = 8;\n    PDF417Common.EMPTY_INT_ARRAY = new Int32Array([]);\n    /**\n     * The sorted table of all possible symbols. Extracted from the PDF417\n     * specification. The index of a symbol in this table corresponds to the\n     * index into the codeword table.\n     */\n    PDF417Common.SYMBOL_TABLE = Int32Array.from([\n        0x1025e, 0x1027a, 0x1029e, 0x102bc, 0x102f2, 0x102f4, 0x1032e, 0x1034e, 0x1035c, 0x10396, 0x103a6, 0x103ac,\n        0x10422, 0x10428, 0x10436, 0x10442, 0x10444, 0x10448, 0x10450, 0x1045e, 0x10466, 0x1046c, 0x1047a, 0x10482,\n        0x1049e, 0x104a0, 0x104bc, 0x104c6, 0x104d8, 0x104ee, 0x104f2, 0x104f4, 0x10504, 0x10508, 0x10510, 0x1051e,\n        0x10520, 0x1053c, 0x10540, 0x10578, 0x10586, 0x1058c, 0x10598, 0x105b0, 0x105be, 0x105ce, 0x105dc, 0x105e2,\n        0x105e4, 0x105e8, 0x105f6, 0x1062e, 0x1064e, 0x1065c, 0x1068e, 0x1069c, 0x106b8, 0x106de, 0x106fa, 0x10716,\n        0x10726, 0x1072c, 0x10746, 0x1074c, 0x10758, 0x1076e, 0x10792, 0x10794, 0x107a2, 0x107a4, 0x107a8, 0x107b6,\n        0x10822, 0x10828, 0x10842, 0x10848, 0x10850, 0x1085e, 0x10866, 0x1086c, 0x1087a, 0x10882, 0x10884, 0x10890,\n        0x1089e, 0x108a0, 0x108bc, 0x108c6, 0x108cc, 0x108d8, 0x108ee, 0x108f2, 0x108f4, 0x10902, 0x10908, 0x1091e,\n        0x10920, 0x1093c, 0x10940, 0x10978, 0x10986, 0x10998, 0x109b0, 0x109be, 0x109ce, 0x109dc, 0x109e2, 0x109e4,\n        0x109e8, 0x109f6, 0x10a08, 0x10a10, 0x10a1e, 0x10a20, 0x10a3c, 0x10a40, 0x10a78, 0x10af0, 0x10b06, 0x10b0c,\n        0x10b18, 0x10b30, 0x10b3e, 0x10b60, 0x10b7c, 0x10b8e, 0x10b9c, 0x10bb8, 0x10bc2, 0x10bc4, 0x10bc8, 0x10bd0,\n        0x10bde, 0x10be6, 0x10bec, 0x10c2e, 0x10c4e, 0x10c5c, 0x10c62, 0x10c64, 0x10c68, 0x10c76, 0x10c8e, 0x10c9c,\n        0x10cb8, 0x10cc2, 0x10cc4, 0x10cc8, 0x10cd0, 0x10cde, 0x10ce6, 0x10cec, 0x10cfa, 0x10d0e, 0x10d1c, 0x10d38,\n        0x10d70, 0x10d7e, 0x10d82, 0x10d84, 0x10d88, 0x10d90, 0x10d9e, 0x10da0, 0x10dbc, 0x10dc6, 0x10dcc, 0x10dd8,\n        0x10dee, 0x10df2, 0x10df4, 0x10e16, 0x10e26, 0x10e2c, 0x10e46, 0x10e58, 0x10e6e, 0x10e86, 0x10e8c, 0x10e98,\n        0x10eb0, 0x10ebe, 0x10ece, 0x10edc, 0x10f0a, 0x10f12, 0x10f14, 0x10f22, 0x10f28, 0x10f36, 0x10f42, 0x10f44,\n        0x10f48, 0x10f50, 0x10f5e, 0x10f66, 0x10f6c, 0x10fb2, 0x10fb4, 0x11022, 0x11028, 0x11042, 0x11048, 0x11050,\n        0x1105e, 0x1107a, 0x11082, 0x11084, 0x11090, 0x1109e, 0x110a0, 0x110bc, 0x110c6, 0x110cc, 0x110d8, 0x110ee,\n        0x110f2, 0x110f4, 0x11102, 0x1111e, 0x11120, 0x1113c, 0x11140, 0x11178, 0x11186, 0x11198, 0x111b0, 0x111be,\n        0x111ce, 0x111dc, 0x111e2, 0x111e4, 0x111e8, 0x111f6, 0x11208, 0x1121e, 0x11220, 0x11278, 0x112f0, 0x1130c,\n        0x11330, 0x1133e, 0x11360, 0x1137c, 0x1138e, 0x1139c, 0x113b8, 0x113c2, 0x113c8, 0x113d0, 0x113de, 0x113e6,\n        0x113ec, 0x11408, 0x11410, 0x1141e, 0x11420, 0x1143c, 0x11440, 0x11478, 0x114f0, 0x115e0, 0x1160c, 0x11618,\n        0x11630, 0x1163e, 0x11660, 0x1167c, 0x116c0, 0x116f8, 0x1171c, 0x11738, 0x11770, 0x1177e, 0x11782, 0x11784,\n        0x11788, 0x11790, 0x1179e, 0x117a0, 0x117bc, 0x117c6, 0x117cc, 0x117d8, 0x117ee, 0x1182e, 0x11834, 0x1184e,\n        0x1185c, 0x11862, 0x11864, 0x11868, 0x11876, 0x1188e, 0x1189c, 0x118b8, 0x118c2, 0x118c8, 0x118d0, 0x118de,\n        0x118e6, 0x118ec, 0x118fa, 0x1190e, 0x1191c, 0x11938, 0x11970, 0x1197e, 0x11982, 0x11984, 0x11990, 0x1199e,\n        0x119a0, 0x119bc, 0x119c6, 0x119cc, 0x119d8, 0x119ee, 0x119f2, 0x119f4, 0x11a0e, 0x11a1c, 0x11a38, 0x11a70,\n        0x11a7e, 0x11ae0, 0x11afc, 0x11b08, 0x11b10, 0x11b1e, 0x11b20, 0x11b3c, 0x11b40, 0x11b78, 0x11b8c, 0x11b98,\n        0x11bb0, 0x11bbe, 0x11bce, 0x11bdc, 0x11be2, 0x11be4, 0x11be8, 0x11bf6, 0x11c16, 0x11c26, 0x11c2c, 0x11c46,\n        0x11c4c, 0x11c58, 0x11c6e, 0x11c86, 0x11c98, 0x11cb0, 0x11cbe, 0x11cce, 0x11cdc, 0x11ce2, 0x11ce4, 0x11ce8,\n        0x11cf6, 0x11d06, 0x11d0c, 0x11d18, 0x11d30, 0x11d3e, 0x11d60, 0x11d7c, 0x11d8e, 0x11d9c, 0x11db8, 0x11dc4,\n        0x11dc8, 0x11dd0, 0x11dde, 0x11de6, 0x11dec, 0x11dfa, 0x11e0a, 0x11e12, 0x11e14, 0x11e22, 0x11e24, 0x11e28,\n        0x11e36, 0x11e42, 0x11e44, 0x11e50, 0x11e5e, 0x11e66, 0x11e6c, 0x11e82, 0x11e84, 0x11e88, 0x11e90, 0x11e9e,\n        0x11ea0, 0x11ebc, 0x11ec6, 0x11ecc, 0x11ed8, 0x11eee, 0x11f1a, 0x11f2e, 0x11f32, 0x11f34, 0x11f4e, 0x11f5c,\n        0x11f62, 0x11f64, 0x11f68, 0x11f76, 0x12048, 0x1205e, 0x12082, 0x12084, 0x12090, 0x1209e, 0x120a0, 0x120bc,\n        0x120d8, 0x120f2, 0x120f4, 0x12108, 0x1211e, 0x12120, 0x1213c, 0x12140, 0x12178, 0x12186, 0x12198, 0x121b0,\n        0x121be, 0x121e2, 0x121e4, 0x121e8, 0x121f6, 0x12204, 0x12210, 0x1221e, 0x12220, 0x12278, 0x122f0, 0x12306,\n        0x1230c, 0x12330, 0x1233e, 0x12360, 0x1237c, 0x1238e, 0x1239c, 0x123b8, 0x123c2, 0x123c8, 0x123d0, 0x123e6,\n        0x123ec, 0x1241e, 0x12420, 0x1243c, 0x124f0, 0x125e0, 0x12618, 0x1263e, 0x12660, 0x1267c, 0x126c0, 0x126f8,\n        0x12738, 0x12770, 0x1277e, 0x12782, 0x12784, 0x12790, 0x1279e, 0x127a0, 0x127bc, 0x127c6, 0x127cc, 0x127d8,\n        0x127ee, 0x12820, 0x1283c, 0x12840, 0x12878, 0x128f0, 0x129e0, 0x12bc0, 0x12c18, 0x12c30, 0x12c3e, 0x12c60,\n        0x12c7c, 0x12cc0, 0x12cf8, 0x12df0, 0x12e1c, 0x12e38, 0x12e70, 0x12e7e, 0x12ee0, 0x12efc, 0x12f04, 0x12f08,\n        0x12f10, 0x12f20, 0x12f3c, 0x12f40, 0x12f78, 0x12f86, 0x12f8c, 0x12f98, 0x12fb0, 0x12fbe, 0x12fce, 0x12fdc,\n        0x1302e, 0x1304e, 0x1305c, 0x13062, 0x13068, 0x1308e, 0x1309c, 0x130b8, 0x130c2, 0x130c8, 0x130d0, 0x130de,\n        0x130ec, 0x130fa, 0x1310e, 0x13138, 0x13170, 0x1317e, 0x13182, 0x13184, 0x13190, 0x1319e, 0x131a0, 0x131bc,\n        0x131c6, 0x131cc, 0x131d8, 0x131f2, 0x131f4, 0x1320e, 0x1321c, 0x13270, 0x1327e, 0x132e0, 0x132fc, 0x13308,\n        0x1331e, 0x13320, 0x1333c, 0x13340, 0x13378, 0x13386, 0x13398, 0x133b0, 0x133be, 0x133ce, 0x133dc, 0x133e2,\n        0x133e4, 0x133e8, 0x133f6, 0x1340e, 0x1341c, 0x13438, 0x13470, 0x1347e, 0x134e0, 0x134fc, 0x135c0, 0x135f8,\n        0x13608, 0x13610, 0x1361e, 0x13620, 0x1363c, 0x13640, 0x13678, 0x136f0, 0x1370c, 0x13718, 0x13730, 0x1373e,\n        0x13760, 0x1377c, 0x1379c, 0x137b8, 0x137c2, 0x137c4, 0x137c8, 0x137d0, 0x137de, 0x137e6, 0x137ec, 0x13816,\n        0x13826, 0x1382c, 0x13846, 0x1384c, 0x13858, 0x1386e, 0x13874, 0x13886, 0x13898, 0x138b0, 0x138be, 0x138ce,\n        0x138dc, 0x138e2, 0x138e4, 0x138e8, 0x13906, 0x1390c, 0x13930, 0x1393e, 0x13960, 0x1397c, 0x1398e, 0x1399c,\n        0x139b8, 0x139c8, 0x139d0, 0x139de, 0x139e6, 0x139ec, 0x139fa, 0x13a06, 0x13a0c, 0x13a18, 0x13a30, 0x13a3e,\n        0x13a60, 0x13a7c, 0x13ac0, 0x13af8, 0x13b0e, 0x13b1c, 0x13b38, 0x13b70, 0x13b7e, 0x13b88, 0x13b90, 0x13b9e,\n        0x13ba0, 0x13bbc, 0x13bcc, 0x13bd8, 0x13bee, 0x13bf2, 0x13bf4, 0x13c12, 0x13c14, 0x13c22, 0x13c24, 0x13c28,\n        0x13c36, 0x13c42, 0x13c48, 0x13c50, 0x13c5e, 0x13c66, 0x13c6c, 0x13c82, 0x13c84, 0x13c90, 0x13c9e, 0x13ca0,\n        0x13cbc, 0x13cc6, 0x13ccc, 0x13cd8, 0x13cee, 0x13d02, 0x13d04, 0x13d08, 0x13d10, 0x13d1e, 0x13d20, 0x13d3c,\n        0x13d40, 0x13d78, 0x13d86, 0x13d8c, 0x13d98, 0x13db0, 0x13dbe, 0x13dce, 0x13ddc, 0x13de4, 0x13de8, 0x13df6,\n        0x13e1a, 0x13e2e, 0x13e32, 0x13e34, 0x13e4e, 0x13e5c, 0x13e62, 0x13e64, 0x13e68, 0x13e76, 0x13e8e, 0x13e9c,\n        0x13eb8, 0x13ec2, 0x13ec4, 0x13ec8, 0x13ed0, 0x13ede, 0x13ee6, 0x13eec, 0x13f26, 0x13f2c, 0x13f3a, 0x13f46,\n        0x13f4c, 0x13f58, 0x13f6e, 0x13f72, 0x13f74, 0x14082, 0x1409e, 0x140a0, 0x140bc, 0x14104, 0x14108, 0x14110,\n        0x1411e, 0x14120, 0x1413c, 0x14140, 0x14178, 0x1418c, 0x14198, 0x141b0, 0x141be, 0x141e2, 0x141e4, 0x141e8,\n        0x14208, 0x14210, 0x1421e, 0x14220, 0x1423c, 0x14240, 0x14278, 0x142f0, 0x14306, 0x1430c, 0x14318, 0x14330,\n        0x1433e, 0x14360, 0x1437c, 0x1438e, 0x143c2, 0x143c4, 0x143c8, 0x143d0, 0x143e6, 0x143ec, 0x14408, 0x14410,\n        0x1441e, 0x14420, 0x1443c, 0x14440, 0x14478, 0x144f0, 0x145e0, 0x1460c, 0x14618, 0x14630, 0x1463e, 0x14660,\n        0x1467c, 0x146c0, 0x146f8, 0x1471c, 0x14738, 0x14770, 0x1477e, 0x14782, 0x14784, 0x14788, 0x14790, 0x147a0,\n        0x147bc, 0x147c6, 0x147cc, 0x147d8, 0x147ee, 0x14810, 0x14820, 0x1483c, 0x14840, 0x14878, 0x148f0, 0x149e0,\n        0x14bc0, 0x14c30, 0x14c3e, 0x14c60, 0x14c7c, 0x14cc0, 0x14cf8, 0x14df0, 0x14e38, 0x14e70, 0x14e7e, 0x14ee0,\n        0x14efc, 0x14f04, 0x14f08, 0x14f10, 0x14f1e, 0x14f20, 0x14f3c, 0x14f40, 0x14f78, 0x14f86, 0x14f8c, 0x14f98,\n        0x14fb0, 0x14fce, 0x14fdc, 0x15020, 0x15040, 0x15078, 0x150f0, 0x151e0, 0x153c0, 0x15860, 0x1587c, 0x158c0,\n        0x158f8, 0x159f0, 0x15be0, 0x15c70, 0x15c7e, 0x15ce0, 0x15cfc, 0x15dc0, 0x15df8, 0x15e08, 0x15e10, 0x15e20,\n        0x15e40, 0x15e78, 0x15ef0, 0x15f0c, 0x15f18, 0x15f30, 0x15f60, 0x15f7c, 0x15f8e, 0x15f9c, 0x15fb8, 0x1604e,\n        0x1605c, 0x1608e, 0x1609c, 0x160b8, 0x160c2, 0x160c4, 0x160c8, 0x160de, 0x1610e, 0x1611c, 0x16138, 0x16170,\n        0x1617e, 0x16184, 0x16188, 0x16190, 0x1619e, 0x161a0, 0x161bc, 0x161c6, 0x161cc, 0x161d8, 0x161f2, 0x161f4,\n        0x1620e, 0x1621c, 0x16238, 0x16270, 0x1627e, 0x162e0, 0x162fc, 0x16304, 0x16308, 0x16310, 0x1631e, 0x16320,\n        0x1633c, 0x16340, 0x16378, 0x16386, 0x1638c, 0x16398, 0x163b0, 0x163be, 0x163ce, 0x163dc, 0x163e2, 0x163e4,\n        0x163e8, 0x163f6, 0x1640e, 0x1641c, 0x16438, 0x16470, 0x1647e, 0x164e0, 0x164fc, 0x165c0, 0x165f8, 0x16610,\n        0x1661e, 0x16620, 0x1663c, 0x16640, 0x16678, 0x166f0, 0x16718, 0x16730, 0x1673e, 0x16760, 0x1677c, 0x1678e,\n        0x1679c, 0x167b8, 0x167c2, 0x167c4, 0x167c8, 0x167d0, 0x167de, 0x167e6, 0x167ec, 0x1681c, 0x16838, 0x16870,\n        0x168e0, 0x168fc, 0x169c0, 0x169f8, 0x16bf0, 0x16c10, 0x16c1e, 0x16c20, 0x16c3c, 0x16c40, 0x16c78, 0x16cf0,\n        0x16de0, 0x16e18, 0x16e30, 0x16e3e, 0x16e60, 0x16e7c, 0x16ec0, 0x16ef8, 0x16f1c, 0x16f38, 0x16f70, 0x16f7e,\n        0x16f84, 0x16f88, 0x16f90, 0x16f9e, 0x16fa0, 0x16fbc, 0x16fc6, 0x16fcc, 0x16fd8, 0x17026, 0x1702c, 0x17046,\n        0x1704c, 0x17058, 0x1706e, 0x17086, 0x1708c, 0x17098, 0x170b0, 0x170be, 0x170ce, 0x170dc, 0x170e8, 0x17106,\n        0x1710c, 0x17118, 0x17130, 0x1713e, 0x17160, 0x1717c, 0x1718e, 0x1719c, 0x171b8, 0x171c2, 0x171c4, 0x171c8,\n        0x171d0, 0x171de, 0x171e6, 0x171ec, 0x171fa, 0x17206, 0x1720c, 0x17218, 0x17230, 0x1723e, 0x17260, 0x1727c,\n        0x172c0, 0x172f8, 0x1730e, 0x1731c, 0x17338, 0x17370, 0x1737e, 0x17388, 0x17390, 0x1739e, 0x173a0, 0x173bc,\n        0x173cc, 0x173d8, 0x173ee, 0x173f2, 0x173f4, 0x1740c, 0x17418, 0x17430, 0x1743e, 0x17460, 0x1747c, 0x174c0,\n        0x174f8, 0x175f0, 0x1760e, 0x1761c, 0x17638, 0x17670, 0x1767e, 0x176e0, 0x176fc, 0x17708, 0x17710, 0x1771e,\n        0x17720, 0x1773c, 0x17740, 0x17778, 0x17798, 0x177b0, 0x177be, 0x177dc, 0x177e2, 0x177e4, 0x177e8, 0x17822,\n        0x17824, 0x17828, 0x17836, 0x17842, 0x17844, 0x17848, 0x17850, 0x1785e, 0x17866, 0x1786c, 0x17882, 0x17884,\n        0x17888, 0x17890, 0x1789e, 0x178a0, 0x178bc, 0x178c6, 0x178cc, 0x178d8, 0x178ee, 0x178f2, 0x178f4, 0x17902,\n        0x17904, 0x17908, 0x17910, 0x1791e, 0x17920, 0x1793c, 0x17940, 0x17978, 0x17986, 0x1798c, 0x17998, 0x179b0,\n        0x179be, 0x179ce, 0x179dc, 0x179e2, 0x179e4, 0x179e8, 0x179f6, 0x17a04, 0x17a08, 0x17a10, 0x17a1e, 0x17a20,\n        0x17a3c, 0x17a40, 0x17a78, 0x17af0, 0x17b06, 0x17b0c, 0x17b18, 0x17b30, 0x17b3e, 0x17b60, 0x17b7c, 0x17b8e,\n        0x17b9c, 0x17bb8, 0x17bc4, 0x17bc8, 0x17bd0, 0x17bde, 0x17be6, 0x17bec, 0x17c2e, 0x17c32, 0x17c34, 0x17c4e,\n        0x17c5c, 0x17c62, 0x17c64, 0x17c68, 0x17c76, 0x17c8e, 0x17c9c, 0x17cb8, 0x17cc2, 0x17cc4, 0x17cc8, 0x17cd0,\n        0x17cde, 0x17ce6, 0x17cec, 0x17d0e, 0x17d1c, 0x17d38, 0x17d70, 0x17d82, 0x17d84, 0x17d88, 0x17d90, 0x17d9e,\n        0x17da0, 0x17dbc, 0x17dc6, 0x17dcc, 0x17dd8, 0x17dee, 0x17e26, 0x17e2c, 0x17e3a, 0x17e46, 0x17e4c, 0x17e58,\n        0x17e6e, 0x17e72, 0x17e74, 0x17e86, 0x17e8c, 0x17e98, 0x17eb0, 0x17ece, 0x17edc, 0x17ee2, 0x17ee4, 0x17ee8,\n        0x17ef6, 0x1813a, 0x18172, 0x18174, 0x18216, 0x18226, 0x1823a, 0x1824c, 0x18258, 0x1826e, 0x18272, 0x18274,\n        0x18298, 0x182be, 0x182e2, 0x182e4, 0x182e8, 0x182f6, 0x1835e, 0x1837a, 0x183ae, 0x183d6, 0x18416, 0x18426,\n        0x1842c, 0x1843a, 0x18446, 0x18458, 0x1846e, 0x18472, 0x18474, 0x18486, 0x184b0, 0x184be, 0x184ce, 0x184dc,\n        0x184e2, 0x184e4, 0x184e8, 0x184f6, 0x18506, 0x1850c, 0x18518, 0x18530, 0x1853e, 0x18560, 0x1857c, 0x1858e,\n        0x1859c, 0x185b8, 0x185c2, 0x185c4, 0x185c8, 0x185d0, 0x185de, 0x185e6, 0x185ec, 0x185fa, 0x18612, 0x18614,\n        0x18622, 0x18628, 0x18636, 0x18642, 0x18650, 0x1865e, 0x1867a, 0x18682, 0x18684, 0x18688, 0x18690, 0x1869e,\n        0x186a0, 0x186bc, 0x186c6, 0x186cc, 0x186d8, 0x186ee, 0x186f2, 0x186f4, 0x1872e, 0x1874e, 0x1875c, 0x18796,\n        0x187a6, 0x187ac, 0x187d2, 0x187d4, 0x18826, 0x1882c, 0x1883a, 0x18846, 0x1884c, 0x18858, 0x1886e, 0x18872,\n        0x18874, 0x18886, 0x18898, 0x188b0, 0x188be, 0x188ce, 0x188dc, 0x188e2, 0x188e4, 0x188e8, 0x188f6, 0x1890c,\n        0x18930, 0x1893e, 0x18960, 0x1897c, 0x1898e, 0x189b8, 0x189c2, 0x189c8, 0x189d0, 0x189de, 0x189e6, 0x189ec,\n        0x189fa, 0x18a18, 0x18a30, 0x18a3e, 0x18a60, 0x18a7c, 0x18ac0, 0x18af8, 0x18b1c, 0x18b38, 0x18b70, 0x18b7e,\n        0x18b82, 0x18b84, 0x18b88, 0x18b90, 0x18b9e, 0x18ba0, 0x18bbc, 0x18bc6, 0x18bcc, 0x18bd8, 0x18bee, 0x18bf2,\n        0x18bf4, 0x18c22, 0x18c24, 0x18c28, 0x18c36, 0x18c42, 0x18c48, 0x18c50, 0x18c5e, 0x18c66, 0x18c7a, 0x18c82,\n        0x18c84, 0x18c90, 0x18c9e, 0x18ca0, 0x18cbc, 0x18ccc, 0x18cf2, 0x18cf4, 0x18d04, 0x18d08, 0x18d10, 0x18d1e,\n        0x18d20, 0x18d3c, 0x18d40, 0x18d78, 0x18d86, 0x18d98, 0x18dce, 0x18de2, 0x18de4, 0x18de8, 0x18e2e, 0x18e32,\n        0x18e34, 0x18e4e, 0x18e5c, 0x18e62, 0x18e64, 0x18e68, 0x18e8e, 0x18e9c, 0x18eb8, 0x18ec2, 0x18ec4, 0x18ec8,\n        0x18ed0, 0x18efa, 0x18f16, 0x18f26, 0x18f2c, 0x18f46, 0x18f4c, 0x18f58, 0x18f6e, 0x18f8a, 0x18f92, 0x18f94,\n        0x18fa2, 0x18fa4, 0x18fa8, 0x18fb6, 0x1902c, 0x1903a, 0x19046, 0x1904c, 0x19058, 0x19072, 0x19074, 0x19086,\n        0x19098, 0x190b0, 0x190be, 0x190ce, 0x190dc, 0x190e2, 0x190e8, 0x190f6, 0x19106, 0x1910c, 0x19130, 0x1913e,\n        0x19160, 0x1917c, 0x1918e, 0x1919c, 0x191b8, 0x191c2, 0x191c8, 0x191d0, 0x191de, 0x191e6, 0x191ec, 0x191fa,\n        0x19218, 0x1923e, 0x19260, 0x1927c, 0x192c0, 0x192f8, 0x19338, 0x19370, 0x1937e, 0x19382, 0x19384, 0x19390,\n        0x1939e, 0x193a0, 0x193bc, 0x193c6, 0x193cc, 0x193d8, 0x193ee, 0x193f2, 0x193f4, 0x19430, 0x1943e, 0x19460,\n        0x1947c, 0x194c0, 0x194f8, 0x195f0, 0x19638, 0x19670, 0x1967e, 0x196e0, 0x196fc, 0x19702, 0x19704, 0x19708,\n        0x19710, 0x19720, 0x1973c, 0x19740, 0x19778, 0x19786, 0x1978c, 0x19798, 0x197b0, 0x197be, 0x197ce, 0x197dc,\n        0x197e2, 0x197e4, 0x197e8, 0x19822, 0x19824, 0x19842, 0x19848, 0x19850, 0x1985e, 0x19866, 0x1987a, 0x19882,\n        0x19884, 0x19890, 0x1989e, 0x198a0, 0x198bc, 0x198cc, 0x198f2, 0x198f4, 0x19902, 0x19908, 0x1991e, 0x19920,\n        0x1993c, 0x19940, 0x19978, 0x19986, 0x19998, 0x199ce, 0x199e2, 0x199e4, 0x199e8, 0x19a08, 0x19a10, 0x19a1e,\n        0x19a20, 0x19a3c, 0x19a40, 0x19a78, 0x19af0, 0x19b18, 0x19b3e, 0x19b60, 0x19b9c, 0x19bc2, 0x19bc4, 0x19bc8,\n        0x19bd0, 0x19be6, 0x19c2e, 0x19c34, 0x19c4e, 0x19c5c, 0x19c62, 0x19c64, 0x19c68, 0x19c8e, 0x19c9c, 0x19cb8,\n        0x19cc2, 0x19cc8, 0x19cd0, 0x19ce6, 0x19cfa, 0x19d0e, 0x19d1c, 0x19d38, 0x19d70, 0x19d7e, 0x19d82, 0x19d84,\n        0x19d88, 0x19d90, 0x19da0, 0x19dcc, 0x19df2, 0x19df4, 0x19e16, 0x19e26, 0x19e2c, 0x19e46, 0x19e4c, 0x19e58,\n        0x19e74, 0x19e86, 0x19e8c, 0x19e98, 0x19eb0, 0x19ebe, 0x19ece, 0x19ee2, 0x19ee4, 0x19ee8, 0x19f0a, 0x19f12,\n        0x19f14, 0x19f22, 0x19f24, 0x19f28, 0x19f42, 0x19f44, 0x19f48, 0x19f50, 0x19f5e, 0x19f6c, 0x19f9a, 0x19fae,\n        0x19fb2, 0x19fb4, 0x1a046, 0x1a04c, 0x1a072, 0x1a074, 0x1a086, 0x1a08c, 0x1a098, 0x1a0b0, 0x1a0be, 0x1a0e2,\n        0x1a0e4, 0x1a0e8, 0x1a0f6, 0x1a106, 0x1a10c, 0x1a118, 0x1a130, 0x1a13e, 0x1a160, 0x1a17c, 0x1a18e, 0x1a19c,\n        0x1a1b8, 0x1a1c2, 0x1a1c4, 0x1a1c8, 0x1a1d0, 0x1a1de, 0x1a1e6, 0x1a1ec, 0x1a218, 0x1a230, 0x1a23e, 0x1a260,\n        0x1a27c, 0x1a2c0, 0x1a2f8, 0x1a31c, 0x1a338, 0x1a370, 0x1a37e, 0x1a382, 0x1a384, 0x1a388, 0x1a390, 0x1a39e,\n        0x1a3a0, 0x1a3bc, 0x1a3c6, 0x1a3cc, 0x1a3d8, 0x1a3ee, 0x1a3f2, 0x1a3f4, 0x1a418, 0x1a430, 0x1a43e, 0x1a460,\n        0x1a47c, 0x1a4c0, 0x1a4f8, 0x1a5f0, 0x1a61c, 0x1a638, 0x1a670, 0x1a67e, 0x1a6e0, 0x1a6fc, 0x1a702, 0x1a704,\n        0x1a708, 0x1a710, 0x1a71e, 0x1a720, 0x1a73c, 0x1a740, 0x1a778, 0x1a786, 0x1a78c, 0x1a798, 0x1a7b0, 0x1a7be,\n        0x1a7ce, 0x1a7dc, 0x1a7e2, 0x1a7e4, 0x1a7e8, 0x1a830, 0x1a860, 0x1a87c, 0x1a8c0, 0x1a8f8, 0x1a9f0, 0x1abe0,\n        0x1ac70, 0x1ac7e, 0x1ace0, 0x1acfc, 0x1adc0, 0x1adf8, 0x1ae04, 0x1ae08, 0x1ae10, 0x1ae20, 0x1ae3c, 0x1ae40,\n        0x1ae78, 0x1aef0, 0x1af06, 0x1af0c, 0x1af18, 0x1af30, 0x1af3e, 0x1af60, 0x1af7c, 0x1af8e, 0x1af9c, 0x1afb8,\n        0x1afc4, 0x1afc8, 0x1afd0, 0x1afde, 0x1b042, 0x1b05e, 0x1b07a, 0x1b082, 0x1b084, 0x1b088, 0x1b090, 0x1b09e,\n        0x1b0a0, 0x1b0bc, 0x1b0cc, 0x1b0f2, 0x1b0f4, 0x1b102, 0x1b104, 0x1b108, 0x1b110, 0x1b11e, 0x1b120, 0x1b13c,\n        0x1b140, 0x1b178, 0x1b186, 0x1b198, 0x1b1ce, 0x1b1e2, 0x1b1e4, 0x1b1e8, 0x1b204, 0x1b208, 0x1b210, 0x1b21e,\n        0x1b220, 0x1b23c, 0x1b240, 0x1b278, 0x1b2f0, 0x1b30c, 0x1b33e, 0x1b360, 0x1b39c, 0x1b3c2, 0x1b3c4, 0x1b3c8,\n        0x1b3d0, 0x1b3e6, 0x1b410, 0x1b41e, 0x1b420, 0x1b43c, 0x1b440, 0x1b478, 0x1b4f0, 0x1b5e0, 0x1b618, 0x1b660,\n        0x1b67c, 0x1b6c0, 0x1b738, 0x1b782, 0x1b784, 0x1b788, 0x1b790, 0x1b79e, 0x1b7a0, 0x1b7cc, 0x1b82e, 0x1b84e,\n        0x1b85c, 0x1b88e, 0x1b89c, 0x1b8b8, 0x1b8c2, 0x1b8c4, 0x1b8c8, 0x1b8d0, 0x1b8e6, 0x1b8fa, 0x1b90e, 0x1b91c,\n        0x1b938, 0x1b970, 0x1b97e, 0x1b982, 0x1b984, 0x1b988, 0x1b990, 0x1b99e, 0x1b9a0, 0x1b9cc, 0x1b9f2, 0x1b9f4,\n        0x1ba0e, 0x1ba1c, 0x1ba38, 0x1ba70, 0x1ba7e, 0x1bae0, 0x1bafc, 0x1bb08, 0x1bb10, 0x1bb20, 0x1bb3c, 0x1bb40,\n        0x1bb98, 0x1bbce, 0x1bbe2, 0x1bbe4, 0x1bbe8, 0x1bc16, 0x1bc26, 0x1bc2c, 0x1bc46, 0x1bc4c, 0x1bc58, 0x1bc72,\n        0x1bc74, 0x1bc86, 0x1bc8c, 0x1bc98, 0x1bcb0, 0x1bcbe, 0x1bcce, 0x1bce2, 0x1bce4, 0x1bce8, 0x1bd06, 0x1bd0c,\n        0x1bd18, 0x1bd30, 0x1bd3e, 0x1bd60, 0x1bd7c, 0x1bd9c, 0x1bdc2, 0x1bdc4, 0x1bdc8, 0x1bdd0, 0x1bde6, 0x1bdfa,\n        0x1be12, 0x1be14, 0x1be22, 0x1be24, 0x1be28, 0x1be42, 0x1be44, 0x1be48, 0x1be50, 0x1be5e, 0x1be66, 0x1be82,\n        0x1be84, 0x1be88, 0x1be90, 0x1be9e, 0x1bea0, 0x1bebc, 0x1becc, 0x1bef4, 0x1bf1a, 0x1bf2e, 0x1bf32, 0x1bf34,\n        0x1bf4e, 0x1bf5c, 0x1bf62, 0x1bf64, 0x1bf68, 0x1c09a, 0x1c0b2, 0x1c0b4, 0x1c11a, 0x1c132, 0x1c134, 0x1c162,\n        0x1c164, 0x1c168, 0x1c176, 0x1c1ba, 0x1c21a, 0x1c232, 0x1c234, 0x1c24e, 0x1c25c, 0x1c262, 0x1c264, 0x1c268,\n        0x1c276, 0x1c28e, 0x1c2c2, 0x1c2c4, 0x1c2c8, 0x1c2d0, 0x1c2de, 0x1c2e6, 0x1c2ec, 0x1c2fa, 0x1c316, 0x1c326,\n        0x1c33a, 0x1c346, 0x1c34c, 0x1c372, 0x1c374, 0x1c41a, 0x1c42e, 0x1c432, 0x1c434, 0x1c44e, 0x1c45c, 0x1c462,\n        0x1c464, 0x1c468, 0x1c476, 0x1c48e, 0x1c49c, 0x1c4b8, 0x1c4c2, 0x1c4c8, 0x1c4d0, 0x1c4de, 0x1c4e6, 0x1c4ec,\n        0x1c4fa, 0x1c51c, 0x1c538, 0x1c570, 0x1c57e, 0x1c582, 0x1c584, 0x1c588, 0x1c590, 0x1c59e, 0x1c5a0, 0x1c5bc,\n        0x1c5c6, 0x1c5cc, 0x1c5d8, 0x1c5ee, 0x1c5f2, 0x1c5f4, 0x1c616, 0x1c626, 0x1c62c, 0x1c63a, 0x1c646, 0x1c64c,\n        0x1c658, 0x1c66e, 0x1c672, 0x1c674, 0x1c686, 0x1c68c, 0x1c698, 0x1c6b0, 0x1c6be, 0x1c6ce, 0x1c6dc, 0x1c6e2,\n        0x1c6e4, 0x1c6e8, 0x1c712, 0x1c714, 0x1c722, 0x1c728, 0x1c736, 0x1c742, 0x1c744, 0x1c748, 0x1c750, 0x1c75e,\n        0x1c766, 0x1c76c, 0x1c77a, 0x1c7ae, 0x1c7d6, 0x1c7ea, 0x1c81a, 0x1c82e, 0x1c832, 0x1c834, 0x1c84e, 0x1c85c,\n        0x1c862, 0x1c864, 0x1c868, 0x1c876, 0x1c88e, 0x1c89c, 0x1c8b8, 0x1c8c2, 0x1c8c8, 0x1c8d0, 0x1c8de, 0x1c8e6,\n        0x1c8ec, 0x1c8fa, 0x1c90e, 0x1c938, 0x1c970, 0x1c97e, 0x1c982, 0x1c984, 0x1c990, 0x1c99e, 0x1c9a0, 0x1c9bc,\n        0x1c9c6, 0x1c9cc, 0x1c9d8, 0x1c9ee, 0x1c9f2, 0x1c9f4, 0x1ca38, 0x1ca70, 0x1ca7e, 0x1cae0, 0x1cafc, 0x1cb02,\n        0x1cb04, 0x1cb08, 0x1cb10, 0x1cb20, 0x1cb3c, 0x1cb40, 0x1cb78, 0x1cb86, 0x1cb8c, 0x1cb98, 0x1cbb0, 0x1cbbe,\n        0x1cbce, 0x1cbdc, 0x1cbe2, 0x1cbe4, 0x1cbe8, 0x1cbf6, 0x1cc16, 0x1cc26, 0x1cc2c, 0x1cc3a, 0x1cc46, 0x1cc58,\n        0x1cc72, 0x1cc74, 0x1cc86, 0x1ccb0, 0x1ccbe, 0x1ccce, 0x1cce2, 0x1cce4, 0x1cce8, 0x1cd06, 0x1cd0c, 0x1cd18,\n        0x1cd30, 0x1cd3e, 0x1cd60, 0x1cd7c, 0x1cd9c, 0x1cdc2, 0x1cdc4, 0x1cdc8, 0x1cdd0, 0x1cdde, 0x1cde6, 0x1cdfa,\n        0x1ce22, 0x1ce28, 0x1ce42, 0x1ce50, 0x1ce5e, 0x1ce66, 0x1ce7a, 0x1ce82, 0x1ce84, 0x1ce88, 0x1ce90, 0x1ce9e,\n        0x1cea0, 0x1cebc, 0x1cecc, 0x1cef2, 0x1cef4, 0x1cf2e, 0x1cf32, 0x1cf34, 0x1cf4e, 0x1cf5c, 0x1cf62, 0x1cf64,\n        0x1cf68, 0x1cf96, 0x1cfa6, 0x1cfac, 0x1cfca, 0x1cfd2, 0x1cfd4, 0x1d02e, 0x1d032, 0x1d034, 0x1d04e, 0x1d05c,\n        0x1d062, 0x1d064, 0x1d068, 0x1d076, 0x1d08e, 0x1d09c, 0x1d0b8, 0x1d0c2, 0x1d0c4, 0x1d0c8, 0x1d0d0, 0x1d0de,\n        0x1d0e6, 0x1d0ec, 0x1d0fa, 0x1d11c, 0x1d138, 0x1d170, 0x1d17e, 0x1d182, 0x1d184, 0x1d188, 0x1d190, 0x1d19e,\n        0x1d1a0, 0x1d1bc, 0x1d1c6, 0x1d1cc, 0x1d1d8, 0x1d1ee, 0x1d1f2, 0x1d1f4, 0x1d21c, 0x1d238, 0x1d270, 0x1d27e,\n        0x1d2e0, 0x1d2fc, 0x1d302, 0x1d304, 0x1d308, 0x1d310, 0x1d31e, 0x1d320, 0x1d33c, 0x1d340, 0x1d378, 0x1d386,\n        0x1d38c, 0x1d398, 0x1d3b0, 0x1d3be, 0x1d3ce, 0x1d3dc, 0x1d3e2, 0x1d3e4, 0x1d3e8, 0x1d3f6, 0x1d470, 0x1d47e,\n        0x1d4e0, 0x1d4fc, 0x1d5c0, 0x1d5f8, 0x1d604, 0x1d608, 0x1d610, 0x1d620, 0x1d640, 0x1d678, 0x1d6f0, 0x1d706,\n        0x1d70c, 0x1d718, 0x1d730, 0x1d73e, 0x1d760, 0x1d77c, 0x1d78e, 0x1d79c, 0x1d7b8, 0x1d7c2, 0x1d7c4, 0x1d7c8,\n        0x1d7d0, 0x1d7de, 0x1d7e6, 0x1d7ec, 0x1d826, 0x1d82c, 0x1d83a, 0x1d846, 0x1d84c, 0x1d858, 0x1d872, 0x1d874,\n        0x1d886, 0x1d88c, 0x1d898, 0x1d8b0, 0x1d8be, 0x1d8ce, 0x1d8e2, 0x1d8e4, 0x1d8e8, 0x1d8f6, 0x1d90c, 0x1d918,\n        0x1d930, 0x1d93e, 0x1d960, 0x1d97c, 0x1d99c, 0x1d9c2, 0x1d9c4, 0x1d9c8, 0x1d9d0, 0x1d9e6, 0x1d9fa, 0x1da0c,\n        0x1da18, 0x1da30, 0x1da3e, 0x1da60, 0x1da7c, 0x1dac0, 0x1daf8, 0x1db38, 0x1db82, 0x1db84, 0x1db88, 0x1db90,\n        0x1db9e, 0x1dba0, 0x1dbcc, 0x1dbf2, 0x1dbf4, 0x1dc22, 0x1dc42, 0x1dc44, 0x1dc48, 0x1dc50, 0x1dc5e, 0x1dc66,\n        0x1dc7a, 0x1dc82, 0x1dc84, 0x1dc88, 0x1dc90, 0x1dc9e, 0x1dca0, 0x1dcbc, 0x1dccc, 0x1dcf2, 0x1dcf4, 0x1dd04,\n        0x1dd08, 0x1dd10, 0x1dd1e, 0x1dd20, 0x1dd3c, 0x1dd40, 0x1dd78, 0x1dd86, 0x1dd98, 0x1ddce, 0x1dde2, 0x1dde4,\n        0x1dde8, 0x1de2e, 0x1de32, 0x1de34, 0x1de4e, 0x1de5c, 0x1de62, 0x1de64, 0x1de68, 0x1de8e, 0x1de9c, 0x1deb8,\n        0x1dec2, 0x1dec4, 0x1dec8, 0x1ded0, 0x1dee6, 0x1defa, 0x1df16, 0x1df26, 0x1df2c, 0x1df46, 0x1df4c, 0x1df58,\n        0x1df72, 0x1df74, 0x1df8a, 0x1df92, 0x1df94, 0x1dfa2, 0x1dfa4, 0x1dfa8, 0x1e08a, 0x1e092, 0x1e094, 0x1e0a2,\n        0x1e0a4, 0x1e0a8, 0x1e0b6, 0x1e0da, 0x1e10a, 0x1e112, 0x1e114, 0x1e122, 0x1e124, 0x1e128, 0x1e136, 0x1e142,\n        0x1e144, 0x1e148, 0x1e150, 0x1e166, 0x1e16c, 0x1e17a, 0x1e19a, 0x1e1b2, 0x1e1b4, 0x1e20a, 0x1e212, 0x1e214,\n        0x1e222, 0x1e224, 0x1e228, 0x1e236, 0x1e242, 0x1e248, 0x1e250, 0x1e25e, 0x1e266, 0x1e26c, 0x1e27a, 0x1e282,\n        0x1e284, 0x1e288, 0x1e290, 0x1e2a0, 0x1e2bc, 0x1e2c6, 0x1e2cc, 0x1e2d8, 0x1e2ee, 0x1e2f2, 0x1e2f4, 0x1e31a,\n        0x1e332, 0x1e334, 0x1e35c, 0x1e362, 0x1e364, 0x1e368, 0x1e3ba, 0x1e40a, 0x1e412, 0x1e414, 0x1e422, 0x1e428,\n        0x1e436, 0x1e442, 0x1e448, 0x1e450, 0x1e45e, 0x1e466, 0x1e46c, 0x1e47a, 0x1e482, 0x1e484, 0x1e490, 0x1e49e,\n        0x1e4a0, 0x1e4bc, 0x1e4c6, 0x1e4cc, 0x1e4d8, 0x1e4ee, 0x1e4f2, 0x1e4f4, 0x1e502, 0x1e504, 0x1e508, 0x1e510,\n        0x1e51e, 0x1e520, 0x1e53c, 0x1e540, 0x1e578, 0x1e586, 0x1e58c, 0x1e598, 0x1e5b0, 0x1e5be, 0x1e5ce, 0x1e5dc,\n        0x1e5e2, 0x1e5e4, 0x1e5e8, 0x1e5f6, 0x1e61a, 0x1e62e, 0x1e632, 0x1e634, 0x1e64e, 0x1e65c, 0x1e662, 0x1e668,\n        0x1e68e, 0x1e69c, 0x1e6b8, 0x1e6c2, 0x1e6c4, 0x1e6c8, 0x1e6d0, 0x1e6e6, 0x1e6fa, 0x1e716, 0x1e726, 0x1e72c,\n        0x1e73a, 0x1e746, 0x1e74c, 0x1e758, 0x1e772, 0x1e774, 0x1e792, 0x1e794, 0x1e7a2, 0x1e7a4, 0x1e7a8, 0x1e7b6,\n        0x1e812, 0x1e814, 0x1e822, 0x1e824, 0x1e828, 0x1e836, 0x1e842, 0x1e844, 0x1e848, 0x1e850, 0x1e85e, 0x1e866,\n        0x1e86c, 0x1e87a, 0x1e882, 0x1e884, 0x1e888, 0x1e890, 0x1e89e, 0x1e8a0, 0x1e8bc, 0x1e8c6, 0x1e8cc, 0x1e8d8,\n        0x1e8ee, 0x1e8f2, 0x1e8f4, 0x1e902, 0x1e904, 0x1e908, 0x1e910, 0x1e920, 0x1e93c, 0x1e940, 0x1e978, 0x1e986,\n        0x1e98c, 0x1e998, 0x1e9b0, 0x1e9be, 0x1e9ce, 0x1e9dc, 0x1e9e2, 0x1e9e4, 0x1e9e8, 0x1e9f6, 0x1ea04, 0x1ea08,\n        0x1ea10, 0x1ea20, 0x1ea40, 0x1ea78, 0x1eaf0, 0x1eb06, 0x1eb0c, 0x1eb18, 0x1eb30, 0x1eb3e, 0x1eb60, 0x1eb7c,\n        0x1eb8e, 0x1eb9c, 0x1ebb8, 0x1ebc2, 0x1ebc4, 0x1ebc8, 0x1ebd0, 0x1ebde, 0x1ebe6, 0x1ebec, 0x1ec1a, 0x1ec2e,\n        0x1ec32, 0x1ec34, 0x1ec4e, 0x1ec5c, 0x1ec62, 0x1ec64, 0x1ec68, 0x1ec8e, 0x1ec9c, 0x1ecb8, 0x1ecc2, 0x1ecc4,\n        0x1ecc8, 0x1ecd0, 0x1ece6, 0x1ecfa, 0x1ed0e, 0x1ed1c, 0x1ed38, 0x1ed70, 0x1ed7e, 0x1ed82, 0x1ed84, 0x1ed88,\n        0x1ed90, 0x1ed9e, 0x1eda0, 0x1edcc, 0x1edf2, 0x1edf4, 0x1ee16, 0x1ee26, 0x1ee2c, 0x1ee3a, 0x1ee46, 0x1ee4c,\n        0x1ee58, 0x1ee6e, 0x1ee72, 0x1ee74, 0x1ee86, 0x1ee8c, 0x1ee98, 0x1eeb0, 0x1eebe, 0x1eece, 0x1eedc, 0x1eee2,\n        0x1eee4, 0x1eee8, 0x1ef12, 0x1ef22, 0x1ef24, 0x1ef28, 0x1ef36, 0x1ef42, 0x1ef44, 0x1ef48, 0x1ef50, 0x1ef5e,\n        0x1ef66, 0x1ef6c, 0x1ef7a, 0x1efae, 0x1efb2, 0x1efb4, 0x1efd6, 0x1f096, 0x1f0a6, 0x1f0ac, 0x1f0ba, 0x1f0ca,\n        0x1f0d2, 0x1f0d4, 0x1f116, 0x1f126, 0x1f12c, 0x1f13a, 0x1f146, 0x1f14c, 0x1f158, 0x1f16e, 0x1f172, 0x1f174,\n        0x1f18a, 0x1f192, 0x1f194, 0x1f1a2, 0x1f1a4, 0x1f1a8, 0x1f1da, 0x1f216, 0x1f226, 0x1f22c, 0x1f23a, 0x1f246,\n        0x1f258, 0x1f26e, 0x1f272, 0x1f274, 0x1f286, 0x1f28c, 0x1f298, 0x1f2b0, 0x1f2be, 0x1f2ce, 0x1f2dc, 0x1f2e2,\n        0x1f2e4, 0x1f2e8, 0x1f2f6, 0x1f30a, 0x1f312, 0x1f314, 0x1f322, 0x1f328, 0x1f342, 0x1f344, 0x1f348, 0x1f350,\n        0x1f35e, 0x1f366, 0x1f37a, 0x1f39a, 0x1f3ae, 0x1f3b2, 0x1f3b4, 0x1f416, 0x1f426, 0x1f42c, 0x1f43a, 0x1f446,\n        0x1f44c, 0x1f458, 0x1f46e, 0x1f472, 0x1f474, 0x1f486, 0x1f48c, 0x1f498, 0x1f4b0, 0x1f4be, 0x1f4ce, 0x1f4dc,\n        0x1f4e2, 0x1f4e4, 0x1f4e8, 0x1f4f6, 0x1f506, 0x1f50c, 0x1f518, 0x1f530, 0x1f53e, 0x1f560, 0x1f57c, 0x1f58e,\n        0x1f59c, 0x1f5b8, 0x1f5c2, 0x1f5c4, 0x1f5c8, 0x1f5d0, 0x1f5de, 0x1f5e6, 0x1f5ec, 0x1f5fa, 0x1f60a, 0x1f612,\n        0x1f614, 0x1f622, 0x1f624, 0x1f628, 0x1f636, 0x1f642, 0x1f644, 0x1f648, 0x1f650, 0x1f65e, 0x1f666, 0x1f67a,\n        0x1f682, 0x1f684, 0x1f688, 0x1f690, 0x1f69e, 0x1f6a0, 0x1f6bc, 0x1f6cc, 0x1f6f2, 0x1f6f4, 0x1f71a, 0x1f72e,\n        0x1f732, 0x1f734, 0x1f74e, 0x1f75c, 0x1f762, 0x1f764, 0x1f768, 0x1f776, 0x1f796, 0x1f7a6, 0x1f7ac, 0x1f7ba,\n        0x1f7d2, 0x1f7d4, 0x1f89a, 0x1f8ae, 0x1f8b2, 0x1f8b4, 0x1f8d6, 0x1f8ea, 0x1f91a, 0x1f92e, 0x1f932, 0x1f934,\n        0x1f94e, 0x1f95c, 0x1f962, 0x1f964, 0x1f968, 0x1f976, 0x1f996, 0x1f9a6, 0x1f9ac, 0x1f9ba, 0x1f9ca, 0x1f9d2,\n        0x1f9d4, 0x1fa1a, 0x1fa2e, 0x1fa32, 0x1fa34, 0x1fa4e, 0x1fa5c, 0x1fa62, 0x1fa64, 0x1fa68, 0x1fa76, 0x1fa8e,\n        0x1fa9c, 0x1fab8, 0x1fac2, 0x1fac4, 0x1fac8, 0x1fad0, 0x1fade, 0x1fae6, 0x1faec, 0x1fb16, 0x1fb26, 0x1fb2c,\n        0x1fb3a, 0x1fb46, 0x1fb4c, 0x1fb58, 0x1fb6e, 0x1fb72, 0x1fb74, 0x1fb8a, 0x1fb92, 0x1fb94, 0x1fba2, 0x1fba4,\n        0x1fba8, 0x1fbb6, 0x1fbda\n    ]);\n    /**\n     * This table contains to codewords for all symbols.\n     */\n    PDF417Common.CODEWORD_TABLE = Int32Array.from([\n        2627, 1819, 2622, 2621, 1813, 1812, 2729, 2724, 2723, 2779, 2774, 2773, 902, 896, 908, 868, 865, 861, 859, 2511,\n        873, 871, 1780, 835, 2493, 825, 2491, 842, 837, 844, 1764, 1762, 811, 810, 809, 2483, 807, 2482, 806, 2480, 815,\n        814, 813, 812, 2484, 817, 816, 1745, 1744, 1742, 1746, 2655, 2637, 2635, 2626, 2625, 2623, 2628, 1820, 2752,\n        2739, 2737, 2728, 2727, 2725, 2730, 2785, 2783, 2778, 2777, 2775, 2780, 787, 781, 747, 739, 736, 2413, 754, 752,\n        1719, 692, 689, 681, 2371, 678, 2369, 700, 697, 694, 703, 1688, 1686, 642, 638, 2343, 631, 2341, 627, 2338, 651,\n        646, 643, 2345, 654, 652, 1652, 1650, 1647, 1654, 601, 599, 2322, 596, 2321, 594, 2319, 2317, 611, 610, 608, 606,\n        2324, 603, 2323, 615, 614, 612, 1617, 1616, 1614, 1612, 616, 1619, 1618, 2575, 2538, 2536, 905, 901, 898, 909,\n        2509, 2507, 2504, 870, 867, 864, 860, 2512, 875, 872, 1781, 2490, 2489, 2487, 2485, 1748, 836, 834, 832, 830,\n        2494, 827, 2492, 843, 841, 839, 845, 1765, 1763, 2701, 2676, 2674, 2653, 2648, 2656, 2634, 2633, 2631, 2629,\n        1821, 2638, 2636, 2770, 2763, 2761, 2750, 2745, 2753, 2736, 2735, 2733, 2731, 1848, 2740, 2738, 2786, 2784, 591,\n        588, 576, 569, 566, 2296, 1590, 537, 534, 526, 2276, 522, 2274, 545, 542, 539, 548, 1572, 1570, 481, 2245, 466,\n        2242, 462, 2239, 492, 485, 482, 2249, 496, 494, 1534, 1531, 1528, 1538, 413, 2196, 406, 2191, 2188, 425, 419,\n        2202, 415, 2199, 432, 430, 427, 1472, 1467, 1464, 433, 1476, 1474, 368, 367, 2160, 365, 2159, 362, 2157, 2155,\n        2152, 378, 377, 375, 2166, 372, 2165, 369, 2162, 383, 381, 379, 2168, 1419, 1418, 1416, 1414, 385, 1411, 384,\n        1423, 1422, 1420, 1424, 2461, 802, 2441, 2439, 790, 786, 783, 794, 2409, 2406, 2403, 750, 742, 738, 2414, 756,\n        753, 1720, 2367, 2365, 2362, 2359, 1663, 693, 691, 684, 2373, 680, 2370, 702, 699, 696, 704, 1690, 1687, 2337,\n        2336, 2334, 2332, 1624, 2329, 1622, 640, 637, 2344, 634, 2342, 630, 2340, 650, 648, 645, 2346, 655, 653, 1653,\n        1651, 1649, 1655, 2612, 2597, 2595, 2571, 2568, 2565, 2576, 2534, 2529, 2526, 1787, 2540, 2537, 907, 904, 900,\n        910, 2503, 2502, 2500, 2498, 1768, 2495, 1767, 2510, 2508, 2506, 869, 866, 863, 2513, 876, 874, 1782, 2720, 2713,\n        2711, 2697, 2694, 2691, 2702, 2672, 2670, 2664, 1828, 2678, 2675, 2647, 2646, 2644, 2642, 1823, 2639, 1822, 2654,\n        2652, 2650, 2657, 2771, 1855, 2765, 2762, 1850, 1849, 2751, 2749, 2747, 2754, 353, 2148, 344, 342, 336, 2142,\n        332, 2140, 345, 1375, 1373, 306, 2130, 299, 2128, 295, 2125, 319, 314, 311, 2132, 1354, 1352, 1349, 1356, 262,\n        257, 2101, 253, 2096, 2093, 274, 273, 267, 2107, 263, 2104, 280, 278, 275, 1316, 1311, 1308, 1320, 1318, 2052,\n        202, 2050, 2044, 2040, 219, 2063, 212, 2060, 208, 2055, 224, 221, 2066, 1260, 1258, 1252, 231, 1248, 229, 1266,\n        1264, 1261, 1268, 155, 1998, 153, 1996, 1994, 1991, 1988, 165, 164, 2007, 162, 2006, 159, 2003, 2000, 172, 171,\n        169, 2012, 166, 2010, 1186, 1184, 1182, 1179, 175, 1176, 173, 1192, 1191, 1189, 1187, 176, 1194, 1193, 2313,\n        2307, 2305, 592, 589, 2294, 2292, 2289, 578, 572, 568, 2297, 580, 1591, 2272, 2267, 2264, 1547, 538, 536, 529,\n        2278, 525, 2275, 547, 544, 541, 1574, 1571, 2237, 2235, 2229, 1493, 2225, 1489, 478, 2247, 470, 2244, 465, 2241,\n        493, 488, 484, 2250, 498, 495, 1536, 1533, 1530, 1539, 2187, 2186, 2184, 2182, 1432, 2179, 1430, 2176, 1427, 414,\n        412, 2197, 409, 2195, 405, 2193, 2190, 426, 424, 421, 2203, 418, 2201, 431, 429, 1473, 1471, 1469, 1466, 434,\n        1477, 1475, 2478, 2472, 2470, 2459, 2457, 2454, 2462, 803, 2437, 2432, 2429, 1726, 2443, 2440, 792, 789, 785,\n        2401, 2399, 2393, 1702, 2389, 1699, 2411, 2408, 2405, 745, 741, 2415, 758, 755, 1721, 2358, 2357, 2355, 2353,\n        1661, 2350, 1660, 2347, 1657, 2368, 2366, 2364, 2361, 1666, 690, 687, 2374, 683, 2372, 701, 698, 705, 1691, 1689,\n        2619, 2617, 2610, 2608, 2605, 2613, 2593, 2588, 2585, 1803, 2599, 2596, 2563, 2561, 2555, 1797, 2551, 1795, 2573,\n        2570, 2567, 2577, 2525, 2524, 2522, 2520, 1786, 2517, 1785, 2514, 1783, 2535, 2533, 2531, 2528, 1788, 2541, 2539,\n        906, 903, 911, 2721, 1844, 2715, 2712, 1838, 1836, 2699, 2696, 2693, 2703, 1827, 1826, 1824, 2673, 2671, 2669,\n        2666, 1829, 2679, 2677, 1858, 1857, 2772, 1854, 1853, 1851, 1856, 2766, 2764, 143, 1987, 139, 1986, 135, 133,\n        131, 1984, 128, 1983, 125, 1981, 138, 137, 136, 1985, 1133, 1132, 1130, 112, 110, 1974, 107, 1973, 104, 1971,\n        1969, 122, 121, 119, 117, 1977, 114, 1976, 124, 1115, 1114, 1112, 1110, 1117, 1116, 84, 83, 1953, 81, 1952, 78,\n        1950, 1948, 1945, 94, 93, 91, 1959, 88, 1958, 85, 1955, 99, 97, 95, 1961, 1086, 1085, 1083, 1081, 1078, 100,\n        1090, 1089, 1087, 1091, 49, 47, 1917, 44, 1915, 1913, 1910, 1907, 59, 1926, 56, 1925, 53, 1922, 1919, 66, 64,\n        1931, 61, 1929, 1042, 1040, 1038, 71, 1035, 70, 1032, 68, 1048, 1047, 1045, 1043, 1050, 1049, 12, 10, 1869, 1867,\n        1864, 1861, 21, 1880, 19, 1877, 1874, 1871, 28, 1888, 25, 1886, 22, 1883, 982, 980, 977, 974, 32, 30, 991, 989,\n        987, 984, 34, 995, 994, 992, 2151, 2150, 2147, 2146, 2144, 356, 355, 354, 2149, 2139, 2138, 2136, 2134, 1359,\n        343, 341, 338, 2143, 335, 2141, 348, 347, 346, 1376, 1374, 2124, 2123, 2121, 2119, 1326, 2116, 1324, 310, 308,\n        305, 2131, 302, 2129, 298, 2127, 320, 318, 316, 313, 2133, 322, 321, 1355, 1353, 1351, 1357, 2092, 2091, 2089,\n        2087, 1276, 2084, 1274, 2081, 1271, 259, 2102, 256, 2100, 252, 2098, 2095, 272, 269, 2108, 266, 2106, 281, 279,\n        277, 1317, 1315, 1313, 1310, 282, 1321, 1319, 2039, 2037, 2035, 2032, 1203, 2029, 1200, 1197, 207, 2053, 205,\n        2051, 201, 2049, 2046, 2043, 220, 218, 2064, 215, 2062, 211, 2059, 228, 226, 223, 2069, 1259, 1257, 1254, 232,\n        1251, 230, 1267, 1265, 1263, 2316, 2315, 2312, 2311, 2309, 2314, 2304, 2303, 2301, 2299, 1593, 2308, 2306, 590,\n        2288, 2287, 2285, 2283, 1578, 2280, 1577, 2295, 2293, 2291, 579, 577, 574, 571, 2298, 582, 581, 1592, 2263, 2262,\n        2260, 2258, 1545, 2255, 1544, 2252, 1541, 2273, 2271, 2269, 2266, 1550, 535, 532, 2279, 528, 2277, 546, 543, 549,\n        1575, 1573, 2224, 2222, 2220, 1486, 2217, 1485, 2214, 1482, 1479, 2238, 2236, 2234, 2231, 1496, 2228, 1492, 480,\n        477, 2248, 473, 2246, 469, 2243, 490, 487, 2251, 497, 1537, 1535, 1532, 2477, 2476, 2474, 2479, 2469, 2468, 2466,\n        2464, 1730, 2473, 2471, 2453, 2452, 2450, 2448, 1729, 2445, 1728, 2460, 2458, 2456, 2463, 805, 804, 2428, 2427,\n        2425, 2423, 1725, 2420, 1724, 2417, 1722, 2438, 2436, 2434, 2431, 1727, 2444, 2442, 793, 791, 788, 795, 2388,\n        2386, 2384, 1697, 2381, 1696, 2378, 1694, 1692, 2402, 2400, 2398, 2395, 1703, 2392, 1701, 2412, 2410, 2407, 751,\n        748, 744, 2416, 759, 757, 1807, 2620, 2618, 1806, 1805, 2611, 2609, 2607, 2614, 1802, 1801, 1799, 2594, 2592,\n        2590, 2587, 1804, 2600, 2598, 1794, 1793, 1791, 1789, 2564, 2562, 2560, 2557, 1798, 2554, 1796, 2574, 2572, 2569,\n        2578, 1847, 1846, 2722, 1843, 1842, 1840, 1845, 2716, 2714, 1835, 1834, 1832, 1830, 1839, 1837, 2700, 2698, 2695,\n        2704, 1817, 1811, 1810, 897, 862, 1777, 829, 826, 838, 1760, 1758, 808, 2481, 1741, 1740, 1738, 1743, 2624, 1818,\n        2726, 2776, 782, 740, 737, 1715, 686, 679, 695, 1682, 1680, 639, 628, 2339, 647, 644, 1645, 1643, 1640, 1648,\n        602, 600, 597, 595, 2320, 593, 2318, 609, 607, 604, 1611, 1610, 1608, 1606, 613, 1615, 1613, 2328, 926, 924, 892,\n        886, 899, 857, 850, 2505, 1778, 824, 823, 821, 819, 2488, 818, 2486, 833, 831, 828, 840, 1761, 1759, 2649, 2632,\n        2630, 2746, 2734, 2732, 2782, 2781, 570, 567, 1587, 531, 527, 523, 540, 1566, 1564, 476, 467, 463, 2240, 486,\n        483, 1524, 1521, 1518, 1529, 411, 403, 2192, 399, 2189, 423, 416, 1462, 1457, 1454, 428, 1468, 1465, 2210, 366,\n        363, 2158, 360, 2156, 357, 2153, 376, 373, 370, 2163, 1410, 1409, 1407, 1405, 382, 1402, 380, 1417, 1415, 1412,\n        1421, 2175, 2174, 777, 774, 771, 784, 732, 725, 722, 2404, 743, 1716, 676, 674, 668, 2363, 665, 2360, 685, 1684,\n        1681, 626, 624, 622, 2335, 620, 2333, 617, 2330, 641, 635, 649, 1646, 1644, 1642, 2566, 928, 925, 2530, 2527,\n        894, 891, 888, 2501, 2499, 2496, 858, 856, 854, 851, 1779, 2692, 2668, 2665, 2645, 2643, 2640, 2651, 2768, 2759,\n        2757, 2744, 2743, 2741, 2748, 352, 1382, 340, 337, 333, 1371, 1369, 307, 300, 296, 2126, 315, 312, 1347, 1342,\n        1350, 261, 258, 250, 2097, 246, 2094, 271, 268, 264, 1306, 1301, 1298, 276, 1312, 1309, 2115, 203, 2048, 195,\n        2045, 191, 2041, 213, 209, 2056, 1246, 1244, 1238, 225, 1234, 222, 1256, 1253, 1249, 1262, 2080, 2079, 154, 1997,\n        150, 1995, 147, 1992, 1989, 163, 160, 2004, 156, 2001, 1175, 1174, 1172, 1170, 1167, 170, 1164, 167, 1185, 1183,\n        1180, 1177, 174, 1190, 1188, 2025, 2024, 2022, 587, 586, 564, 559, 556, 2290, 573, 1588, 520, 518, 512, 2268,\n        508, 2265, 530, 1568, 1565, 461, 457, 2233, 450, 2230, 446, 2226, 479, 471, 489, 1526, 1523, 1520, 397, 395,\n        2185, 392, 2183, 389, 2180, 2177, 410, 2194, 402, 422, 1463, 1461, 1459, 1456, 1470, 2455, 799, 2433, 2430, 779,\n        776, 773, 2397, 2394, 2390, 734, 728, 724, 746, 1717, 2356, 2354, 2351, 2348, 1658, 677, 675, 673, 670, 667, 688,\n        1685, 1683, 2606, 2589, 2586, 2559, 2556, 2552, 927, 2523, 2521, 2518, 2515, 1784, 2532, 895, 893, 890, 2718,\n        2709, 2707, 2689, 2687, 2684, 2663, 2662, 2660, 2658, 1825, 2667, 2769, 1852, 2760, 2758, 142, 141, 1139, 1138,\n        134, 132, 129, 126, 1982, 1129, 1128, 1126, 1131, 113, 111, 108, 105, 1972, 101, 1970, 120, 118, 115, 1109, 1108,\n        1106, 1104, 123, 1113, 1111, 82, 79, 1951, 75, 1949, 72, 1946, 92, 89, 86, 1956, 1077, 1076, 1074, 1072, 98,\n        1069, 96, 1084, 1082, 1079, 1088, 1968, 1967, 48, 45, 1916, 42, 1914, 39, 1911, 1908, 60, 57, 54, 1923, 50, 1920,\n        1031, 1030, 1028, 1026, 67, 1023, 65, 1020, 62, 1041, 1039, 1036, 1033, 69, 1046, 1044, 1944, 1943, 1941, 11, 9,\n        1868, 7, 1865, 1862, 1859, 20, 1878, 16, 1875, 13, 1872, 970, 968, 966, 963, 29, 960, 26, 23, 983, 981, 978, 975,\n        33, 971, 31, 990, 988, 985, 1906, 1904, 1902, 993, 351, 2145, 1383, 331, 330, 328, 326, 2137, 323, 2135, 339,\n        1372, 1370, 294, 293, 291, 289, 2122, 286, 2120, 283, 2117, 309, 303, 317, 1348, 1346, 1344, 245, 244, 242, 2090,\n        239, 2088, 236, 2085, 2082, 260, 2099, 249, 270, 1307, 1305, 1303, 1300, 1314, 189, 2038, 186, 2036, 183, 2033,\n        2030, 2026, 206, 198, 2047, 194, 216, 1247, 1245, 1243, 1240, 227, 1237, 1255, 2310, 2302, 2300, 2286, 2284,\n        2281, 565, 563, 561, 558, 575, 1589, 2261, 2259, 2256, 2253, 1542, 521, 519, 517, 514, 2270, 511, 533, 1569,\n        1567, 2223, 2221, 2218, 2215, 1483, 2211, 1480, 459, 456, 453, 2232, 449, 474, 491, 1527, 1525, 1522, 2475, 2467,\n        2465, 2451, 2449, 2446, 801, 800, 2426, 2424, 2421, 2418, 1723, 2435, 780, 778, 775, 2387, 2385, 2382, 2379,\n        1695, 2375, 1693, 2396, 735, 733, 730, 727, 749, 1718, 2616, 2615, 2604, 2603, 2601, 2584, 2583, 2581, 2579,\n        1800, 2591, 2550, 2549, 2547, 2545, 1792, 2542, 1790, 2558, 929, 2719, 1841, 2710, 2708, 1833, 1831, 2690, 2688,\n        2686, 1815, 1809, 1808, 1774, 1756, 1754, 1737, 1736, 1734, 1739, 1816, 1711, 1676, 1674, 633, 629, 1638, 1636,\n        1633, 1641, 598, 1605, 1604, 1602, 1600, 605, 1609, 1607, 2327, 887, 853, 1775, 822, 820, 1757, 1755, 1584, 524,\n        1560, 1558, 468, 464, 1514, 1511, 1508, 1519, 408, 404, 400, 1452, 1447, 1444, 417, 1458, 1455, 2208, 364, 361,\n        358, 2154, 1401, 1400, 1398, 1396, 374, 1393, 371, 1408, 1406, 1403, 1413, 2173, 2172, 772, 726, 723, 1712, 672,\n        669, 666, 682, 1678, 1675, 625, 623, 621, 618, 2331, 636, 632, 1639, 1637, 1635, 920, 918, 884, 880, 889, 849,\n        848, 847, 846, 2497, 855, 852, 1776, 2641, 2742, 2787, 1380, 334, 1367, 1365, 301, 297, 1340, 1338, 1335, 1343,\n        255, 251, 247, 1296, 1291, 1288, 265, 1302, 1299, 2113, 204, 196, 192, 2042, 1232, 1230, 1224, 214, 1220, 210,\n        1242, 1239, 1235, 1250, 2077, 2075, 151, 148, 1993, 144, 1990, 1163, 1162, 1160, 1158, 1155, 161, 1152, 157,\n        1173, 1171, 1168, 1165, 168, 1181, 1178, 2021, 2020, 2018, 2023, 585, 560, 557, 1585, 516, 509, 1562, 1559, 458,\n        447, 2227, 472, 1516, 1513, 1510, 398, 396, 393, 390, 2181, 386, 2178, 407, 1453, 1451, 1449, 1446, 420, 1460,\n        2209, 769, 764, 720, 712, 2391, 729, 1713, 664, 663, 661, 659, 2352, 656, 2349, 671, 1679, 1677, 2553, 922, 919,\n        2519, 2516, 885, 883, 881, 2685, 2661, 2659, 2767, 2756, 2755, 140, 1137, 1136, 130, 127, 1125, 1124, 1122, 1127,\n        109, 106, 102, 1103, 1102, 1100, 1098, 116, 1107, 1105, 1980, 80, 76, 73, 1947, 1068, 1067, 1065, 1063, 90, 1060,\n        87, 1075, 1073, 1070, 1080, 1966, 1965, 46, 43, 40, 1912, 36, 1909, 1019, 1018, 1016, 1014, 58, 1011, 55, 1008,\n        51, 1029, 1027, 1024, 1021, 63, 1037, 1034, 1940, 1939, 1937, 1942, 8, 1866, 4, 1863, 1, 1860, 956, 954, 952,\n        949, 946, 17, 14, 969, 967, 964, 961, 27, 957, 24, 979, 976, 972, 1901, 1900, 1898, 1896, 986, 1905, 1903, 350,\n        349, 1381, 329, 327, 324, 1368, 1366, 292, 290, 287, 284, 2118, 304, 1341, 1339, 1337, 1345, 243, 240, 237, 2086,\n        233, 2083, 254, 1297, 1295, 1293, 1290, 1304, 2114, 190, 187, 184, 2034, 180, 2031, 177, 2027, 199, 1233, 1231,\n        1229, 1226, 217, 1223, 1241, 2078, 2076, 584, 555, 554, 552, 550, 2282, 562, 1586, 507, 506, 504, 502, 2257, 499,\n        2254, 515, 1563, 1561, 445, 443, 441, 2219, 438, 2216, 435, 2212, 460, 454, 475, 1517, 1515, 1512, 2447, 798,\n        797, 2422, 2419, 770, 768, 766, 2383, 2380, 2376, 721, 719, 717, 714, 731, 1714, 2602, 2582, 2580, 2548, 2546,\n        2543, 923, 921, 2717, 2706, 2705, 2683, 2682, 2680, 1771, 1752, 1750, 1733, 1732, 1731, 1735, 1814, 1707, 1670,\n        1668, 1631, 1629, 1626, 1634, 1599, 1598, 1596, 1594, 1603, 1601, 2326, 1772, 1753, 1751, 1581, 1554, 1552, 1504,\n        1501, 1498, 1509, 1442, 1437, 1434, 401, 1448, 1445, 2206, 1392, 1391, 1389, 1387, 1384, 359, 1399, 1397, 1394,\n        1404, 2171, 2170, 1708, 1672, 1669, 619, 1632, 1630, 1628, 1773, 1378, 1363, 1361, 1333, 1328, 1336, 1286, 1281,\n        1278, 248, 1292, 1289, 2111, 1218, 1216, 1210, 197, 1206, 193, 1228, 1225, 1221, 1236, 2073, 2071, 1151, 1150,\n        1148, 1146, 152, 1143, 149, 1140, 145, 1161, 1159, 1156, 1153, 158, 1169, 1166, 2017, 2016, 2014, 2019, 1582,\n        510, 1556, 1553, 452, 448, 1506, 1500, 394, 391, 387, 1443, 1441, 1439, 1436, 1450, 2207, 765, 716, 713, 1709,\n        662, 660, 657, 1673, 1671, 916, 914, 879, 878, 877, 882, 1135, 1134, 1121, 1120, 1118, 1123, 1097, 1096, 1094,\n        1092, 103, 1101, 1099, 1979, 1059, 1058, 1056, 1054, 77, 1051, 74, 1066, 1064, 1061, 1071, 1964, 1963, 1007,\n        1006, 1004, 1002, 999, 41, 996, 37, 1017, 1015, 1012, 1009, 52, 1025, 1022, 1936, 1935, 1933, 1938, 942, 940,\n        938, 935, 932, 5, 2, 955, 953, 950, 947, 18, 943, 15, 965, 962, 958, 1895, 1894, 1892, 1890, 973, 1899, 1897,\n        1379, 325, 1364, 1362, 288, 285, 1334, 1332, 1330, 241, 238, 234, 1287, 1285, 1283, 1280, 1294, 2112, 188, 185,\n        181, 178, 2028, 1219, 1217, 1215, 1212, 200, 1209, 1227, 2074, 2072, 583, 553, 551, 1583, 505, 503, 500, 513,\n        1557, 1555, 444, 442, 439, 436, 2213, 455, 451, 1507, 1505, 1502, 796, 763, 762, 760, 767, 711, 710, 708, 706,\n        2377, 718, 715, 1710, 2544, 917, 915, 2681, 1627, 1597, 1595, 2325, 1769, 1749, 1747, 1499, 1438, 1435, 2204,\n        1390, 1388, 1385, 1395, 2169, 2167, 1704, 1665, 1662, 1625, 1623, 1620, 1770, 1329, 1282, 1279, 2109, 1214, 1207,\n        1222, 2068, 2065, 1149, 1147, 1144, 1141, 146, 1157, 1154, 2013, 2011, 2008, 2015, 1579, 1549, 1546, 1495, 1487,\n        1433, 1431, 1428, 1425, 388, 1440, 2205, 1705, 658, 1667, 1664, 1119, 1095, 1093, 1978, 1057, 1055, 1052, 1062,\n        1962, 1960, 1005, 1003, 1000, 997, 38, 1013, 1010, 1932, 1930, 1927, 1934, 941, 939, 936, 933, 6, 930, 3, 951,\n        948, 944, 1889, 1887, 1884, 1881, 959, 1893, 1891, 35, 1377, 1360, 1358, 1327, 1325, 1322, 1331, 1277, 1275,\n        1272, 1269, 235, 1284, 2110, 1205, 1204, 1201, 1198, 182, 1195, 179, 1213, 2070, 2067, 1580, 501, 1551, 1548,\n        440, 437, 1497, 1494, 1490, 1503, 761, 709, 707, 1706, 913, 912, 2198, 1386, 2164, 2161, 1621, 1766, 2103, 1208,\n        2058, 2054, 1145, 1142, 2005, 2002, 1999, 2009, 1488, 1429, 1426, 2200, 1698, 1659, 1656, 1975, 1053, 1957, 1954,\n        1001, 998, 1924, 1921, 1918, 1928, 937, 934, 931, 1879, 1876, 1873, 1870, 945, 1885, 1882, 1323, 1273, 1270,\n        2105, 1202, 1199, 1196, 1211, 2061, 2057, 1576, 1543, 1540, 1484, 1481, 1478, 1491, 1700\n    ]);\n    return PDF417Common;\n}());\nexport default PDF417Common;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA,mCAAmC;;;;AAYnC,2BAA2B;AAC3B;AACA,+BAA+B;AAC/B,qDAAqD;AACrD;AAfA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAMA;;;CAGC,GACD,IAAI,eAA8B;IAC9B,SAAS,gBACT;IACA,aAAa,SAAS,CAAC,YAAY,GAAG,YACtC;IACA;;;;KAIC,GACD,cAAc;IACd,aAAa,cAAc,GAAG,SAAU,cAAc;QAClD,OAAO,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC;IACzB;IACA,aAAa,UAAU,GAAG,SAAU,IAAI;QACpC,IAAI,KAAK;QACT,IAAI,QAAQ,QAAQ,CAAC,KAAK,MAAM,EAAE;YAC9B,OAAO,aAAa,eAAe;QACvC;QACA,IAAI,SAAS,IAAI,WAAW,KAAK,MAAM;QACvC,IAAI,IAAI;QACR,IAAI;YACA,IAAK,IAAI,SAAS,SAAS,OAAO,WAAW,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,WAAW,OAAO,IAAI,GAAI;gBAClG,IAAI,UAAU,SAAS,KAAK;gBAC5B,MAAM,CAAC,IAAI,GAAG;YAClB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,OAAO,MAAM,GAAG,GAAG,IAAI,CAAC;YACpE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA;;;KAGC,GACD,aAAa,WAAW,GAAG,SAAU,OAAO,KAAK,GAAN;QACvC,IAAI,IAAI,sKAAA,CAAA,UAAM,CAAC,YAAY,CAAC,aAAa,YAAY,EAAE,SAAS;QAChE,IAAI,IAAI,GAAG;YACP,OAAO,CAAC;QACZ;QACA,OAAO,CAAC,aAAa,cAAc,CAAC,EAAE,GAAG,CAAC,IAAI,aAAa,mBAAmB;IAClF;IACA,aAAa,mBAAmB,GAAG;IACnC,oCAAoC;IACpC,aAAa,wBAAwB,GAAG,aAAa,mBAAmB,GAAG;IAC3E,aAAa,mBAAmB,GAAG;IACnC,aAAa,mBAAmB,GAAG;IACnC,wFAAwF;IACxF,qEAAqE;IACrE,aAAa,mBAAmB,GAAG;IACnC,aAAa,uBAAuB,GAAG;IACvC,aAAa,cAAc,GAAG;IAC9B,aAAa,eAAe,GAAG,IAAI,WAAW,EAAE;IAChD;;;;KAIC,GACD,aAAa,YAAY,GAAG,WAAW,IAAI,CAAC;QACxC;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QAAS;QACnG;QAAS;QAAS;KACrB;IACD;;KAEC,GACD,aAAa,cAAc,GAAG,WAAW,IAAI,CAAC;QAC1C;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC3G;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAC5G;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAC5G;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAC5G;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAC7G;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAC1G;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QACzG;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAM;QAC3G;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QACzG;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QACzG;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAC1G;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAC1G;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QACxG;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAC1G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAC1G;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAC3G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QACvG;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAC1G;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAC3G;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC7G;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QACxG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QACzG;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QACxG;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAAI;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAI;QAAI;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAI;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACxG;QAAM;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAI;QAC1G;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAC5G;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAC3G;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QACxG;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAC1G;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAC3G;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QACzG;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAC1G;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAC7G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QACxG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACxG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QACxG;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAC7G;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QACzG;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAC3G;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAC3G;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QACxG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QACzG;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QACzG;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAC5G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAC3G;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QACxG;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QACxG;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAC5G;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QACxG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAC1G;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAC5G;QAAM;QAAM;QAAK;QAAM;QAAM;QAAI;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAI;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAI;QAAI;QAAI;QAAM;QAAI;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAC9G;QAAM;QAAG;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAI;QAAM;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAC7G;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QACzG;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAC5G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAC1G;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QACvG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAC1G;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAC3G;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAC5G;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAC1G;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAC1G;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QACxG;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAC5G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QACzG;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAC5G;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAC5G;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAI;QAAI;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAC5G;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAI;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAC1G;QAAI;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAG;QAAM;QAAG;QAAM;QAAG;QAAM;QAAK;QAAK;QACzG;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAC3G;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAC5G;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAC1G;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAC7G;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QACzG;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACxG;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QACzG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACzG;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAK;QAAI;QAAK;QAAI;QAAM;QAAM;QAAM;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QACzG;QAAK;QAAK;QAAK;QAAG;QAAG;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QACxG;QAAM;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAC3G;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QACzG;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC1G;QAAM;QAAK;QAAK;QAAM;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACxG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC1G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAG;QAC1G;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAI;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QACxG;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC3G;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC5G;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;QAAM;QACvG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACvF;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/detector/PDF417DetectorResult.js"], "sourcesContent": ["/*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// import java.util.List;\n/**\n * <AUTHOR>\n */\nvar PDF417DetectorResult = /** @class */ (function () {\n    function PDF417DetectorResult(bits, points) {\n        this.bits = bits;\n        this.points = points;\n    }\n    PDF417DetectorResult.prototype.getBits = function () {\n        return this.bits;\n    };\n    PDF417DetectorResult.prototype.getPoints = function () {\n        return this.points;\n    };\n    return PDF417DetectorResult;\n}());\nexport default PDF417DetectorResult;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA,yBAAyB;AACzB;;CAEC;;;AACD,IAAI,uBAAsC;IACtC,SAAS,qBAAqB,IAAI,EAAE,MAAM;QACtC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,qBAAqB,SAAS,CAAC,OAAO,GAAG;QACrC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,qBAAqB,SAAS,CAAC,SAAS,GAAG;QACvC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/detector/Detector.js"], "sourcesContent": ["/*\n* Copyright 2009 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\nimport System from '../../util/System';\nimport Arrays from '../../util/Arrays';\nimport PDF417DetectorResult from './PDF417DetectorResult';\n// import java.util.ArrayList;\n// import java.util.Arrays;\n// import java.util.List;\n// import java.util.Map;\n/**\n * <p>Encapsulates logic that can detect a PDF417 Code in an image, even if the\n * PDF417 Code is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Grau\n */\nvar Detector = /** @class */ (function () {\n    function Detector() {\n    }\n    /**\n     * <p>Detects a PDF417 Code in an image. Only checks 0 and 180 degree rotations.</p>\n     *\n     * @param image barcode image to decode\n     * @param hints optional hints to detector\n     * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n     * be found and returned\n     * @return {@link PDF417DetectorResult} encapsulating results of detecting a PDF417 code\n     * @throws NotFoundException if no PDF417 Code can be found\n     */\n    Detector.detectMultiple = function (image, hints, multiple) {\n        // TODO detection improvement, tryHarder could try several different luminance thresholds/blackpoints or even\n        // different binarizers\n        // boolean tryHarder = hints != null && hints.containsKey(DecodeHintType.TRY_HARDER);\n        var bitMatrix = image.getBlackMatrix();\n        var barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n        if (!barcodeCoordinates.length) {\n            bitMatrix = bitMatrix.clone();\n            bitMatrix.rotate180();\n            barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n        }\n        return new PDF417DetectorResult(bitMatrix, barcodeCoordinates);\n    };\n    /**\n     * Detects PDF417 codes in an image. Only checks 0 degree rotation\n     * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n     * be found and returned\n     * @param bitMatrix bit matrix to detect barcodes in\n     * @return List of ResultPoint arrays containing the coordinates of found barcodes\n     */\n    Detector.detect = function (multiple, bitMatrix) {\n        var e_1, _a;\n        var barcodeCoordinates = new Array();\n        var row = 0;\n        var column = 0;\n        var foundBarcodeInRow = false;\n        while (row < bitMatrix.getHeight()) {\n            var vertices = Detector.findVertices(bitMatrix, row, column);\n            if (vertices[0] == null && vertices[3] == null) {\n                if (!foundBarcodeInRow) {\n                    // we didn't find any barcode so that's the end of searching\n                    break;\n                }\n                // we didn't find a barcode starting at the given column and row. Try again from the first column and slightly\n                // below the lowest barcode we found so far.\n                foundBarcodeInRow = false;\n                column = 0;\n                try {\n                    for (var barcodeCoordinates_1 = (e_1 = void 0, __values(barcodeCoordinates)), barcodeCoordinates_1_1 = barcodeCoordinates_1.next(); !barcodeCoordinates_1_1.done; barcodeCoordinates_1_1 = barcodeCoordinates_1.next()) {\n                        var barcodeCoordinate = barcodeCoordinates_1_1.value;\n                        if (barcodeCoordinate[1] != null) {\n                            row = Math.trunc(Math.max(row, barcodeCoordinate[1].getY()));\n                        }\n                        if (barcodeCoordinate[3] != null) {\n                            row = Math.max(row, Math.trunc(barcodeCoordinate[3].getY()));\n                        }\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (barcodeCoordinates_1_1 && !barcodeCoordinates_1_1.done && (_a = barcodeCoordinates_1.return)) _a.call(barcodeCoordinates_1);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n                row += Detector.ROW_STEP;\n                continue;\n            }\n            foundBarcodeInRow = true;\n            barcodeCoordinates.push(vertices);\n            if (!multiple) {\n                break;\n            }\n            // if we didn't find a right row indicator column, then continue the search for the next barcode after the\n            // start pattern of the barcode just found.\n            if (vertices[2] != null) {\n                column = Math.trunc(vertices[2].getX());\n                row = Math.trunc(vertices[2].getY());\n            }\n            else {\n                column = Math.trunc(vertices[4].getX());\n                row = Math.trunc(vertices[4].getY());\n            }\n        }\n        return barcodeCoordinates;\n    };\n    /**\n     * Locate the vertices and the codewords area of a black blob using the Start\n     * and Stop patterns as locators.\n     *\n     * @param matrix the scanned barcode image.\n     * @return an array containing the vertices:\n     *           vertices[0] x, y top left barcode\n     *           vertices[1] x, y bottom left barcode\n     *           vertices[2] x, y top right barcode\n     *           vertices[3] x, y bottom right barcode\n     *           vertices[4] x, y top left codeword area\n     *           vertices[5] x, y bottom left codeword area\n     *           vertices[6] x, y top right codeword area\n     *           vertices[7] x, y bottom right codeword area\n     */\n    Detector.findVertices = function (matrix, startRow, startColumn) {\n        var height = matrix.getHeight();\n        var width = matrix.getWidth();\n        // const result = new ResultPoint[8];\n        var result = new Array(8);\n        Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.START_PATTERN), Detector.INDEXES_START_PATTERN);\n        if (result[4] != null) {\n            startColumn = Math.trunc(result[4].getX());\n            startRow = Math.trunc(result[4].getY());\n        }\n        Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.STOP_PATTERN), Detector.INDEXES_STOP_PATTERN);\n        return result;\n    };\n    Detector.copyToResult = function (result, tmpResult, destinationIndexes) {\n        for (var i = 0; i < destinationIndexes.length; i++) {\n            result[destinationIndexes[i]] = tmpResult[i];\n        }\n    };\n    Detector.findRowsWithPattern = function (matrix, height, width, startRow, startColumn, pattern) {\n        // const result = new ResultPoint[4];\n        var result = new Array(4);\n        var found = false;\n        var counters = new Int32Array(pattern.length);\n        for (; startRow < height; startRow += Detector.ROW_STEP) {\n            var loc = Detector.findGuardPattern(matrix, startColumn, startRow, width, false, pattern, counters);\n            if (loc != null) {\n                while (startRow > 0) {\n                    var previousRowLoc = Detector.findGuardPattern(matrix, startColumn, --startRow, width, false, pattern, counters);\n                    if (previousRowLoc != null) {\n                        loc = previousRowLoc;\n                    }\n                    else {\n                        startRow++;\n                        break;\n                    }\n                }\n                result[0] = new ResultPoint(loc[0], startRow);\n                result[1] = new ResultPoint(loc[1], startRow);\n                found = true;\n                break;\n            }\n        }\n        var stopRow = startRow + 1;\n        // Last row of the current symbol that contains pattern\n        if (found) {\n            var skippedRowCount = 0;\n            var previousRowLoc = Int32Array.from([Math.trunc(result[0].getX()), Math.trunc(result[1].getX())]);\n            for (; stopRow < height; stopRow++) {\n                var loc = Detector.findGuardPattern(matrix, previousRowLoc[0], stopRow, width, false, pattern, counters);\n                // a found pattern is only considered to belong to the same barcode if the start and end positions\n                // don't differ too much. Pattern drift should be not bigger than two for consecutive rows. With\n                // a higher number of skipped rows drift could be larger. To keep it simple for now, we allow a slightly\n                // larger drift and don't check for skipped rows.\n                if (loc != null &&\n                    Math.abs(previousRowLoc[0] - loc[0]) < Detector.MAX_PATTERN_DRIFT &&\n                    Math.abs(previousRowLoc[1] - loc[1]) < Detector.MAX_PATTERN_DRIFT) {\n                    previousRowLoc = loc;\n                    skippedRowCount = 0;\n                }\n                else {\n                    if (skippedRowCount > Detector.SKIPPED_ROW_COUNT_MAX) {\n                        break;\n                    }\n                    else {\n                        skippedRowCount++;\n                    }\n                }\n            }\n            stopRow -= skippedRowCount + 1;\n            result[2] = new ResultPoint(previousRowLoc[0], stopRow);\n            result[3] = new ResultPoint(previousRowLoc[1], stopRow);\n        }\n        if (stopRow - startRow < Detector.BARCODE_MIN_HEIGHT) {\n            Arrays.fill(result, null);\n        }\n        return result;\n    };\n    /**\n     * @param matrix row of black/white values to search\n     * @param column x position to start search\n     * @param row y position to start search\n     * @param width the number of pixels to search on this row\n     * @param pattern pattern of counts of number of black and white pixels that are\n     *                 being searched for as a pattern\n     * @param counters array of counters, as long as pattern, to re-use\n     * @return start/end horizontal offset of guard pattern, as an array of two ints.\n     */\n    Detector.findGuardPattern = function (matrix, column, row, width, whiteFirst, pattern, counters) {\n        Arrays.fillWithin(counters, 0, counters.length, 0);\n        var patternStart = column;\n        var pixelDrift = 0;\n        // if there are black pixels left of the current pixel shift to the left, but only for MAX_PIXEL_DRIFT pixels\n        while (matrix.get(patternStart, row) && patternStart > 0 && pixelDrift++ < Detector.MAX_PIXEL_DRIFT) {\n            patternStart--;\n        }\n        var x = patternStart;\n        var counterPosition = 0;\n        var patternLength = pattern.length;\n        for (var isWhite = whiteFirst; x < width; x++) {\n            var pixel = matrix.get(x, row);\n            if (pixel !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n                        return new Int32Array([patternStart, x]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        if (counterPosition === patternLength - 1 &&\n            Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n            return new Int32Array([patternStart, x - 1]);\n        }\n        return null;\n    };\n    /**\n     * Determines how closely a set of observed counts of runs of black/white\n     * values matches a given target pattern. This is reported as the ratio of\n     * the total variance from the expected pattern proportions across all\n     * pattern elements, to the length of the pattern.\n     *\n     * @param counters observed counters\n     * @param pattern expected pattern\n     * @param maxIndividualVariance The most any counter can differ before we give up\n     * @return ratio of total variance between counters and pattern compared to total pattern size\n     */\n    Detector.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n        var numCounters = counters.length;\n        var total = 0;\n        var patternLength = 0;\n        for (var i = 0; i < numCounters; i++) {\n            total += counters[i];\n            patternLength += pattern[i];\n        }\n        if (total < patternLength) {\n            // If we don't even have one pixel per unit of bar width, assume this\n            // is too small to reliably match, so fail:\n            return /*Float.POSITIVE_INFINITY*/ Infinity;\n        }\n        // We're going to fake floating-point math in integers. We just need to use more bits.\n        // Scale up patternLength so that intermediate values below like scaledCounter will have\n        // more \"significant digits\".\n        var unitBarWidth = total / patternLength;\n        maxIndividualVariance *= unitBarWidth;\n        var totalVariance = 0.0;\n        for (var x = 0; x < numCounters; x++) {\n            var counter = counters[x];\n            var scaledPattern = pattern[x] * unitBarWidth;\n            var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n            if (variance > maxIndividualVariance) {\n                return /*Float.POSITIVE_INFINITY*/ Infinity;\n            }\n            totalVariance += variance;\n        }\n        return totalVariance / total;\n    };\n    Detector.INDEXES_START_PATTERN = Int32Array.from([0, 4, 1, 5]);\n    Detector.INDEXES_STOP_PATTERN = Int32Array.from([6, 2, 7, 3]);\n    Detector.MAX_AVG_VARIANCE = 0.42;\n    Detector.MAX_INDIVIDUAL_VARIANCE = 0.8;\n    // B S B S B S B S Bar/Space pattern\n    // 11111111 0 1 0 1 0 1 000\n    Detector.START_PATTERN = Int32Array.from([8, 1, 1, 1, 1, 1, 1, 3]);\n    // 1111111 0 1 000 1 0 1 00 1\n    Detector.STOP_PATTERN = Int32Array.from([7, 1, 1, 3, 1, 1, 1, 2, 1]);\n    Detector.MAX_PIXEL_DRIFT = 3;\n    Detector.MAX_PATTERN_DRIFT = 5;\n    // if we set the value too low, then we don't detect the correct height of the bar if the start patterns are damaged.\n    // if we set the value too high, then we might detect the start pattern from a neighbor barcode.\n    Detector.SKIPPED_ROW_COUNT_MAX = 25;\n    // A PDF471 barcode should have at least 3 rows, with each row being >= 3 times the module width. Therefore it should be at least\n    // 9 pixels tall. To be conservative, we use about half the size to ensure we don't miss it.\n    Detector.ROW_STEP = 5;\n    Detector.BARCODE_MIN_HEIGHT = 10;\n    return Detector;\n}());\nexport default Detector;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAYA,6CAA6C;AAC7C,uCAAuC;AACvC;AACA;AACA;AACA;AAhBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;AAOA,8BAA8B;AAC9B,2BAA2B;AAC3B,yBAAyB;AACzB,wBAAwB;AACxB;;;;;;;CAOC,GACD,IAAI,WAA0B;IAC1B,SAAS,YACT;IACA;;;;;;;;;KASC,GACD,SAAS,cAAc,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,QAAQ;QACtD,6GAA6G;QAC7G,uBAAuB;QACvB,qFAAqF;QACrF,IAAI,YAAY,MAAM,cAAc;QACpC,IAAI,qBAAqB,SAAS,MAAM,CAAC,UAAU;QACnD,IAAI,CAAC,mBAAmB,MAAM,EAAE;YAC5B,YAAY,UAAU,KAAK;YAC3B,UAAU,SAAS;YACnB,qBAAqB,SAAS,MAAM,CAAC,UAAU;QACnD;QACA,OAAO,IAAI,kMAAA,CAAA,UAAoB,CAAC,WAAW;IAC/C;IACA;;;;;;KAMC,GACD,SAAS,MAAM,GAAG,SAAU,QAAQ,EAAE,SAAS;QAC3C,IAAI,KAAK;QACT,IAAI,qBAAqB,IAAI;QAC7B,IAAI,MAAM;QACV,IAAI,SAAS;QACb,IAAI,oBAAoB;QACxB,MAAO,MAAM,UAAU,SAAS,GAAI;YAChC,IAAI,WAAW,SAAS,YAAY,CAAC,WAAW,KAAK;YACrD,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,QAAQ,CAAC,EAAE,IAAI,MAAM;gBAC5C,IAAI,CAAC,mBAAmB;oBAEpB;gBACJ;gBACA,8GAA8G;gBAC9G,4CAA4C;gBAC5C,oBAAoB;gBACpB,SAAS;gBACT,IAAI;oBACA,IAAK,IAAI,uBAAuB,CAAC,MAAM,KAAK,GAAG,SAAS,mBAAmB,GAAG,yBAAyB,qBAAqB,IAAI,IAAI,CAAC,uBAAuB,IAAI,EAAE,yBAAyB,qBAAqB,IAAI,GAAI;wBACpN,IAAI,oBAAoB,uBAAuB,KAAK;wBACpD,IAAI,iBAAiB,CAAC,EAAE,IAAI,MAAM;4BAC9B,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC,EAAE,CAAC,IAAI;wBAC5D;wBACA,IAAI,iBAAiB,CAAC,EAAE,IAAI,MAAM;4BAC9B,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,IAAI;wBAC5D;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,0BAA0B,CAAC,uBAAuB,IAAI,IAAI,CAAC,KAAK,qBAAqB,MAAM,GAAG,GAAG,IAAI,CAAC;oBAC9G,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;gBACA,OAAO,SAAS,QAAQ;gBACxB;YACJ;YACA,oBAAoB;YACpB,mBAAmB,IAAI,CAAC;YACxB,IAAI,CAAC,UAAU;gBACX;YACJ;YACA,0GAA0G;YAC1G,2CAA2C;YAC3C,IAAI,QAAQ,CAAC,EAAE,IAAI,MAAM;gBACrB,SAAS,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI;gBACpC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI;YACrC,OACK;gBACD,SAAS,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI;gBACpC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI;YACrC;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;;;KAcC,GACD,SAAS,YAAY,GAAG,SAAU,MAAM,EAAE,QAAQ,EAAE,WAAW;QAC3D,IAAI,SAAS,OAAO,SAAS;QAC7B,IAAI,QAAQ,OAAO,QAAQ;QAC3B,qCAAqC;QACrC,IAAI,SAAS,IAAI,MAAM;QACvB,SAAS,YAAY,CAAC,QAAQ,SAAS,mBAAmB,CAAC,QAAQ,QAAQ,OAAO,UAAU,aAAa,SAAS,aAAa,GAAG,SAAS,qBAAqB;QAChK,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM;YACnB,cAAc,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;YACvC,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;QACxC;QACA,SAAS,YAAY,CAAC,QAAQ,SAAS,mBAAmB,CAAC,QAAQ,QAAQ,OAAO,UAAU,aAAa,SAAS,YAAY,GAAG,SAAS,oBAAoB;QAC9J,OAAO;IACX;IACA,SAAS,YAAY,GAAG,SAAU,MAAM,EAAE,SAAS,EAAE,kBAAkB;QACnE,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,MAAM,EAAE,IAAK;YAChD,MAAM,CAAC,kBAAkB,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE;QAChD;IACJ;IACA,SAAS,mBAAmB,GAAG,SAAU,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO;QAC1F,qCAAqC;QACrC,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,QAAQ;QACZ,IAAI,WAAW,IAAI,WAAW,QAAQ,MAAM;QAC5C,MAAO,WAAW,QAAQ,YAAY,SAAS,QAAQ,CAAE;YACrD,IAAI,MAAM,SAAS,gBAAgB,CAAC,QAAQ,aAAa,UAAU,OAAO,OAAO,SAAS;YAC1F,IAAI,OAAO,MAAM;gBACb,MAAO,WAAW,EAAG;oBACjB,IAAI,iBAAiB,SAAS,gBAAgB,CAAC,QAAQ,aAAa,EAAE,UAAU,OAAO,OAAO,SAAS;oBACvG,IAAI,kBAAkB,MAAM;wBACxB,MAAM;oBACV,OACK;wBACD;wBACA;oBACJ;gBACJ;gBACA,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,GAAG,CAAC,EAAE,EAAE;gBACpC,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,GAAG,CAAC,EAAE,EAAE;gBACpC,QAAQ;gBACR;YACJ;QACJ;QACA,IAAI,UAAU,WAAW;QACzB,uDAAuD;QACvD,IAAI,OAAO;YACP,IAAI,kBAAkB;YACtB,IAAI,iBAAiB,WAAW,IAAI,CAAC;gBAAC,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;gBAAK,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;aAAI;YACjG,MAAO,UAAU,QAAQ,UAAW;gBAChC,IAAI,MAAM,SAAS,gBAAgB,CAAC,QAAQ,cAAc,CAAC,EAAE,EAAE,SAAS,OAAO,OAAO,SAAS;gBAC/F,kGAAkG;gBAClG,gGAAgG;gBAChG,wGAAwG;gBACxG,iDAAiD;gBACjD,IAAI,OAAO,QACP,KAAK,GAAG,CAAC,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,iBAAiB,IACjE,KAAK,GAAG,CAAC,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,iBAAiB,EAAE;oBACnE,iBAAiB;oBACjB,kBAAkB;gBACtB,OACK;oBACD,IAAI,kBAAkB,SAAS,qBAAqB,EAAE;wBAClD;oBACJ,OACK;wBACD;oBACJ;gBACJ;YACJ;YACA,WAAW,kBAAkB;YAC7B,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,cAAc,CAAC,EAAE,EAAE;YAC/C,MAAM,CAAC,EAAE,GAAG,IAAI,mKAAA,CAAA,UAAW,CAAC,cAAc,CAAC,EAAE,EAAE;QACnD;QACA,IAAI,UAAU,WAAW,SAAS,kBAAkB,EAAE;YAClD,sKAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;QACxB;QACA,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,SAAS,gBAAgB,GAAG,SAAU,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ;QAC3F,sKAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,MAAM,EAAE;QAChD,IAAI,eAAe;QACnB,IAAI,aAAa;QACjB,6GAA6G;QAC7G,MAAO,OAAO,GAAG,CAAC,cAAc,QAAQ,eAAe,KAAK,eAAe,SAAS,eAAe,CAAE;YACjG;QACJ;QACA,IAAI,IAAI;QACR,IAAI,kBAAkB;QACtB,IAAI,gBAAgB,QAAQ,MAAM;QAClC,IAAK,IAAI,UAAU,YAAY,IAAI,OAAO,IAAK;YAC3C,IAAI,QAAQ,OAAO,GAAG,CAAC,GAAG;YAC1B,IAAI,UAAU,SAAS;gBACnB,QAAQ,CAAC,gBAAgB;YAC7B,OACK;gBACD,IAAI,oBAAoB,gBAAgB,GAAG;oBACvC,IAAI,SAAS,oBAAoB,CAAC,UAAU,SAAS,SAAS,uBAAuB,IAAI,SAAS,gBAAgB,EAAE;wBAChH,OAAO,IAAI,WAAW;4BAAC;4BAAc;yBAAE;oBAC3C;oBACA,gBAAgB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACzC,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,kBAAkB;oBAC7D,QAAQ,CAAC,kBAAkB,EAAE,GAAG;oBAChC,QAAQ,CAAC,gBAAgB,GAAG;oBAC5B;gBACJ,OACK;oBACD;gBACJ;gBACA,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,UAAU,CAAC;YACf;QACJ;QACA,IAAI,oBAAoB,gBAAgB,KACpC,SAAS,oBAAoB,CAAC,UAAU,SAAS,SAAS,uBAAuB,IAAI,SAAS,gBAAgB,EAAE;YAChH,OAAO,IAAI,WAAW;gBAAC;gBAAc,IAAI;aAAE;QAC/C;QACA,OAAO;IACX;IACA;;;;;;;;;;KAUC,GACD,SAAS,oBAAoB,GAAG,SAAU,QAAQ,EAAE,OAAO,EAAE,qBAAqB;QAC9E,IAAI,cAAc,SAAS,MAAM;QACjC,IAAI,QAAQ;QACZ,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,SAAS,QAAQ,CAAC,EAAE;YACpB,iBAAiB,OAAO,CAAC,EAAE;QAC/B;QACA,IAAI,QAAQ,eAAe;YACvB,qEAAqE;YACrE,2CAA2C;YAC3C,OAAO,yBAAyB,GAAG;QACvC;QACA,sFAAsF;QACtF,wFAAwF;QACxF,6BAA6B;QAC7B,IAAI,eAAe,QAAQ;QAC3B,yBAAyB;QACzB,IAAI,gBAAgB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,IAAI,UAAU,QAAQ,CAAC,EAAE;YACzB,IAAI,gBAAgB,OAAO,CAAC,EAAE,GAAG;YACjC,IAAI,WAAW,UAAU,gBAAgB,UAAU,gBAAgB,gBAAgB;YACnF,IAAI,WAAW,uBAAuB;gBAClC,OAAO,yBAAyB,GAAG;YACvC;YACA,iBAAiB;QACrB;QACA,OAAO,gBAAgB;IAC3B;IACA,SAAS,qBAAqB,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;KAAE;IAC7D,SAAS,oBAAoB,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;KAAE;IAC5D,SAAS,gBAAgB,GAAG;IAC5B,SAAS,uBAAuB,GAAG;IACnC,oCAAoC;IACpC,2BAA2B;IAC3B,SAAS,aAAa,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IACjE,6BAA6B;IAC7B,SAAS,YAAY,GAAG,WAAW,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IACnE,SAAS,eAAe,GAAG;IAC3B,SAAS,iBAAiB,GAAG;IAC7B,qHAAqH;IACrH,gGAAgG;IAChG,SAAS,qBAAqB,GAAG;IACjC,iIAAiI;IACjI,4FAA4F;IAC5F,SAAS,QAAQ,GAAG;IACpB,SAAS,kBAAkB,GAAG;IAC9B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/ec/ModulusPoly.js"], "sourcesContent": ["/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport System from '../../../util/System';\nimport StringBuilder from '../../../util/StringBuilder';\n/**\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGFPoly\n */\nvar ModulusPoly = /** @class */ (function () {\n    function ModulusPoly(field, coefficients) {\n        if (coefficients.length === 0) {\n            throw new IllegalArgumentException();\n        }\n        this.field = field;\n        var coefficientsLength = /*int*/ coefficients.length;\n        if (coefficientsLength > 1 && coefficients[0] === 0) {\n            // Leading term must be non-zero for anything except the constant polynomial \"0\"\n            var firstNonZero = /*int*/ 1;\n            while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n                firstNonZero++;\n            }\n            if (firstNonZero === coefficientsLength) {\n                this.coefficients = new Int32Array([0]);\n            }\n            else {\n                this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n                System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n            }\n        }\n        else {\n            this.coefficients = coefficients;\n        }\n    }\n    ModulusPoly.prototype.getCoefficients = function () {\n        return this.coefficients;\n    };\n    /**\n     * @return degree of this polynomial\n     */\n    ModulusPoly.prototype.getDegree = function () {\n        return this.coefficients.length - 1;\n    };\n    /**\n     * @return true iff this polynomial is the monomial \"0\"\n     */\n    ModulusPoly.prototype.isZero = function () {\n        return this.coefficients[0] === 0;\n    };\n    /**\n     * @return coefficient of x^degree term in this polynomial\n     */\n    ModulusPoly.prototype.getCoefficient = function (degree) {\n        return this.coefficients[this.coefficients.length - 1 - degree];\n    };\n    /**\n     * @return evaluation of this polynomial at a given point\n     */\n    ModulusPoly.prototype.evaluateAt = function (a) {\n        var e_1, _a;\n        if (a === 0) {\n            // Just return the x^0 coefficient\n            return this.getCoefficient(0);\n        }\n        if (a === 1) {\n            // Just the sum of the coefficients\n            var sum = /*int*/ 0;\n            try {\n                for (var _b = __values(this.coefficients), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var coefficient = _c.value /*int*/;\n                    sum = this.field.add(sum, coefficient);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return sum;\n        }\n        var result = /*int*/ this.coefficients[0];\n        var size = /*int*/ this.coefficients.length;\n        for (var i /*int*/ = 1; i < size; i++) {\n            result = this.field.add(this.field.multiply(a, result), this.coefficients[i]);\n        }\n        return result;\n    };\n    ModulusPoly.prototype.add = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (this.isZero()) {\n            return other;\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        var smallerCoefficients = this.coefficients;\n        var largerCoefficients = other.coefficients;\n        if (smallerCoefficients.length > largerCoefficients.length) {\n            var temp = smallerCoefficients;\n            smallerCoefficients = largerCoefficients;\n            largerCoefficients = temp;\n        }\n        var sumDiff = new Int32Array(largerCoefficients.length);\n        var lengthDiff = /*int*/ largerCoefficients.length - smallerCoefficients.length;\n        // Copy high-order terms only found in higher-degree polynomial's coefficients\n        System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n        for (var i /*int*/ = lengthDiff; i < largerCoefficients.length; i++) {\n            sumDiff[i] = this.field.add(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n        }\n        return new ModulusPoly(this.field, sumDiff);\n    };\n    ModulusPoly.prototype.subtract = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        return this.add(other.negative());\n    };\n    ModulusPoly.prototype.multiply = function (other) {\n        if (other instanceof ModulusPoly) {\n            return this.multiplyOther(other);\n        }\n        return this.multiplyScalar(other);\n    };\n    ModulusPoly.prototype.multiplyOther = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (this.isZero() || other.isZero()) {\n            // return this.field.getZero();\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        var aCoefficients = this.coefficients;\n        var aLength = /*int*/ aCoefficients.length;\n        var bCoefficients = other.coefficients;\n        var bLength = /*int*/ bCoefficients.length;\n        var product = new Int32Array(aLength + bLength - 1);\n        for (var i /*int*/ = 0; i < aLength; i++) {\n            var aCoeff = /*int*/ aCoefficients[i];\n            for (var j /*int*/ = 0; j < bLength; j++) {\n                product[i + j] = this.field.add(product[i + j], this.field.multiply(aCoeff, bCoefficients[j]));\n            }\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    ModulusPoly.prototype.negative = function () {\n        var size = /*int*/ this.coefficients.length;\n        var negativeCoefficients = new Int32Array(size);\n        for (var i /*int*/ = 0; i < size; i++) {\n            negativeCoefficients[i] = this.field.subtract(0, this.coefficients[i]);\n        }\n        return new ModulusPoly(this.field, negativeCoefficients);\n    };\n    ModulusPoly.prototype.multiplyScalar = function (scalar) {\n        if (scalar === 0) {\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        if (scalar === 1) {\n            return this;\n        }\n        var size = /*int*/ this.coefficients.length;\n        var product = new Int32Array(size);\n        for (var i /*int*/ = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], scalar);\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    ModulusPoly.prototype.multiplyByMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        var size = /*int*/ this.coefficients.length;\n        var product = new Int32Array(size + degree);\n        for (var i /*int*/ = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], coefficient);\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    /*\n    ModulusPoly[] divide(other: ModulusPoly) {\n      if (!field.equals(other.field)) {\n        throw new IllegalArgumentException(\"ModulusPolys do not have same ModulusGF field\");\n      }\n      if (other.isZero()) {\n        throw new IllegalArgumentException(\"Divide by 0\");\n      }\n  \n      let quotient: ModulusPoly = field.getZero();\n      let remainder: ModulusPoly = this;\n  \n      let denominatorLeadingTerm: /*int/ number = other.getCoefficient(other.getDegree());\n      let inverseDenominatorLeadingTerm: /*int/ number = field.inverse(denominatorLeadingTerm);\n  \n      while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n        let degreeDifference: /*int/ number = remainder.getDegree() - other.getDegree();\n        let scale: /*int/ number = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n        let term: ModulusPoly = other.multiplyByMonomial(degreeDifference, scale);\n        let iterationQuotient: ModulusPoly = field.buildMonomial(degreeDifference, scale);\n        quotient = quotient.add(iterationQuotient);\n        remainder = remainder.subtract(term);\n      }\n  \n      return new ModulusPoly[] { quotient, remainder };\n    }\n    */\n    // @Override\n    ModulusPoly.prototype.toString = function () {\n        var result = new StringBuilder( /*8 * this.getDegree()*/); // dynamic string size in JS\n        for (var degree /*int*/ = this.getDegree(); degree >= 0; degree--) {\n            var coefficient = /*int*/ this.getCoefficient(degree);\n            if (coefficient !== 0) {\n                if (coefficient < 0) {\n                    result.append(' - ');\n                    coefficient = -coefficient;\n                }\n                else {\n                    if (result.length() > 0) {\n                        result.append(' + ');\n                    }\n                }\n                if (degree === 0 || coefficient !== 1) {\n                    result.append(coefficient);\n                }\n                if (degree !== 0) {\n                    if (degree === 1) {\n                        result.append('x');\n                    }\n                    else {\n                        result.append('x^');\n                        result.append(degree);\n                    }\n                }\n            }\n        }\n        return result.toString();\n    };\n    return ModulusPoly;\n}());\nexport default ModulusPoly;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAYA,8CAA8C;AAC9C;AACA;AACA;AAdA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;AAKA;;;CAGC,GACD,IAAI,cAA6B;IAC7B,SAAS,YAAY,KAAK,EAAE,YAAY;QACpC,IAAI,aAAa,MAAM,KAAK,GAAG;YAC3B,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,qBAAqB,KAAK,GAAG,aAAa,MAAM;QACpD,IAAI,qBAAqB,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;YACjD,gFAAgF;YAChF,IAAI,eAAe,KAAK,GAAG;YAC3B,MAAO,eAAe,sBAAsB,YAAY,CAAC,aAAa,KAAK,EAAG;gBAC1E;YACJ;YACA,IAAI,iBAAiB,oBAAoB;gBACrC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW;oBAAC;iBAAE;YAC1C,OACK;gBACD,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,qBAAqB;gBACxD,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,cAAc,cAAc,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;YAC/F;QACJ,OACK;YACD,IAAI,CAAC,YAAY,GAAG;QACxB;IACJ;IACA,YAAY,SAAS,CAAC,eAAe,GAAG;QACpC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,SAAS,GAAG;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IACtC;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,MAAM,GAAG;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK;IACpC;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;QACnD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,OAAO;IACnE;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC;QAC1C,IAAI,KAAK;QACT,IAAI,MAAM,GAAG;YACT,kCAAkC;YAClC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B;QACA,IAAI,MAAM,GAAG;YACT,mCAAmC;YACnC,IAAI,MAAM,KAAK,GAAG;YAClB,IAAI;gBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;oBACjF,IAAI,cAAc,GAAG,KAAK,CAAC,KAAK;oBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;gBAC9B;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;gBACpD,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,OAAO;QACX;QACA,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;QACzC,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;QAC3C,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,MAAM,IAAK;YACnC,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK;QACvC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,IAAI,CAAC,MAAM,IAAI;YACf,OAAO;QACX;QACA,IAAI,MAAM,MAAM,IAAI;YAChB,OAAO,IAAI;QACf;QACA,IAAI,sBAAsB,IAAI,CAAC,YAAY;QAC3C,IAAI,qBAAqB,MAAM,YAAY;QAC3C,IAAI,oBAAoB,MAAM,GAAG,mBAAmB,MAAM,EAAE;YACxD,IAAI,OAAO;YACX,sBAAsB;YACtB,qBAAqB;QACzB;QACA,IAAI,UAAU,IAAI,WAAW,mBAAmB,MAAM;QACtD,IAAI,aAAa,KAAK,GAAG,mBAAmB,MAAM,GAAG,oBAAoB,MAAM;QAC/E,8EAA8E;QAC9E,sKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAS,GAAG;QACpD,IAAK,IAAI,EAAE,KAAK,MAAK,YAAY,IAAI,mBAAmB,MAAM,EAAE,IAAK;YACjE,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,WAAW,EAAE,kBAAkB,CAAC,EAAE;QAC1F;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;IACvC;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,MAAM,MAAM,IAAI;YAChB,OAAO,IAAI;QACf;QACA,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ;IAClC;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC5C,IAAI,iBAAiB,aAAa;YAC9B,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B;QACA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACjD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YACjC,MAAM,IAAI,gLAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI;YACjC,+BAA+B;YAC/B,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE,IAAI,WAAW;gBAAC;aAAE;QACzD;QACA,IAAI,gBAAgB,IAAI,CAAC,YAAY;QACrC,IAAI,UAAU,KAAK,GAAG,cAAc,MAAM;QAC1C,IAAI,gBAAgB,MAAM,YAAY;QACtC,IAAI,UAAU,KAAK,GAAG,cAAc,MAAM;QAC1C,IAAI,UAAU,IAAI,WAAW,UAAU,UAAU;QACjD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,SAAS,IAAK;YACtC,IAAI,SAAS,KAAK,GAAG,aAAa,CAAC,EAAE;YACrC,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,SAAS,IAAK;gBACtC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,aAAa,CAAC,EAAE;YAChG;QACJ;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;IACvC;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;QAC3C,IAAI,uBAAuB,IAAI,WAAW;QAC1C,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,MAAM,IAAK;YACnC,oBAAoB,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;QACzE;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;IACvC;IACA,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;QACnD,IAAI,WAAW,GAAG;YACd,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE,IAAI,WAAW;gBAAC;aAAE;QACzD;QACA,IAAI,WAAW,GAAG;YACd,OAAO,IAAI;QACf;QACA,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;QAC3C,IAAI,UAAU,IAAI,WAAW;QAC7B,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,MAAM,IAAK;YACnC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;QAC3D;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;IACvC;IACA,YAAY,SAAS,CAAC,kBAAkB,GAAG,SAAU,MAAM,EAAE,WAAW;QACpE,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,IAAI,gBAAgB,GAAG;YACnB,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE,IAAI,WAAW;gBAAC;aAAE;QACzD;QACA,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;QAC3C,IAAI,UAAU,IAAI,WAAW,OAAO;QACpC,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,MAAM,IAAK;YACnC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;QAC3D;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;IACvC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BA,GACA,YAAY;IACZ,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa,IAA6B,4BAA4B;QACvF,IAAK,IAAI,OAAO,KAAK,MAAK,IAAI,CAAC,SAAS,IAAI,UAAU,GAAG,SAAU;YAC/D,IAAI,cAAc,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;YAC9C,IAAI,gBAAgB,GAAG;gBACnB,IAAI,cAAc,GAAG;oBACjB,OAAO,MAAM,CAAC;oBACd,cAAc,CAAC;gBACnB,OACK;oBACD,IAAI,OAAO,MAAM,KAAK,GAAG;wBACrB,OAAO,MAAM,CAAC;oBAClB;gBACJ;gBACA,IAAI,WAAW,KAAK,gBAAgB,GAAG;oBACnC,OAAO,MAAM,CAAC;gBAClB;gBACA,IAAI,WAAW,GAAG;oBACd,IAAI,WAAW,GAAG;wBACd,OAAO,MAAM,CAAC;oBAClB,OACK;wBACD,OAAO,MAAM,CAAC;wBACd,OAAO,MAAM,CAAC;oBAClB;gBACJ;YACJ;QACJ;QACA,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/ec/ModulusBase.js"], "sourcesContent": ["import IllegalArgumentException from '../../../IllegalArgumentException';\nimport ArithmeticException from '../../../ArithmeticException';\nvar ModulusBase = /** @class */ (function () {\n    function ModulusBase() {\n    }\n    ModulusBase.prototype.add = function (a, b) {\n        return (a + b) % this.modulus;\n    };\n    ModulusBase.prototype.subtract = function (a, b) {\n        return (this.modulus + a - b) % this.modulus;\n    };\n    ModulusBase.prototype.exp = function (a) {\n        return this.expTable[a];\n    };\n    ModulusBase.prototype.log = function (a) {\n        if (a === 0) {\n            throw new IllegalArgumentException();\n        }\n        return this.logTable[a];\n    };\n    ModulusBase.prototype.inverse = function (a) {\n        if (a === 0) {\n            throw new ArithmeticException();\n        }\n        return this.expTable[this.modulus - this.logTable[a] - 1];\n    };\n    ModulusBase.prototype.multiply = function (a, b) {\n        if (a === 0 || b === 0) {\n            return 0;\n        }\n        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.modulus - 1)];\n    };\n    ModulusBase.prototype.getSize = function () {\n        return this.modulus;\n    };\n    ModulusBase.prototype.equals = function (o) {\n        return o === this;\n    };\n    return ModulusBase;\n}());\nexport default ModulusBase;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,cAA6B;IAC7B,SAAS,eACT;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC,EAAE,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;IACjC;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,CAAC,EAAE,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;IAChD;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC3B;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC;QACnC,IAAI,MAAM,GAAG;YACT,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC3B;IACA,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC;QACvC,IAAI,MAAM,GAAG;YACT,MAAM,IAAI,2KAAA,CAAA,UAAmB;QACjC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE;IAC7D;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,CAAC,EAAE,CAAC;QAC3C,IAAI,MAAM,KAAK,MAAM,GAAG;YACpB,OAAO;QACX;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;IACpF;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC5B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACtC,OAAO,MAAM,IAAI;IACrB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/ec/ModulusGF.js"], "sourcesContent": ["/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../../PDF417Common';\nimport ModulusPoly from './ModulusPoly';\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport ModulusBase from './ModulusBase';\n/**\n * <p>A field based on powers of a generator integer, modulo some modulus.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGF\n */\nvar ModulusGF = /** @class */ (function (_super) {\n    __extends(ModulusGF, _super);\n    // private /*final*/ modulus: /*int*/ number;\n    function ModulusGF(modulus, generator) {\n        var _this = _super.call(this) || this;\n        _this.modulus = modulus;\n        _this.expTable = new Int32Array(modulus);\n        _this.logTable = new Int32Array(modulus);\n        var x = /*int*/ 1;\n        for (var i /*int*/ = 0; i < modulus; i++) {\n            _this.expTable[i] = x;\n            x = (x * generator) % modulus;\n        }\n        for (var i /*int*/ = 0; i < modulus - 1; i++) {\n            _this.logTable[_this.expTable[i]] = i;\n        }\n        // logTable[0] == 0 but this should never be used\n        _this.zero = new ModulusPoly(_this, new Int32Array([0]));\n        _this.one = new ModulusPoly(_this, new Int32Array([1]));\n        return _this;\n    }\n    ModulusGF.prototype.getZero = function () {\n        return this.zero;\n    };\n    ModulusGF.prototype.getOne = function () {\n        return this.one;\n    };\n    ModulusGF.prototype.buildMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.zero;\n        }\n        var coefficients = new Int32Array(degree + 1);\n        coefficients[0] = coefficient;\n        return new ModulusPoly(this, coefficients);\n    };\n    ModulusGF.PDF417_GF = new ModulusGF(PDF417Common.NUMBER_OF_CODEWORDS, 3);\n    return ModulusGF;\n}(ModulusBase));\nexport default ModulusGF;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAcD,8CAA8C;AAC9C,+CAA+C;AAC/C;AACA;AACA;AACA;AAlBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;AAOA;;;;;CAKC,GACD,IAAI,YAA2B,SAAU,MAAM;IAC3C,UAAU,WAAW;IACrB,6CAA6C;IAC7C,SAAS,UAAU,OAAO,EAAE,SAAS;QACjC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,OAAO,GAAG;QAChB,MAAM,QAAQ,GAAG,IAAI,WAAW;QAChC,MAAM,QAAQ,GAAG,IAAI,WAAW;QAChC,IAAI,IAAI,KAAK,GAAG;QAChB,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,SAAS,IAAK;YACtC,MAAM,QAAQ,CAAC,EAAE,GAAG;YACpB,IAAI,AAAC,IAAI,YAAa;QAC1B;QACA,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,UAAU,GAAG,IAAK;YAC1C,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,EAAE,CAAC,GAAG;QACxC;QACA,iDAAiD;QACjD,MAAM,IAAI,GAAG,IAAI,8LAAA,CAAA,UAAW,CAAC,OAAO,IAAI,WAAW;YAAC;SAAE;QACtD,MAAM,GAAG,GAAG,IAAI,8LAAA,CAAA,UAAW,CAAC,OAAO,IAAI,WAAW;YAAC;SAAE;QACrD,OAAO;IACX;IACA,UAAU,SAAS,CAAC,OAAO,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,UAAU,SAAS,CAAC,MAAM,GAAG;QACzB,OAAO,IAAI,CAAC,GAAG;IACnB;IACA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,MAAM,EAAE,WAAW;QAC7D,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,gLAAA,CAAA,UAAwB;QACtC;QACA,IAAI,gBAAgB,GAAG;YACnB,OAAO,IAAI,CAAC,IAAI;QACpB;QACA,IAAI,eAAe,IAAI,WAAW,SAAS;QAC3C,YAAY,CAAC,EAAE,GAAG;QAClB,OAAO,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,EAAE;IACjC;IACA,UAAU,SAAS,GAAG,IAAI,UAAU,8KAAA,CAAA,UAAY,CAAC,mBAAmB,EAAE;IACtE,OAAO;AACX,EAAE,8LAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/ec/ErrorCorrection.js"], "sourcesContent": ["/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../../ChecksumException';\nimport ModulusPoly from './ModulusPoly';\nimport ModulusGF from './ModulusGF';\n/**\n * <p>PDF417 error correction implementation.</p>\n *\n * <p>This <a href=\"http://en.wikipedia.org/wiki/Reed%E2%80%93Solomon_error_correction#Example\">example</a>\n * is quite useful in understanding the algorithm.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.ReedSolomonDecoder\n */\nvar ErrorCorrection = /** @class */ (function () {\n    function ErrorCorrection() {\n        this.field = ModulusGF.PDF417_GF;\n    }\n    /**\n     * @param received received codewords\n     * @param numECCodewords number of those codewords used for EC\n     * @param erasures location of erasures\n     * @return number of errors\n     * @throws ChecksumException if errors cannot be corrected, maybe because of too many errors\n     */\n    ErrorCorrection.prototype.decode = function (received, numECCodewords, erasures) {\n        var e_1, _a;\n        var poly = new ModulusPoly(this.field, received);\n        var S = new Int32Array(numECCodewords);\n        var error = false;\n        for (var i /*int*/ = numECCodewords; i > 0; i--) {\n            var evaluation = poly.evaluateAt(this.field.exp(i));\n            S[numECCodewords - i] = evaluation;\n            if (evaluation !== 0) {\n                error = true;\n            }\n        }\n        if (!error) {\n            return 0;\n        }\n        var knownErrors = this.field.getOne();\n        if (erasures != null) {\n            try {\n                for (var erasures_1 = __values(erasures), erasures_1_1 = erasures_1.next(); !erasures_1_1.done; erasures_1_1 = erasures_1.next()) {\n                    var erasure = erasures_1_1.value;\n                    var b = this.field.exp(received.length - 1 - erasure);\n                    // Add (1 - bx) term:\n                    var term = new ModulusPoly(this.field, new Int32Array([this.field.subtract(0, b), 1]));\n                    knownErrors = knownErrors.multiply(term);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (erasures_1_1 && !erasures_1_1.done && (_a = erasures_1.return)) _a.call(erasures_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        var syndrome = new ModulusPoly(this.field, S);\n        // syndrome = syndrome.multiply(knownErrors);\n        var sigmaOmega = this.runEuclideanAlgorithm(this.field.buildMonomial(numECCodewords, 1), syndrome, numECCodewords);\n        var sigma = sigmaOmega[0];\n        var omega = sigmaOmega[1];\n        // sigma = sigma.multiply(knownErrors);\n        var errorLocations = this.findErrorLocations(sigma);\n        var errorMagnitudes = this.findErrorMagnitudes(omega, sigma, errorLocations);\n        for (var i /*int*/ = 0; i < errorLocations.length; i++) {\n            var position = received.length - 1 - this.field.log(errorLocations[i]);\n            if (position < 0) {\n                throw ChecksumException.getChecksumInstance();\n            }\n            received[position] = this.field.subtract(received[position], errorMagnitudes[i]);\n        }\n        return errorLocations.length;\n    };\n    /**\n     *\n     * @param ModulusPoly\n     * @param a\n     * @param ModulusPoly\n     * @param b\n     * @param int\n     * @param R\n     * @throws ChecksumException\n     */\n    ErrorCorrection.prototype.runEuclideanAlgorithm = function (a, b, R) {\n        // Assume a's degree is >= b's\n        if (a.getDegree() < b.getDegree()) {\n            var temp = a;\n            a = b;\n            b = temp;\n        }\n        var rLast = a;\n        var r = b;\n        var tLast = this.field.getZero();\n        var t = this.field.getOne();\n        // Run Euclidean algorithm until r's degree is less than R/2\n        while (r.getDegree() >= Math.round(R / 2)) {\n            var rLastLast = rLast;\n            var tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            // Divide rLastLast by rLast, with quotient in q and remainder in r\n            if (rLast.isZero()) {\n                // Oops, Euclidean algorithm already terminated?\n                throw ChecksumException.getChecksumInstance();\n            }\n            r = rLastLast;\n            var q = this.field.getZero();\n            var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n            var dltInverse = this.field.inverse(denominatorLeadingTerm);\n            while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n                var degreeDiff = r.getDegree() - rLast.getDegree();\n                var scale = this.field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n                q = q.add(this.field.buildMonomial(degreeDiff, scale));\n                r = r.subtract(rLast.multiplyByMonomial(degreeDiff, scale));\n            }\n            t = q.multiply(tLast).subtract(tLastLast).negative();\n        }\n        var sigmaTildeAtZero = t.getCoefficient(0);\n        if (sigmaTildeAtZero === 0) {\n            throw ChecksumException.getChecksumInstance();\n        }\n        var inverse = this.field.inverse(sigmaTildeAtZero);\n        var sigma = t.multiply(inverse);\n        var omega = r.multiply(inverse);\n        return [sigma, omega];\n    };\n    /**\n     *\n     * @param errorLocator\n     * @throws ChecksumException\n     */\n    ErrorCorrection.prototype.findErrorLocations = function (errorLocator) {\n        // This is a direct application of Chien's search\n        var numErrors = errorLocator.getDegree();\n        var result = new Int32Array(numErrors);\n        var e = 0;\n        for (var i /*int*/ = 1; i < this.field.getSize() && e < numErrors; i++) {\n            if (errorLocator.evaluateAt(i) === 0) {\n                result[e] = this.field.inverse(i);\n                e++;\n            }\n        }\n        if (e !== numErrors) {\n            throw ChecksumException.getChecksumInstance();\n        }\n        return result;\n    };\n    ErrorCorrection.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocator, errorLocations) {\n        var errorLocatorDegree = errorLocator.getDegree();\n        var formalDerivativeCoefficients = new Int32Array(errorLocatorDegree);\n        for (var i /*int*/ = 1; i <= errorLocatorDegree; i++) {\n            formalDerivativeCoefficients[errorLocatorDegree - i] =\n                this.field.multiply(i, errorLocator.getCoefficient(i));\n        }\n        var formalDerivative = new ModulusPoly(this.field, formalDerivativeCoefficients);\n        // This is directly applying Forney's Formula\n        var s = errorLocations.length;\n        var result = new Int32Array(s);\n        for (var i /*int*/ = 0; i < s; i++) {\n            var xiInverse = this.field.inverse(errorLocations[i]);\n            var numerator = this.field.subtract(0, errorEvaluator.evaluateAt(xiInverse));\n            var denominator = this.field.inverse(formalDerivative.evaluateAt(xiInverse));\n            result[i] = this.field.multiply(numerator, denominator);\n        }\n        return result;\n    };\n    return ErrorCorrection;\n}());\nexport default ErrorCorrection;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAYA,8CAA8C;AAC9C,6CAA6C;AAC7C;AACA;AACA;AAfA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;AAMA;;;;;;;;CAQC,GACD,IAAI,kBAAiC;IACjC,SAAS;QACL,IAAI,CAAC,KAAK,GAAG,4LAAA,CAAA,UAAS,CAAC,SAAS;IACpC;IACA;;;;;;KAMC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,cAAc,EAAE,QAAQ;QAC3E,IAAI,KAAK;QACT,IAAI,OAAO,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,CAAC,KAAK,EAAE;QACvC,IAAI,IAAI,IAAI,WAAW;QACvB,IAAI,QAAQ;QACZ,IAAK,IAAI,EAAE,KAAK,MAAK,gBAAgB,IAAI,GAAG,IAAK;YAC7C,IAAI,aAAa,KAAK,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAChD,CAAC,CAAC,iBAAiB,EAAE,GAAG;YACxB,IAAI,eAAe,GAAG;gBAClB,QAAQ;YACZ;QACJ;QACA,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,MAAM;QACnC,IAAI,YAAY,MAAM;YAClB,IAAI;gBACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9H,IAAI,UAAU,aAAa,KAAK;oBAChC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,MAAM,GAAG,IAAI;oBAC7C,qBAAqB;oBACrB,IAAI,OAAO,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,WAAW;wBAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG;wBAAI;qBAAE;oBACpF,cAAc,YAAY,QAAQ,CAAC;gBACvC;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,IAAI,WAAW,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,CAAC,KAAK,EAAE;QAC3C,6CAA6C;QAC7C,IAAI,aAAa,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,gBAAgB,IAAI,UAAU;QACnG,IAAI,QAAQ,UAAU,CAAC,EAAE;QACzB,IAAI,QAAQ,UAAU,CAAC,EAAE;QACzB,uCAAuC;QACvC,IAAI,iBAAiB,IAAI,CAAC,kBAAkB,CAAC;QAC7C,IAAI,kBAAkB,IAAI,CAAC,mBAAmB,CAAC,OAAO,OAAO;QAC7D,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YACpD,IAAI,WAAW,SAAS,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACrE,IAAI,WAAW,GAAG;gBACd,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;QACnF;QACA,OAAO,eAAe,MAAM;IAChC;IACA;;;;;;;;;KASC,GACD,gBAAgB,SAAS,CAAC,qBAAqB,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/D,8BAA8B;QAC9B,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI;YAC/B,IAAI,OAAO;YACX,IAAI;YACJ,IAAI;QACR;QACA,IAAI,QAAQ;QACZ,IAAI,IAAI;QACR,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO;QAC9B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;QACzB,4DAA4D;QAC5D,MAAO,EAAE,SAAS,MAAM,KAAK,KAAK,CAAC,IAAI,GAAI;YACvC,IAAI,YAAY;YAChB,IAAI,YAAY;YAChB,QAAQ;YACR,QAAQ;YACR,mEAAmE;YACnE,IAAI,MAAM,MAAM,IAAI;gBAChB,gDAAgD;gBAChD,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,IAAI;YACJ,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;YAC1B,IAAI,yBAAyB,MAAM,cAAc,CAAC,MAAM,SAAS;YACjE,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACpC,MAAO,EAAE,SAAS,MAAM,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,GAAI;gBACtD,IAAI,aAAa,EAAE,SAAS,KAAK,MAAM,SAAS;gBAChD,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,EAAE,SAAS,KAAK;gBACjE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY;gBAC/C,IAAI,EAAE,QAAQ,CAAC,MAAM,kBAAkB,CAAC,YAAY;YACxD;YACA,IAAI,EAAE,QAAQ,CAAC,OAAO,QAAQ,CAAC,WAAW,QAAQ;QACtD;QACA,IAAI,mBAAmB,EAAE,cAAc,CAAC;QACxC,IAAI,qBAAqB,GAAG;YACxB,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;QAC/C;QACA,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC,IAAI,QAAQ,EAAE,QAAQ,CAAC;QACvB,IAAI,QAAQ,EAAE,QAAQ,CAAC;QACvB,OAAO;YAAC;YAAO;SAAM;IACzB;IACA;;;;KAIC,GACD,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAU,YAAY;QACjE,iDAAiD;QACjD,IAAI,YAAY,aAAa,SAAS;QACtC,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAI,IAAI;QACR,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,IAAI,WAAW,IAAK;YACpE,IAAI,aAAa,UAAU,CAAC,OAAO,GAAG;gBAClC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC/B;YACJ;QACJ;QACA,IAAI,MAAM,WAAW;YACjB,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;QAC/C;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,mBAAmB,GAAG,SAAU,cAAc,EAAE,YAAY,EAAE,cAAc;QAClG,IAAI,qBAAqB,aAAa,SAAS;QAC/C,IAAI,+BAA+B,IAAI,WAAW;QAClD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,KAAK,oBAAoB,IAAK;YAClD,4BAA4B,CAAC,qBAAqB,EAAE,GAChD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,aAAa,cAAc,CAAC;QAC3D;QACA,IAAI,mBAAmB,IAAI,8LAAA,CAAA,UAAW,CAAC,IAAI,CAAC,KAAK,EAAE;QACnD,6CAA6C;QAC7C,IAAI,IAAI,eAAe,MAAM;QAC7B,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAK;YAChC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YACpD,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,eAAe,UAAU,CAAC;YACjE,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,UAAU,CAAC;YACjE,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW;QAC/C;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/BoundingBox.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\n/**\n * <AUTHOR>\n */\nvar BoundingBox = /** @class */ (function () {\n    function BoundingBox(image, topLeft, bottomLeft, topRight, bottomRight) {\n        if (image instanceof BoundingBox) {\n            this.constructor_2(image);\n        }\n        else {\n            this.constructor_1(image, topLeft, bottomLeft, topRight, bottomRight);\n        }\n    }\n    /**\n     *\n     * @param image\n     * @param topLeft\n     * @param bottomLeft\n     * @param topRight\n     * @param bottomRight\n     *\n     * @throws NotFoundException\n     */\n    BoundingBox.prototype.constructor_1 = function (image, topLeft, bottomLeft, topRight, bottomRight) {\n        var leftUnspecified = topLeft == null || bottomLeft == null;\n        var rightUnspecified = topRight == null || bottomRight == null;\n        if (leftUnspecified && rightUnspecified) {\n            throw new NotFoundException();\n        }\n        if (leftUnspecified) {\n            topLeft = new ResultPoint(0, topRight.getY());\n            bottomLeft = new ResultPoint(0, bottomRight.getY());\n        }\n        else if (rightUnspecified) {\n            topRight = new ResultPoint(image.getWidth() - 1, topLeft.getY());\n            bottomRight = new ResultPoint(image.getWidth() - 1, bottomLeft.getY());\n        }\n        this.image = image;\n        this.topLeft = topLeft;\n        this.bottomLeft = bottomLeft;\n        this.topRight = topRight;\n        this.bottomRight = bottomRight;\n        this.minX = Math.trunc(Math.min(topLeft.getX(), bottomLeft.getX()));\n        this.maxX = Math.trunc(Math.max(topRight.getX(), bottomRight.getX()));\n        this.minY = Math.trunc(Math.min(topLeft.getY(), topRight.getY()));\n        this.maxY = Math.trunc(Math.max(bottomLeft.getY(), bottomRight.getY()));\n    };\n    BoundingBox.prototype.constructor_2 = function (boundingBox) {\n        this.image = boundingBox.image;\n        this.topLeft = boundingBox.getTopLeft();\n        this.bottomLeft = boundingBox.getBottomLeft();\n        this.topRight = boundingBox.getTopRight();\n        this.bottomRight = boundingBox.getBottomRight();\n        this.minX = boundingBox.getMinX();\n        this.maxX = boundingBox.getMaxX();\n        this.minY = boundingBox.getMinY();\n        this.maxY = boundingBox.getMaxY();\n    };\n    /**\n     * @throws NotFoundException\n     */\n    BoundingBox.merge = function (leftBox, rightBox) {\n        if (leftBox == null) {\n            return rightBox;\n        }\n        if (rightBox == null) {\n            return leftBox;\n        }\n        return new BoundingBox(leftBox.image, leftBox.topLeft, leftBox.bottomLeft, rightBox.topRight, rightBox.bottomRight);\n    };\n    /**\n     * @throws NotFoundException\n     */\n    BoundingBox.prototype.addMissingRows = function (missingStartRows, missingEndRows, isLeft) {\n        var newTopLeft = this.topLeft;\n        var newBottomLeft = this.bottomLeft;\n        var newTopRight = this.topRight;\n        var newBottomRight = this.bottomRight;\n        if (missingStartRows > 0) {\n            var top_1 = isLeft ? this.topLeft : this.topRight;\n            var newMinY = Math.trunc(top_1.getY() - missingStartRows);\n            if (newMinY < 0) {\n                newMinY = 0;\n            }\n            var newTop = new ResultPoint(top_1.getX(), newMinY);\n            if (isLeft) {\n                newTopLeft = newTop;\n            }\n            else {\n                newTopRight = newTop;\n            }\n        }\n        if (missingEndRows > 0) {\n            var bottom = isLeft ? this.bottomLeft : this.bottomRight;\n            var newMaxY = Math.trunc(bottom.getY() + missingEndRows);\n            if (newMaxY >= this.image.getHeight()) {\n                newMaxY = this.image.getHeight() - 1;\n            }\n            var newBottom = new ResultPoint(bottom.getX(), newMaxY);\n            if (isLeft) {\n                newBottomLeft = newBottom;\n            }\n            else {\n                newBottomRight = newBottom;\n            }\n        }\n        return new BoundingBox(this.image, newTopLeft, newBottomLeft, newTopRight, newBottomRight);\n    };\n    BoundingBox.prototype.getMinX = function () {\n        return this.minX;\n    };\n    BoundingBox.prototype.getMaxX = function () {\n        return this.maxX;\n    };\n    BoundingBox.prototype.getMinY = function () {\n        return this.minY;\n    };\n    BoundingBox.prototype.getMaxY = function () {\n        return this.maxY;\n    };\n    BoundingBox.prototype.getTopLeft = function () {\n        return this.topLeft;\n    };\n    BoundingBox.prototype.getTopRight = function () {\n        return this.topRight;\n    };\n    BoundingBox.prototype.getBottomLeft = function () {\n        return this.bottomLeft;\n    };\n    BoundingBox.prototype.getBottomRight = function () {\n        return this.bottomRight;\n    };\n    return BoundingBox;\n}());\nexport default BoundingBox;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA,2CAA2C;AAC3C,6CAA6C;;;;AAC7C;AACA,uCAAuC;AACvC;;;AACA;;CAEC,GACD,IAAI,cAA6B;IAC7B,SAAS,YAAY,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW;QAClE,IAAI,iBAAiB,aAAa;YAC9B,IAAI,CAAC,aAAa,CAAC;QACvB,OACK;YACD,IAAI,CAAC,aAAa,CAAC,OAAO,SAAS,YAAY,UAAU;QAC7D;IACJ;IACA;;;;;;;;;KASC,GACD,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW;QAC7F,IAAI,kBAAkB,WAAW,QAAQ,cAAc;QACvD,IAAI,mBAAmB,YAAY,QAAQ,eAAe;QAC1D,IAAI,mBAAmB,kBAAkB;YACrC,MAAM,IAAI,yKAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,iBAAiB;YACjB,UAAU,IAAI,mKAAA,CAAA,UAAW,CAAC,GAAG,SAAS,IAAI;YAC1C,aAAa,IAAI,mKAAA,CAAA,UAAW,CAAC,GAAG,YAAY,IAAI;QACpD,OACK,IAAI,kBAAkB;YACvB,WAAW,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM,QAAQ,KAAK,GAAG,QAAQ,IAAI;YAC7D,cAAc,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM,QAAQ,KAAK,GAAG,WAAW,IAAI;QACvE;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI,WAAW,IAAI;QAC/D,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,IAAI,IAAI,YAAY,IAAI;QACjE,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI,SAAS,IAAI;QAC7D,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,WAAW,IAAI,IAAI,YAAY,IAAI;IACvE;IACA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,WAAW;QACvD,IAAI,CAAC,KAAK,GAAG,YAAY,KAAK;QAC9B,IAAI,CAAC,OAAO,GAAG,YAAY,UAAU;QACrC,IAAI,CAAC,UAAU,GAAG,YAAY,aAAa;QAC3C,IAAI,CAAC,QAAQ,GAAG,YAAY,WAAW;QACvC,IAAI,CAAC,WAAW,GAAG,YAAY,cAAc;QAC7C,IAAI,CAAC,IAAI,GAAG,YAAY,OAAO;QAC/B,IAAI,CAAC,IAAI,GAAG,YAAY,OAAO;QAC/B,IAAI,CAAC,IAAI,GAAG,YAAY,OAAO;QAC/B,IAAI,CAAC,IAAI,GAAG,YAAY,OAAO;IACnC;IACA;;KAEC,GACD,YAAY,KAAK,GAAG,SAAU,OAAO,EAAE,QAAQ;QAC3C,IAAI,WAAW,MAAM;YACjB,OAAO;QACX;QACA,IAAI,YAAY,MAAM;YAClB,OAAO;QACX;QACA,OAAO,IAAI,YAAY,QAAQ,KAAK,EAAE,QAAQ,OAAO,EAAE,QAAQ,UAAU,EAAE,SAAS,QAAQ,EAAE,SAAS,WAAW;IACtH;IACA;;KAEC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,gBAAgB,EAAE,cAAc,EAAE,MAAM;QACrF,IAAI,aAAa,IAAI,CAAC,OAAO;QAC7B,IAAI,gBAAgB,IAAI,CAAC,UAAU;QACnC,IAAI,cAAc,IAAI,CAAC,QAAQ;QAC/B,IAAI,iBAAiB,IAAI,CAAC,WAAW;QACrC,IAAI,mBAAmB,GAAG;YACtB,IAAI,QAAQ,SAAS,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ;YACjD,IAAI,UAAU,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK;YACxC,IAAI,UAAU,GAAG;gBACb,UAAU;YACd;YACA,IAAI,SAAS,IAAI,mKAAA,CAAA,UAAW,CAAC,MAAM,IAAI,IAAI;YAC3C,IAAI,QAAQ;gBACR,aAAa;YACjB,OACK;gBACD,cAAc;YAClB;QACJ;QACA,IAAI,iBAAiB,GAAG;YACpB,IAAI,SAAS,SAAS,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;YACxD,IAAI,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK;YACzC,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI;gBACnC,UAAU,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK;YACvC;YACA,IAAI,YAAY,IAAI,mKAAA,CAAA,UAAW,CAAC,OAAO,IAAI,IAAI;YAC/C,IAAI,QAAQ;gBACR,gBAAgB;YACpB,OACK;gBACD,iBAAiB;YACrB;QACJ;QACA,OAAO,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE,YAAY,eAAe,aAAa;IAC/E;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC5B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC5B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC5B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC5B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,YAAY,SAAS,CAAC,UAAU,GAAG;QAC/B,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,YAAY,SAAS,CAAC,WAAW,GAAG;QAChC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,YAAY,SAAS,CAAC,aAAa,GAAG;QAClC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,YAAY,SAAS,CAAC,cAAc,GAAG;QACnC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/BarcodeMetadata.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeMetadata = /** @class */ (function () {\n    function BarcodeMetadata(columnCount, rowCountUpperPart, rowCountLowerPart, errorCorrectionLevel) {\n        this.columnCount = columnCount;\n        this.errorCorrectionLevel = errorCorrectionLevel;\n        this.rowCountUpperPart = rowCountUpperPart;\n        this.rowCountLowerPart = rowCountLowerPart;\n        this.rowCount = rowCountUpperPart + rowCountLowerPart;\n    }\n    BarcodeMetadata.prototype.getColumnCount = function () {\n        return this.columnCount;\n    };\n    BarcodeMetadata.prototype.getErrorCorrectionLevel = function () {\n        return this.errorCorrectionLevel;\n    };\n    BarcodeMetadata.prototype.getRowCount = function () {\n        return this.rowCount;\n    };\n    BarcodeMetadata.prototype.getRowCountUpperPart = function () {\n        return this.rowCountUpperPart;\n    };\n    BarcodeMetadata.prototype.getRowCountLowerPart = function () {\n        return this.rowCountLowerPart;\n    };\n    return BarcodeMetadata;\n}());\nexport default BarcodeMetadata;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,2CAA2C;AAC3C;;CAEC;;;AACD,IAAI,kBAAiC;IACjC,SAAS,gBAAgB,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,oBAAoB;QAC5F,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG,oBAAoB;IACxC;IACA,gBAAgB,SAAS,CAAC,cAAc,GAAG;QACvC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,gBAAgB,SAAS,CAAC,uBAAuB,GAAG;QAChD,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,gBAAgB,SAAS,CAAC,WAAW,GAAG;QACpC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,gBAAgB,SAAS,CAAC,oBAAoB,GAAG;QAC7C,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,gBAAgB,SAAS,CAAC,oBAAoB,GAAG;QAC7C,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/DetectionResultColumn.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import java.util.Formatter;\nimport Formatter from '../../util/Formatter';\nimport BoundingBox from './BoundingBox';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultColumn = /** @class */ (function () {\n    function DetectionResultColumn(boundingBox) {\n        this.boundingBox = new BoundingBox(boundingBox);\n        // this.codewords = new Codeword[boundingBox.getMaxY() - boundingBox.getMinY() + 1];\n        this.codewords = new Array(boundingBox.getMaxY() - boundingBox.getMinY() + 1);\n    }\n    /*final*/ DetectionResultColumn.prototype.getCodewordNearby = function (imageRow) {\n        var codeword = this.getCodeword(imageRow);\n        if (codeword != null) {\n            return codeword;\n        }\n        for (var i = 1; i < DetectionResultColumn.MAX_NEARBY_DISTANCE; i++) {\n            var nearImageRow = this.imageRowToCodewordIndex(imageRow) - i;\n            if (nearImageRow >= 0) {\n                codeword = this.codewords[nearImageRow];\n                if (codeword != null) {\n                    return codeword;\n                }\n            }\n            nearImageRow = this.imageRowToCodewordIndex(imageRow) + i;\n            if (nearImageRow < this.codewords.length) {\n                codeword = this.codewords[nearImageRow];\n                if (codeword != null) {\n                    return codeword;\n                }\n            }\n        }\n        return null;\n    };\n    /*final int*/ DetectionResultColumn.prototype.imageRowToCodewordIndex = function (imageRow) {\n        return imageRow - this.boundingBox.getMinY();\n    };\n    /*final void*/ DetectionResultColumn.prototype.setCodeword = function (imageRow, codeword) {\n        this.codewords[this.imageRowToCodewordIndex(imageRow)] = codeword;\n    };\n    /*final*/ DetectionResultColumn.prototype.getCodeword = function (imageRow) {\n        return this.codewords[this.imageRowToCodewordIndex(imageRow)];\n    };\n    /*final*/ DetectionResultColumn.prototype.getBoundingBox = function () {\n        return this.boundingBox;\n    };\n    /*final*/ DetectionResultColumn.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    // @Override\n    DetectionResultColumn.prototype.toString = function () {\n        var e_1, _a;\n        var formatter = new Formatter();\n        var row = 0;\n        try {\n            for (var _b = __values(this.codewords), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value;\n                if (codeword == null) {\n                    formatter.format('%3d:    |   %n', row++);\n                    continue;\n                }\n                formatter.format('%3d: %3d|%3d%n', row++, codeword.getRowNumber(), codeword.getValue());\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return formatter.toString();\n    };\n    DetectionResultColumn.MAX_NEARBY_DISTANCE = 5;\n    return DetectionResultColumn;\n}());\nexport default DetectionResultColumn;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,2CAA2C;AAC3C,8BAA8B;AAC9B;AACA;AAdA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAKA;;CAEC,GACD,IAAI,wBAAuC;IACvC,SAAS,sBAAsB,WAAW;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,wLAAA,CAAA,UAAW,CAAC;QACnC,oFAAoF;QACpF,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK;IAC/E;IACA,OAAO,GAAG,sBAAsB,SAAS,CAAC,iBAAiB,GAAG,SAAU,QAAQ;QAC5E,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;QAChC,IAAI,YAAY,MAAM;YAClB,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,sBAAsB,mBAAmB,EAAE,IAAK;YAChE,IAAI,eAAe,IAAI,CAAC,uBAAuB,CAAC,YAAY;YAC5D,IAAI,gBAAgB,GAAG;gBACnB,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa;gBACvC,IAAI,YAAY,MAAM;oBAClB,OAAO;gBACX;YACJ;YACA,eAAe,IAAI,CAAC,uBAAuB,CAAC,YAAY;YACxD,IAAI,eAAe,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACtC,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa;gBACvC,IAAI,YAAY,MAAM;oBAClB,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,WAAW,GAAG,sBAAsB,SAAS,CAAC,uBAAuB,GAAG,SAAU,QAAQ;QACtF,OAAO,WAAW,IAAI,CAAC,WAAW,CAAC,OAAO;IAC9C;IACA,YAAY,GAAG,sBAAsB,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ,EAAE,QAAQ;QACrF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,GAAG;IAC7D;IACA,OAAO,GAAG,sBAAsB,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ;QACtE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU;IACjE;IACA,OAAO,GAAG,sBAAsB,SAAS,CAAC,cAAc,GAAG;QACvD,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,GAAG,sBAAsB,SAAS,CAAC,YAAY,GAAG;QACrD,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,YAAY;IACZ,sBAAsB,SAAS,CAAC,QAAQ,GAAG;QACvC,IAAI,KAAK;QACT,IAAI,YAAY,IAAI,yKAAA,CAAA,UAAS;QAC7B,IAAI,MAAM;QACV,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC9E,IAAI,WAAW,GAAG,KAAK;gBACvB,IAAI,YAAY,MAAM;oBAClB,UAAU,MAAM,CAAC,kBAAkB;oBACnC;gBACJ;gBACA,UAAU,MAAM,CAAC,kBAAkB,OAAO,SAAS,YAAY,IAAI,SAAS,QAAQ;YACxF;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,UAAU,QAAQ;IAC7B;IACA,sBAAsB,mBAAmB,GAAG;IAC5C,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7079, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/BarcodeValue.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.HashMap;\n// import java.util.Map;\n// import java.util.Map.Entry;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeValue = /** @class */ (function () {\n    function BarcodeValue() {\n        this.values = new Map();\n    }\n    /**\n     * Add an occurrence of a value\n     */\n    BarcodeValue.prototype.setValue = function (value) {\n        value = Math.trunc(value);\n        var confidence = this.values.get(value);\n        if (confidence == null) {\n            confidence = 0;\n        }\n        confidence++;\n        this.values.set(value, confidence);\n    };\n    /**\n     * Determines the maximum occurrence of a set value and returns all values which were set with this occurrence.\n     * @return an array of int, containing the values with the highest occurrence, or null, if no value was set\n     */\n    BarcodeValue.prototype.getValue = function () {\n        var e_1, _a;\n        var maxConfidence = -1;\n        var result = new Array();\n        var _loop_1 = function (key, value) {\n            var entry = {\n                getKey: function () { return key; },\n                getValue: function () { return value; },\n            };\n            if (entry.getValue() > maxConfidence) {\n                maxConfidence = entry.getValue();\n                result = [];\n                result.push(entry.getKey());\n            }\n            else if (entry.getValue() === maxConfidence) {\n                result.push(entry.getKey());\n            }\n        };\n        try {\n            for (var _b = __values(this.values.entries()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var _d = __read(_c.value, 2), key = _d[0], value = _d[1];\n                _loop_1(key, value);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return PDF417Common.toIntArray(result);\n    };\n    BarcodeValue.prototype.getConfidence = function (value) {\n        return this.values.get(value);\n    };\n    return BarcodeValue;\n}());\nexport default BarcodeValue;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AA4BD,2CAA2C;AAC3C,+CAA+C;AAC/C;AA7BA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;AACA,IAAI,SAAS,4CAAS,yCAAK,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACX;;AAIA,8BAA8B;AAC9B,+BAA+B;AAC/B,4BAA4B;AAC5B,wBAAwB;AACxB,8BAA8B;AAC9B;;CAEC,GACD,IAAI,eAA8B;IAC9B,SAAS;QACL,IAAI,CAAC,MAAM,GAAG,IAAI;IACtB;IACA;;KAEC,GACD,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC7C,QAAQ,KAAK,KAAK,CAAC;QACnB,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACjC,IAAI,cAAc,MAAM;YACpB,aAAa;QACjB;QACA;QACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;IAC3B;IACA;;;KAGC,GACD,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,IAAI,KAAK;QACT,IAAI,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI;QACjB,IAAI,UAAU,SAAU,GAAG,EAAE,KAAK;YAC9B,IAAI,QAAQ;gBACR,QAAQ;oBAAc,OAAO;gBAAK;gBAClC,UAAU;oBAAc,OAAO;gBAAO;YAC1C;YACA,IAAI,MAAM,QAAQ,KAAK,eAAe;gBAClC,gBAAgB,MAAM,QAAQ;gBAC9B,SAAS,EAAE;gBACX,OAAO,IAAI,CAAC,MAAM,MAAM;YAC5B,OACK,IAAI,MAAM,QAAQ,OAAO,eAAe;gBACzC,OAAO,IAAI,CAAC,MAAM,MAAM;YAC5B;QACJ;QACA,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACrF,IAAI,KAAK,OAAO,GAAG,KAAK,EAAE,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;gBACxD,QAAQ,KAAK;YACjB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,8KAAA,CAAA,UAAY,CAAC,UAAU,CAAC;IACnC;IACA,aAAa,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QAClD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IAC3B;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/DetectionResultRowIndicatorColumn.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport BarcodeMetadata from './BarcodeMetadata';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport BarcodeValue from './BarcodeValue';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultRowIndicatorColumn = /** @class */ (function (_super) {\n    __extends(DetectionResultRowIndicatorColumn, _super);\n    function DetectionResultRowIndicatorColumn(boundingBox, isLeft) {\n        var _this = _super.call(this, boundingBox) || this;\n        _this._isLeft = isLeft;\n        return _this;\n    }\n    DetectionResultRowIndicatorColumn.prototype.setRowNumbers = function () {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value /*Codeword*/;\n                if (codeword != null) {\n                    codeword.setRowNumberAsRowIndicatorColumn();\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    // TODO implement properly\n    // TODO maybe we should add missing codewords to store the correct row number to make\n    // finding row numbers for other columns easier\n    // use row height count to make detection of invalid row numbers more reliable\n    DetectionResultRowIndicatorColumn.prototype.adjustCompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n        var codewords = this.getCodewords();\n        this.setRowNumbers();\n        this.removeIncorrectCodewords(codewords, barcodeMetadata);\n        var boundingBox = this.getBoundingBox();\n        var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n        var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n        var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n        var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n        // We need to be careful using the average row height. Barcode could be skewed so that we have smaller and\n        // taller rows\n        // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n        var barcodeRow = -1;\n        var maxRowHeight = 1;\n        var currentRowHeight = 0;\n        for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var codeword = codewords[codewordsRow];\n            //      float expectedRowNumber = (codewordsRow - firstRow) / averageRowHeight;\n            //      if (Math.abs(codeword.getRowNumber() - expectedRowNumber) > 2) {\n            //        SimpleLog.log(LEVEL.WARNING,\n            //            \"Removing codeword, rowNumberSkew too high, codeword[\" + codewordsRow + \"]: Expected Row: \" +\n            //                expectedRowNumber + \", RealRow: \" + codeword.getRowNumber() + \", value: \" + codeword.getValue());\n            //        codewords[codewordsRow] = null;\n            //      }\n            var rowDifference = codeword.getRowNumber() - barcodeRow;\n            // TODO improve handling with case where first row indicator doesn't start with 0\n            if (rowDifference === 0) {\n                currentRowHeight++;\n            }\n            else if (rowDifference === 1) {\n                maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n                currentRowHeight = 1;\n                barcodeRow = codeword.getRowNumber();\n            }\n            else if (rowDifference < 0 ||\n                codeword.getRowNumber() >= barcodeMetadata.getRowCount() ||\n                rowDifference > codewordsRow) {\n                codewords[codewordsRow] = null;\n            }\n            else {\n                var checkedRows = void 0;\n                if (maxRowHeight > 2) {\n                    checkedRows = (maxRowHeight - 2) * rowDifference;\n                }\n                else {\n                    checkedRows = rowDifference;\n                }\n                var closePreviousCodewordFound = checkedRows >= codewordsRow;\n                for (var i /*int*/ = 1; i <= checkedRows && !closePreviousCodewordFound; i++) {\n                    // there must be (height * rowDifference) number of codewords missing. For now we assume height = 1.\n                    // This should hopefully get rid of most problems already.\n                    closePreviousCodewordFound = codewords[codewordsRow - i] != null;\n                }\n                if (closePreviousCodewordFound) {\n                    codewords[codewordsRow] = null;\n                }\n                else {\n                    barcodeRow = codeword.getRowNumber();\n                    currentRowHeight = 1;\n                }\n            }\n        }\n        // return (int) (averageRowHeight + 0.5);\n    };\n    DetectionResultRowIndicatorColumn.prototype.getRowHeights = function () {\n        var e_2, _a;\n        var barcodeMetadata = this.getBarcodeMetadata();\n        if (barcodeMetadata == null) {\n            return null;\n        }\n        this.adjustIncompleteIndicatorColumnRowNumbers(barcodeMetadata);\n        var result = new Int32Array(barcodeMetadata.getRowCount());\n        try {\n            for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value /*Codeword*/;\n                if (codeword != null) {\n                    var rowNumber = codeword.getRowNumber();\n                    if (rowNumber >= result.length) {\n                        // We have more rows than the barcode metadata allows for, ignore them.\n                        continue;\n                    }\n                    result[rowNumber]++;\n                } // else throw exception?\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return result;\n    };\n    // TODO maybe we should add missing codewords to store the correct row number to make\n    // finding row numbers for other columns easier\n    // use row height count to make detection of invalid row numbers more reliable\n    DetectionResultRowIndicatorColumn.prototype.adjustIncompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n        var boundingBox = this.getBoundingBox();\n        var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n        var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n        var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n        var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n        // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n        var codewords = this.getCodewords();\n        var barcodeRow = -1;\n        var maxRowHeight = 1;\n        var currentRowHeight = 0;\n        for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var codeword = codewords[codewordsRow];\n            codeword.setRowNumberAsRowIndicatorColumn();\n            var rowDifference = codeword.getRowNumber() - barcodeRow;\n            // TODO improve handling with case where first row indicator doesn't start with 0\n            if (rowDifference === 0) {\n                currentRowHeight++;\n            }\n            else if (rowDifference === 1) {\n                maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n                currentRowHeight = 1;\n                barcodeRow = codeword.getRowNumber();\n            }\n            else if (codeword.getRowNumber() >= barcodeMetadata.getRowCount()) {\n                codewords[codewordsRow] = null;\n            }\n            else {\n                barcodeRow = codeword.getRowNumber();\n                currentRowHeight = 1;\n            }\n        }\n        // return (int) (averageRowHeight + 0.5);\n    };\n    DetectionResultRowIndicatorColumn.prototype.getBarcodeMetadata = function () {\n        var e_3, _a;\n        var codewords = this.getCodewords();\n        var barcodeColumnCount = new BarcodeValue();\n        var barcodeRowCountUpperPart = new BarcodeValue();\n        var barcodeRowCountLowerPart = new BarcodeValue();\n        var barcodeECLevel = new BarcodeValue();\n        try {\n            for (var codewords_1 = __values(codewords), codewords_1_1 = codewords_1.next(); !codewords_1_1.done; codewords_1_1 = codewords_1.next()) {\n                var codeword = codewords_1_1.value /*Codeword*/;\n                if (codeword == null) {\n                    continue;\n                }\n                codeword.setRowNumberAsRowIndicatorColumn();\n                var rowIndicatorValue = codeword.getValue() % 30;\n                var codewordRowNumber = codeword.getRowNumber();\n                if (!this._isLeft) {\n                    codewordRowNumber += 2;\n                }\n                switch (codewordRowNumber % 3) {\n                    case 0:\n                        barcodeRowCountUpperPart.setValue(rowIndicatorValue * 3 + 1);\n                        break;\n                    case 1:\n                        barcodeECLevel.setValue(rowIndicatorValue / 3);\n                        barcodeRowCountLowerPart.setValue(rowIndicatorValue % 3);\n                        break;\n                    case 2:\n                        barcodeColumnCount.setValue(rowIndicatorValue + 1);\n                        break;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (codewords_1_1 && !codewords_1_1.done && (_a = codewords_1.return)) _a.call(codewords_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        // Maybe we should check if we have ambiguous values?\n        if ((barcodeColumnCount.getValue().length === 0) ||\n            (barcodeRowCountUpperPart.getValue().length === 0) ||\n            (barcodeRowCountLowerPart.getValue().length === 0) ||\n            (barcodeECLevel.getValue().length === 0) ||\n            barcodeColumnCount.getValue()[0] < 1 ||\n            barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] < PDF417Common.MIN_ROWS_IN_BARCODE ||\n            barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] > PDF417Common.MAX_ROWS_IN_BARCODE) {\n            return null;\n        }\n        var barcodeMetadata = new BarcodeMetadata(barcodeColumnCount.getValue()[0], barcodeRowCountUpperPart.getValue()[0], barcodeRowCountLowerPart.getValue()[0], barcodeECLevel.getValue()[0]);\n        this.removeIncorrectCodewords(codewords, barcodeMetadata);\n        return barcodeMetadata;\n    };\n    DetectionResultRowIndicatorColumn.prototype.removeIncorrectCodewords = function (codewords, barcodeMetadata) {\n        // Remove codewords which do not match the metadata\n        // TODO Maybe we should keep the incorrect codewords for the start and end positions?\n        for (var codewordRow /*int*/ = 0; codewordRow < codewords.length; codewordRow++) {\n            var codeword = codewords[codewordRow];\n            if (codewords[codewordRow] == null) {\n                continue;\n            }\n            var rowIndicatorValue = codeword.getValue() % 30;\n            var codewordRowNumber = codeword.getRowNumber();\n            if (codewordRowNumber > barcodeMetadata.getRowCount()) {\n                codewords[codewordRow] = null;\n                continue;\n            }\n            if (!this._isLeft) {\n                codewordRowNumber += 2;\n            }\n            switch (codewordRowNumber % 3) {\n                case 0:\n                    if (rowIndicatorValue * 3 + 1 !== barcodeMetadata.getRowCountUpperPart()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n                case 1:\n                    if (Math.trunc(rowIndicatorValue / 3) !== barcodeMetadata.getErrorCorrectionLevel() ||\n                        rowIndicatorValue % 3 !== barcodeMetadata.getRowCountLowerPart()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n                case 2:\n                    if (rowIndicatorValue + 1 !== barcodeMetadata.getColumnCount()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n            }\n        }\n    };\n    DetectionResultRowIndicatorColumn.prototype.isLeft = function () {\n        return this._isLeft;\n    };\n    // @Override\n    DetectionResultRowIndicatorColumn.prototype.toString = function () {\n        return 'IsLeft: ' + this._isLeft + '\\n' + _super.prototype.toString.call(this);\n    };\n    return DetectionResultRowIndicatorColumn;\n}(DetectionResultColumn));\nexport default DetectionResultRowIndicatorColumn;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAyBD,+CAA+C;AAC/C;AACA;AACA;AACA;AA5BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;AAMA;;CAEC,GACD,IAAI,oCAAmD,SAAU,MAAM;IACnE,UAAU,mCAAmC;IAC7C,SAAS,kCAAkC,WAAW,EAAE,MAAM;QAC1D,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI;QAClD,MAAM,OAAO,GAAG;QAChB,OAAO;IACX;IACA,kCAAkC,SAAS,CAAC,aAAa,GAAG;QACxD,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACnF,IAAI,WAAW,GAAG,KAAK,CAAC,UAAU;gBAClC,IAAI,YAAY,MAAM;oBAClB,SAAS,gCAAgC;gBAC7C;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA,0BAA0B;IAC1B,qFAAqF;IACrF,+CAA+C;IAC/C,8EAA8E;IAC9E,kCAAkC,SAAS,CAAC,uCAAuC,GAAG,SAAU,eAAe;QAC3G,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,wBAAwB,CAAC,WAAW;QACzC,IAAI,cAAc,IAAI,CAAC,cAAc;QACrC,IAAI,MAAM,IAAI,CAAC,OAAO,GAAG,YAAY,UAAU,KAAK,YAAY,WAAW;QAC3E,IAAI,SAAS,IAAI,CAAC,OAAO,GAAG,YAAY,aAAa,KAAK,YAAY,cAAc;QACpF,IAAI,WAAW,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI;QAC/D,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,OAAO,IAAI;QACjE,0GAA0G;QAC1G,cAAc;QACd,6FAA6F;QAC7F,IAAI,aAAa,CAAC;QAClB,IAAI,eAAe;QACnB,IAAI,mBAAmB;QACvB,IAAK,IAAI,aAAa,KAAK,MAAK,UAAU,eAAe,SAAS,eAAgB;YAC9E,IAAI,SAAS,CAAC,aAAa,IAAI,MAAM;gBACjC;YACJ;YACA,IAAI,WAAW,SAAS,CAAC,aAAa;YACtC,+EAA+E;YAC/E,wEAAwE;YACxE,sCAAsC;YACtC,2GAA2G;YAC3G,mHAAmH;YACnH,yCAAyC;YACzC,SAAS;YACT,IAAI,gBAAgB,SAAS,YAAY,KAAK;YAC9C,iFAAiF;YACjF,IAAI,kBAAkB,GAAG;gBACrB;YACJ,OACK,IAAI,kBAAkB,GAAG;gBAC1B,eAAe,KAAK,GAAG,CAAC,cAAc;gBACtC,mBAAmB;gBACnB,aAAa,SAAS,YAAY;YACtC,OACK,IAAI,gBAAgB,KACrB,SAAS,YAAY,MAAM,gBAAgB,WAAW,MACtD,gBAAgB,cAAc;gBAC9B,SAAS,CAAC,aAAa,GAAG;YAC9B,OACK;gBACD,IAAI,cAAc,KAAK;gBACvB,IAAI,eAAe,GAAG;oBAClB,cAAc,CAAC,eAAe,CAAC,IAAI;gBACvC,OACK;oBACD,cAAc;gBAClB;gBACA,IAAI,6BAA6B,eAAe;gBAChD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,KAAK,eAAe,CAAC,4BAA4B,IAAK;oBAC1E,oGAAoG;oBACpG,0DAA0D;oBAC1D,6BAA6B,SAAS,CAAC,eAAe,EAAE,IAAI;gBAChE;gBACA,IAAI,4BAA4B;oBAC5B,SAAS,CAAC,aAAa,GAAG;gBAC9B,OACK;oBACD,aAAa,SAAS,YAAY;oBAClC,mBAAmB;gBACvB;YACJ;QACJ;IACA,yCAAyC;IAC7C;IACA,kCAAkC,SAAS,CAAC,aAAa,GAAG;QACxD,IAAI,KAAK;QACT,IAAI,kBAAkB,IAAI,CAAC,kBAAkB;QAC7C,IAAI,mBAAmB,MAAM;YACzB,OAAO;QACX;QACA,IAAI,CAAC,yCAAyC,CAAC;QAC/C,IAAI,SAAS,IAAI,WAAW,gBAAgB,WAAW;QACvD,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBACnF,IAAI,WAAW,GAAG,KAAK,CAAC,UAAU;gBAClC,IAAI,YAAY,MAAM;oBAClB,IAAI,YAAY,SAAS,YAAY;oBACrC,IAAI,aAAa,OAAO,MAAM,EAAE;wBAE5B;oBACJ;oBACA,MAAM,CAAC,UAAU;gBACrB,EAAE,wBAAwB;YAC9B;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,qFAAqF;IACrF,+CAA+C;IAC/C,8EAA8E;IAC9E,kCAAkC,SAAS,CAAC,yCAAyC,GAAG,SAAU,eAAe;QAC7G,IAAI,cAAc,IAAI,CAAC,cAAc;QACrC,IAAI,MAAM,IAAI,CAAC,OAAO,GAAG,YAAY,UAAU,KAAK,YAAY,WAAW;QAC3E,IAAI,SAAS,IAAI,CAAC,OAAO,GAAG,YAAY,aAAa,KAAK,YAAY,cAAc;QACpF,IAAI,WAAW,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI;QAC/D,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,OAAO,IAAI;QACjE,6FAA6F;QAC7F,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,aAAa,CAAC;QAClB,IAAI,eAAe;QACnB,IAAI,mBAAmB;QACvB,IAAK,IAAI,aAAa,KAAK,MAAK,UAAU,eAAe,SAAS,eAAgB;YAC9E,IAAI,SAAS,CAAC,aAAa,IAAI,MAAM;gBACjC;YACJ;YACA,IAAI,WAAW,SAAS,CAAC,aAAa;YACtC,SAAS,gCAAgC;YACzC,IAAI,gBAAgB,SAAS,YAAY,KAAK;YAC9C,iFAAiF;YACjF,IAAI,kBAAkB,GAAG;gBACrB;YACJ,OACK,IAAI,kBAAkB,GAAG;gBAC1B,eAAe,KAAK,GAAG,CAAC,cAAc;gBACtC,mBAAmB;gBACnB,aAAa,SAAS,YAAY;YACtC,OACK,IAAI,SAAS,YAAY,MAAM,gBAAgB,WAAW,IAAI;gBAC/D,SAAS,CAAC,aAAa,GAAG;YAC9B,OACK;gBACD,aAAa,SAAS,YAAY;gBAClC,mBAAmB;YACvB;QACJ;IACA,yCAAyC;IAC7C;IACA,kCAAkC,SAAS,CAAC,kBAAkB,GAAG;QAC7D,IAAI,KAAK;QACT,IAAI,YAAY,IAAI,CAAC,YAAY;QACjC,IAAI,qBAAqB,IAAI,yLAAA,CAAA,UAAY;QACzC,IAAI,2BAA2B,IAAI,yLAAA,CAAA,UAAY;QAC/C,IAAI,2BAA2B,IAAI,yLAAA,CAAA,UAAY;QAC/C,IAAI,iBAAiB,IAAI,yLAAA,CAAA,UAAY;QACrC,IAAI;YACA,IAAK,IAAI,cAAc,SAAS,YAAY,gBAAgB,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,EAAE,gBAAgB,YAAY,IAAI,GAAI;gBACrI,IAAI,WAAW,cAAc,KAAK,CAAC,UAAU;gBAC7C,IAAI,YAAY,MAAM;oBAClB;gBACJ;gBACA,SAAS,gCAAgC;gBACzC,IAAI,oBAAoB,SAAS,QAAQ,KAAK;gBAC9C,IAAI,oBAAoB,SAAS,YAAY;gBAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACf,qBAAqB;gBACzB;gBACA,OAAQ,oBAAoB;oBACxB,KAAK;wBACD,yBAAyB,QAAQ,CAAC,oBAAoB,IAAI;wBAC1D;oBACJ,KAAK;wBACD,eAAe,QAAQ,CAAC,oBAAoB;wBAC5C,yBAAyB,QAAQ,CAAC,oBAAoB;wBACtD;oBACJ,KAAK;wBACD,mBAAmB,QAAQ,CAAC,oBAAoB;wBAChD;gBACR;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,iBAAiB,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,YAAY,MAAM,GAAG,GAAG,IAAI,CAAC;YACnF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,qDAAqD;QACrD,IAAI,AAAC,mBAAmB,QAAQ,GAAG,MAAM,KAAK,KACzC,yBAAyB,QAAQ,GAAG,MAAM,KAAK,KAC/C,yBAAyB,QAAQ,GAAG,MAAM,KAAK,KAC/C,eAAe,QAAQ,GAAG,MAAM,KAAK,KACtC,mBAAmB,QAAQ,EAAE,CAAC,EAAE,GAAG,KACnC,yBAAyB,QAAQ,EAAE,CAAC,EAAE,GAAG,yBAAyB,QAAQ,EAAE,CAAC,EAAE,GAAG,8KAAA,CAAA,UAAY,CAAC,mBAAmB,IAClH,yBAAyB,QAAQ,EAAE,CAAC,EAAE,GAAG,yBAAyB,QAAQ,EAAE,CAAC,EAAE,GAAG,8KAAA,CAAA,UAAY,CAAC,mBAAmB,EAAE;YACpH,OAAO;QACX;QACA,IAAI,kBAAkB,IAAI,4LAAA,CAAA,UAAe,CAAC,mBAAmB,QAAQ,EAAE,CAAC,EAAE,EAAE,yBAAyB,QAAQ,EAAE,CAAC,EAAE,EAAE,yBAAyB,QAAQ,EAAE,CAAC,EAAE,EAAE,eAAe,QAAQ,EAAE,CAAC,EAAE;QACxL,IAAI,CAAC,wBAAwB,CAAC,WAAW;QACzC,OAAO;IACX;IACA,kCAAkC,SAAS,CAAC,wBAAwB,GAAG,SAAU,SAAS,EAAE,eAAe;QACvG,mDAAmD;QACnD,qFAAqF;QACrF,IAAK,IAAI,YAAY,KAAK,MAAK,GAAG,cAAc,UAAU,MAAM,EAAE,cAAe;YAC7E,IAAI,WAAW,SAAS,CAAC,YAAY;YACrC,IAAI,SAAS,CAAC,YAAY,IAAI,MAAM;gBAChC;YACJ;YACA,IAAI,oBAAoB,SAAS,QAAQ,KAAK;YAC9C,IAAI,oBAAoB,SAAS,YAAY;YAC7C,IAAI,oBAAoB,gBAAgB,WAAW,IAAI;gBACnD,SAAS,CAAC,YAAY,GAAG;gBACzB;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,qBAAqB;YACzB;YACA,OAAQ,oBAAoB;gBACxB,KAAK;oBACD,IAAI,oBAAoB,IAAI,MAAM,gBAAgB,oBAAoB,IAAI;wBACtE,SAAS,CAAC,YAAY,GAAG;oBAC7B;oBACA;gBACJ,KAAK;oBACD,IAAI,KAAK,KAAK,CAAC,oBAAoB,OAAO,gBAAgB,uBAAuB,MAC7E,oBAAoB,MAAM,gBAAgB,oBAAoB,IAAI;wBAClE,SAAS,CAAC,YAAY,GAAG;oBAC7B;oBACA;gBACJ,KAAK;oBACD,IAAI,oBAAoB,MAAM,gBAAgB,cAAc,IAAI;wBAC5D,SAAS,CAAC,YAAY,GAAG;oBAC7B;oBACA;YACR;QACJ;IACJ;IACA,kCAAkC,SAAS,CAAC,MAAM,GAAG;QACjD,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,YAAY;IACZ,kCAAkC,SAAS,CAAC,QAAQ,GAAG;QACnD,OAAO,aAAa,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;IACjF;IACA,OAAO;AACX,EAAE,kMAAA,CAAA,UAAqB;uCACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/DetectionResult.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Formatter from '../../util/Formatter';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResult = /** @class */ (function () {\n    function DetectionResult(barcodeMetadata, boundingBox) {\n        /*final*/ this.ADJUST_ROW_NUMBER_SKIP = 2;\n        this.barcodeMetadata = barcodeMetadata;\n        this.barcodeColumnCount = barcodeMetadata.getColumnCount();\n        this.boundingBox = boundingBox;\n        // this.detectionResultColumns = new DetectionResultColumn[this.barcodeColumnCount + 2];\n        this.detectionResultColumns = new Array(this.barcodeColumnCount + 2);\n    }\n    DetectionResult.prototype.getDetectionResultColumns = function () {\n        this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]);\n        this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount + 1]);\n        var unadjustedCodewordCount = PDF417Common.MAX_CODEWORDS_IN_BARCODE;\n        var previousUnadjustedCount;\n        do {\n            previousUnadjustedCount = unadjustedCodewordCount;\n            unadjustedCodewordCount = this.adjustRowNumbersAndGetCount();\n        } while (unadjustedCodewordCount > 0 && unadjustedCodewordCount < previousUnadjustedCount);\n        return this.detectionResultColumns;\n    };\n    DetectionResult.prototype.adjustIndicatorColumnRowNumbers = function (detectionResultColumn) {\n        if (detectionResultColumn != null) {\n            detectionResultColumn\n                .adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata);\n        }\n    };\n    // TODO ensure that no detected codewords with unknown row number are left\n    // we should be able to estimate the row height and use it as a hint for the row number\n    // we should also fill the rows top to bottom and bottom to top\n    /**\n     * @return number of codewords which don't have a valid row number. Note that the count is not accurate as codewords\n     * will be counted several times. It just serves as an indicator to see when we can stop adjusting row numbers\n     */\n    DetectionResult.prototype.adjustRowNumbersAndGetCount = function () {\n        var unadjustedCount = this.adjustRowNumbersByRow();\n        if (unadjustedCount === 0) {\n            return 0;\n        }\n        for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1; barcodeColumn++) {\n            var codewords = this.detectionResultColumns[barcodeColumn].getCodewords();\n            for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n                if (codewords[codewordsRow] == null) {\n                    continue;\n                }\n                if (!codewords[codewordsRow].hasValidRowNumber()) {\n                    this.adjustRowNumbers(barcodeColumn, codewordsRow, codewords);\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.prototype.adjustRowNumbersByRow = function () {\n        this.adjustRowNumbersFromBothRI();\n        // TODO we should only do full row adjustments if row numbers of left and right row indicator column match.\n        // Maybe it's even better to calculated the height (rows: d) and divide it by the number of barcode\n        // rows. This, together with the LRI and RRI row numbers should allow us to get a good estimate where a row\n        // number starts and ends.\n        var unadjustedCount = this.adjustRowNumbersFromLRI();\n        return unadjustedCount + this.adjustRowNumbersFromRRI();\n    };\n    DetectionResult.prototype.adjustRowNumbersFromBothRI = function () {\n        if (this.detectionResultColumns[0] == null || this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n            return;\n        }\n        var LRIcodewords = this.detectionResultColumns[0].getCodewords();\n        var RRIcodewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < LRIcodewords.length; codewordsRow++) {\n            if (LRIcodewords[codewordsRow] != null &&\n                RRIcodewords[codewordsRow] != null &&\n                LRIcodewords[codewordsRow].getRowNumber() === RRIcodewords[codewordsRow].getRowNumber()) {\n                for (var barcodeColumn /*int*/ = 1; barcodeColumn <= this.barcodeColumnCount; barcodeColumn++) {\n                    var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                    if (codeword == null) {\n                        continue;\n                    }\n                    codeword.setRowNumber(LRIcodewords[codewordsRow].getRowNumber());\n                    if (!codeword.hasValidRowNumber()) {\n                        this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow] = null;\n                    }\n                }\n            }\n        }\n    };\n    DetectionResult.prototype.adjustRowNumbersFromRRI = function () {\n        if (this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n            return 0;\n        }\n        var unadjustedCount = 0;\n        var codewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n            var invalidRowCounts = 0;\n            for (var barcodeColumn /*int*/ = this.barcodeColumnCount + 1; barcodeColumn > 0 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn--) {\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword != null) {\n                    invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n                    if (!codeword.hasValidRowNumber()) {\n                        unadjustedCount++;\n                    }\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.prototype.adjustRowNumbersFromLRI = function () {\n        if (this.detectionResultColumns[0] == null) {\n            return 0;\n        }\n        var unadjustedCount = 0;\n        var codewords = this.detectionResultColumns[0].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n            var invalidRowCounts = 0;\n            for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn++) {\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword != null) {\n                    invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n                    if (!codeword.hasValidRowNumber()) {\n                        unadjustedCount++;\n                    }\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.adjustRowNumberIfValid = function (rowIndicatorRowNumber, invalidRowCounts, codeword) {\n        if (codeword == null) {\n            return invalidRowCounts;\n        }\n        if (!codeword.hasValidRowNumber()) {\n            if (codeword.isValidRowNumber(rowIndicatorRowNumber)) {\n                codeword.setRowNumber(rowIndicatorRowNumber);\n                invalidRowCounts = 0;\n            }\n            else {\n                ++invalidRowCounts;\n            }\n        }\n        return invalidRowCounts;\n    };\n    DetectionResult.prototype.adjustRowNumbers = function (barcodeColumn, codewordsRow, codewords) {\n        var e_1, _a;\n        if (this.detectionResultColumns[barcodeColumn - 1] == null) {\n            return;\n        }\n        var codeword = codewords[codewordsRow];\n        var previousColumnCodewords = this.detectionResultColumns[barcodeColumn - 1].getCodewords();\n        var nextColumnCodewords = previousColumnCodewords;\n        if (this.detectionResultColumns[barcodeColumn + 1] != null) {\n            nextColumnCodewords = this.detectionResultColumns[barcodeColumn + 1].getCodewords();\n        }\n        // let otherCodewords: Codeword[] = new Codeword[14];\n        var otherCodewords = new Array(14);\n        otherCodewords[2] = previousColumnCodewords[codewordsRow];\n        otherCodewords[3] = nextColumnCodewords[codewordsRow];\n        if (codewordsRow > 0) {\n            otherCodewords[0] = codewords[codewordsRow - 1];\n            otherCodewords[4] = previousColumnCodewords[codewordsRow - 1];\n            otherCodewords[5] = nextColumnCodewords[codewordsRow - 1];\n        }\n        if (codewordsRow > 1) {\n            otherCodewords[8] = codewords[codewordsRow - 2];\n            otherCodewords[10] = previousColumnCodewords[codewordsRow - 2];\n            otherCodewords[11] = nextColumnCodewords[codewordsRow - 2];\n        }\n        if (codewordsRow < codewords.length - 1) {\n            otherCodewords[1] = codewords[codewordsRow + 1];\n            otherCodewords[6] = previousColumnCodewords[codewordsRow + 1];\n            otherCodewords[7] = nextColumnCodewords[codewordsRow + 1];\n        }\n        if (codewordsRow < codewords.length - 2) {\n            otherCodewords[9] = codewords[codewordsRow + 2];\n            otherCodewords[12] = previousColumnCodewords[codewordsRow + 2];\n            otherCodewords[13] = nextColumnCodewords[codewordsRow + 2];\n        }\n        try {\n            for (var otherCodewords_1 = __values(otherCodewords), otherCodewords_1_1 = otherCodewords_1.next(); !otherCodewords_1_1.done; otherCodewords_1_1 = otherCodewords_1.next()) {\n                var otherCodeword = otherCodewords_1_1.value;\n                if (DetectionResult.adjustRowNumber(codeword, otherCodeword)) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (otherCodewords_1_1 && !otherCodewords_1_1.done && (_a = otherCodewords_1.return)) _a.call(otherCodewords_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    /**\n     * @return true, if row number was adjusted, false otherwise\n     */\n    DetectionResult.adjustRowNumber = function (codeword, otherCodeword) {\n        if (otherCodeword == null) {\n            return false;\n        }\n        if (otherCodeword.hasValidRowNumber() && otherCodeword.getBucket() === codeword.getBucket()) {\n            codeword.setRowNumber(otherCodeword.getRowNumber());\n            return true;\n        }\n        return false;\n    };\n    DetectionResult.prototype.getBarcodeColumnCount = function () {\n        return this.barcodeColumnCount;\n    };\n    DetectionResult.prototype.getBarcodeRowCount = function () {\n        return this.barcodeMetadata.getRowCount();\n    };\n    DetectionResult.prototype.getBarcodeECLevel = function () {\n        return this.barcodeMetadata.getErrorCorrectionLevel();\n    };\n    DetectionResult.prototype.setBoundingBox = function (boundingBox) {\n        this.boundingBox = boundingBox;\n    };\n    DetectionResult.prototype.getBoundingBox = function () {\n        return this.boundingBox;\n    };\n    DetectionResult.prototype.setDetectionResultColumn = function (barcodeColumn, detectionResultColumn) {\n        this.detectionResultColumns[barcodeColumn] = detectionResultColumn;\n    };\n    DetectionResult.prototype.getDetectionResultColumn = function (barcodeColumn) {\n        return this.detectionResultColumns[barcodeColumn];\n    };\n    // @Override\n    DetectionResult.prototype.toString = function () {\n        var rowIndicatorColumn = this.detectionResultColumns[0];\n        if (rowIndicatorColumn == null) {\n            rowIndicatorColumn = this.detectionResultColumns[this.barcodeColumnCount + 1];\n        }\n        // try (\n        var formatter = new Formatter();\n        // ) {\n        for (var codewordsRow /*int*/ = 0; codewordsRow < rowIndicatorColumn.getCodewords().length; codewordsRow++) {\n            formatter.format('CW %3d:', codewordsRow);\n            for (var barcodeColumn /*int*/ = 0; barcodeColumn < this.barcodeColumnCount + 2; barcodeColumn++) {\n                if (this.detectionResultColumns[barcodeColumn] == null) {\n                    formatter.format('    |   ');\n                    continue;\n                }\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword == null) {\n                    formatter.format('    |   ');\n                    continue;\n                }\n                formatter.format(' %3d|%3d', codeword.getRowNumber(), codeword.getValue());\n            }\n            formatter.format('%n');\n        }\n        return formatter.toString();\n        // }\n    };\n    return DetectionResult;\n}());\nexport default DetectionResult;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,2CAA2C;AAC3C,+CAA+C;AAC/C;AACA;AAdA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;AAKA;;CAEC,GACD,IAAI,kBAAiC;IACjC,SAAS,gBAAgB,eAAe,EAAE,WAAW;QACjD,OAAO,GAAG,IAAI,CAAC,sBAAsB,GAAG;QACxC,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,cAAc;QACxD,IAAI,CAAC,WAAW,GAAG;QACnB,wFAAwF;QACxF,IAAI,CAAC,sBAAsB,GAAG,IAAI,MAAM,IAAI,CAAC,kBAAkB,GAAG;IACtE;IACA,gBAAgB,SAAS,CAAC,yBAAyB,GAAG;QAClD,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE;QACnE,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAC7F,IAAI,0BAA0B,8KAAA,CAAA,UAAY,CAAC,wBAAwB;QACnE,IAAI;QACJ,GAAG;YACC,0BAA0B;YAC1B,0BAA0B,IAAI,CAAC,2BAA2B;QAC9D,QAAS,0BAA0B,KAAK,0BAA0B,wBAAyB;QAC3F,OAAO,IAAI,CAAC,sBAAsB;IACtC;IACA,gBAAgB,SAAS,CAAC,+BAA+B,GAAG,SAAU,qBAAqB;QACvF,IAAI,yBAAyB,MAAM;YAC/B,sBACK,uCAAuC,CAAC,IAAI,CAAC,eAAe;QACrE;IACJ;IACA,0EAA0E;IAC1E,uFAAuF;IACvF,+DAA+D;IAC/D;;;KAGC,GACD,gBAAgB,SAAS,CAAC,2BAA2B,GAAG;QACpD,IAAI,kBAAkB,IAAI,CAAC,qBAAqB;QAChD,IAAI,oBAAoB,GAAG;YACvB,OAAO;QACX;QACA,IAAK,IAAI,cAAc,KAAK,MAAK,GAAG,gBAAgB,IAAI,CAAC,kBAAkB,GAAG,GAAG,gBAAiB;YAC9F,IAAI,YAAY,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY;YACvE,IAAK,IAAI,aAAa,KAAK,MAAK,GAAG,eAAe,UAAU,MAAM,EAAE,eAAgB;gBAChF,IAAI,SAAS,CAAC,aAAa,IAAI,MAAM;oBACjC;gBACJ;gBACA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,iBAAiB,IAAI;oBAC9C,IAAI,CAAC,gBAAgB,CAAC,eAAe,cAAc;gBACvD;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG;QAC9C,IAAI,CAAC,0BAA0B;QAC/B,2GAA2G;QAC3G,mGAAmG;QACnG,2GAA2G;QAC3G,0BAA0B;QAC1B,IAAI,kBAAkB,IAAI,CAAC,uBAAuB;QAClD,OAAO,kBAAkB,IAAI,CAAC,uBAAuB;IACzD;IACA,gBAAgB,SAAS,CAAC,0BAA0B,GAAG;QACnD,IAAI,IAAI,CAAC,sBAAsB,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,IAAI,MAAM;YAC5G;QACJ;QACA,IAAI,eAAe,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,YAAY;QAC9D,IAAI,eAAe,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,YAAY;QACxF,IAAK,IAAI,aAAa,KAAK,MAAK,GAAG,eAAe,aAAa,MAAM,EAAE,eAAgB;YACnF,IAAI,YAAY,CAAC,aAAa,IAAI,QAC9B,YAAY,CAAC,aAAa,IAAI,QAC9B,YAAY,CAAC,aAAa,CAAC,YAAY,OAAO,YAAY,CAAC,aAAa,CAAC,YAAY,IAAI;gBACzF,IAAK,IAAI,cAAc,KAAK,MAAK,GAAG,iBAAiB,IAAI,CAAC,kBAAkB,EAAE,gBAAiB;oBAC3F,IAAI,WAAW,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,aAAa;oBACtF,IAAI,YAAY,MAAM;wBAClB;oBACJ;oBACA,SAAS,YAAY,CAAC,YAAY,CAAC,aAAa,CAAC,YAAY;oBAC7D,IAAI,CAAC,SAAS,iBAAiB,IAAI;wBAC/B,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,aAAa,GAAG;oBAC9E;gBACJ;YACJ;QACJ;IACJ;IACA,gBAAgB,SAAS,CAAC,uBAAuB,GAAG;QAChD,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,IAAI,MAAM;YAClE,OAAO;QACX;QACA,IAAI,kBAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,YAAY;QACrF,IAAK,IAAI,aAAa,KAAK,MAAK,GAAG,eAAe,UAAU,MAAM,EAAE,eAAgB;YAChF,IAAI,SAAS,CAAC,aAAa,IAAI,MAAM;gBACjC;YACJ;YACA,IAAI,wBAAwB,SAAS,CAAC,aAAa,CAAC,YAAY;YAChE,IAAI,mBAAmB;YACvB,IAAK,IAAI,cAAc,KAAK,MAAK,IAAI,CAAC,kBAAkB,GAAG,GAAG,gBAAgB,KAAK,mBAAmB,IAAI,CAAC,sBAAsB,EAAE,gBAAiB;gBAChJ,IAAI,WAAW,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,aAAa;gBACtF,IAAI,YAAY,MAAM;oBAClB,mBAAmB,gBAAgB,sBAAsB,CAAC,uBAAuB,kBAAkB;oBACnG,IAAI,CAAC,SAAS,iBAAiB,IAAI;wBAC/B;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,uBAAuB,GAAG;QAChD,IAAI,IAAI,CAAC,sBAAsB,CAAC,EAAE,IAAI,MAAM;YACxC,OAAO;QACX;QACA,IAAI,kBAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,YAAY;QAC3D,IAAK,IAAI,aAAa,KAAK,MAAK,GAAG,eAAe,UAAU,MAAM,EAAE,eAAgB;YAChF,IAAI,SAAS,CAAC,aAAa,IAAI,MAAM;gBACjC;YACJ;YACA,IAAI,wBAAwB,SAAS,CAAC,aAAa,CAAC,YAAY;YAChE,IAAI,mBAAmB;YACvB,IAAK,IAAI,cAAc,KAAK,MAAK,GAAG,gBAAgB,IAAI,CAAC,kBAAkB,GAAG,KAAK,mBAAmB,IAAI,CAAC,sBAAsB,EAAE,gBAAiB;gBAChJ,IAAI,WAAW,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,aAAa;gBACtF,IAAI,YAAY,MAAM;oBAClB,mBAAmB,gBAAgB,sBAAsB,CAAC,uBAAuB,kBAAkB;oBACnG,IAAI,CAAC,SAAS,iBAAiB,IAAI;wBAC/B;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,sBAAsB,GAAG,SAAU,qBAAqB,EAAE,gBAAgB,EAAE,QAAQ;QAChG,IAAI,YAAY,MAAM;YAClB,OAAO;QACX;QACA,IAAI,CAAC,SAAS,iBAAiB,IAAI;YAC/B,IAAI,SAAS,gBAAgB,CAAC,wBAAwB;gBAClD,SAAS,YAAY,CAAC;gBACtB,mBAAmB;YACvB,OACK;gBACD,EAAE;YACN;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG,SAAU,aAAa,EAAE,YAAY,EAAE,SAAS;QACzF,IAAI,KAAK;QACT,IAAI,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,IAAI,MAAM;YACxD;QACJ;QACA,IAAI,WAAW,SAAS,CAAC,aAAa;QACtC,IAAI,0BAA0B,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,YAAY;QACzF,IAAI,sBAAsB;QAC1B,IAAI,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,IAAI,MAAM;YACxD,sBAAsB,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,YAAY;QACrF;QACA,qDAAqD;QACrD,IAAI,iBAAiB,IAAI,MAAM;QAC/B,cAAc,CAAC,EAAE,GAAG,uBAAuB,CAAC,aAAa;QACzD,cAAc,CAAC,EAAE,GAAG,mBAAmB,CAAC,aAAa;QACrD,IAAI,eAAe,GAAG;YAClB,cAAc,CAAC,EAAE,GAAG,SAAS,CAAC,eAAe,EAAE;YAC/C,cAAc,CAAC,EAAE,GAAG,uBAAuB,CAAC,eAAe,EAAE;YAC7D,cAAc,CAAC,EAAE,GAAG,mBAAmB,CAAC,eAAe,EAAE;QAC7D;QACA,IAAI,eAAe,GAAG;YAClB,cAAc,CAAC,EAAE,GAAG,SAAS,CAAC,eAAe,EAAE;YAC/C,cAAc,CAAC,GAAG,GAAG,uBAAuB,CAAC,eAAe,EAAE;YAC9D,cAAc,CAAC,GAAG,GAAG,mBAAmB,CAAC,eAAe,EAAE;QAC9D;QACA,IAAI,eAAe,UAAU,MAAM,GAAG,GAAG;YACrC,cAAc,CAAC,EAAE,GAAG,SAAS,CAAC,eAAe,EAAE;YAC/C,cAAc,CAAC,EAAE,GAAG,uBAAuB,CAAC,eAAe,EAAE;YAC7D,cAAc,CAAC,EAAE,GAAG,mBAAmB,CAAC,eAAe,EAAE;QAC7D;QACA,IAAI,eAAe,UAAU,MAAM,GAAG,GAAG;YACrC,cAAc,CAAC,EAAE,GAAG,SAAS,CAAC,eAAe,EAAE;YAC/C,cAAc,CAAC,GAAG,GAAG,uBAAuB,CAAC,eAAe,EAAE;YAC9D,cAAc,CAAC,GAAG,GAAG,mBAAmB,CAAC,eAAe,EAAE;QAC9D;QACA,IAAI;YACA,IAAK,IAAI,mBAAmB,SAAS,iBAAiB,qBAAqB,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,EAAE,qBAAqB,iBAAiB,IAAI,GAAI;gBACxK,IAAI,gBAAgB,mBAAmB,KAAK;gBAC5C,IAAI,gBAAgB,eAAe,CAAC,UAAU,gBAAgB;oBAC1D;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,sBAAsB,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,iBAAiB,MAAM,GAAG,GAAG,IAAI,CAAC;YAClG,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;IACA;;KAEC,GACD,gBAAgB,eAAe,GAAG,SAAU,QAAQ,EAAE,aAAa;QAC/D,IAAI,iBAAiB,MAAM;YACvB,OAAO;QACX;QACA,IAAI,cAAc,iBAAiB,MAAM,cAAc,SAAS,OAAO,SAAS,SAAS,IAAI;YACzF,SAAS,YAAY,CAAC,cAAc,YAAY;YAChD,OAAO;QACX;QACA,OAAO;IACX;IACA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG;QAC9C,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,gBAAgB,SAAS,CAAC,kBAAkB,GAAG;QAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW;IAC3C;IACA,gBAAgB,SAAS,CAAC,iBAAiB,GAAG;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,uBAAuB;IACvD;IACA,gBAAgB,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW;QAC5D,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,gBAAgB,SAAS,CAAC,cAAc,GAAG;QACvC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,gBAAgB,SAAS,CAAC,wBAAwB,GAAG,SAAU,aAAa,EAAE,qBAAqB;QAC/F,IAAI,CAAC,sBAAsB,CAAC,cAAc,GAAG;IACjD;IACA,gBAAgB,SAAS,CAAC,wBAAwB,GAAG,SAAU,aAAa;QACxE,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc;IACrD;IACA,YAAY;IACZ,gBAAgB,SAAS,CAAC,QAAQ,GAAG;QACjC,IAAI,qBAAqB,IAAI,CAAC,sBAAsB,CAAC,EAAE;QACvD,IAAI,sBAAsB,MAAM;YAC5B,qBAAqB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE;QACjF;QACA,QAAQ;QACR,IAAI,YAAY,IAAI,yKAAA,CAAA,UAAS;QAC7B,MAAM;QACN,IAAK,IAAI,aAAa,KAAK,MAAK,GAAG,eAAe,mBAAmB,YAAY,GAAG,MAAM,EAAE,eAAgB;YACxG,UAAU,MAAM,CAAC,WAAW;YAC5B,IAAK,IAAI,cAAc,KAAK,MAAK,GAAG,gBAAgB,IAAI,CAAC,kBAAkB,GAAG,GAAG,gBAAiB;gBAC9F,IAAI,IAAI,CAAC,sBAAsB,CAAC,cAAc,IAAI,MAAM;oBACpD,UAAU,MAAM,CAAC;oBACjB;gBACJ;gBACA,IAAI,WAAW,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,aAAa;gBACtF,IAAI,YAAY,MAAM;oBAClB,UAAU,MAAM,CAAC;oBACjB;gBACJ;gBACA,UAAU,MAAM,CAAC,YAAY,SAAS,YAAY,IAAI,SAAS,QAAQ;YAC3E;YACA,UAAU,MAAM,CAAC;QACrB;QACA,OAAO,UAAU,QAAQ;IACzB,IAAI;IACR;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/Codeword.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar Codeword = /** @class */ (function () {\n    function Codeword(startX, endX, bucket, value) {\n        this.rowNumber = Codeword.BARCODE_ROW_UNKNOWN;\n        this.startX = Math.trunc(startX);\n        this.endX = Math.trunc(endX);\n        this.bucket = Math.trunc(bucket);\n        this.value = Math.trunc(value);\n    }\n    Codeword.prototype.hasValidRowNumber = function () {\n        return this.isValidRowNumber(this.rowNumber);\n    };\n    Codeword.prototype.isValidRowNumber = function (rowNumber) {\n        return rowNumber !== Codeword.BARCODE_ROW_UNKNOWN && this.bucket === (rowNumber % 3) * 3;\n    };\n    Codeword.prototype.setRowNumberAsRowIndicatorColumn = function () {\n        this.rowNumber = Math.trunc((Math.trunc(this.value / 30)) * 3 + Math.trunc(this.bucket / 3));\n    };\n    Codeword.prototype.getWidth = function () {\n        return this.endX - this.startX;\n    };\n    Codeword.prototype.getStartX = function () {\n        return this.startX;\n    };\n    Codeword.prototype.getEndX = function () {\n        return this.endX;\n    };\n    Codeword.prototype.getBucket = function () {\n        return this.bucket;\n    };\n    Codeword.prototype.getValue = function () {\n        return this.value;\n    };\n    Codeword.prototype.getRowNumber = function () {\n        return this.rowNumber;\n    };\n    Codeword.prototype.setRowNumber = function (rowNumber) {\n        this.rowNumber = rowNumber;\n    };\n    //   @Override\n    Codeword.prototype.toString = function () {\n        return this.rowNumber + '|' + this.value;\n    };\n    Codeword.BARCODE_ROW_UNKNOWN = -1;\n    return Codeword;\n}());\nexport default Codeword;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,2CAA2C;AAC3C;;CAEC;;;AACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,mBAAmB;QAC7C,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC;IAC5B;IACA,SAAS,SAAS,CAAC,iBAAiB,GAAG;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS;IAC/C;IACA,SAAS,SAAS,CAAC,gBAAgB,GAAG,SAAU,SAAS;QACrD,OAAO,cAAc,SAAS,mBAAmB,IAAI,IAAI,CAAC,MAAM,KAAK,AAAC,YAAY,IAAK;IAC3F;IACA,SAAS,SAAS,CAAC,gCAAgC,GAAG;QAClD,IAAI,CAAC,SAAS,GAAG,KAAK,KAAK,CAAC,AAAC,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,MAAO,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;IAC7F;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM;IAClC;IACA,SAAS,SAAS,CAAC,SAAS,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,SAAS,SAAS,CAAC,SAAS,GAAG;QAC3B,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,SAAS,SAAS,CAAC,YAAY,GAAG;QAC9B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QACjD,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,cAAc;IACd,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,KAAK;IAC5C;IACA,SAAS,mBAAmB,GAAG,CAAC;IAChC,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/PDF417CodewordDecoder.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Float from '../../util/Float';\n/**\n * <AUTHOR>\n * <AUTHOR> GmbH (<EMAIL>)\n */\nvar PDF417CodewordDecoder = /** @class */ (function () {\n    function PDF417CodewordDecoder() {\n    }\n    /* @note\n     * this action have to be performed before first use of class\n     * - static constructor\n     * working with 32bit float (based from Java logic)\n    */\n    PDF417CodewordDecoder.initialize = function () {\n        // Pre-computes the symbol ratio table.\n        for ( /*int*/var i = 0; i < PDF417Common.SYMBOL_TABLE.length; i++) {\n            var currentSymbol = PDF417Common.SYMBOL_TABLE[i];\n            var currentBit = currentSymbol & 0x1;\n            for ( /*int*/var j = 0; j < PDF417Common.BARS_IN_MODULE; j++) {\n                var size = 0.0;\n                while ((currentSymbol & 0x1) === currentBit) {\n                    size += 1.0;\n                    currentSymbol >>= 1;\n                }\n                currentBit = currentSymbol & 0x1;\n                if (!PDF417CodewordDecoder.RATIOS_TABLE[i]) {\n                    PDF417CodewordDecoder.RATIOS_TABLE[i] = new Array(PDF417Common.BARS_IN_MODULE);\n                }\n                PDF417CodewordDecoder.RATIOS_TABLE[i][PDF417Common.BARS_IN_MODULE - j - 1] = Math.fround(size / PDF417Common.MODULES_IN_CODEWORD);\n            }\n        }\n        this.bSymbolTableReady = true;\n    };\n    PDF417CodewordDecoder.getDecodedValue = function (moduleBitCount) {\n        var decodedValue = PDF417CodewordDecoder.getDecodedCodewordValue(PDF417CodewordDecoder.sampleBitCounts(moduleBitCount));\n        if (decodedValue !== -1) {\n            return decodedValue;\n        }\n        return PDF417CodewordDecoder.getClosestDecodedValue(moduleBitCount);\n    };\n    PDF417CodewordDecoder.sampleBitCounts = function (moduleBitCount) {\n        var bitCountSum = MathUtils.sum(moduleBitCount);\n        var result = new Int32Array(PDF417Common.BARS_IN_MODULE);\n        var bitCountIndex = 0;\n        var sumPreviousBits = 0;\n        for ( /*int*/var i = 0; i < PDF417Common.MODULES_IN_CODEWORD; i++) {\n            var sampleIndex = bitCountSum / (2 * PDF417Common.MODULES_IN_CODEWORD) +\n                (i * bitCountSum) / PDF417Common.MODULES_IN_CODEWORD;\n            if (sumPreviousBits + moduleBitCount[bitCountIndex] <= sampleIndex) {\n                sumPreviousBits += moduleBitCount[bitCountIndex];\n                bitCountIndex++;\n            }\n            result[bitCountIndex]++;\n        }\n        return result;\n    };\n    PDF417CodewordDecoder.getDecodedCodewordValue = function (moduleBitCount) {\n        var decodedValue = PDF417CodewordDecoder.getBitValue(moduleBitCount);\n        return PDF417Common.getCodeword(decodedValue) === -1 ? -1 : decodedValue;\n    };\n    PDF417CodewordDecoder.getBitValue = function (moduleBitCount) {\n        var result = /*long*/ 0;\n        for (var /*int*/ i = 0; i < moduleBitCount.length; i++) {\n            for ( /*int*/var bit = 0; bit < moduleBitCount[i]; bit++) {\n                result = (result << 1) | (i % 2 === 0 ? 1 : 0);\n            }\n        }\n        return Math.trunc(result);\n    };\n    // working with 32bit float (as in Java)\n    PDF417CodewordDecoder.getClosestDecodedValue = function (moduleBitCount) {\n        var bitCountSum = MathUtils.sum(moduleBitCount);\n        var bitCountRatios = new Array(PDF417Common.BARS_IN_MODULE);\n        if (bitCountSum > 1) {\n            for (var /*int*/ i = 0; i < bitCountRatios.length; i++) {\n                bitCountRatios[i] = Math.fround(moduleBitCount[i] / bitCountSum);\n            }\n        }\n        var bestMatchError = Float.MAX_VALUE;\n        var bestMatch = -1;\n        if (!this.bSymbolTableReady) {\n            PDF417CodewordDecoder.initialize();\n        }\n        for ( /*int*/var j = 0; j < PDF417CodewordDecoder.RATIOS_TABLE.length; j++) {\n            var error = 0.0;\n            var ratioTableRow = PDF417CodewordDecoder.RATIOS_TABLE[j];\n            for ( /*int*/var k = 0; k < PDF417Common.BARS_IN_MODULE; k++) {\n                var diff = Math.fround(ratioTableRow[k] - bitCountRatios[k]);\n                error += Math.fround(diff * diff);\n                if (error >= bestMatchError) {\n                    break;\n                }\n            }\n            if (error < bestMatchError) {\n                bestMatchError = error;\n                bestMatch = PDF417Common.SYMBOL_TABLE[j];\n            }\n        }\n        return bestMatch;\n    };\n    // flag that the table is ready for use\n    PDF417CodewordDecoder.bSymbolTableReady = false;\n    PDF417CodewordDecoder.RATIOS_TABLE = new Array(PDF417Common.SYMBOL_TABLE.length).map(function (x) { return x = new Array(PDF417Common.BARS_IN_MODULE); });\n    return PDF417CodewordDecoder;\n}());\nexport default PDF417CodewordDecoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA,GACA,2CAA2C;AAC3C,qDAAqD;;;;AACrD;AACA,+CAA+C;AAC/C;AACA;;;;AACA;;;CAGC,GACD,IAAI,wBAAuC;IACvC,SAAS,yBACT;IACA;;;;IAIA,GACA,sBAAsB,UAAU,GAAG;QAC/B,uCAAuC;QACvC,IAAM,KAAK,GAAE,IAAI,IAAI,GAAG,IAAI,8KAAA,CAAA,UAAY,CAAC,YAAY,CAAC,MAAM,EAAE,IAAK;YAC/D,IAAI,gBAAgB,8KAAA,CAAA,UAAY,CAAC,YAAY,CAAC,EAAE;YAChD,IAAI,aAAa,gBAAgB;YACjC,IAAM,KAAK,GAAE,IAAI,IAAI,GAAG,IAAI,8KAAA,CAAA,UAAY,CAAC,cAAc,EAAE,IAAK;gBAC1D,IAAI,OAAO;gBACX,MAAO,CAAC,gBAAgB,GAAG,MAAM,WAAY;oBACzC,QAAQ;oBACR,kBAAkB;gBACtB;gBACA,aAAa,gBAAgB;gBAC7B,IAAI,CAAC,sBAAsB,YAAY,CAAC,EAAE,EAAE;oBACxC,sBAAsB,YAAY,CAAC,EAAE,GAAG,IAAI,MAAM,8KAAA,CAAA,UAAY,CAAC,cAAc;gBACjF;gBACA,sBAAsB,YAAY,CAAC,EAAE,CAAC,8KAAA,CAAA,UAAY,CAAC,cAAc,GAAG,IAAI,EAAE,GAAG,KAAK,MAAM,CAAC,OAAO,8KAAA,CAAA,UAAY,CAAC,mBAAmB;YACpI;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,sBAAsB,eAAe,GAAG,SAAU,cAAc;QAC5D,IAAI,eAAe,sBAAsB,uBAAuB,CAAC,sBAAsB,eAAe,CAAC;QACvG,IAAI,iBAAiB,CAAC,GAAG;YACrB,OAAO;QACX;QACA,OAAO,sBAAsB,sBAAsB,CAAC;IACxD;IACA,sBAAsB,eAAe,GAAG,SAAU,cAAc;QAC5D,IAAI,cAAc,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC;QAChC,IAAI,SAAS,IAAI,WAAW,8KAAA,CAAA,UAAY,CAAC,cAAc;QACvD,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QACtB,IAAM,KAAK,GAAE,IAAI,IAAI,GAAG,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,EAAE,IAAK;YAC/D,IAAI,cAAc,cAAc,CAAC,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,IACjE,AAAC,IAAI,cAAe,8KAAA,CAAA,UAAY,CAAC,mBAAmB;YACxD,IAAI,kBAAkB,cAAc,CAAC,cAAc,IAAI,aAAa;gBAChE,mBAAmB,cAAc,CAAC,cAAc;gBAChD;YACJ;YACA,MAAM,CAAC,cAAc;QACzB;QACA,OAAO;IACX;IACA,sBAAsB,uBAAuB,GAAG,SAAU,cAAc;QACpE,IAAI,eAAe,sBAAsB,WAAW,CAAC;QACrD,OAAO,8KAAA,CAAA,UAAY,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;IAChE;IACA,sBAAsB,WAAW,GAAG,SAAU,cAAc;QACxD,IAAI,SAAS,MAAM,GAAG;QACtB,IAAK,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YACpD,IAAM,KAAK,GAAE,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,EAAE,EAAE,MAAO;gBACtD,SAAS,AAAC,UAAU,IAAK,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC;YACjD;QACJ;QACA,OAAO,KAAK,KAAK,CAAC;IACtB;IACA,wCAAwC;IACxC,sBAAsB,sBAAsB,GAAG,SAAU,cAAc;QACnE,IAAI,cAAc,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC;QAChC,IAAI,iBAAiB,IAAI,MAAM,8KAAA,CAAA,UAAY,CAAC,cAAc;QAC1D,IAAI,cAAc,GAAG;YACjB,IAAK,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBACpD,cAAc,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,cAAc,CAAC,EAAE,GAAG;YACxD;QACJ;QACA,IAAI,iBAAiB,qKAAA,CAAA,UAAK,CAAC,SAAS;QACpC,IAAI,YAAY,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,sBAAsB,UAAU;QACpC;QACA,IAAM,KAAK,GAAE,IAAI,IAAI,GAAG,IAAI,sBAAsB,YAAY,CAAC,MAAM,EAAE,IAAK;YACxE,IAAI,QAAQ;YACZ,IAAI,gBAAgB,sBAAsB,YAAY,CAAC,EAAE;YACzD,IAAM,KAAK,GAAE,IAAI,IAAI,GAAG,IAAI,8KAAA,CAAA,UAAY,CAAC,cAAc,EAAE,IAAK;gBAC1D,IAAI,OAAO,KAAK,MAAM,CAAC,aAAa,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE;gBAC3D,SAAS,KAAK,MAAM,CAAC,OAAO;gBAC5B,IAAI,SAAS,gBAAgB;oBACzB;gBACJ;YACJ;YACA,IAAI,QAAQ,gBAAgB;gBACxB,iBAAiB;gBACjB,YAAY,8KAAA,CAAA,UAAY,CAAC,YAAY,CAAC,EAAE;YAC5C;QACJ;QACA,OAAO;IACX;IACA,uCAAuC;IACvC,sBAAsB,iBAAiB,GAAG;IAC1C,sBAAsB,YAAY,GAAG,IAAI,MAAM,8KAAA,CAAA,UAAY,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI,MAAM,8KAAA,CAAA,UAAY,CAAC,cAAc;IAAG;IACvJ,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8035, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/PDF417ResultMetadata.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ResultMetadata = /** @class */ (function () {\n    function PDF417ResultMetadata() {\n        this.segmentCount = -1;\n        this.fileSize = -1;\n        this.timestamp = -1;\n        this.checksum = -1;\n    }\n    /**\n     * The Segment ID represents the segment of the whole file distributed over different symbols.\n     *\n     * @return File segment index\n     */\n    PDF417ResultMetadata.prototype.getSegmentIndex = function () {\n        return this.segmentIndex;\n    };\n    PDF417ResultMetadata.prototype.setSegmentIndex = function (segmentIndex) {\n        this.segmentIndex = segmentIndex;\n    };\n    /**\n     * Is the same for each related PDF417 symbol\n     *\n     * @return File ID\n     */\n    PDF417ResultMetadata.prototype.getFileId = function () {\n        return this.fileId;\n    };\n    PDF417ResultMetadata.prototype.setFileId = function (fileId) {\n        this.fileId = fileId;\n    };\n    /**\n     * @return always null\n     * @deprecated use dedicated already parsed fields\n     */\n    //   @Deprecated\n    PDF417ResultMetadata.prototype.getOptionalData = function () {\n        return this.optionalData;\n    };\n    /**\n     * @param optionalData old optional data format as int array\n     * @deprecated parse and use new fields\n     */\n    //   @Deprecated\n    PDF417ResultMetadata.prototype.setOptionalData = function (optionalData) {\n        this.optionalData = optionalData;\n    };\n    /**\n     * @return true if it is the last segment\n     */\n    PDF417ResultMetadata.prototype.isLastSegment = function () {\n        return this.lastSegment;\n    };\n    PDF417ResultMetadata.prototype.setLastSegment = function (lastSegment) {\n        this.lastSegment = lastSegment;\n    };\n    /**\n     * @return count of segments, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getSegmentCount = function () {\n        return this.segmentCount;\n    };\n    PDF417ResultMetadata.prototype.setSegmentCount = function (segmentCount /*int*/) {\n        this.segmentCount = segmentCount;\n    };\n    PDF417ResultMetadata.prototype.getSender = function () {\n        return this.sender || null;\n    };\n    PDF417ResultMetadata.prototype.setSender = function (sender) {\n        this.sender = sender;\n    };\n    PDF417ResultMetadata.prototype.getAddressee = function () {\n        return this.addressee || null;\n    };\n    PDF417ResultMetadata.prototype.setAddressee = function (addressee) {\n        this.addressee = addressee;\n    };\n    /**\n     * Filename of the encoded file\n     *\n     * @return filename\n     */\n    PDF417ResultMetadata.prototype.getFileName = function () {\n        return this.fileName;\n    };\n    PDF417ResultMetadata.prototype.setFileName = function (fileName) {\n        this.fileName = fileName;\n    };\n    /**\n     * filesize in bytes of the encoded file\n     *\n     * @return filesize in bytes, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getFileSize = function () {\n        return this.fileSize;\n    };\n    PDF417ResultMetadata.prototype.setFileSize = function (fileSize /*long*/) {\n        this.fileSize = fileSize;\n    };\n    /**\n     * 16-bit CRC checksum using CCITT-16\n     *\n     * @return crc checksum, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getChecksum = function () {\n        return this.checksum;\n    };\n    PDF417ResultMetadata.prototype.setChecksum = function (checksum /*int*/) {\n        this.checksum = checksum;\n    };\n    /**\n     * unix epock timestamp, elapsed seconds since 1970-01-01\n     *\n     * @return elapsed seconds, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getTimestamp = function () {\n        return this.timestamp;\n    };\n    PDF417ResultMetadata.prototype.setTimestamp = function (timestamp /*long*/) {\n        this.timestamp = timestamp;\n    };\n    return PDF417ResultMetadata;\n}());\nexport default PDF417ResultMetadata;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,mCAAmC;AACnC;;CAEC;;;AACD,IAAI,uBAAsC;IACtC,SAAS;QACL,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,CAAC;IACrB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,eAAe,GAAG;QAC7C,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,qBAAqB,SAAS,CAAC,eAAe,GAAG,SAAU,YAAY;QACnE,IAAI,CAAC,YAAY,GAAG;IACxB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,SAAS,GAAG;QACvC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,qBAAqB,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QACvD,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;KAGC,GACD,gBAAgB;IAChB,qBAAqB,SAAS,CAAC,eAAe,GAAG;QAC7C,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;;KAGC,GACD,gBAAgB;IAChB,qBAAqB,SAAS,CAAC,eAAe,GAAG,SAAU,YAAY;QACnE,IAAI,CAAC,YAAY,GAAG;IACxB;IACA;;KAEC,GACD,qBAAqB,SAAS,CAAC,aAAa,GAAG;QAC3C,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,qBAAqB,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW;QACjE,IAAI,CAAC,WAAW,GAAG;IACvB;IACA;;KAEC,GACD,qBAAqB,SAAS,CAAC,eAAe,GAAG;QAC7C,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,qBAAqB,SAAS,CAAC,eAAe,GAAG,SAAU,aAAa,KAAK,GAAN;QACnE,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,qBAAqB,SAAS,CAAC,SAAS,GAAG;QACvC,OAAO,IAAI,CAAC,MAAM,IAAI;IAC1B;IACA,qBAAqB,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QACvD,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,qBAAqB,SAAS,CAAC,YAAY,GAAG;QAC1C,OAAO,IAAI,CAAC,SAAS,IAAI;IAC7B;IACA,qBAAqB,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QAC7D,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,WAAW,GAAG;QACzC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,qBAAqB,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ;QAC3D,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,WAAW,GAAG;QACzC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,qBAAqB,SAAS,CAAC,WAAW,GAAG,SAAU,SAAS,MAAM,GAAP;QAC3D,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,WAAW,GAAG;QACzC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,qBAAqB,SAAS,CAAC,WAAW,GAAG,SAAU,SAAS,KAAK,GAAN;QAC3D,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;KAIC,GACD,qBAAqB,SAAS,CAAC,YAAY,GAAG;QAC1C,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,qBAAqB,SAAS,CAAC,YAAY,GAAG,SAAU,UAAU,MAAM,GAAP;QAC7D,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.common.CharacterSetECI;\nimport CharacterSetECI from '../../common/CharacterSetECI';\n// import com.google.zxing.common.DecoderResult;\nimport DecoderResult from '../../common/DecoderResult';\n// import com.google.zxing.pdf417.PDF417ResultMetadata;\nimport PDF417ResultMetadata from '../PDF417ResultMetadata';\n// import java.io.ByteArrayOutputStream;\n// import java.math.BigInteger;\n// import java.nio.charset.Charset;\n// import java.nio.charset.StandardCharsets;\n// import java.util.Arrays;\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\nimport Integer from '../../util/Integer';\nimport Long from '../../util/Long';\nimport ByteArrayOutputStream from '../../util/ByteArrayOutputStream';\nimport StringEncoding from '../../util/StringEncoding';\n/*private*/ var Mode;\n(function (Mode) {\n    Mode[Mode[\"ALPHA\"] = 0] = \"ALPHA\";\n    Mode[Mode[\"LOWER\"] = 1] = \"LOWER\";\n    Mode[Mode[\"MIXED\"] = 2] = \"MIXED\";\n    Mode[Mode[\"PUNCT\"] = 3] = \"PUNCT\";\n    Mode[Mode[\"ALPHA_SHIFT\"] = 4] = \"ALPHA_SHIFT\";\n    Mode[Mode[\"PUNCT_SHIFT\"] = 5] = \"PUNCT_SHIFT\";\n})(Mode || (Mode = {}));\n/**\n * Indirectly access the global BigInt constructor, it\n * allows browsers that doesn't support BigInt to run\n * the library without breaking due to \"undefined BigInt\"\n * errors.\n */\nfunction getBigIntConstructor() {\n    if (typeof window !== 'undefined') {\n        return window['BigInt'] || null;\n    }\n    if (typeof global !== 'undefined') {\n        return global['BigInt'] || null;\n    }\n    if (typeof self !== 'undefined') {\n        return self['BigInt'] || null;\n    }\n    throw new Error('Can\\'t search globals for BigInt!');\n}\n/**\n * Used to store the BigInt constructor.\n */\nvar BigInteger;\n/**\n * This function creates a bigint value. It allows browsers\n * that doesn't support BigInt to run the rest of the library\n * by not directly accessing the BigInt constructor.\n */\nfunction createBigInt(num) {\n    if (typeof BigInteger === 'undefined') {\n        BigInteger = getBigIntConstructor();\n    }\n    if (BigInteger === null) {\n        throw new Error('BigInt is not supported!');\n    }\n    return BigInteger(num);\n}\nfunction getEXP900() {\n    // in Java - array with length = 16\n    var EXP900 = [];\n    EXP900[0] = createBigInt(1);\n    var nineHundred = createBigInt(900);\n    EXP900[1] = nineHundred;\n    // in Java - array with length = 16\n    for (var i /*int*/ = 2; i < 16; i++) {\n        EXP900[i] = EXP900[i - 1] * nineHundred;\n    }\n    return EXP900;\n}\n/**\n * <p>This class contains the methods for decoding the PDF417 codewords.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> Grau\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    //   private DecodedBitStreamParser() {\n    // }\n    /**\n     *\n     * @param codewords\n     * @param ecLevel\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.decode = function (codewords, ecLevel) {\n        // pass encoding to result (will be used for decode symbols in byte mode)\n        var result = new StringBuilder('');\n        // let encoding: Charset = StandardCharsets.ISO_8859_1;\n        var encoding = CharacterSetECI.ISO8859_1;\n        /**\n         * @note the next command is specific from this TypeScript library\n         * because TS can't properly cast some values to char and\n         * convert it to string later correctly due to encoding\n         * differences from Java version. As reported here:\n         * https://github.com/zxing-js/library/pull/264/files#r382831593\n         */\n        result.enableDecoding(encoding);\n        // Get compaction mode\n        var codeIndex = 1;\n        var code = codewords[codeIndex++];\n        var resultMetadata = new PDF417ResultMetadata();\n        while (codeIndex < codewords[0]) {\n            switch (code) {\n                case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                    codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    codeIndex = DecodedBitStreamParser.byteCompaction(code, codewords, encoding, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                    result.append(/*(char)*/ codewords[codeIndex++]);\n                    break;\n                case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                    codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.ECI_CHARSET:\n                    var charsetECI = CharacterSetECI.getCharacterSetECIByValue(codewords[codeIndex++]);\n                    // encoding = Charset.forName(charsetECI.getName());\n                    break;\n                case DecodedBitStreamParser.ECI_GENERAL_PURPOSE:\n                    // Can't do anything with generic ECI; skip its 2 characters\n                    codeIndex += 2;\n                    break;\n                case DecodedBitStreamParser.ECI_USER_DEFINED:\n                    // Can't do anything with user ECI; skip its 1 character\n                    codeIndex++;\n                    break;\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    codeIndex = DecodedBitStreamParser.decodeMacroBlock(codewords, codeIndex, resultMetadata);\n                    break;\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                    // Should not see these outside a macro block\n                    throw new FormatException();\n                default:\n                    // Default to text compaction. During testing numerous barcodes\n                    // appeared to be missing the starting mode. In these cases defaulting\n                    // to text compaction seems to work.\n                    codeIndex--;\n                    codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n                    break;\n            }\n            if (codeIndex < codewords.length) {\n                code = codewords[codeIndex++];\n            }\n            else {\n                throw FormatException.getFormatInstance();\n            }\n        }\n        if (result.length() === 0) {\n            throw FormatException.getFormatInstance();\n        }\n        var decoderResult = new DecoderResult(null, result.toString(), null, ecLevel);\n        decoderResult.setOther(resultMetadata);\n        return decoderResult;\n    };\n    /**\n     *\n     * @param int\n     * @param param1\n     * @param codewords\n     * @param int\n     * @param codeIndex\n     * @param PDF417ResultMetadata\n     * @param resultMetadata\n     *\n     * @throws FormatException\n     */\n    // @SuppressWarnings(\"deprecation\")\n    DecodedBitStreamParser.decodeMacroBlock = function (codewords, codeIndex, resultMetadata) {\n        if (codeIndex + DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS > codewords[0]) {\n            // we must have at least two bytes left for the segment index\n            throw FormatException.getFormatInstance();\n        }\n        var segmentIndexArray = new Int32Array(DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS);\n        for (var i /*int*/ = 0; i < DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS; i++, codeIndex++) {\n            segmentIndexArray[i] = codewords[codeIndex];\n        }\n        resultMetadata.setSegmentIndex(Integer.parseInt(DecodedBitStreamParser.decodeBase900toBase10(segmentIndexArray, DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS)));\n        var fileId = new StringBuilder();\n        codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, fileId);\n        resultMetadata.setFileId(fileId.toString());\n        var optionalFieldsStart = -1;\n        if (codewords[codeIndex] === DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD) {\n            optionalFieldsStart = codeIndex + 1;\n        }\n        while (codeIndex < codewords[0]) {\n            switch (codewords[codeIndex]) {\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    codeIndex++;\n                    switch (codewords[codeIndex]) {\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:\n                            var fileName = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, fileName);\n                            resultMetadata.setFileName(fileName.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER:\n                            var sender = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, sender);\n                            resultMetadata.setSender(sender.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:\n                            var addressee = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, addressee);\n                            resultMetadata.setAddressee(addressee.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:\n                            var segmentCount = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, segmentCount);\n                            resultMetadata.setSegmentCount(Integer.parseInt(segmentCount.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:\n                            var timestamp = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, timestamp);\n                            resultMetadata.setTimestamp(Long.parseLong(timestamp.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:\n                            var checksum = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, checksum);\n                            resultMetadata.setChecksum(Integer.parseInt(checksum.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:\n                            var fileSize = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, fileSize);\n                            resultMetadata.setFileSize(Long.parseLong(fileSize.toString()));\n                            break;\n                        default:\n                            throw FormatException.getFormatInstance();\n                    }\n                    break;\n                case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                    codeIndex++;\n                    resultMetadata.setLastSegment(true);\n                    break;\n                default:\n                    throw FormatException.getFormatInstance();\n            }\n        }\n        // copy optional fields to additional options\n        if (optionalFieldsStart !== -1) {\n            var optionalFieldsLength = codeIndex - optionalFieldsStart;\n            if (resultMetadata.isLastSegment()) {\n                // do not include terminator\n                optionalFieldsLength--;\n            }\n            resultMetadata.setOptionalData(Arrays.copyOfRange(codewords, optionalFieldsStart, optionalFieldsStart + optionalFieldsLength));\n        }\n        return codeIndex;\n    };\n    /**\n     * Text Compaction mode (see 5.4.1.5) permits all printable ASCII characters to be\n     * encoded, i.e. values 32 - 126 inclusive in accordance with ISO/IEC 646 (IRV), as\n     * well as selected control characters.\n     *\n     * @param codewords The array of codewords (data + error)\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     */\n    DecodedBitStreamParser.textCompaction = function (codewords, codeIndex, result) {\n        // 2 character per codeword\n        var textCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n        // Used to hold the byte compaction value if there is a mode shift\n        var byteCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n        var index = 0;\n        var end = false;\n        while ((codeIndex < codewords[0]) && !end) {\n            var code = codewords[codeIndex++];\n            if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                textCompactionData[index] = code / 30;\n                textCompactionData[index + 1] = code % 30;\n                index += 2;\n            }\n            else {\n                switch (code) {\n                    case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                        // reinitialize text compaction mode to alpha sub mode\n                        textCompactionData[index++] = DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH;\n                        break;\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                        codeIndex--;\n                        end = true;\n                        break;\n                    case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                        // The Mode Shift codeword 913 shall cause a temporary\n                        // switch from Text Compaction mode to Byte Compaction mode.\n                        // This switch shall be in effect for only the next codeword,\n                        // after which the mode shall revert to the prevailing sub-mode\n                        // of the Text Compaction mode. Codeword 913 is only available\n                        // in Text Compaction mode; its use is described in 5.4.2.4.\n                        textCompactionData[index] = DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE;\n                        code = codewords[codeIndex++];\n                        byteCompactionData[index] = code;\n                        index++;\n                        break;\n                }\n            }\n        }\n        DecodedBitStreamParser.decodeTextCompaction(textCompactionData, byteCompactionData, index, result);\n        return codeIndex;\n    };\n    /**\n     * The Text Compaction mode includes all the printable ASCII characters\n     * (i.e. values from 32 to 126) and three ASCII control characters: HT or tab\n     * (9: e), LF or line feed (10: e), and CR or carriage\n     * return (13: e). The Text Compaction mode also includes various latch\n     * and shift characters which are used exclusively within the mode. The Text\n     * Compaction mode encodes up to 2 characters per codeword. The compaction rules\n     * for converting data into PDF417 codewords are defined in 5.4.2.2. The sub-mode\n     * switches are defined in 5.4.2.3.\n     *\n     * @param textCompactionData The text compaction data.\n     * @param byteCompactionData The byte compaction data if there\n     *                           was a mode shift.\n     * @param length             The size of the text compaction and byte compaction data.\n     * @param result             The decoded data is appended to the result.\n     */\n    DecodedBitStreamParser.decodeTextCompaction = function (textCompactionData, byteCompactionData, length, result) {\n        // Beginning from an initial state of the Alpha sub-mode\n        // The default compaction mode for PDF417 in effect at the start of each symbol shall always be Text\n        // Compaction mode Alpha sub-mode (alphabetic: uppercase). A latch codeword from another mode to the Text\n        // Compaction mode shall always switch to the Text Compaction Alpha sub-mode.\n        var subMode = Mode.ALPHA;\n        var priorToShiftMode = Mode.ALPHA;\n        var i = 0;\n        while (i < length) {\n            var subModeCh = textCompactionData[i];\n            var ch = /*char*/ '';\n            switch (subMode) {\n                case Mode.ALPHA:\n                    // Alpha (alphabetic: uppercase)\n                    if (subModeCh < 26) {\n                        // Upper case Alpha Character\n                        // Note: 65 = 'A' ASCII -> there is byte code of symbol\n                        ch = /*(char)('A' + subModeCh) */ String.fromCharCode(65 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.LL:\n                                subMode = Mode.LOWER;\n                                break;\n                            case DecodedBitStreamParser.ML:\n                                subMode = Mode.MIXED;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.LOWER:\n                    // Lower (alphabetic: lowercase)\n                    if (subModeCh < 26) {\n                        ch = /*(char)('a' + subModeCh)*/ String.fromCharCode(97 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.AS:\n                                // Shift to alpha\n                                priorToShiftMode = subMode;\n                                subMode = Mode.ALPHA_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.ML:\n                                subMode = Mode.MIXED;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                // TODO Does this need to use the current character encoding? See other occurrences below\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.MIXED:\n                    // Mixed (punctuation: e)\n                    if (subModeCh < DecodedBitStreamParser.PL) {\n                        ch = DecodedBitStreamParser.MIXED_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PL:\n                                subMode = Mode.PUNCT;\n                                break;\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.LL:\n                                subMode = Mode.LOWER;\n                                break;\n                            case DecodedBitStreamParser.AL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.PUNCT:\n                    // Punctuation\n                    if (subModeCh < DecodedBitStreamParser.PAL) {\n                        ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PAL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.ALPHA_SHIFT:\n                    // Restore sub-mode\n                    subMode = priorToShiftMode;\n                    if (subModeCh < 26) {\n                        ch = /*(char)('A' + subModeCh)*/ String.fromCharCode(65 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.PUNCT_SHIFT:\n                    // Restore sub-mode\n                    subMode = priorToShiftMode;\n                    if (subModeCh < DecodedBitStreamParser.PAL) {\n                        ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PAL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                // PS before Shift-to-Byte is used as a padding character,\n                                // see 5.4.2.4 of the specification\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n            }\n            // if (ch !== 0) {\n            if (ch !== '') {\n                // Append decoded character to result\n                result.append(ch);\n            }\n            i++;\n        }\n    };\n    /**\n     * Byte Compaction mode (see 5.4.3) permits all 256 possible 8-bit byte values to be encoded.\n     * This includes all ASCII characters value 0 to 127 inclusive and provides for international\n     * character set support.\n     *\n     * @param mode      The byte compaction mode i.e. 901 or 924\n     * @param codewords The array of codewords (data + error)\n     * @param encoding  Currently active character encoding\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     */\n    DecodedBitStreamParser.byteCompaction = function (mode, codewords, encoding, codeIndex, result) {\n        var decodedBytes = new ByteArrayOutputStream();\n        var count = 0;\n        var value = /*long*/ 0;\n        var end = false;\n        switch (mode) {\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                // Total number of Byte Compaction characters to be encoded\n                // is not a multiple of 6\n                var byteCompactedCodewords = new Int32Array(6);\n                var nextCode = codewords[codeIndex++];\n                while ((codeIndex < codewords[0]) && !end) {\n                    byteCompactedCodewords[count++] = nextCode;\n                    // Base 900\n                    value = 900 * value + nextCode;\n                    nextCode = codewords[codeIndex++];\n                    // perhaps it should be ok to check only nextCode >= TEXT_COMPACTION_MODE_LATCH\n                    switch (nextCode) {\n                        case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                        case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                            codeIndex--;\n                            end = true;\n                            break;\n                        default:\n                            if ((count % 5 === 0) && (count > 0)) {\n                                // Decode every 5 codewords\n                                // Convert to Base 256\n                                for (var j /*int*/ = 0; j < 6; ++j) {\n                                    /* @note\n                                     * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n                                     * So the next bitwise operation could not be done with simple numbers\n                                     */\n                                    decodedBytes.write(/*(byte)*/ Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n                                }\n                                value = 0;\n                                count = 0;\n                            }\n                            break;\n                    }\n                }\n                // if the end of all codewords is reached the last codeword needs to be added\n                if (codeIndex === codewords[0] && nextCode < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                    byteCompactedCodewords[count++] = nextCode;\n                }\n                // If Byte Compaction mode is invoked with codeword 901,\n                // the last group of codewords is interpreted directly\n                // as one byte per codeword, without compaction.\n                for (var i /*int*/ = 0; i < count; i++) {\n                    decodedBytes.write(/*(byte)*/ byteCompactedCodewords[i]);\n                }\n                break;\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                // Total number of Byte Compaction characters to be encoded\n                // is an integer multiple of 6\n                while (codeIndex < codewords[0] && !end) {\n                    var code = codewords[codeIndex++];\n                    if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                        count++;\n                        // Base 900\n                        value = 900 * value + code;\n                    }\n                    else {\n                        switch (code) {\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                            case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                                codeIndex--;\n                                end = true;\n                                break;\n                        }\n                    }\n                    if ((count % 5 === 0) && (count > 0)) {\n                        // Decode every 5 codewords\n                        // Convert to Base 256\n                        /* @note\n                         * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n                         * So the next bitwise operation could not be done with simple numbers\n                        */\n                        for (var j /*int*/ = 0; j < 6; ++j) {\n                            decodedBytes.write(/*(byte)*/ Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n                        }\n                        value = 0;\n                        count = 0;\n                    }\n                }\n                break;\n        }\n        result.append(StringEncoding.decode(decodedBytes.toByteArray(), encoding));\n        return codeIndex;\n    };\n    /**\n     * Numeric Compaction mode (see 5.4.4) permits efficient encoding of numeric data strings.\n     *\n     * @param codewords The array of codewords (data + error)\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.numericCompaction = function (codewords, codeIndex /*int*/, result) {\n        var count = 0;\n        var end = false;\n        var numericCodewords = new Int32Array(DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS);\n        while (codeIndex < codewords[0] && !end) {\n            var code = codewords[codeIndex++];\n            if (codeIndex === codewords[0]) {\n                end = true;\n            }\n            if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                numericCodewords[count] = code;\n                count++;\n            }\n            else {\n                switch (code) {\n                    case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                        codeIndex--;\n                        end = true;\n                        break;\n                }\n            }\n            if ((count % DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS === 0 || code === DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH || end) && count > 0) {\n                // Re-invoking Numeric Compaction mode (by using codeword 902\n                // while in Numeric Compaction mode) serves  to terminate the\n                // current Numeric Compaction mode grouping as described in 5.4.4.2,\n                // and then to start a new one grouping.\n                result.append(DecodedBitStreamParser.decodeBase900toBase10(numericCodewords, count));\n                count = 0;\n            }\n        }\n        return codeIndex;\n    };\n    /**\n     * Convert a list of Numeric Compacted codewords from Base 900 to Base 10.\n     *\n     * @param codewords The array of codewords\n     * @param count     The number of codewords\n     * @return The decoded string representing the Numeric data.\n     *\n     * EXAMPLE\n     * Encode the fifteen digit numeric string 000213298174000\n     * Prefix the numeric string with a 1 and set the initial value of\n     * t = 1 000 213 298 174 000\n     * Calculate codeword 0\n     * d0 = 1 000 213 298 174 000 mod 900 = 200\n     *\n     * t = 1 000 213 298 174 000 div 900 = 1 111 348 109 082\n     * Calculate codeword 1\n     * d1 = 1 111 348 109 082 mod 900 = 282\n     *\n     * t = 1 111 348 109 082 div 900 = 1 234 831 232\n     * Calculate codeword 2\n     * d2 = 1 234 831 232 mod 900 = 632\n     *\n     * t = 1 234 831 232 div 900 = 1 372 034\n     * Calculate codeword 3\n     * d3 = 1 372 034 mod 900 = 434\n     *\n     * t = 1 372 034 div 900 = 1 524\n     * Calculate codeword 4\n     * d4 = 1 524 mod 900 = 624\n     *\n     * t = 1 524 div 900 = 1\n     * Calculate codeword 5\n     * d5 = 1 mod 900 = 1\n     * t = 1 div 900 = 0\n     * Codeword sequence is: 1, 624, 434, 632, 282, 200\n     *\n     * Decode the above codewords involves\n     *   1 x 900 power of 5 + 624 x 900 power of 4 + 434 x 900 power of 3 +\n     * 632 x 900 power of 2 + 282 x 900 power of 1 + 200 x 900 power of 0 = 1000213298174000\n     *\n     * Remove leading 1 =>  Result is 000213298174000\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.decodeBase900toBase10 = function (codewords, count) {\n        var result = createBigInt(0);\n        for (var i /*int*/ = 0; i < count; i++) {\n            result += DecodedBitStreamParser.EXP900[count - i - 1] * createBigInt(codewords[i]);\n        }\n        var resultString = result.toString();\n        if (resultString.charAt(0) !== '1') {\n            throw new FormatException();\n        }\n        return resultString.substring(1);\n    };\n    DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH = 900;\n    DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH = 901;\n    DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH = 902;\n    DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6 = 924;\n    DecodedBitStreamParser.ECI_USER_DEFINED = 925;\n    DecodedBitStreamParser.ECI_GENERAL_PURPOSE = 926;\n    DecodedBitStreamParser.ECI_CHARSET = 927;\n    DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK = 928;\n    DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD = 923;\n    DecodedBitStreamParser.MACRO_PDF417_TERMINATOR = 922;\n    DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE = 913;\n    DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS = 15;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME = 0;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT = 1;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP = 2;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER = 3;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE = 4;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE = 5;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM = 6;\n    DecodedBitStreamParser.PL = 25;\n    DecodedBitStreamParser.LL = 27;\n    DecodedBitStreamParser.AS = 27;\n    DecodedBitStreamParser.ML = 28;\n    DecodedBitStreamParser.AL = 28;\n    DecodedBitStreamParser.PS = 29;\n    DecodedBitStreamParser.PAL = 29;\n    DecodedBitStreamParser.PUNCT_CHARS = ';<>@[\\\\]_`~!\\r\\t,:\\n-.$/\"|*()?{}\\'';\n    DecodedBitStreamParser.MIXED_CHARS = '0123456789&\\r\\t,:#-.$/+%*=^';\n    /**\n     * Table containing values for the exponent of 900.\n     * This is used in the numeric compaction decode algorithm.\n     */\n    DecodedBitStreamParser.EXP900 = getBigIntConstructor() ? getEXP900() : [];\n    DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS = 2;\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GACD,2CAA2C;AAC3C,2CAA2C;;;;AAC3C;AACA,kDAAkD;AAClD;AACA,gDAAgD;AAChD;AACA,uDAAuD;AACvD;AACA,wCAAwC;AACxC,+BAA+B;AAC/B,mCAAmC;AACnC,4CAA4C;AAC5C,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,SAAS,GAAG,IAAI;AAChB,CAAC,SAAU,IAAI;IACX,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG;IAChC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG;AACpC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrB;;;;;CAKC,GACD,SAAS;IACL,IAAI,OAAO,WAAW,aAAa;QAC/B,OAAO,MAAM,CAAC,SAAS,IAAI;IAC/B;IACA,wCAAmC;QAC/B,OAAO,2CAAM,CAAC,SAAS,IAAI;IAC/B;;;AAKJ;AACA;;CAEC,GACD,IAAI;AACJ;;;;CAIC,GACD,SAAS,aAAa,GAAG;IACrB,IAAI,OAAO,eAAe,aAAa;QACnC,aAAa;IACjB;IACA,IAAI,eAAe,MAAM;QACrB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,WAAW;AACtB;AACA,SAAS;IACL,mCAAmC;IACnC,IAAI,SAAS,EAAE;IACf,MAAM,CAAC,EAAE,GAAG,aAAa;IACzB,IAAI,cAAc,aAAa;IAC/B,MAAM,CAAC,EAAE,GAAG;IACZ,mCAAmC;IACnC,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,IAAI,IAAK;QACjC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG;IAChC;IACA,OAAO;AACX;AACA;;;;;CAKC,GACD,IAAI,yBAAwC;IACxC,SAAS,0BACT;IACA,uCAAuC;IACvC,IAAI;IACJ;;;;;;KAMC,GACD,uBAAuB,MAAM,GAAG,SAAU,SAAS,EAAE,OAAO;QACxD,yEAAyE;QACzE,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa,CAAC;QAC/B,uDAAuD;QACvD,IAAI,WAAW,iLAAA,CAAA,UAAe,CAAC,SAAS;QACxC;;;;;;SAMC,GACD,OAAO,cAAc,CAAC;QACtB,sBAAsB;QACtB,IAAI,YAAY;QAChB,IAAI,OAAO,SAAS,CAAC,YAAY;QACjC,IAAI,iBAAiB,IAAI,sLAAA,CAAA,UAAoB;QAC7C,MAAO,YAAY,SAAS,CAAC,EAAE,CAAE;YAC7B,OAAQ;gBACJ,KAAK,uBAAuB,0BAA0B;oBAClD,YAAY,uBAAuB,cAAc,CAAC,WAAW,WAAW;oBACxE;gBACJ,KAAK,uBAAuB,0BAA0B;gBACtD,KAAK,uBAAuB,4BAA4B;oBACpD,YAAY,uBAAuB,cAAc,CAAC,MAAM,WAAW,UAAU,WAAW;oBACxF;gBACJ,KAAK,uBAAuB,kCAAkC;oBAC1D,OAAO,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,YAAY;oBAC/C;gBACJ,KAAK,uBAAuB,6BAA6B;oBACrD,YAAY,uBAAuB,iBAAiB,CAAC,WAAW,WAAW;oBAC3E;gBACJ,KAAK,uBAAuB,WAAW;oBACnC,IAAI,aAAa,iLAAA,CAAA,UAAe,CAAC,yBAAyB,CAAC,SAAS,CAAC,YAAY;oBAEjF;gBACJ,KAAK,uBAAuB,mBAAmB;oBAC3C,4DAA4D;oBAC5D,aAAa;oBACb;gBACJ,KAAK,uBAAuB,gBAAgB;oBACxC,wDAAwD;oBACxD;oBACA;gBACJ,KAAK,uBAAuB,gCAAgC;oBACxD,YAAY,uBAAuB,gBAAgB,CAAC,WAAW,WAAW;oBAC1E;gBACJ,KAAK,uBAAuB,iCAAiC;gBAC7D,KAAK,uBAAuB,uBAAuB;oBAC/C,6CAA6C;oBAC7C,MAAM,IAAI,uKAAA,CAAA,UAAe;gBAC7B;oBACI,+DAA+D;oBAC/D,sEAAsE;oBACtE,oCAAoC;oBACpC;oBACA,YAAY,uBAAuB,cAAc,CAAC,WAAW,WAAW;oBACxE;YACR;YACA,IAAI,YAAY,UAAU,MAAM,EAAE;gBAC9B,OAAO,SAAS,CAAC,YAAY;YACjC,OACK;gBACD,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;YAC3C;QACJ;QACA,IAAI,OAAO,MAAM,OAAO,GAAG;YACvB,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;QAC3C;QACA,IAAI,gBAAgB,IAAI,+KAAA,CAAA,UAAa,CAAC,MAAM,OAAO,QAAQ,IAAI,MAAM;QACrE,cAAc,QAAQ,CAAC;QACvB,OAAO;IACX;IACA;;;;;;;;;;;KAWC,GACD,mCAAmC;IACnC,uBAAuB,gBAAgB,GAAG,SAAU,SAAS,EAAE,SAAS,EAAE,cAAc;QACpF,IAAI,YAAY,uBAAuB,4BAA4B,GAAG,SAAS,CAAC,EAAE,EAAE;YAChF,6DAA6D;YAC7D,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;QAC3C;QACA,IAAI,oBAAoB,IAAI,WAAW,uBAAuB,4BAA4B;QAC1F,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,uBAAuB,4BAA4B,EAAE,KAAK,YAAa;YAC/F,iBAAiB,CAAC,EAAE,GAAG,SAAS,CAAC,UAAU;QAC/C;QACA,eAAe,eAAe,CAAC,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,uBAAuB,qBAAqB,CAAC,mBAAmB,uBAAuB,4BAA4B;QACnK,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;QAC9B,YAAY,uBAAuB,cAAc,CAAC,WAAW,WAAW;QACxE,eAAe,SAAS,CAAC,OAAO,QAAQ;QACxC,IAAI,sBAAsB,CAAC;QAC3B,IAAI,SAAS,CAAC,UAAU,KAAK,uBAAuB,iCAAiC,EAAE;YACnF,sBAAsB,YAAY;QACtC;QACA,MAAO,YAAY,SAAS,CAAC,EAAE,CAAE;YAC7B,OAAQ,SAAS,CAAC,UAAU;gBACxB,KAAK,uBAAuB,iCAAiC;oBACzD;oBACA,OAAQ,SAAS,CAAC,UAAU;wBACxB,KAAK,uBAAuB,qCAAqC;4BAC7D,IAAI,WAAW,IAAI,6KAAA,CAAA,UAAa;4BAChC,YAAY,uBAAuB,cAAc,CAAC,WAAW,YAAY,GAAG;4BAC5E,eAAe,WAAW,CAAC,SAAS,QAAQ;4BAC5C;wBACJ,KAAK,uBAAuB,kCAAkC;4BAC1D,IAAI,SAAS,IAAI,6KAAA,CAAA,UAAa;4BAC9B,YAAY,uBAAuB,cAAc,CAAC,WAAW,YAAY,GAAG;4BAC5E,eAAe,SAAS,CAAC,OAAO,QAAQ;4BACxC;wBACJ,KAAK,uBAAuB,qCAAqC;4BAC7D,IAAI,YAAY,IAAI,6KAAA,CAAA,UAAa;4BACjC,YAAY,uBAAuB,cAAc,CAAC,WAAW,YAAY,GAAG;4BAC5E,eAAe,YAAY,CAAC,UAAU,QAAQ;4BAC9C;wBACJ,KAAK,uBAAuB,yCAAyC;4BACjE,IAAI,eAAe,IAAI,6KAAA,CAAA,UAAa;4BACpC,YAAY,uBAAuB,iBAAiB,CAAC,WAAW,YAAY,GAAG;4BAC/E,eAAe,eAAe,CAAC,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,aAAa,QAAQ;4BACrE;wBACJ,KAAK,uBAAuB,sCAAsC;4BAC9D,IAAI,YAAY,IAAI,6KAAA,CAAA,UAAa;4BACjC,YAAY,uBAAuB,iBAAiB,CAAC,WAAW,YAAY,GAAG;4BAC/E,eAAe,YAAY,CAAC,oKAAA,CAAA,UAAI,CAAC,SAAS,CAAC,UAAU,QAAQ;4BAC7D;wBACJ,KAAK,uBAAuB,oCAAoC;4BAC5D,IAAI,WAAW,IAAI,6KAAA,CAAA,UAAa;4BAChC,YAAY,uBAAuB,iBAAiB,CAAC,WAAW,YAAY,GAAG;4BAC/E,eAAe,WAAW,CAAC,uKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAS,QAAQ;4BAC7D;wBACJ,KAAK,uBAAuB,qCAAqC;4BAC7D,IAAI,WAAW,IAAI,6KAAA,CAAA,UAAa;4BAChC,YAAY,uBAAuB,iBAAiB,CAAC,WAAW,YAAY,GAAG;4BAC/E,eAAe,WAAW,CAAC,oKAAA,CAAA,UAAI,CAAC,SAAS,CAAC,SAAS,QAAQ;4BAC3D;wBACJ;4BACI,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;oBAC/C;oBACA;gBACJ,KAAK,uBAAuB,uBAAuB;oBAC/C;oBACA,eAAe,cAAc,CAAC;oBAC9B;gBACJ;oBACI,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;YAC/C;QACJ;QACA,6CAA6C;QAC7C,IAAI,wBAAwB,CAAC,GAAG;YAC5B,IAAI,uBAAuB,YAAY;YACvC,IAAI,eAAe,aAAa,IAAI;gBAChC,4BAA4B;gBAC5B;YACJ;YACA,eAAe,eAAe,CAAC,sKAAA,CAAA,UAAM,CAAC,WAAW,CAAC,WAAW,qBAAqB,sBAAsB;QAC5G;QACA,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,uBAAuB,cAAc,GAAG,SAAU,SAAS,EAAE,SAAS,EAAE,MAAM;QAC1E,2BAA2B;QAC3B,IAAI,qBAAqB,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,IAAI;QACrE,kEAAkE;QAClE,IAAI,qBAAqB,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,IAAI;QACrE,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,MAAO,AAAC,YAAY,SAAS,CAAC,EAAE,IAAK,CAAC,IAAK;YACvC,IAAI,OAAO,SAAS,CAAC,YAAY;YACjC,IAAI,OAAO,uBAAuB,0BAA0B,EAAE;gBAC1D,kBAAkB,CAAC,MAAM,GAAG,OAAO;gBACnC,kBAAkB,CAAC,QAAQ,EAAE,GAAG,OAAO;gBACvC,SAAS;YACb,OACK;gBACD,OAAQ;oBACJ,KAAK,uBAAuB,0BAA0B;wBAClD,sDAAsD;wBACtD,kBAAkB,CAAC,QAAQ,GAAG,uBAAuB,0BAA0B;wBAC/E;oBACJ,KAAK,uBAAuB,0BAA0B;oBACtD,KAAK,uBAAuB,4BAA4B;oBACxD,KAAK,uBAAuB,6BAA6B;oBACzD,KAAK,uBAAuB,gCAAgC;oBAC5D,KAAK,uBAAuB,iCAAiC;oBAC7D,KAAK,uBAAuB,uBAAuB;wBAC/C;wBACA,MAAM;wBACN;oBACJ,KAAK,uBAAuB,kCAAkC;wBAC1D,sDAAsD;wBACtD,4DAA4D;wBAC5D,6DAA6D;wBAC7D,+DAA+D;wBAC/D,8DAA8D;wBAC9D,4DAA4D;wBAC5D,kBAAkB,CAAC,MAAM,GAAG,uBAAuB,kCAAkC;wBACrF,OAAO,SAAS,CAAC,YAAY;wBAC7B,kBAAkB,CAAC,MAAM,GAAG;wBAC5B;wBACA;gBACR;YACJ;QACJ;QACA,uBAAuB,oBAAoB,CAAC,oBAAoB,oBAAoB,OAAO;QAC3F,OAAO;IACX;IACA;;;;;;;;;;;;;;;KAeC,GACD,uBAAuB,oBAAoB,GAAG,SAAU,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,EAAE,MAAM;QAC1G,wDAAwD;QACxD,oGAAoG;QACpG,yGAAyG;QACzG,6EAA6E;QAC7E,IAAI,UAAU,KAAK,KAAK;QACxB,IAAI,mBAAmB,KAAK,KAAK;QACjC,IAAI,IAAI;QACR,MAAO,IAAI,OAAQ;YACf,IAAI,YAAY,kBAAkB,CAAC,EAAE;YACrC,IAAI,KAAK,MAAM,GAAG;YAClB,OAAQ;gBACJ,KAAK,KAAK,KAAK;oBACX,gCAAgC;oBAChC,IAAI,YAAY,IAAI;wBAChB,6BAA6B;wBAC7B,uDAAuD;wBACvD,KAAK,0BAA0B,GAAG,OAAO,YAAY,CAAC,KAAK;oBAC/D,OACK;wBACD,OAAQ;4BACJ,KAAK;gCACD,KAAK;gCACL;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,uBAAuB;gCACvB,mBAAmB;gCACnB,UAAU,KAAK,WAAW;gCAC1B;4BACJ,KAAK,uBAAuB,kCAAkC;gCAC1D,OAAO,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,EAAE;gCAC9C;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;gBACJ,KAAK,KAAK,KAAK;oBACX,gCAAgC;oBAChC,IAAI,YAAY,IAAI;wBAChB,KAAK,yBAAyB,GAAG,OAAO,YAAY,CAAC,KAAK;oBAC9D,OACK;wBACD,OAAQ;4BACJ,KAAK;gCACD,KAAK;gCACL;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,iBAAiB;gCACjB,mBAAmB;gCACnB,UAAU,KAAK,WAAW;gCAC1B;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,uBAAuB;gCACvB,mBAAmB;gCACnB,UAAU,KAAK,WAAW;gCAC1B;4BACJ,KAAK,uBAAuB,kCAAkC;gCAC1D,yFAAyF;gCACzF,OAAO,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,EAAE;gCAC9C;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;gBACJ,KAAK,KAAK,KAAK;oBACX,yBAAyB;oBACzB,IAAI,YAAY,uBAAuB,EAAE,EAAE;wBACvC,KAAK,uBAAuB,WAAW,CAAC,UAAU;oBACtD,OACK;wBACD,OAAQ;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK;gCACD,KAAK;gCACL;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,EAAE;gCAC1B,uBAAuB;gCACvB,mBAAmB;gCACnB,UAAU,KAAK,WAAW;gCAC1B;4BACJ,KAAK,uBAAuB,kCAAkC;gCAC1D,OAAO,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,EAAE;gCAC9C;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;gBACJ,KAAK,KAAK,KAAK;oBACX,cAAc;oBACd,IAAI,YAAY,uBAAuB,GAAG,EAAE;wBACxC,KAAK,uBAAuB,WAAW,CAAC,UAAU;oBACtD,OACK;wBACD,OAAQ;4BACJ,KAAK,uBAAuB,GAAG;gCAC3B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,kCAAkC;gCAC1D,OAAO,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,EAAE;gCAC9C;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;gBACJ,KAAK,KAAK,WAAW;oBACjB,mBAAmB;oBACnB,UAAU;oBACV,IAAI,YAAY,IAAI;wBAChB,KAAK,yBAAyB,GAAG,OAAO,YAAY,CAAC,KAAK;oBAC9D,OACK;wBACD,OAAQ;4BACJ,KAAK;gCACD,KAAK;gCACL;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;gBACJ,KAAK,KAAK,WAAW;oBACjB,mBAAmB;oBACnB,UAAU;oBACV,IAAI,YAAY,uBAAuB,GAAG,EAAE;wBACxC,KAAK,uBAAuB,WAAW,CAAC,UAAU;oBACtD,OACK;wBACD,OAAQ;4BACJ,KAAK,uBAAuB,GAAG;gCAC3B,UAAU,KAAK,KAAK;gCACpB;4BACJ,KAAK,uBAAuB,kCAAkC;gCAC1D,0DAA0D;gCAC1D,mCAAmC;gCACnC,OAAO,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,EAAE;gCAC9C;4BACJ,KAAK,uBAAuB,0BAA0B;gCAClD,UAAU,KAAK,KAAK;gCACpB;wBACR;oBACJ;oBACA;YACR;YACA,kBAAkB;YAClB,IAAI,OAAO,IAAI;gBACX,qCAAqC;gBACrC,OAAO,MAAM,CAAC;YAClB;YACA;QACJ;IACJ;IACA;;;;;;;;;;;KAWC,GACD,uBAAuB,cAAc,GAAG,SAAU,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;QAC1F,IAAI,eAAe,IAAI,qLAAA,CAAA,UAAqB;QAC5C,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,GAAG;QACrB,IAAI,MAAM;QACV,OAAQ;YACJ,KAAK,uBAAuB,0BAA0B;gBAClD,2DAA2D;gBAC3D,yBAAyB;gBACzB,IAAI,yBAAyB,IAAI,WAAW;gBAC5C,IAAI,WAAW,SAAS,CAAC,YAAY;gBACrC,MAAO,AAAC,YAAY,SAAS,CAAC,EAAE,IAAK,CAAC,IAAK;oBACvC,sBAAsB,CAAC,QAAQ,GAAG;oBAClC,WAAW;oBACX,QAAQ,MAAM,QAAQ;oBACtB,WAAW,SAAS,CAAC,YAAY;oBACjC,+EAA+E;oBAC/E,OAAQ;wBACJ,KAAK,uBAAuB,0BAA0B;wBACtD,KAAK,uBAAuB,0BAA0B;wBACtD,KAAK,uBAAuB,6BAA6B;wBACzD,KAAK,uBAAuB,4BAA4B;wBACxD,KAAK,uBAAuB,gCAAgC;wBAC5D,KAAK,uBAAuB,iCAAiC;wBAC7D,KAAK,uBAAuB,uBAAuB;4BAC/C;4BACA,MAAM;4BACN;wBACJ;4BACI,IAAI,AAAC,QAAQ,MAAM,KAAO,QAAQ,GAAI;gCAClC,2BAA2B;gCAC3B,sBAAsB;gCACtB,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,EAAE,EAAG;oCAChC;;;qCAGC,GACD,aAAa,KAAK,CAAC,QAAQ,GAAG,OAAO,aAAa,UAAU,aAAa,IAAI,CAAC,IAAI,CAAC;gCACvF;gCACA,QAAQ;gCACR,QAAQ;4BACZ;4BACA;oBACR;gBACJ;gBACA,6EAA6E;gBAC7E,IAAI,cAAc,SAAS,CAAC,EAAE,IAAI,WAAW,uBAAuB,0BAA0B,EAAE;oBAC5F,sBAAsB,CAAC,QAAQ,GAAG;gBACtC;gBACA,wDAAwD;gBACxD,sDAAsD;gBACtD,gDAAgD;gBAChD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,OAAO,IAAK;oBACpC,aAAa,KAAK,CAAC,QAAQ,GAAG,sBAAsB,CAAC,EAAE;gBAC3D;gBACA;YACJ,KAAK,uBAAuB,4BAA4B;gBACpD,2DAA2D;gBAC3D,8BAA8B;gBAC9B,MAAO,YAAY,SAAS,CAAC,EAAE,IAAI,CAAC,IAAK;oBACrC,IAAI,OAAO,SAAS,CAAC,YAAY;oBACjC,IAAI,OAAO,uBAAuB,0BAA0B,EAAE;wBAC1D;wBACA,WAAW;wBACX,QAAQ,MAAM,QAAQ;oBAC1B,OACK;wBACD,OAAQ;4BACJ,KAAK,uBAAuB,0BAA0B;4BACtD,KAAK,uBAAuB,0BAA0B;4BACtD,KAAK,uBAAuB,6BAA6B;4BACzD,KAAK,uBAAuB,4BAA4B;4BACxD,KAAK,uBAAuB,gCAAgC;4BAC5D,KAAK,uBAAuB,iCAAiC;4BAC7D,KAAK,uBAAuB,uBAAuB;gCAC/C;gCACA,MAAM;gCACN;wBACR;oBACJ;oBACA,IAAI,AAAC,QAAQ,MAAM,KAAO,QAAQ,GAAI;wBAClC,2BAA2B;wBAC3B,sBAAsB;wBACtB;;;wBAGA,GACA,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,EAAE,EAAG;4BAChC,aAAa,KAAK,CAAC,QAAQ,GAAG,OAAO,aAAa,UAAU,aAAa,IAAI,CAAC,IAAI,CAAC;wBACvF;wBACA,QAAQ;wBACR,QAAQ;oBACZ;gBACJ;gBACA;QACR;QACA,OAAO,MAAM,CAAC,8KAAA,CAAA,UAAc,CAAC,MAAM,CAAC,aAAa,WAAW,IAAI;QAChE,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,uBAAuB,iBAAiB,GAAG,SAAU,SAAS,EAAE,UAAU,KAAK,GAAN,EAAU,MAAM;QACrF,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,IAAI,mBAAmB,IAAI,WAAW,uBAAuB,qBAAqB;QAClF,MAAO,YAAY,SAAS,CAAC,EAAE,IAAI,CAAC,IAAK;YACrC,IAAI,OAAO,SAAS,CAAC,YAAY;YACjC,IAAI,cAAc,SAAS,CAAC,EAAE,EAAE;gBAC5B,MAAM;YACV;YACA,IAAI,OAAO,uBAAuB,0BAA0B,EAAE;gBAC1D,gBAAgB,CAAC,MAAM,GAAG;gBAC1B;YACJ,OACK;gBACD,OAAQ;oBACJ,KAAK,uBAAuB,0BAA0B;oBACtD,KAAK,uBAAuB,0BAA0B;oBACtD,KAAK,uBAAuB,4BAA4B;oBACxD,KAAK,uBAAuB,gCAAgC;oBAC5D,KAAK,uBAAuB,iCAAiC;oBAC7D,KAAK,uBAAuB,uBAAuB;wBAC/C;wBACA,MAAM;wBACN;gBACR;YACJ;YACA,IAAI,CAAC,QAAQ,uBAAuB,qBAAqB,KAAK,KAAK,SAAS,uBAAuB,6BAA6B,IAAI,GAAG,KAAK,QAAQ,GAAG;gBACnJ,6DAA6D;gBAC7D,6DAA6D;gBAC7D,oEAAoE;gBACpE,wCAAwC;gBACxC,OAAO,MAAM,CAAC,uBAAuB,qBAAqB,CAAC,kBAAkB;gBAC7E,QAAQ;YACZ;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2CC,GACD,uBAAuB,qBAAqB,GAAG,SAAU,SAAS,EAAE,KAAK;QACrE,IAAI,SAAS,aAAa;QAC1B,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,OAAO,IAAK;YACpC,UAAU,uBAAuB,MAAM,CAAC,QAAQ,IAAI,EAAE,GAAG,aAAa,SAAS,CAAC,EAAE;QACtF;QACA,IAAI,eAAe,OAAO,QAAQ;QAClC,IAAI,aAAa,MAAM,CAAC,OAAO,KAAK;YAChC,MAAM,IAAI,uKAAA,CAAA,UAAe;QAC7B;QACA,OAAO,aAAa,SAAS,CAAC;IAClC;IACA,uBAAuB,0BAA0B,GAAG;IACpD,uBAAuB,0BAA0B,GAAG;IACpD,uBAAuB,6BAA6B,GAAG;IACvD,uBAAuB,4BAA4B,GAAG;IACtD,uBAAuB,gBAAgB,GAAG;IAC1C,uBAAuB,mBAAmB,GAAG;IAC7C,uBAAuB,WAAW,GAAG;IACrC,uBAAuB,gCAAgC,GAAG;IAC1D,uBAAuB,iCAAiC,GAAG;IAC3D,uBAAuB,uBAAuB,GAAG;IACjD,uBAAuB,kCAAkC,GAAG;IAC5D,uBAAuB,qBAAqB,GAAG;IAC/C,uBAAuB,qCAAqC,GAAG;IAC/D,uBAAuB,yCAAyC,GAAG;IACnE,uBAAuB,sCAAsC,GAAG;IAChE,uBAAuB,kCAAkC,GAAG;IAC5D,uBAAuB,qCAAqC,GAAG;IAC/D,uBAAuB,qCAAqC,GAAG;IAC/D,uBAAuB,oCAAoC,GAAG;IAC9D,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,EAAE,GAAG;IAC5B,uBAAuB,GAAG,GAAG;IAC7B,uBAAuB,WAAW,GAAG;IACrC,uBAAuB,WAAW,GAAG;IACrC;;;KAGC,GACD,uBAAuB,MAAM,GAAG,yBAAyB,cAAc,EAAE;IACzE,uBAAuB,4BAA4B,GAAG;IACtD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8934, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/decoder/PDF417ScanningDecoder.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../ChecksumException';\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import com.google.zxing.pdf417.decoder.ec.ErrorCorrection;\nimport ErrorCorrection from './ec/ErrorCorrection';\n// local\nimport BoundingBox from './BoundingBox';\nimport DetectionResultRowIndicatorColumn from './DetectionResultRowIndicatorColumn';\nimport DetectionResult from './DetectionResult';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport Codeword from './Codeword';\nimport BarcodeValue from './BarcodeValue';\nimport PDF417CodewordDecoder from './PDF417CodewordDecoder';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n// utils\nimport Formatter from '../../util/Formatter';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.Formatter;\n// import java.util.List;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ScanningDecoder = /** @class */ (function () {\n    function PDF417ScanningDecoder() {\n    }\n    /**\n     * @TODO don't pass in minCodewordWidth and maxCodewordWidth, pass in barcode columns for start and stop pattern\n     *\n     * columns. That way width can be deducted from the pattern column.\n     * This approach also allows to detect more details about the barcode, e.g. if a bar type (white or black) is wider\n     * than it should be. This can happen if the scanner used a bad blackpoint.\n     *\n     * @param BitMatrix\n     * @param image\n     * @param ResultPoint\n     * @param imageTopLeft\n     * @param ResultPoint\n     * @param imageBottomLeft\n     * @param ResultPoint\n     * @param imageTopRight\n     * @param ResultPoint\n     * @param imageBottomRight\n     * @param int\n     * @param minCodewordWidth\n     * @param int\n     * @param maxCodewordWidth\n     *\n     * @throws NotFoundException\n     * @throws FormatException\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.decode = function (image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight, minCodewordWidth, maxCodewordWidth) {\n        var boundingBox = new BoundingBox(image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight);\n        var leftRowIndicatorColumn = null;\n        var rightRowIndicatorColumn = null;\n        var detectionResult;\n        for (var firstPass /*boolean*/ = true;; firstPass = false) {\n            if (imageTopLeft != null) {\n                leftRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopLeft, true, minCodewordWidth, maxCodewordWidth);\n            }\n            if (imageTopRight != null) {\n                rightRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopRight, false, minCodewordWidth, maxCodewordWidth);\n            }\n            detectionResult = PDF417ScanningDecoder.merge(leftRowIndicatorColumn, rightRowIndicatorColumn);\n            if (detectionResult == null) {\n                throw NotFoundException.getNotFoundInstance();\n            }\n            var resultBox = detectionResult.getBoundingBox();\n            if (firstPass && resultBox != null &&\n                (resultBox.getMinY() < boundingBox.getMinY() || resultBox.getMaxY() > boundingBox.getMaxY())) {\n                boundingBox = resultBox;\n            }\n            else {\n                break;\n            }\n        }\n        detectionResult.setBoundingBox(boundingBox);\n        var maxBarcodeColumn = detectionResult.getBarcodeColumnCount() + 1;\n        detectionResult.setDetectionResultColumn(0, leftRowIndicatorColumn);\n        detectionResult.setDetectionResultColumn(maxBarcodeColumn, rightRowIndicatorColumn);\n        var leftToRight = leftRowIndicatorColumn != null;\n        for (var barcodeColumnCount /*int*/ = 1; barcodeColumnCount <= maxBarcodeColumn; barcodeColumnCount++) {\n            var barcodeColumn = leftToRight ? barcodeColumnCount : maxBarcodeColumn - barcodeColumnCount;\n            if (detectionResult.getDetectionResultColumn(barcodeColumn) !== /* null */ undefined) {\n                // This will be the case for the opposite row indicator column, which doesn't need to be decoded again.\n                continue;\n            }\n            var detectionResultColumn = void 0;\n            if (barcodeColumn === 0 || barcodeColumn === maxBarcodeColumn) {\n                detectionResultColumn = new DetectionResultRowIndicatorColumn(boundingBox, barcodeColumn === 0);\n            }\n            else {\n                detectionResultColumn = new DetectionResultColumn(boundingBox);\n            }\n            detectionResult.setDetectionResultColumn(barcodeColumn, detectionResultColumn);\n            var startColumn = -1;\n            var previousStartColumn = startColumn;\n            // TODO start at a row for which we know the start position, then detect upwards and downwards from there.\n            for (var imageRow /*int*/ = boundingBox.getMinY(); imageRow <= boundingBox.getMaxY(); imageRow++) {\n                startColumn = PDF417ScanningDecoder.getStartColumn(detectionResult, barcodeColumn, imageRow, leftToRight);\n                if (startColumn < 0 || startColumn > boundingBox.getMaxX()) {\n                    if (previousStartColumn === -1) {\n                        continue;\n                    }\n                    startColumn = previousStartColumn;\n                }\n                var codeword = PDF417ScanningDecoder.detectCodeword(image, boundingBox.getMinX(), boundingBox.getMaxX(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n                if (codeword != null) {\n                    detectionResultColumn.setCodeword(imageRow, codeword);\n                    previousStartColumn = startColumn;\n                    minCodewordWidth = Math.min(minCodewordWidth, codeword.getWidth());\n                    maxCodewordWidth = Math.max(maxCodewordWidth, codeword.getWidth());\n                }\n            }\n        }\n        return PDF417ScanningDecoder.createDecoderResult(detectionResult);\n    };\n    /**\n     *\n     * @param leftRowIndicatorColumn\n     * @param rightRowIndicatorColumn\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.merge = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n        if (leftRowIndicatorColumn == null && rightRowIndicatorColumn == null) {\n            return null;\n        }\n        var barcodeMetadata = PDF417ScanningDecoder.getBarcodeMetadata(leftRowIndicatorColumn, rightRowIndicatorColumn);\n        if (barcodeMetadata == null) {\n            return null;\n        }\n        var boundingBox = BoundingBox.merge(PDF417ScanningDecoder.adjustBoundingBox(leftRowIndicatorColumn), PDF417ScanningDecoder.adjustBoundingBox(rightRowIndicatorColumn));\n        return new DetectionResult(barcodeMetadata, boundingBox);\n    };\n    /**\n     *\n     * @param rowIndicatorColumn\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.adjustBoundingBox = function (rowIndicatorColumn) {\n        var e_1, _a;\n        if (rowIndicatorColumn == null) {\n            return null;\n        }\n        var rowHeights = rowIndicatorColumn.getRowHeights();\n        if (rowHeights == null) {\n            return null;\n        }\n        var maxRowHeight = PDF417ScanningDecoder.getMax(rowHeights);\n        var missingStartRows = 0;\n        try {\n            for (var rowHeights_1 = __values(rowHeights), rowHeights_1_1 = rowHeights_1.next(); !rowHeights_1_1.done; rowHeights_1_1 = rowHeights_1.next()) {\n                var rowHeight = rowHeights_1_1.value /*int*/;\n                missingStartRows += maxRowHeight - rowHeight;\n                if (rowHeight > 0) {\n                    break;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (rowHeights_1_1 && !rowHeights_1_1.done && (_a = rowHeights_1.return)) _a.call(rowHeights_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var codewords = rowIndicatorColumn.getCodewords();\n        for (var row /*int*/ = 0; missingStartRows > 0 && codewords[row] == null; row++) {\n            missingStartRows--;\n        }\n        var missingEndRows = 0;\n        for (var row /*int*/ = rowHeights.length - 1; row >= 0; row--) {\n            missingEndRows += maxRowHeight - rowHeights[row];\n            if (rowHeights[row] > 0) {\n                break;\n            }\n        }\n        for (var row /*int*/ = codewords.length - 1; missingEndRows > 0 && codewords[row] == null; row--) {\n            missingEndRows--;\n        }\n        return rowIndicatorColumn.getBoundingBox().addMissingRows(missingStartRows, missingEndRows, rowIndicatorColumn.isLeft());\n    };\n    PDF417ScanningDecoder.getMax = function (values) {\n        var e_2, _a;\n        var maxValue = -1;\n        try {\n            for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\n                var value = values_1_1.value /*int*/;\n                maxValue = Math.max(maxValue, value);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return maxValue;\n    };\n    PDF417ScanningDecoder.getBarcodeMetadata = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n        var leftBarcodeMetadata;\n        if (leftRowIndicatorColumn == null ||\n            (leftBarcodeMetadata = leftRowIndicatorColumn.getBarcodeMetadata()) == null) {\n            return rightRowIndicatorColumn == null ? null : rightRowIndicatorColumn.getBarcodeMetadata();\n        }\n        var rightBarcodeMetadata;\n        if (rightRowIndicatorColumn == null ||\n            (rightBarcodeMetadata = rightRowIndicatorColumn.getBarcodeMetadata()) == null) {\n            return leftBarcodeMetadata;\n        }\n        if (leftBarcodeMetadata.getColumnCount() !== rightBarcodeMetadata.getColumnCount() &&\n            leftBarcodeMetadata.getErrorCorrectionLevel() !== rightBarcodeMetadata.getErrorCorrectionLevel() &&\n            leftBarcodeMetadata.getRowCount() !== rightBarcodeMetadata.getRowCount()) {\n            return null;\n        }\n        return leftBarcodeMetadata;\n    };\n    PDF417ScanningDecoder.getRowIndicatorColumn = function (image, boundingBox, startPoint, leftToRight, minCodewordWidth, maxCodewordWidth) {\n        var rowIndicatorColumn = new DetectionResultRowIndicatorColumn(boundingBox, leftToRight);\n        for (var i /*int*/ = 0; i < 2; i++) {\n            var increment = i === 0 ? 1 : -1;\n            var startColumn = Math.trunc(Math.trunc(startPoint.getX()));\n            for (var imageRow /*int*/ = Math.trunc(Math.trunc(startPoint.getY())); imageRow <= boundingBox.getMaxY() &&\n                imageRow >= boundingBox.getMinY(); imageRow += increment) {\n                var codeword = PDF417ScanningDecoder.detectCodeword(image, 0, image.getWidth(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n                if (codeword != null) {\n                    rowIndicatorColumn.setCodeword(imageRow, codeword);\n                    if (leftToRight) {\n                        startColumn = codeword.getStartX();\n                    }\n                    else {\n                        startColumn = codeword.getEndX();\n                    }\n                }\n            }\n        }\n        return rowIndicatorColumn;\n    };\n    /**\n     *\n     * @param detectionResult\n     * @param BarcodeValue\n     * @param param2\n     * @param param3\n     * @param barcodeMatrix\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.adjustCodewordCount = function (detectionResult, barcodeMatrix) {\n        var barcodeMatrix01 = barcodeMatrix[0][1];\n        var numberOfCodewords = barcodeMatrix01.getValue();\n        var calculatedNumberOfCodewords = detectionResult.getBarcodeColumnCount() *\n            detectionResult.getBarcodeRowCount() -\n            PDF417ScanningDecoder.getNumberOfECCodeWords(detectionResult.getBarcodeECLevel());\n        if (numberOfCodewords.length === 0) {\n            if (calculatedNumberOfCodewords < 1 || calculatedNumberOfCodewords > PDF417Common.MAX_CODEWORDS_IN_BARCODE) {\n                throw NotFoundException.getNotFoundInstance();\n            }\n            barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n        }\n        else if (numberOfCodewords[0] !== calculatedNumberOfCodewords) {\n            // The calculated one is more reliable as it is derived from the row indicator columns\n            barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n        }\n    };\n    /**\n     *\n     * @param detectionResult\n     *\n     * @throws FormatException\n     * @throws ChecksumException\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.createDecoderResult = function (detectionResult) {\n        var barcodeMatrix = PDF417ScanningDecoder.createBarcodeMatrix(detectionResult);\n        PDF417ScanningDecoder.adjustCodewordCount(detectionResult, barcodeMatrix);\n        var erasures /*Collection<Integer>*/ = new Array();\n        var codewords = new Int32Array(detectionResult.getBarcodeRowCount() * detectionResult.getBarcodeColumnCount());\n        var ambiguousIndexValuesList = /*List<int[]>*/ [];\n        var ambiguousIndexesList = /*Collection<Integer>*/ new Array();\n        for (var row /*int*/ = 0; row < detectionResult.getBarcodeRowCount(); row++) {\n            for (var column /*int*/ = 0; column < detectionResult.getBarcodeColumnCount(); column++) {\n                var values = barcodeMatrix[row][column + 1].getValue();\n                var codewordIndex = row * detectionResult.getBarcodeColumnCount() + column;\n                if (values.length === 0) {\n                    erasures.push(codewordIndex);\n                }\n                else if (values.length === 1) {\n                    codewords[codewordIndex] = values[0];\n                }\n                else {\n                    ambiguousIndexesList.push(codewordIndex);\n                    ambiguousIndexValuesList.push(values);\n                }\n            }\n        }\n        var ambiguousIndexValues = new Array(ambiguousIndexValuesList.length);\n        for (var i /*int*/ = 0; i < ambiguousIndexValues.length; i++) {\n            ambiguousIndexValues[i] = ambiguousIndexValuesList[i];\n        }\n        return PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues(detectionResult.getBarcodeECLevel(), codewords, PDF417Common.toIntArray(erasures), PDF417Common.toIntArray(ambiguousIndexesList), ambiguousIndexValues);\n    };\n    /**\n     * This method deals with the fact, that the decoding process doesn't always yield a single most likely value. The\n     * current error correction implementation doesn't deal with erasures very well, so it's better to provide a value\n     * for these ambiguous codewords instead of treating it as an erasure. The problem is that we don't know which of\n     * the ambiguous values to choose. We try decode using the first value, and if that fails, we use another of the\n     * ambiguous values and try to decode again. This usually only happens on very hard to read and decode barcodes,\n     * so decoding the normal barcodes is not affected by this.\n     *\n     * @param erasureArray contains the indexes of erasures\n     * @param ambiguousIndexes array with the indexes that have more than one most likely value\n     * @param ambiguousIndexValues two dimensional array that contains the ambiguous values. The first dimension must\n     * be the same length as the ambiguousIndexes array\n     *\n     * @throws FormatException\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues = function (ecLevel, codewords, erasureArray, ambiguousIndexes, ambiguousIndexValues) {\n        var ambiguousIndexCount = new Int32Array(ambiguousIndexes.length);\n        var tries = 100;\n        while (tries-- > 0) {\n            for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n                codewords[ambiguousIndexes[i]] = ambiguousIndexValues[i][ambiguousIndexCount[i]];\n            }\n            try {\n                return PDF417ScanningDecoder.decodeCodewords(codewords, ecLevel, erasureArray);\n            }\n            catch (err) {\n                var ignored = err instanceof ChecksumException;\n                if (!ignored) {\n                    throw err;\n                }\n            }\n            if (ambiguousIndexCount.length === 0) {\n                throw ChecksumException.getChecksumInstance();\n            }\n            for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n                if (ambiguousIndexCount[i] < ambiguousIndexValues[i].length - 1) {\n                    ambiguousIndexCount[i]++;\n                    break;\n                }\n                else {\n                    ambiguousIndexCount[i] = 0;\n                    if (i === ambiguousIndexCount.length - 1) {\n                        throw ChecksumException.getChecksumInstance();\n                    }\n                }\n            }\n        }\n        throw ChecksumException.getChecksumInstance();\n    };\n    PDF417ScanningDecoder.createBarcodeMatrix = function (detectionResult) {\n        var e_3, _a, e_4, _b;\n        // let barcodeMatrix: BarcodeValue[][] =\n        // new BarcodeValue[detectionResult.getBarcodeRowCount()][detectionResult.getBarcodeColumnCount() + 2];\n        var barcodeMatrix = Array.from({ length: detectionResult.getBarcodeRowCount() }, function () { return new Array(detectionResult.getBarcodeColumnCount() + 2); });\n        for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n            for (var column_1 /*int*/ = 0; column_1 < barcodeMatrix[row].length; column_1++) {\n                barcodeMatrix[row][column_1] = new BarcodeValue();\n            }\n        }\n        var column = 0;\n        try {\n            for (var _c = __values(detectionResult.getDetectionResultColumns()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var detectionResultColumn = _d.value /*DetectionResultColumn*/;\n                if (detectionResultColumn != null) {\n                    try {\n                        for (var _e = (e_4 = void 0, __values(detectionResultColumn.getCodewords())), _f = _e.next(); !_f.done; _f = _e.next()) {\n                            var codeword = _f.value /*Codeword*/;\n                            if (codeword != null) {\n                                var rowNumber = codeword.getRowNumber();\n                                if (rowNumber >= 0) {\n                                    if (rowNumber >= barcodeMatrix.length) {\n                                        // We have more rows than the barcode metadata allows for, ignore them.\n                                        continue;\n                                    }\n                                    barcodeMatrix[rowNumber][column].setValue(codeword.getValue());\n                                }\n                            }\n                        }\n                    }\n                    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                    finally {\n                        try {\n                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                        }\n                        finally { if (e_4) throw e_4.error; }\n                    }\n                }\n                column++;\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return barcodeMatrix;\n    };\n    PDF417ScanningDecoder.isValidBarcodeColumn = function (detectionResult, barcodeColumn) {\n        return barcodeColumn >= 0 && barcodeColumn <= detectionResult.getBarcodeColumnCount() + 1;\n    };\n    PDF417ScanningDecoder.getStartColumn = function (detectionResult, barcodeColumn, imageRow, leftToRight) {\n        var e_5, _a;\n        var offset = leftToRight ? 1 : -1;\n        var codeword = null;\n        if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodeword(imageRow);\n        }\n        if (codeword != null) {\n            return leftToRight ? codeword.getEndX() : codeword.getStartX();\n        }\n        codeword = detectionResult.getDetectionResultColumn(barcodeColumn).getCodewordNearby(imageRow);\n        if (codeword != null) {\n            return leftToRight ? codeword.getStartX() : codeword.getEndX();\n        }\n        if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodewordNearby(imageRow);\n        }\n        if (codeword != null) {\n            return leftToRight ? codeword.getEndX() : codeword.getStartX();\n        }\n        var skippedColumns = 0;\n        while (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            barcodeColumn -= offset;\n            try {\n                for (var _b = (e_5 = void 0, __values(detectionResult.getDetectionResultColumn(barcodeColumn).getCodewords())), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var previousRowCodeword = _c.value /*Codeword*/;\n                    if (previousRowCodeword != null) {\n                        return (leftToRight ? previousRowCodeword.getEndX() : previousRowCodeword.getStartX()) +\n                            offset *\n                                skippedColumns *\n                                (previousRowCodeword.getEndX() - previousRowCodeword.getStartX());\n                    }\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            skippedColumns++;\n        }\n        return leftToRight ? detectionResult.getBoundingBox().getMinX() : detectionResult.getBoundingBox().getMaxX();\n    };\n    PDF417ScanningDecoder.detectCodeword = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth) {\n        startColumn = PDF417ScanningDecoder.adjustCodewordStartColumn(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n        // we usually know fairly exact now how long a codeword is. We should provide minimum and maximum expected length\n        // and try to adjust the read pixels, e.g. remove single pixel errors or try to cut off exceeding pixels.\n        // min and maxCodewordWidth should not be used as they are calculated for the whole barcode an can be inaccurate\n        // for the current position\n        var moduleBitCount = PDF417ScanningDecoder.getModuleBitCount(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n        if (moduleBitCount == null) {\n            return null;\n        }\n        var endColumn;\n        var codewordBitCount = MathUtils.sum(moduleBitCount);\n        if (leftToRight) {\n            endColumn = startColumn + codewordBitCount;\n        }\n        else {\n            for (var i /*int*/ = 0; i < moduleBitCount.length / 2; i++) {\n                var tmpCount = moduleBitCount[i];\n                moduleBitCount[i] = moduleBitCount[moduleBitCount.length - 1 - i];\n                moduleBitCount[moduleBitCount.length - 1 - i] = tmpCount;\n            }\n            endColumn = startColumn;\n            startColumn = endColumn - codewordBitCount;\n        }\n        // TODO implement check for width and correction of black and white bars\n        // use start (and maybe stop pattern) to determine if black bars are wider than white bars. If so, adjust.\n        // should probably done only for codewords with a lot more than 17 bits.\n        // The following fixes 10-1.png, which has wide black bars and small white bars\n        //    for (let i /*int*/ = 0; i < moduleBitCount.length; i++) {\n        //      if (i % 2 === 0) {\n        //        moduleBitCount[i]--;\n        //      } else {\n        //        moduleBitCount[i]++;\n        //      }\n        //    }\n        // We could also use the width of surrounding codewords for more accurate results, but this seems\n        // sufficient for now\n        if (!PDF417ScanningDecoder.checkCodewordSkew(codewordBitCount, minCodewordWidth, maxCodewordWidth)) {\n            // We could try to use the startX and endX position of the codeword in the same column in the previous row,\n            // create the bit count from it and normalize it to 8. This would help with single pixel errors.\n            return null;\n        }\n        var decodedValue = PDF417CodewordDecoder.getDecodedValue(moduleBitCount);\n        var codeword = PDF417Common.getCodeword(decodedValue);\n        if (codeword === -1) {\n            return null;\n        }\n        return new Codeword(startColumn, endColumn, PDF417ScanningDecoder.getCodewordBucketNumber(decodedValue), codeword);\n    };\n    PDF417ScanningDecoder.getModuleBitCount = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow) {\n        var imageColumn = startColumn;\n        var moduleBitCount = new Int32Array(8);\n        var moduleNumber = 0;\n        var increment = leftToRight ? 1 : -1;\n        var previousPixelValue = leftToRight;\n        while ((leftToRight ? imageColumn < maxColumn : imageColumn >= minColumn) &&\n            moduleNumber < moduleBitCount.length) {\n            if (image.get(imageColumn, imageRow) === previousPixelValue) {\n                moduleBitCount[moduleNumber]++;\n                imageColumn += increment;\n            }\n            else {\n                moduleNumber++;\n                previousPixelValue = !previousPixelValue;\n            }\n        }\n        if (moduleNumber === moduleBitCount.length ||\n            ((imageColumn === (leftToRight ? maxColumn : minColumn)) &&\n                moduleNumber === moduleBitCount.length - 1)) {\n            return moduleBitCount;\n        }\n        return null;\n    };\n    PDF417ScanningDecoder.getNumberOfECCodeWords = function (barcodeECLevel) {\n        return 2 << barcodeECLevel;\n    };\n    PDF417ScanningDecoder.adjustCodewordStartColumn = function (image, minColumn, maxColumn, leftToRight, codewordStartColumn, imageRow) {\n        var correctedStartColumn = codewordStartColumn;\n        var increment = leftToRight ? -1 : 1;\n        // there should be no black pixels before the start column. If there are, then we need to start earlier.\n        for (var i /*int*/ = 0; i < 2; i++) {\n            while ((leftToRight ? correctedStartColumn >= minColumn : correctedStartColumn < maxColumn) &&\n                leftToRight === image.get(correctedStartColumn, imageRow)) {\n                if (Math.abs(codewordStartColumn - correctedStartColumn) > PDF417ScanningDecoder.CODEWORD_SKEW_SIZE) {\n                    return codewordStartColumn;\n                }\n                correctedStartColumn += increment;\n            }\n            increment = -increment;\n            leftToRight = !leftToRight;\n        }\n        return correctedStartColumn;\n    };\n    PDF417ScanningDecoder.checkCodewordSkew = function (codewordSize, minCodewordWidth, maxCodewordWidth) {\n        return minCodewordWidth - PDF417ScanningDecoder.CODEWORD_SKEW_SIZE <= codewordSize &&\n            codewordSize <= maxCodewordWidth + PDF417ScanningDecoder.CODEWORD_SKEW_SIZE;\n    };\n    /**\n     * @throws FormatException,\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.decodeCodewords = function (codewords, ecLevel, erasures) {\n        if (codewords.length === 0) {\n            throw FormatException.getFormatInstance();\n        }\n        var numECCodewords = 1 << (ecLevel + 1);\n        var correctedErrorsCount = PDF417ScanningDecoder.correctErrors(codewords, erasures, numECCodewords);\n        PDF417ScanningDecoder.verifyCodewordCount(codewords, numECCodewords);\n        // Decode the codewords\n        var decoderResult = DecodedBitStreamParser.decode(codewords, '' + ecLevel);\n        decoderResult.setErrorsCorrected(correctedErrorsCount);\n        decoderResult.setErasures(erasures.length);\n        return decoderResult;\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place.</p>\n     *\n     * @param codewords   data and error correction codewords\n     * @param erasures positions of any known erasures\n     * @param numECCodewords number of error correction codewords that are available in codewords\n     * @throws ChecksumException if error correction fails\n     */\n    PDF417ScanningDecoder.correctErrors = function (codewords, erasures, numECCodewords) {\n        if (erasures != null &&\n            erasures.length > numECCodewords / 2 + PDF417ScanningDecoder.MAX_ERRORS ||\n            numECCodewords < 0 ||\n            numECCodewords > PDF417ScanningDecoder.MAX_EC_CODEWORDS) {\n            // Too many errors or EC Codewords is corrupted\n            throw ChecksumException.getChecksumInstance();\n        }\n        return PDF417ScanningDecoder.errorCorrection.decode(codewords, numECCodewords, erasures);\n    };\n    /**\n     * Verify that all is OK with the codeword array.\n     * @throws FormatException\n     */\n    PDF417ScanningDecoder.verifyCodewordCount = function (codewords, numECCodewords) {\n        if (codewords.length < 4) {\n            // Codeword array size should be at least 4 allowing for\n            // Count CW, At least one Data CW, Error Correction CW, Error Correction CW\n            throw FormatException.getFormatInstance();\n        }\n        // The first codeword, the Symbol Length Descriptor, shall always encode the total number of data\n        // codewords in the symbol, including the Symbol Length Descriptor itself, data codewords and pad\n        // codewords, but excluding the number of error correction codewords.\n        var numberOfCodewords = codewords[0];\n        if (numberOfCodewords > codewords.length) {\n            throw FormatException.getFormatInstance();\n        }\n        if (numberOfCodewords === 0) {\n            // Reset to the length of the array - 8 (Allow for at least level 3 Error Correction (8 Error Codewords)\n            if (numECCodewords < codewords.length) {\n                codewords[0] = codewords.length - numECCodewords;\n            }\n            else {\n                throw FormatException.getFormatInstance();\n            }\n        }\n    };\n    PDF417ScanningDecoder.getBitCountForCodeword = function (codeword) {\n        var result = new Int32Array(8);\n        var previousValue = 0;\n        var i = result.length - 1;\n        while (true) {\n            if ((codeword & 0x1) !== previousValue) {\n                previousValue = codeword & 0x1;\n                i--;\n                if (i < 0) {\n                    break;\n                }\n            }\n            result[i]++;\n            codeword >>= 1;\n        }\n        return result;\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber = function (codeword) {\n        if (codeword instanceof Int32Array) {\n            return this.getCodewordBucketNumber_Int32Array(codeword);\n        }\n        return this.getCodewordBucketNumber_number(codeword);\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber_number = function (codeword) {\n        return PDF417ScanningDecoder.getCodewordBucketNumber(PDF417ScanningDecoder.getBitCountForCodeword(codeword));\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber_Int32Array = function (moduleBitCount) {\n        return (moduleBitCount[0] - moduleBitCount[2] + moduleBitCount[4] - moduleBitCount[6] + 9) % 9;\n    };\n    PDF417ScanningDecoder.toString = function (barcodeMatrix) {\n        var formatter = new Formatter();\n        // try (let formatter = new Formatter()) {\n        for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n            formatter.format('Row %2d: ', row);\n            for (var column /*int*/ = 0; column < barcodeMatrix[row].length; column++) {\n                var barcodeValue = barcodeMatrix[row][column];\n                if (barcodeValue.getValue().length === 0) {\n                    formatter.format('        ', null);\n                }\n                else {\n                    formatter.format('%4d(%2d)', barcodeValue.getValue()[0], barcodeValue.getConfidence(barcodeValue.getValue()[0]));\n                }\n            }\n            formatter.format('%n');\n        }\n        return formatter.toString();\n        // }\n    };\n    /*final*/ PDF417ScanningDecoder.CODEWORD_SKEW_SIZE = 2;\n    /*final*/ PDF417ScanningDecoder.MAX_ERRORS = 3;\n    /*final*/ PDF417ScanningDecoder.MAX_EC_CODEWORDS = 512;\n    /*final*/ PDF417ScanningDecoder.errorCorrection = new ErrorCorrection();\n    return PDF417ScanningDecoder;\n}());\nexport default PDF417ScanningDecoder;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAcA;;;AAYA,2CAA2C;AAC3C,6CAA6C;AAC7C;AACA,2CAA2C;AAC3C;AACA,6CAA6C;AAC7C;AACA,qDAAqD;AACrD;AACA,+CAA+C;AAC/C;AACA,6DAA6D;AAC7D;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AAlCA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;;;;;;;AAyBA,8BAA8B;AAC9B,+BAA+B;AAC/B,8BAA8B;AAC9B,yBAAyB;AACzB;;CAEC,GACD,IAAI,wBAAuC;IACvC,SAAS,yBACT;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,sBAAsB,MAAM,GAAG,SAAU,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;QAC9I,IAAI,cAAc,IAAI,wLAAA,CAAA,UAAW,CAAC,OAAO,cAAc,iBAAiB,eAAe;QACvF,IAAI,yBAAyB;QAC7B,IAAI,0BAA0B;QAC9B,IAAI;QACJ,IAAK,IAAI,UAAU,SAAS,MAAK,OAAO,YAAY,MAAO;YACvD,IAAI,gBAAgB,MAAM;gBACtB,yBAAyB,sBAAsB,qBAAqB,CAAC,OAAO,aAAa,cAAc,MAAM,kBAAkB;YACnI;YACA,IAAI,iBAAiB,MAAM;gBACvB,0BAA0B,sBAAsB,qBAAqB,CAAC,OAAO,aAAa,eAAe,OAAO,kBAAkB;YACtI;YACA,kBAAkB,sBAAsB,KAAK,CAAC,wBAAwB;YACtE,IAAI,mBAAmB,MAAM;gBACzB,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,IAAI,YAAY,gBAAgB,cAAc;YAC9C,IAAI,aAAa,aAAa,QAC1B,CAAC,UAAU,OAAO,KAAK,YAAY,OAAO,MAAM,UAAU,OAAO,KAAK,YAAY,OAAO,EAAE,GAAG;gBAC9F,cAAc;YAClB,OACK;gBACD;YACJ;QACJ;QACA,gBAAgB,cAAc,CAAC;QAC/B,IAAI,mBAAmB,gBAAgB,qBAAqB,KAAK;QACjE,gBAAgB,wBAAwB,CAAC,GAAG;QAC5C,gBAAgB,wBAAwB,CAAC,kBAAkB;QAC3D,IAAI,cAAc,0BAA0B;QAC5C,IAAK,IAAI,mBAAmB,KAAK,MAAK,GAAG,sBAAsB,kBAAkB,qBAAsB;YACnG,IAAI,gBAAgB,cAAc,qBAAqB,mBAAmB;YAC1E,IAAI,gBAAgB,wBAAwB,CAAC,mBAAmB,QAAQ,GAAG,WAAW;gBAElF;YACJ;YACA,IAAI,wBAAwB,KAAK;YACjC,IAAI,kBAAkB,KAAK,kBAAkB,kBAAkB;gBAC3D,wBAAwB,IAAI,8MAAA,CAAA,UAAiC,CAAC,aAAa,kBAAkB;YACjG,OACK;gBACD,wBAAwB,IAAI,kMAAA,CAAA,UAAqB,CAAC;YACtD;YACA,gBAAgB,wBAAwB,CAAC,eAAe;YACxD,IAAI,cAAc,CAAC;YACnB,IAAI,sBAAsB;YAC1B,0GAA0G;YAC1G,IAAK,IAAI,SAAS,KAAK,MAAK,YAAY,OAAO,IAAI,YAAY,YAAY,OAAO,IAAI,WAAY;gBAC9F,cAAc,sBAAsB,cAAc,CAAC,iBAAiB,eAAe,UAAU;gBAC7F,IAAI,cAAc,KAAK,cAAc,YAAY,OAAO,IAAI;oBACxD,IAAI,wBAAwB,CAAC,GAAG;wBAC5B;oBACJ;oBACA,cAAc;gBAClB;gBACA,IAAI,WAAW,sBAAsB,cAAc,CAAC,OAAO,YAAY,OAAO,IAAI,YAAY,OAAO,IAAI,aAAa,aAAa,UAAU,kBAAkB;gBAC/J,IAAI,YAAY,MAAM;oBAClB,sBAAsB,WAAW,CAAC,UAAU;oBAC5C,sBAAsB;oBACtB,mBAAmB,KAAK,GAAG,CAAC,kBAAkB,SAAS,QAAQ;oBAC/D,mBAAmB,KAAK,GAAG,CAAC,kBAAkB,SAAS,QAAQ;gBACnE;YACJ;QACJ;QACA,OAAO,sBAAsB,mBAAmB,CAAC;IACrD;IACA;;;;;;KAMC,GACD,sBAAsB,KAAK,GAAG,SAAU,sBAAsB,EAAE,uBAAuB;QACnF,IAAI,0BAA0B,QAAQ,2BAA2B,MAAM;YACnE,OAAO;QACX;QACA,IAAI,kBAAkB,sBAAsB,kBAAkB,CAAC,wBAAwB;QACvF,IAAI,mBAAmB,MAAM;YACzB,OAAO;QACX;QACA,IAAI,cAAc,wLAAA,CAAA,UAAW,CAAC,KAAK,CAAC,sBAAsB,iBAAiB,CAAC,yBAAyB,sBAAsB,iBAAiB,CAAC;QAC7I,OAAO,IAAI,4LAAA,CAAA,UAAe,CAAC,iBAAiB;IAChD;IACA;;;;;KAKC,GACD,sBAAsB,iBAAiB,GAAG,SAAU,kBAAkB;QAClE,IAAI,KAAK;QACT,IAAI,sBAAsB,MAAM;YAC5B,OAAO;QACX;QACA,IAAI,aAAa,mBAAmB,aAAa;QACjD,IAAI,cAAc,MAAM;YACpB,OAAO;QACX;QACA,IAAI,eAAe,sBAAsB,MAAM,CAAC;QAChD,IAAI,mBAAmB;QACvB,IAAI;YACA,IAAK,IAAI,eAAe,SAAS,aAAa,iBAAiB,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,EAAE,iBAAiB,aAAa,IAAI,GAAI;gBAC5I,IAAI,YAAY,eAAe,KAAK,CAAC,KAAK;gBAC1C,oBAAoB,eAAe;gBACnC,IAAI,YAAY,GAAG;oBACf;gBACJ;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,kBAAkB,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,aAAa,MAAM,GAAG,GAAG,IAAI,CAAC;YACtF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,YAAY,mBAAmB,YAAY;QAC/C,IAAK,IAAI,IAAI,KAAK,MAAK,GAAG,mBAAmB,KAAK,SAAS,CAAC,IAAI,IAAI,MAAM,MAAO;YAC7E;QACJ;QACA,IAAI,iBAAiB;QACrB,IAAK,IAAI,IAAI,KAAK,MAAK,WAAW,MAAM,GAAG,GAAG,OAAO,GAAG,MAAO;YAC3D,kBAAkB,eAAe,UAAU,CAAC,IAAI;YAChD,IAAI,UAAU,CAAC,IAAI,GAAG,GAAG;gBACrB;YACJ;QACJ;QACA,IAAK,IAAI,IAAI,KAAK,MAAK,UAAU,MAAM,GAAG,GAAG,iBAAiB,KAAK,SAAS,CAAC,IAAI,IAAI,MAAM,MAAO;YAC9F;QACJ;QACA,OAAO,mBAAmB,cAAc,GAAG,cAAc,CAAC,kBAAkB,gBAAgB,mBAAmB,MAAM;IACzH;IACA,sBAAsB,MAAM,GAAG,SAAU,MAAM;QAC3C,IAAI,KAAK;QACT,IAAI,WAAW,CAAC;QAChB,IAAI;YACA,IAAK,IAAI,WAAW,SAAS,SAAS,aAAa,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,aAAa,SAAS,IAAI,GAAI;gBAChH,IAAI,QAAQ,WAAW,KAAK,CAAC,KAAK;gBAClC,WAAW,KAAK,GAAG,CAAC,UAAU;YAClC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;YAC1E,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,sBAAsB,kBAAkB,GAAG,SAAU,sBAAsB,EAAE,uBAAuB;QAChG,IAAI;QACJ,IAAI,0BAA0B,QAC1B,CAAC,sBAAsB,uBAAuB,kBAAkB,EAAE,KAAK,MAAM;YAC7E,OAAO,2BAA2B,OAAO,OAAO,wBAAwB,kBAAkB;QAC9F;QACA,IAAI;QACJ,IAAI,2BAA2B,QAC3B,CAAC,uBAAuB,wBAAwB,kBAAkB,EAAE,KAAK,MAAM;YAC/E,OAAO;QACX;QACA,IAAI,oBAAoB,cAAc,OAAO,qBAAqB,cAAc,MAC5E,oBAAoB,uBAAuB,OAAO,qBAAqB,uBAAuB,MAC9F,oBAAoB,WAAW,OAAO,qBAAqB,WAAW,IAAI;YAC1E,OAAO;QACX;QACA,OAAO;IACX;IACA,sBAAsB,qBAAqB,GAAG,SAAU,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB;QACnI,IAAI,qBAAqB,IAAI,8MAAA,CAAA,UAAiC,CAAC,aAAa;QAC5E,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAK;YAChC,IAAI,YAAY,MAAM,IAAI,IAAI,CAAC;YAC/B,IAAI,cAAc,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI;YACvD,IAAK,IAAI,SAAS,KAAK,MAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,MAAM,YAAY,YAAY,OAAO,MAClG,YAAY,YAAY,OAAO,IAAI,YAAY,UAAW;gBAC1D,IAAI,WAAW,sBAAsB,cAAc,CAAC,OAAO,GAAG,MAAM,QAAQ,IAAI,aAAa,aAAa,UAAU,kBAAkB;gBACtI,IAAI,YAAY,MAAM;oBAClB,mBAAmB,WAAW,CAAC,UAAU;oBACzC,IAAI,aAAa;wBACb,cAAc,SAAS,SAAS;oBACpC,OACK;wBACD,cAAc,SAAS,OAAO;oBAClC;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,sBAAsB,mBAAmB,GAAG,SAAU,eAAe,EAAE,aAAa;QAChF,IAAI,kBAAkB,aAAa,CAAC,EAAE,CAAC,EAAE;QACzC,IAAI,oBAAoB,gBAAgB,QAAQ;QAChD,IAAI,8BAA8B,gBAAgB,qBAAqB,KACnE,gBAAgB,kBAAkB,KAClC,sBAAsB,sBAAsB,CAAC,gBAAgB,iBAAiB;QAClF,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAChC,IAAI,8BAA8B,KAAK,8BAA8B,8KAAA,CAAA,UAAY,CAAC,wBAAwB,EAAE;gBACxG,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,gBAAgB,QAAQ,CAAC;QAC7B,OACK,IAAI,iBAAiB,CAAC,EAAE,KAAK,6BAA6B;YAC3D,sFAAsF;YACtF,gBAAgB,QAAQ,CAAC;QAC7B;IACJ;IACA;;;;;;;KAOC,GACD,sBAAsB,mBAAmB,GAAG,SAAU,eAAe;QACjE,IAAI,gBAAgB,sBAAsB,mBAAmB,CAAC;QAC9D,sBAAsB,mBAAmB,CAAC,iBAAiB;QAC3D,IAAI,SAAS,qBAAqB,MAAK,IAAI;QAC3C,IAAI,YAAY,IAAI,WAAW,gBAAgB,kBAAkB,KAAK,gBAAgB,qBAAqB;QAC3G,IAAI,2BAA2B,aAAa,GAAG,EAAE;QACjD,IAAI,uBAAuB,qBAAqB,GAAG,IAAI;QACvD,IAAK,IAAI,IAAI,KAAK,MAAK,GAAG,MAAM,gBAAgB,kBAAkB,IAAI,MAAO;YACzE,IAAK,IAAI,OAAO,KAAK,MAAK,GAAG,SAAS,gBAAgB,qBAAqB,IAAI,SAAU;gBACrF,IAAI,SAAS,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ;gBACpD,IAAI,gBAAgB,MAAM,gBAAgB,qBAAqB,KAAK;gBACpE,IAAI,OAAO,MAAM,KAAK,GAAG;oBACrB,SAAS,IAAI,CAAC;gBAClB,OACK,IAAI,OAAO,MAAM,KAAK,GAAG;oBAC1B,SAAS,CAAC,cAAc,GAAG,MAAM,CAAC,EAAE;gBACxC,OACK;oBACD,qBAAqB,IAAI,CAAC;oBAC1B,yBAAyB,IAAI,CAAC;gBAClC;YACJ;QACJ;QACA,IAAI,uBAAuB,IAAI,MAAM,yBAAyB,MAAM;QACpE,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,qBAAqB,MAAM,EAAE,IAAK;YAC1D,oBAAoB,CAAC,EAAE,GAAG,wBAAwB,CAAC,EAAE;QACzD;QACA,OAAO,sBAAsB,sCAAsC,CAAC,gBAAgB,iBAAiB,IAAI,WAAW,8KAAA,CAAA,UAAY,CAAC,UAAU,CAAC,WAAW,8KAAA,CAAA,UAAY,CAAC,UAAU,CAAC,uBAAuB;IAC1M;IACA;;;;;;;;;;;;;;;KAeC,GACD,sBAAsB,sCAAsC,GAAG,SAAU,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,oBAAoB;QAC7I,IAAI,sBAAsB,IAAI,WAAW,iBAAiB,MAAM;QAChE,IAAI,QAAQ;QACZ,MAAO,UAAU,EAAG;YAChB,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;gBACzD,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACpF;YACA,IAAI;gBACA,OAAO,sBAAsB,eAAe,CAAC,WAAW,SAAS;YACrE,EACA,OAAO,KAAK;gBACR,IAAI,UAAU,eAAe,yKAAA,CAAA,UAAiB;gBAC9C,IAAI,CAAC,SAAS;oBACV,MAAM;gBACV;YACJ;YACA,IAAI,oBAAoB,MAAM,KAAK,GAAG;gBAClC,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;gBACzD,IAAI,mBAAmB,CAAC,EAAE,GAAG,oBAAoB,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;oBAC7D,mBAAmB,CAAC,EAAE;oBACtB;gBACJ,OACK;oBACD,mBAAmB,CAAC,EAAE,GAAG;oBACzB,IAAI,MAAM,oBAAoB,MAAM,GAAG,GAAG;wBACtC,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;oBAC/C;gBACJ;YACJ;QACJ;QACA,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;IAC/C;IACA,sBAAsB,mBAAmB,GAAG,SAAU,eAAe;QACjE,IAAI,KAAK,IAAI,KAAK;QAClB,wCAAwC;QACxC,uGAAuG;QACvG,IAAI,gBAAgB,MAAM,IAAI,CAAC;YAAE,QAAQ,gBAAgB,kBAAkB;QAAG,GAAG;YAAc,OAAO,IAAI,MAAM,gBAAgB,qBAAqB,KAAK;QAAI;QAC9J,IAAK,IAAI,IAAI,KAAK,MAAK,GAAG,MAAM,cAAc,MAAM,EAAE,MAAO;YACzD,IAAK,IAAI,SAAS,KAAK,MAAK,GAAG,WAAW,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,WAAY;gBAC7E,aAAa,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,yLAAA,CAAA,UAAY;YACnD;QACJ;QACA,IAAI,SAAS;QACb,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,gBAAgB,yBAAyB,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC3G,IAAI,wBAAwB,GAAG,KAAK,CAAC,uBAAuB;gBAC5D,IAAI,yBAAyB,MAAM;oBAC/B,IAAI;wBACA,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,sBAAsB,YAAY,GAAG,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;4BACpH,IAAI,WAAW,GAAG,KAAK,CAAC,UAAU;4BAClC,IAAI,YAAY,MAAM;gCAClB,IAAI,YAAY,SAAS,YAAY;gCACrC,IAAI,aAAa,GAAG;oCAChB,IAAI,aAAa,cAAc,MAAM,EAAE;wCAEnC;oCACJ;oCACA,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,QAAQ;gCAC/D;4BACJ;wBACJ;oBACJ,EACA,OAAO,OAAO;wBAAE,MAAM;4BAAE,OAAO;wBAAM;oBAAG,SAChC;wBACJ,IAAI;4BACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;wBACpD,SACQ;4BAAE,IAAI,KAAK,MAAM,IAAI,KAAK;wBAAE;oBACxC;gBACJ;gBACA;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;IACA,sBAAsB,oBAAoB,GAAG,SAAU,eAAe,EAAE,aAAa;QACjF,OAAO,iBAAiB,KAAK,iBAAiB,gBAAgB,qBAAqB,KAAK;IAC5F;IACA,sBAAsB,cAAc,GAAG,SAAU,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW;QAClG,IAAI,KAAK;QACT,IAAI,SAAS,cAAc,IAAI,CAAC;QAChC,IAAI,WAAW;QACf,IAAI,sBAAsB,oBAAoB,CAAC,iBAAiB,gBAAgB,SAAS;YACrF,WAAW,gBAAgB,wBAAwB,CAAC,gBAAgB,QAAQ,WAAW,CAAC;QAC5F;QACA,IAAI,YAAY,MAAM;YAClB,OAAO,cAAc,SAAS,OAAO,KAAK,SAAS,SAAS;QAChE;QACA,WAAW,gBAAgB,wBAAwB,CAAC,eAAe,iBAAiB,CAAC;QACrF,IAAI,YAAY,MAAM;YAClB,OAAO,cAAc,SAAS,SAAS,KAAK,SAAS,OAAO;QAChE;QACA,IAAI,sBAAsB,oBAAoB,CAAC,iBAAiB,gBAAgB,SAAS;YACrF,WAAW,gBAAgB,wBAAwB,CAAC,gBAAgB,QAAQ,iBAAiB,CAAC;QAClG;QACA,IAAI,YAAY,MAAM;YAClB,OAAO,cAAc,SAAS,OAAO,KAAK,SAAS,SAAS;QAChE;QACA,IAAI,iBAAiB;QACrB,MAAO,sBAAsB,oBAAoB,CAAC,iBAAiB,gBAAgB,QAAS;YACxF,iBAAiB;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,SAAS,gBAAgB,wBAAwB,CAAC,eAAe,YAAY,GAAG,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;oBACtJ,IAAI,sBAAsB,GAAG,KAAK,CAAC,UAAU;oBAC7C,IAAI,uBAAuB,MAAM;wBAC7B,OAAO,CAAC,cAAc,oBAAoB,OAAO,KAAK,oBAAoB,SAAS,EAAE,IACjF,SACI,iBACA,CAAC,oBAAoB,OAAO,KAAK,oBAAoB,SAAS,EAAE;oBAC5E;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;gBACpD,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA;QACJ;QACA,OAAO,cAAc,gBAAgB,cAAc,GAAG,OAAO,KAAK,gBAAgB,cAAc,GAAG,OAAO;IAC9G;IACA,sBAAsB,cAAc,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAE,gBAAgB;QAChJ,cAAc,sBAAsB,yBAAyB,CAAC,OAAO,WAAW,WAAW,aAAa,aAAa;QACrH,iHAAiH;QACjH,yGAAyG;QACzG,gHAAgH;QAChH,2BAA2B;QAC3B,IAAI,iBAAiB,sBAAsB,iBAAiB,CAAC,OAAO,WAAW,WAAW,aAAa,aAAa;QACpH,IAAI,kBAAkB,MAAM;YACxB,OAAO;QACX;QACA,IAAI;QACJ,IAAI,mBAAmB,uLAAA,CAAA,UAAS,CAAC,GAAG,CAAC;QACrC,IAAI,aAAa;YACb,YAAY,cAAc;QAC9B,OACK;YACD,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,eAAe,MAAM,GAAG,GAAG,IAAK;gBACxD,IAAI,WAAW,cAAc,CAAC,EAAE;gBAChC,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,eAAe,MAAM,GAAG,IAAI,EAAE;gBACjE,cAAc,CAAC,eAAe,MAAM,GAAG,IAAI,EAAE,GAAG;YACpD;YACA,YAAY;YACZ,cAAc,YAAY;QAC9B;QACA,wEAAwE;QACxE,0GAA0G;QAC1G,wEAAwE;QACxE,+EAA+E;QAC/E,+DAA+D;QAC/D,0BAA0B;QAC1B,8BAA8B;QAC9B,gBAAgB;QAChB,8BAA8B;QAC9B,SAAS;QACT,OAAO;QACP,iGAAiG;QACjG,qBAAqB;QACrB,IAAI,CAAC,sBAAsB,iBAAiB,CAAC,kBAAkB,kBAAkB,mBAAmB;YAChG,2GAA2G;YAC3G,gGAAgG;YAChG,OAAO;QACX;QACA,IAAI,eAAe,kMAAA,CAAA,UAAqB,CAAC,eAAe,CAAC;QACzD,IAAI,WAAW,8KAAA,CAAA,UAAY,CAAC,WAAW,CAAC;QACxC,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO;QACX;QACA,OAAO,IAAI,qLAAA,CAAA,UAAQ,CAAC,aAAa,WAAW,sBAAsB,uBAAuB,CAAC,eAAe;IAC7G;IACA,sBAAsB,iBAAiB,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ;QAC/G,IAAI,cAAc;QAClB,IAAI,iBAAiB,IAAI,WAAW;QACpC,IAAI,eAAe;QACnB,IAAI,YAAY,cAAc,IAAI,CAAC;QACnC,IAAI,qBAAqB;QACzB,MAAO,CAAC,cAAc,cAAc,YAAY,eAAe,SAAS,KACpE,eAAe,eAAe,MAAM,CAAE;YACtC,IAAI,MAAM,GAAG,CAAC,aAAa,cAAc,oBAAoB;gBACzD,cAAc,CAAC,aAAa;gBAC5B,eAAe;YACnB,OACK;gBACD;gBACA,qBAAqB,CAAC;YAC1B;QACJ;QACA,IAAI,iBAAiB,eAAe,MAAM,IACrC,AAAC,gBAAgB,CAAC,cAAc,YAAY,SAAS,KAClD,iBAAiB,eAAe,MAAM,GAAG,GAAI;YACjD,OAAO;QACX;QACA,OAAO;IACX;IACA,sBAAsB,sBAAsB,GAAG,SAAU,cAAc;QACnE,OAAO,KAAK;IAChB;IACA,sBAAsB,yBAAyB,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ;QAC/H,IAAI,uBAAuB;QAC3B,IAAI,YAAY,cAAc,CAAC,IAAI;QACnC,wGAAwG;QACxG,IAAK,IAAI,EAAE,KAAK,MAAK,GAAG,IAAI,GAAG,IAAK;YAChC,MAAO,CAAC,cAAc,wBAAwB,YAAY,uBAAuB,SAAS,KACtF,gBAAgB,MAAM,GAAG,CAAC,sBAAsB,UAAW;gBAC3D,IAAI,KAAK,GAAG,CAAC,sBAAsB,wBAAwB,sBAAsB,kBAAkB,EAAE;oBACjG,OAAO;gBACX;gBACA,wBAAwB;YAC5B;YACA,YAAY,CAAC;YACb,cAAc,CAAC;QACnB;QACA,OAAO;IACX;IACA,sBAAsB,iBAAiB,GAAG,SAAU,YAAY,EAAE,gBAAgB,EAAE,gBAAgB;QAChG,OAAO,mBAAmB,sBAAsB,kBAAkB,IAAI,gBAClE,gBAAgB,mBAAmB,sBAAsB,kBAAkB;IACnF;IACA;;;KAGC,GACD,sBAAsB,eAAe,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,QAAQ;QAC1E,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;QAC3C;QACA,IAAI,iBAAiB,KAAM,UAAU;QACrC,IAAI,uBAAuB,sBAAsB,aAAa,CAAC,WAAW,UAAU;QACpF,sBAAsB,mBAAmB,CAAC,WAAW;QACrD,uBAAuB;QACvB,IAAI,gBAAgB,mMAAA,CAAA,UAAsB,CAAC,MAAM,CAAC,WAAW,KAAK;QAClE,cAAc,kBAAkB,CAAC;QACjC,cAAc,WAAW,CAAC,SAAS,MAAM;QACzC,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,sBAAsB,aAAa,GAAG,SAAU,SAAS,EAAE,QAAQ,EAAE,cAAc;QAC/E,IAAI,YAAY,QACZ,SAAS,MAAM,GAAG,iBAAiB,IAAI,sBAAsB,UAAU,IACvE,iBAAiB,KACjB,iBAAiB,sBAAsB,gBAAgB,EAAE;YACzD,+CAA+C;YAC/C,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;QAC/C;QACA,OAAO,sBAAsB,eAAe,CAAC,MAAM,CAAC,WAAW,gBAAgB;IACnF;IACA;;;KAGC,GACD,sBAAsB,mBAAmB,GAAG,SAAU,SAAS,EAAE,cAAc;QAC3E,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,wDAAwD;YACxD,2EAA2E;YAC3E,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;QAC3C;QACA,iGAAiG;QACjG,iGAAiG;QACjG,qEAAqE;QACrE,IAAI,oBAAoB,SAAS,CAAC,EAAE;QACpC,IAAI,oBAAoB,UAAU,MAAM,EAAE;YACtC,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;QAC3C;QACA,IAAI,sBAAsB,GAAG;YACzB,wGAAwG;YACxG,IAAI,iBAAiB,UAAU,MAAM,EAAE;gBACnC,SAAS,CAAC,EAAE,GAAG,UAAU,MAAM,GAAG;YACtC,OACK;gBACD,MAAM,uKAAA,CAAA,UAAe,CAAC,iBAAiB;YAC3C;QACJ;IACJ;IACA,sBAAsB,sBAAsB,GAAG,SAAU,QAAQ;QAC7D,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAI,gBAAgB;QACpB,IAAI,IAAI,OAAO,MAAM,GAAG;QACxB,MAAO,KAAM;YACT,IAAI,CAAC,WAAW,GAAG,MAAM,eAAe;gBACpC,gBAAgB,WAAW;gBAC3B;gBACA,IAAI,IAAI,GAAG;oBACP;gBACJ;YACJ;YACA,MAAM,CAAC,EAAE;YACT,aAAa;QACjB;QACA,OAAO;IACX;IACA,sBAAsB,uBAAuB,GAAG,SAAU,QAAQ;QAC9D,IAAI,oBAAoB,YAAY;YAChC,OAAO,IAAI,CAAC,kCAAkC,CAAC;QACnD;QACA,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C;IACA,sBAAsB,8BAA8B,GAAG,SAAU,QAAQ;QACrE,OAAO,sBAAsB,uBAAuB,CAAC,sBAAsB,sBAAsB,CAAC;IACtG;IACA,sBAAsB,kCAAkC,GAAG,SAAU,cAAc;QAC/E,OAAO,CAAC,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,GAAG,CAAC,IAAI;IACjG;IACA,sBAAsB,QAAQ,GAAG,SAAU,aAAa;QACpD,IAAI,YAAY,IAAI,yKAAA,CAAA,UAAS;QAC7B,0CAA0C;QAC1C,IAAK,IAAI,IAAI,KAAK,MAAK,GAAG,MAAM,cAAc,MAAM,EAAE,MAAO;YACzD,UAAU,MAAM,CAAC,aAAa;YAC9B,IAAK,IAAI,OAAO,KAAK,MAAK,GAAG,SAAS,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,SAAU;gBACvE,IAAI,eAAe,aAAa,CAAC,IAAI,CAAC,OAAO;gBAC7C,IAAI,aAAa,QAAQ,GAAG,MAAM,KAAK,GAAG;oBACtC,UAAU,MAAM,CAAC,YAAY;gBACjC,OACK;oBACD,UAAU,MAAM,CAAC,YAAY,aAAa,QAAQ,EAAE,CAAC,EAAE,EAAE,aAAa,aAAa,CAAC,aAAa,QAAQ,EAAE,CAAC,EAAE;gBAClH;YACJ;YACA,UAAU,MAAM,CAAC;QACrB;QACA,OAAO,UAAU,QAAQ;IACzB,IAAI;IACR;IACA,OAAO,GAAG,sBAAsB,kBAAkB,GAAG;IACrD,OAAO,GAAG,sBAAsB,UAAU,GAAG;IAC7C,OAAO,GAAG,sBAAsB,gBAAgB,GAAG;IACnD,OAAO,GAAG,sBAAsB,eAAe,GAAG,IAAI,kMAAA,CAAA,UAAe;IACrE,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40zxing/library/esm/core/pdf417/PDF417Reader.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417;\n// import com.google.zxing.BarcodeFormat;\nimport BarcodeFormat from '../BarcodeFormat';\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../ChecksumException';\n// import com.google.zxing.FormatException;\nimport FormatException from '../FormatException';\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../NotFoundException';\n// import com.google.zxing.Result;\nimport Result from '../Result';\n// import com.google.zxing.common.DecoderResult;\n// import com.google.zxing.multi.MultipleBarcodeReader;\n// import com.google.zxing.pdf417.decoder.PDF417ScanningDecoder;\n// import com.google.zxing.pdf417.detector.Detector;\n// import com.google.zxing.pdf417.detector.PDF417DetectorResult;\nimport PDF417Common from './PDF417Common';\nimport Integer from '../util/Integer';\nimport ResultMetadataType from '../ResultMetadataType';\nimport Detector from './detector/Detector';\nimport PDF417ScanningDecoder from './decoder/PDF417ScanningDecoder';\n// import java.util.ArrayList;\n// import java.util.List;\n// import java.util.Map;\n/**\n * This implementation can detect and decode PDF417 codes in an image.\n *\n * <AUTHOR> Grau\n */\nvar PDF417Reader = /** @class */ (function () {\n    function PDF417Reader() {\n    }\n    // private static /*final Result[]*/ EMPTY_RESULT_ARRAY: Result[] = new Result([0]);\n    /**\n     * Locates and decodes a PDF417 code in an image.\n     *\n     * @return a String representing the content encoded by the PDF417 code\n     * @throws NotFoundException if a PDF417 code cannot be found,\n     * @throws FormatException if a PDF417 cannot be decoded\n     * @throws ChecksumException\n     */\n    // @Override\n    PDF417Reader.prototype.decode = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        var result = PDF417Reader.decode(image, hints, false);\n        if (result == null || result.length === 0 || result[0] == null) {\n            throw NotFoundException.getNotFoundInstance();\n        }\n        return result[0];\n    };\n    /**\n     *\n     * @param BinaryBitmap\n     * @param image\n     * @throws NotFoundException\n     */\n    //   @Override\n    PDF417Reader.prototype.decodeMultiple = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        try {\n            return PDF417Reader.decode(image, hints, true);\n        }\n        catch (ignored) {\n            if (ignored instanceof FormatException || ignored instanceof ChecksumException) {\n                throw NotFoundException.getNotFoundInstance();\n            }\n            throw ignored;\n        }\n    };\n    /**\n     *\n     * @param image\n     * @param hints\n     * @param multiple\n     *\n     * @throws NotFoundException\n     * @throws FormatExceptionß\n     * @throws ChecksumException\n     */\n    PDF417Reader.decode = function (image, hints, multiple) {\n        var e_1, _a;\n        var results = new Array();\n        var detectorResult = Detector.detectMultiple(image, hints, multiple);\n        try {\n            for (var _b = __values(detectorResult.getPoints()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var points = _c.value;\n                var decoderResult = PDF417ScanningDecoder.decode(detectorResult.getBits(), points[4], points[5], points[6], points[7], PDF417Reader.getMinCodewordWidth(points), PDF417Reader.getMaxCodewordWidth(points));\n                var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), undefined, points, BarcodeFormat.PDF_417);\n                result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, decoderResult.getECLevel());\n                var pdf417ResultMetadata = decoderResult.getOther();\n                if (pdf417ResultMetadata != null) {\n                    result.putMetadata(ResultMetadataType.PDF417_EXTRA_METADATA, pdf417ResultMetadata);\n                }\n                results.push(result);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return results.map(function (x) { return x; });\n    };\n    PDF417Reader.getMaxWidth = function (p1, p2) {\n        if (p1 == null || p2 == null) {\n            return 0;\n        }\n        return Math.trunc(Math.abs(p1.getX() - p2.getX()));\n    };\n    PDF417Reader.getMinWidth = function (p1, p2) {\n        if (p1 == null || p2 == null) {\n            return Integer.MAX_VALUE;\n        }\n        return Math.trunc(Math.abs(p1.getX() - p2.getX()));\n    };\n    PDF417Reader.getMaxCodewordWidth = function (p) {\n        return Math.floor(Math.max(Math.max(PDF417Reader.getMaxWidth(p[0], p[4]), PDF417Reader.getMaxWidth(p[6], p[2]) * PDF417Common.MODULES_IN_CODEWORD /\n            PDF417Common.MODULES_IN_STOP_PATTERN), Math.max(PDF417Reader.getMaxWidth(p[1], p[5]), PDF417Reader.getMaxWidth(p[7], p[3]) * PDF417Common.MODULES_IN_CODEWORD /\n            PDF417Common.MODULES_IN_STOP_PATTERN)));\n    };\n    PDF417Reader.getMinCodewordWidth = function (p) {\n        return Math.floor(Math.min(Math.min(PDF417Reader.getMinWidth(p[0], p[4]), PDF417Reader.getMinWidth(p[6], p[2]) * PDF417Common.MODULES_IN_CODEWORD /\n            PDF417Common.MODULES_IN_STOP_PATTERN), Math.min(PDF417Reader.getMinWidth(p[1], p[5]), PDF417Reader.getMinWidth(p[7], p[3]) * PDF417Common.MODULES_IN_CODEWORD /\n            PDF417Common.MODULES_IN_STOP_PATTERN)));\n    };\n    // @Override\n    PDF417Reader.prototype.reset = function () {\n        // nothing needs to be reset\n    };\n    return PDF417Reader;\n}());\nexport default PDF417Reader;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;AAYD,mCAAmC;AACnC,yCAAyC;AACzC;AACA,6CAA6C;AAC7C;AACA,2CAA2C;AAC3C;AACA,6CAA6C;AAC7C;AACA,kCAAkC;AAClC;AACA,gDAAgD;AAChD,uDAAuD;AACvD,gEAAgE;AAChE,oDAAoD;AACpD,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AA/BA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;;;AAsBA,8BAA8B;AAC9B,yBAAyB;AACzB,wBAAwB;AACxB;;;;CAIC,GACD,IAAI,eAA8B;IAC9B,SAAS,gBACT;IACA,oFAAoF;IACpF;;;;;;;KAOC,GACD,YAAY;IACZ,aAAa,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,SAAS,aAAa,MAAM,CAAC,OAAO,OAAO;QAC/C,IAAI,UAAU,QAAQ,OAAO,MAAM,KAAK,KAAK,MAAM,CAAC,EAAE,IAAI,MAAM;YAC5D,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;QAC/C;QACA,OAAO,MAAM,CAAC,EAAE;IACpB;IACA;;;;;KAKC,GACD,cAAc;IACd,aAAa,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1D,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI;YACA,OAAO,aAAa,MAAM,CAAC,OAAO,OAAO;QAC7C,EACA,OAAO,SAAS;YACZ,IAAI,mBAAmB,uKAAA,CAAA,UAAe,IAAI,mBAAmB,yKAAA,CAAA,UAAiB,EAAE;gBAC5E,MAAM,yKAAA,CAAA,UAAiB,CAAC,mBAAmB;YAC/C;YACA,MAAM;QACV;IACJ;IACA;;;;;;;;;KASC,GACD,aAAa,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,QAAQ;QAClD,IAAI,KAAK;QACT,IAAI,UAAU,IAAI;QAClB,IAAI,iBAAiB,sLAAA,CAAA,UAAQ,CAAC,cAAc,CAAC,OAAO,OAAO;QAC3D,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,eAAe,SAAS,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC1F,IAAI,SAAS,GAAG,KAAK;gBACrB,IAAI,gBAAgB,kMAAA,CAAA,UAAqB,CAAC,MAAM,CAAC,eAAe,OAAO,IAAI,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,aAAa,mBAAmB,CAAC,SAAS,aAAa,mBAAmB,CAAC;gBAClM,IAAI,SAAS,IAAI,8JAAA,CAAA,UAAM,CAAC,cAAc,OAAO,IAAI,cAAc,WAAW,IAAI,WAAW,QAAQ,qKAAA,CAAA,UAAa,CAAC,OAAO;gBACtH,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,sBAAsB,EAAE,cAAc,UAAU;gBACtF,IAAI,uBAAuB,cAAc,QAAQ;gBACjD,IAAI,wBAAwB,MAAM;oBAC9B,OAAO,WAAW,CAAC,0KAAA,CAAA,UAAkB,CAAC,qBAAqB,EAAE;gBACjE;gBACA,QAAQ,IAAI,CAAC;YACjB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,QAAQ,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO;QAAG;IAChD;IACA,aAAa,WAAW,GAAG,SAAU,EAAE,EAAE,EAAE;QACvC,IAAI,MAAM,QAAQ,MAAM,MAAM;YAC1B,OAAO;QACX;QACA,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI;IAClD;IACA,aAAa,WAAW,GAAG,SAAU,EAAE,EAAE,EAAE;QACvC,IAAI,MAAM,QAAQ,MAAM,MAAM;YAC1B,OAAO,uKAAA,CAAA,UAAO,CAAC,SAAS;QAC5B;QACA,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI;IAClD;IACA,aAAa,mBAAmB,GAAG,SAAU,CAAC;QAC1C,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,GAC7I,8KAAA,CAAA,UAAY,CAAC,uBAAuB,GAAG,KAAK,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,GAC7J,8KAAA,CAAA,UAAY,CAAC,uBAAuB;IAC5C;IACA,aAAa,mBAAmB,GAAG,SAAU,CAAC;QAC1C,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,GAC7I,8KAAA,CAAA,UAAY,CAAC,uBAAuB,GAAG,KAAK,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,8KAAA,CAAA,UAAY,CAAC,mBAAmB,GAC7J,8KAAA,CAAA,UAAY,CAAC,uBAAuB;IAC5C;IACA,YAAY;IACZ,aAAa,SAAS,CAAC,KAAK,GAAG;IAC3B,4BAA4B;IAChC;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}]}