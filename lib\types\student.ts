// Student Management Types

export interface Guardian {
  name: string
  phone: string
  email?: string
  relationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  address?: string
}

export interface EmergencyContact {
  name: string
  phone: string
  relationship: string
  address?: string
}

export interface Address {
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
}

export interface AttendanceStats {
  totalDays: number
  presentDays: number
  lateDays: number
  absentDays: number
  attendanceRate: number
  lastAttendance?: string
}

export interface Student {
  id: string // DepEd ID format (e.g., 123456789012)
  firstName: string
  middleName?: string
  lastName: string
  email: string
  course: string
  year: string
  section?: string
  grade: '7' | '8' | '9' | '10' | '11' | '12'
  status: 'Active' | 'Inactive' | 'Transferred' | 'Graduated'
  photo?: string
  qrCode?: string
  dateOfBirth?: string
  gender?: 'Male' | 'Female'
  guardian: Guardian
  emergencyContacts: EmergencyContact[]
  address: Address
  enrollmentDate: string
  lastUpdated: string
  attendanceStats?: AttendanceStats
}

// Computed properties helper
export const getFullName = (student: Student): string => {
  const parts = [student.firstName, student.middleName, student.lastName].filter(Boolean)
  return parts.join(' ')
}

// Form types for student registration/editing
export interface StudentFormData {
  // Basic Information
  id: string
  firstName: string
  middleName?: string
  lastName: string
  email: string
  dateOfBirth?: string
  gender?: 'Male' | 'Female'
  
  // Academic Information
  course: string
  year: string
  section?: string
  grade: '7' | '8' | '9' | '10' | '11' | '12'
  
  // Guardian Information
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  guardianAddress?: string
  
  // Emergency Contacts
  emergencyContacts: EmergencyContact[]
  
  // Address Information
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
  
  // Photo
  photo?: File | string
}

// Filter and search types
export interface StudentFilters {
  search?: string
  grade?: string[]
  section?: string[]
  status?: string[]
  course?: string[]
  year?: string[]
}

export interface StudentSortConfig {
  field: keyof Student | 'name'
  direction: 'asc' | 'desc'
}

// Bulk operations
export interface BulkOperation {
  type: 'export' | 'generateQR' | 'updateStatus' | 'delete'
  studentIds: string[]
  options?: Record<string, any>
}

export interface CSVImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

// QR Code types
export interface QRCodeData {
  studentId: string
  name: string
  grade: string
  section?: string
  validUntil?: string
}

export interface QRCodeBatch {
  id: string
  createdAt: string
  studentIds: string[]
  format: 'individual' | 'sheet'
  status: 'generating' | 'ready' | 'failed'
}

// Pagination
export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
}

// API Response types
export interface StudentListResponse {
  students: Student[]
  pagination: PaginationConfig
  filters: StudentFilters
}

export interface StudentResponse {
  student: Student
  success: boolean
  message?: string
}

// Validation schemas (for use with zod)
export const GRADE_LEVELS = ['7', '8', '9', '10', '11', '12'] as const
export const STUDENT_STATUSES = ['Active', 'Inactive', 'Transferred', 'Graduated'] as const
export const GUARDIAN_RELATIONSHIPS = ['Father', 'Mother', 'Guardian', 'Grandparent', 'Sibling', 'Other'] as const
export const GENDERS = ['Male', 'Female'] as const

// Default values
export const DEFAULT_STUDENT: Partial<Student> = {
  status: 'Active',
  emergencyContacts: [],
  country: 'Philippines'
}

export const DEFAULT_PAGINATION: PaginationConfig = {
  page: 1,
  pageSize: 20,
  total: 0
}
