import GenericGF from './GenericGF';
/**
 * <p>Implements Reed-Solomon decoding, as the name implies.</p>
 *
 * <p>The algorithm will not be explained here, but the following references were helpful
 * in creating this implementation:</p>
 *
 * <ul>
 * <li><PERSON>.
 * <a href="http://www.cs.cmu.edu/afs/cs.cmu.edu/project/pscico-guyb/realworld/www/rs_decode.ps">
 * "Decoding Reed-Solomon Codes"</a> (see discussion of <PERSON><PERSON>'s Formula)</li>
 * <li>J<PERSON><PERSON><PERSON>. <a href="www.mth.msu.edu/~jhall/classes/codenotes/GRS.pdf">
 * "Chapter 5. Generalized Reed-Solomon Codes"</a>
 * (see discussion of Euclidean algorithm)</li>
 * </ul>
 *
 * <p>Much credit is due to <PERSON> since portions of this code are an indirect
 * port of his C++ Reed-Solomon implementation.</p>
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
export default class ReedSolomonDecoder {
    private field;
    constructor(field: GenericGF);
    /**
     * <p>Decodes given set of received codewords, which include both data and error-correction
     * codewords. Really, this means it uses Reed-Solomon to detect and correct errors, in-place,
     * in the input.</p>
     *
     * @param received data and error-correction codewords
     * @param twoS number of error-correction codewords available
     * @throws ReedSolomonException if decoding fails for any reason
     */
    decode(received: Int32Array, twoS: number): void;
    private runEuclideanAlgorithm;
    private findErrorLocations;
    private findErrorMagnitudes;
}
